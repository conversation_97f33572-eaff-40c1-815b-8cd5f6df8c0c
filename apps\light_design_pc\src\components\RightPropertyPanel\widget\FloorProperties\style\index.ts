
import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  return {
    root: css`
      position: relative;
      overflow-y: scroll;
      max-height: calc(100vh - 100px);
      text-align: center;
    `,
    rootItem: css`
      display: flex;
      justify-content: space-between;
      padding: 5px 12px;
      input {
        width:35px;
        margin:0;
        border:0px;
      }
      input::-webkit-outer-spin-button,  
      input::-webkit-inner-spin-button{  
        -webkit-appearance: none !important;  
        margin: 0;  
      }
      select {
        border:0;
      }
      .unit {
        font-size:10px;
        right:14px;
        line-height:15px;
      }
    `,
    line: css`
      width: 85%;
      margin: 20px auto;
      height: 1px;
      background: #00000014;
    `,
    clearWholeHouse: css`
      border-radius: 4px;
      background: #F4F5F5;
      border: 1px solid #F4F5F5;
      width: 90%;
      height: 24px;
      text-align: center;
      border: none;
      cursor: pointer;
      margin-bottom:15px;
    `
  }
});
