import React from 'react'
interface ProgressProps {
    title: string;
    color: string;
  }
const progress: React.FC<ProgressProps> = (props: any) => {
    const { title, color } = props;
    return (
    <>
        <div>
            <div style={{textAlign:'center'}}>
                <img style={{width: 168,height: 168}} src={require('@/assets/images/loading1220.webp')} alt="" />
            </div>
            <div style={{color:  color, fontSize: '14px',textAlign:'center'}}>{title}</div>
        </div>
    </>
    )
}

export default progress