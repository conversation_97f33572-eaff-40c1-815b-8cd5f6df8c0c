import { IRoomSpaceAreaType } from "@layoutai/basic_data";
import { CadDrawingLayerType, EventName, LayoutAI_App, LayoutAI_Commands, LayoutAI_CursorState, TRoomEntity, TSubSpaceAreaEntity, T_AddOrDeleteEntityOperationInfo, roomSubAreaService } from "@layoutai/layout_scheme";
import { Box3, Vector3 } from "three";
import { I_MouseEvent, ZEdge, ZRect } from "@layoutai/z_polygon";
import { AI2BaseModeHandler } from "../AI2BaseModeHandler";
import { CadBaseSubHandler } from "./CadBaseSubHandler";

export class AddSpaceAreaSubHandler extends CadBaseSubHandler
{
    _space_area_type : IRoomSpaceAreaType;
    _area_entity : TSubSpaceAreaEntity;

    _target_room_entity : TRoomEntity;
    
    drawEdage: ZEdge;
    drawPoint: Vector3;
    centerpoint: Vector3;
    _candidate_point: Vector3 = null;
    _adsorb_distance: number = 50; // 吸附距离阈值

    static handler_name : string =  "AddSpaceAreaSubHandler";

    static change_area_type_event : string = "ChangeAreaTypeEvent";

    private _mouseDownPt : Vector3 | null = null;
    // 使用 点击的第一个点 
    private _startPt: Vector3 | null = null;
    // 使用 点击的第二个点
    private _endPt: Vector3 | null = null;
     // 记录按下的按键
    private _mouseButton: number = -1; 

    constructor(cad_mode_handler: AI2BaseModeHandler) {
        super(cad_mode_handler);
        this.name = AddSpaceAreaSubHandler.handler_name;
        this._area_entity = null;
        this._candidate_point = new Vector3();
        this.drawEdage = null;
        this.drawPoint = null;
        this.centerpoint = null;
        this._startPt = null;
        this._endPt = null;
    }
    
    enter(state?: number): void {
        // console.log("Enter "+this.name,this._space_area_type,"1");

        LayoutAI_App.emit_M(EventName.SubHandlerChanged,{is_default:false, name:this.name});

        if(this._cad_mode_handler.manager)
        {
            this._cad_mode_handler.manager.setCanvasCursorState(LayoutAI_CursorState.Drawing);
        }
        let subAreaLayer = this.manager.drawing_layers[CadDrawingLayerType.CadSubRoomAreaDrawing];
        if(subAreaLayer && !subAreaLayer.visible)
        {
            subAreaLayer.visible = true;
        }
        // 刷新顶菜显示栏中checkbox的状态
        this.manager.onLayerVisibilityChanged();

        this._target_room_entity = null;
        this._area_entity = null;
        this.drawEdage = null;
        this.drawPoint = null;
        this.centerpoint = null;
        this._startPt = null;
        this._endPt = null;
        
        // 确保第一次进入时就能看到吸附效果
        this.update();
    }
    
    leave(state?: number): void {
        // console.log("Leave "+this.name);
        LayoutAI_App.emit_M(EventName.SubHandlerChanged,{is_default:true, name:this.name});
        this._cad_mode_handler.manager.setCanvasCursorState(LayoutAI_CursorState.Default);

        this.updateCandidateRects();
    }

    // 修改吸附功能，增加更多吸附对象
    adsorbent(pos: Vector3) {
        let t_pos: Vector3 = new Vector3().copy(pos);
        this.drawEdage = null;
        this.drawPoint = null;
        this.centerpoint = null;
        let dist = this._adsorb_distance;
        
        // 获取所有可能的吸附目标 - 包括房间和墙体
        let room_rects = this.container._room_entities.map((room)=>room._room_poly);
        let all_rects = [...room_rects];
        
        for (let rect of all_rects) {
            for (let edge of rect.edges) {
                // 鼠标起始点移动时候的吸附
                let pp = edge.projectEdge2d(t_pos);
                if (Math.abs(pp.y) < Math.abs(dist)) {
                    dist = (pp.y);
                    this.drawEdage = edge;
                    let pos = edge.unprojectEdge2d({ x: pp.x, y: 0 });
                    this.drawPoint = pos;
                }
                
                // 吸附到中心点
                if (Math.abs(t_pos.distanceTo(edge.center)) < 60) {
                    this.drawPoint = edge.center.clone();
                    this.centerpoint = edge.center.clone();
                }
            }
            
            // 吸附到矩形的顶点
            for (let vertex of rect.vertices) {
                if (t_pos.distanceTo(vertex.pos) < dist) {
                    this.drawPoint = vertex.pos.clone();
                    dist = t_pos.distanceTo(vertex.pos);
                }
            }
        }
    }

    onmousedown(ev: I_MouseEvent): void {
        
        // 右键点击取消操作
        if (ev.button == 2) {
            if(this._area_entity?.room_entity)
            {
                this._area_entity.room_entity.removeSubAreaEntity(this._area_entity);
            }
            this._area_entity = null;
            this._startPt = null;
            this._endPt = null;

            this.update();
            LayoutAI_App.RunCommand(LayoutAI_Commands.LeaveSubHandler);
            return;
        }
        this._mouseButton = ev.button;  // 记录按下的按键
        // 记录鼠标按下位置和时间
        this._mouseDownPt = new Vector3(ev.posX, ev.posY, 0);
    }
    
    onmousemove(ev: I_MouseEvent): void {
        
        let pos = new Vector3(ev.posX, ev.posY, 0);
        // 如果鼠标按下并移动，检查是否为拖拽操作
        if(this._mouseDownPt && (this._mouseButton == 0 || this._mouseButton == 1))
        {
            this._cad_mode_handler._is_moving_element = false;
            this.update();
            return;
        }
        this._cad_mode_handler._is_moving_element = true;

        // 应用吸附 - 无论是否已选择房间都应该显示吸附效果
        this.adsorbent(pos);
        if (this.drawPoint) {
            pos.copy(this.drawPoint);
        }

        // 只有在已有起始点的情况下才更新预览
        if (this._target_room_entity && this._startPt) {
            // 临时设置第二个点用于预览
            this._endPt = pos;
            this.updateTargetRect();
        }

        // 确保每次鼠标移动都更新画布，这样吸附效果就能实时显示
        this.update();
    }
    
    onmouseup(ev: I_MouseEvent): void {
        if (!this._mouseDownPt) return;
        

        this._cad_mode_handler._is_moving_element = false;

        // 检查是否是长按移动画布的情况
        let isLongPress = this._mouseDownPt.distanceTo(new Vector3(ev.posX,ev.posY,0)) > this._longPressThreshold;
        this._mouseDownPt = null;
        this._mouseButton = -1;  // 重置按键记录
        // 检查是否是拖拽操作
        if (isLongPress) {
            this.update();
            return;
        }
        let pos = new Vector3(ev.posX, ev.posY, 0);
        // 应用吸附
        this.adsorbent(pos);
        if (this.drawPoint) {
            pos.copy(this.drawPoint);
        }
         // 如果没有起始点，设置第一个点
        if (!this._startPt) {
            this._target_room_entity = this.container._room_entities.find((room)=>room._room_poly.containsPoint(pos,20));
            if(this._target_room_entity) {
                this._startPt = new Vector3().copy(pos);
                
                // 创建区域实体 这个时候创建只是预览使用  在第二个点点击的时候才会真正生成
                if(!this._area_entity) {
                    this._area_entity = roomSubAreaService.createSubAreaPreview(this._target_room_entity,this._space_area_type);
                    
                }
                
                // 清除吸附点，避免显示多余的红点
                this.drawEdage = null;
                this.drawPoint = null;
                this.centerpoint = null;
            }
        } 
        // 已有起始点，设置第二个点并完成绘制
        else {
            
            // 完成绘制并添加操作
            if(this._area_entity) {
                if(this._area_entity.rect.h > 100 && this._area_entity.rect.w > 100) {
                    this._endPt = new Vector3().copy(pos);
                    this.updateTargetRect();    
                    let operation = new T_AddOrDeleteEntityOperationInfo("Add", this._area_entity, this.manager);
                    operation._entity = this._area_entity;
                    operation.redo();
                    this.manager.appendOperationInfo(operation);
                }else{
                    // message.warning(LayoutAI_App.t("矩形太小，无法绘制"));
                    return;
                }
            }
            // 创建过一次之后，清空类型，走roomSubRoomService的自动推荐类型 
            this._space_area_type = null;
            
            // 重置状态但不退出
            this._area_entity = null;
            this._startPt = null;
            this._endPt = null;
        }
        this.update();
    }
    
    onkeydown(ev: KeyboardEvent): boolean {
        return true;
    }
    
    onkeyup(ev: KeyboardEvent): boolean {
        return true;
    }

    private updateTargetRect()
    {
        if(this._endPt && this._startPt && this._area_entity)
        {
            let bbox = new Box3();
            bbox.expandByPoint(this._startPt);
            bbox.expandByPoint(this._endPt);

            let rect = ZRect.fromBox3(bbox);

            this._area_entity.rect.copy(rect);

            this._area_entity._updateSubAreaByParentRoomEntity();

            this.update();
        }
    }

    handleEvent(evt_name: string, evt_param: any): void {
        if(evt_name === AddSpaceAreaSubHandler.change_area_type_event)
        {
            if(evt_param)
            {
                this._space_area_type = evt_param;
            }
        }
    }
    
    drawCanvas(): void {
        if(this._area_entity) {
            this._area_entity.drawEntity(this.painter,{is_selected:false,is_draw_figure:true});
        }
        
        // 绘制第一个点（如果已设置）
        if (this._startPt) {
            let radius = 6 / this.painter._p_sc;
            this.painter.strokeStyle = "#f23dd1";
            this.painter.drawPointCircle(this._startPt, radius, 4);
        }
        
        // 绘制吸附辅助线和点
        if (this.drawEdage && this.drawPoint) {
            this.painter._context.lineWidth = 1;
            this.painter.drawLineSegment(this.drawEdage.center, this.drawPoint, '#f23dd1');
            let radius = 6 / this.painter._p_sc;
            this.painter.strokeStyle = "#f23dd1";
            this.painter.drawPointCircle(this.drawPoint, radius, 4);
        }
        
        if (this.centerpoint) {
            this.painter.drawRectText(new Vector3(this.centerpoint.x + (20 / this.painter._p_sc), this.centerpoint.y - (20 / this.painter._p_sc), 0), 30, 20, 1, "中点");
        }
    }

    // 设置吸附距离
    setAdsorbDistance(distance: number) {
        this._adsorb_distance = distance;
    }
}