import { makeAutoObservable, set } from 'mobx';
import { TRoom } from '@/Apps/LayoutAI/Layout/TRoom';
import { TRoomEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity';
import { TFurnitureEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TFurnitureEntity';
import { TBaseGroupEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseGroupEntity';
import { TBaseEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseEntity';
import { TViewCameraEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity';
interface spaceData {
  // 参数还没定义
}

interface position {
  top: string,
  left: string

}
/**
 * @description 主页数据
 */

interface Size {
  type: string,
  visible: boolean,
}

class HomeStore {
  singleSpaceData: spaceData = {};
  appOpenAsCadPlugin = false;
  ismoveCanvas = true;
  showCommandBar = false; // 是否显示小黑条
  position = {
    top: '0px',
    left: '0px'
  }
  selectData: { rooms: TRoom[], clickOnRoom: boolean } = { rooms: [], clickOnRoom: false };
  currenScheme = {} as any;
  roomInfos = [] as any;
  room2SeriesSampleArray = [] as any;
  selectedRoom = null as TRoomEntity;
  selectEntity = {} as TFurnitureEntity | TBaseGroupEntity | TRoomEntity | any;
  showReplace = false;
  designMode = 'AiCadMode' as string;
  isShowWallTopMenu = false as boolean;
  showWelcomePage = false as boolean;
  showMySchemeList = false as boolean;
  key = 0 as number;
  showDreamerPopup = false as boolean;
  initialDistance = 0;
  scale = 0.1;
  img_base64 = '';
  showSaveLayoutSchemeDialog = false as boolean;
  showSaveLayoutSchemeDialogAndExit = false as boolean;
  shareVisible = false as boolean;
  sizeInfo = {} as Size;
  roomEntities = [] as any;
  attribute = {} as any;
  menu_Key = '' as string;
  viewMode = '2D' as string;  // 当前移动端模式
  currentAIDrawImageID = '' as string;
  refreshAtlas = false as boolean;
  aiDrawLoad = false as boolean;  // 是否加载AI绘制弹窗
  showAiDraw = false as boolean;   // 是否显示AI绘制弹窗
  isSingleRoom = false as boolean;  // 是否是单个房间
  IsLandscape = false as boolean;   // 是否是横屏
  openFilterField = false as boolean;
  showButton = true as boolean;
  guideMapCurrentRoom = null as TRoom;
  zIndexOf3DViewer = 0 as number;
  showAtlas = false as boolean;
  preview3D = false as boolean;
  isShowRightSide = false as boolean;
  isShowAppScene = false as boolean;
  isShowMaterialPanel = false as boolean;
  isshowRoomInfo = false as boolean;
  sunDEnterOpen = false as boolean;    //选套系弹窗

  isdrawPicture = false as boolean; // 是否绘制图片, 标记是否点击渲染出图按钮
  drawPictureMode = 'aiDrawing' as string; // 渲染出图模式---AI绘图aiDrawing、标准渲染render
  aspectRatioMode = 1 as number // 渲染出图比例 1=>4/3  2->16:9  3->3:4  4->9:16
  atlasMode = 'aidraw' as string; // 图册模式---AI绘图aidraw、标准渲染render
  showHouseSchemeAddForm = false as boolean;
  currentViewCameras = [] as TViewCameraEntity[];
  showEnterPage = false as boolean;   // pad端是否显示进入流程的弹窗
  showSubmitInfo = false as boolean; // 是否显示提交信息弹窗
  showCabinetCompute = false as boolean; // 是否显示柜体计算弹窗
  constructor() {
    makeAutoObservable(this, {}, { autoBind: true });
  }
  setIsdrawPicture(data: boolean) {
    this.isdrawPicture = data;
  }
  setDrawPictureMode(data: string) {
    this.drawPictureMode = data;
  }
  setAspectRatioMode(data: number) {
    this.aspectRatioMode = data;
  }
  setShowButton(data: boolean) {
    this.showButton = data;
  }
  // action
  setUserInfo(data: spaceData) {
    this.singleSpaceData = data;
  }

  setAppOpenAsCadPlugin(data: boolean) {
    this.appOpenAsCadPlugin = data;
  }

  setIsmoveCanvas(data: boolean) {
    this.ismoveCanvas = data;
  }

  setShowCommandBar(data: boolean) {
    this.showCommandBar = data;
  }

  setPosition(data: position) {
    this.position = data;
  }
  setSelectData(data: { rooms: TRoom[], clickOnRoom: boolean }) {
    this.selectData = data;
  }
  setCurrenScheme(data: any) {
    this.currenScheme = data;
  }
  setRoomInfos(data: any) {
    this.roomInfos = data;
  }
  setRoom2SeriesSampleArray(data: any) {
    this.room2SeriesSampleArray = data;
  }
  setSelectedRoom(data: TRoomEntity) {
    this.selectedRoom = data;
  }
  setShowReplace(data: boolean) {
    this.showReplace = data;
  }
  setSelectEntity(data: TBaseEntity) {
    this.selectEntity = data;
  }
  setDesignMode(data: string) {
    this.designMode = data;
  }
  setIsShowWallTopMenu(data: boolean) {
    this.isShowWallTopMenu = data;
  }
  setShowWelcomePage(data: boolean) {
    this.showWelcomePage = data;
  }
  setShowMySchemeList(data: boolean) {
    this.showMySchemeList = data;
  }
  setKey(data: number) {
    this.key = data;
  }
  setShowDreamerPopup(data: boolean) {
    this.showDreamerPopup = data;
  }
  setInitialDistance(data: number) {
    this.initialDistance = data;
  }
  setScale(data: number) {
    this.scale = data;
  }
  setImgBase64(data: string) {
    this.img_base64 = data;
  }
  setShowSaveLayoutSchemeDialog(data: boolean) {
    this.showSaveLayoutSchemeDialog = data;
  }
  setShowSaveLayoutSchemeDialogAndExit(data: boolean) {
    this.showSaveLayoutSchemeDialogAndExit = data;
  }
  setShareVisible(data: boolean) {
    this.shareVisible = data;
  }
  setSizeInfo(data: Size) {
    this.sizeInfo = data;
  }
  setRoomEntites(data: any) {
    this.roomEntities = data;
  }

  setAttribute(data: any) {
    this.attribute = data;
  }
  setMenuKey(data: string) {
    this.menu_Key = data;
  }
  setViewMode(data: string) {
    this.viewMode = data;
  }
  setCurrentAIDrawImageID(data: any) {
    this.currentAIDrawImageID = data;
  }
  setRefreshAtlas(data: boolean) {
    this.refreshAtlas = data;
  }
  setAiDrawLoad(data: boolean) {
    this.aiDrawLoad = data;
  }
  setShowAiDraw(data: boolean) {
    this.showAiDraw = data;
  }
  setIsSingleRoom(data: boolean) {
    this.isSingleRoom = data;
  }
  setIsLandscape(data: boolean) {
    this.IsLandscape = data;
  }
  setOpenFilterField(data: boolean) {
    this.openFilterField = data;
  }
  setGuideMapCurrentRoom(data: TRoom) {
    this.guideMapCurrentRoom = data;
  }
  setZIndexOf3DViewer(data: number) {
    this.zIndexOf3DViewer = data;
  }
  setShowAtlas(data: boolean) {
    this.showAtlas = data;
  }
  setAtlasMode(data: string) {
    this.atlasMode = data;
  }  
  setPreview3D(data: boolean) {
    this.preview3D = data;
  }

  setIsShowRightSide(data: boolean) {
    this.isShowRightSide = data;
  }
  setIsShowAppScene(data: boolean) {
    this.isShowAppScene = data;
  }
  setIsShowMaterialPanel(data: boolean) {
    this.isShowMaterialPanel = data;
  }
  setIsShowRoomInfo(data: boolean) {
    this.isshowRoomInfo = data;
  }
  setSunDEnterOpen(data: boolean) {
    this.sunDEnterOpen = data;
  }
  setShowHouseSchemeAddForm(data: boolean) {
    this.showHouseSchemeAddForm = data;
  }
  setCurrentViewCameras(data: TViewCameraEntity[]) {
    this.currentViewCameras = data;
  }
  setShowEnterPage(data: boolean) {
    this.showEnterPage = data;
  }
  setShowSubmitInfo(data: boolean) {
    this.showSubmitInfo = data;
  }
  setShowCabinetCompute(data: boolean) {
    this.showCabinetCompute = data;
  }
}

export default HomeStore;