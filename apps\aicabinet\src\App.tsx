import { Suspense, lazy, useState, useEffect } from "react";
import { Route, Routes, Navigate, BrowserRouter } from "react-router-dom";
import { observer } from "mobx-react";
const Home = lazy(() => import("@/pages/Home/home"));
import { useUserInfo } from "@/models";
import { getUserInfo } from "@/services/user";
import { SSOPlug } from "./utils/sso-plug";

/**
 * @description 获取基础路由
 */
const getBasename = () => {
  if (process.env.NODE_ENV === "development") {
    return "";
  }
  const pathname = window.location.pathname || "";
  if (window.location.hostname?.indexOf("miniapp") > -1) {
    const pathnameArr = pathname.split("/");
    return pathnameArr[1];
  }
  return pathname.substring(1, pathname.lastIndexOf("/"));
};

function App() {
  SSOPlug.init();
  const basename = getBasename();
  const [isLoad, setIsLoad] = useState<boolean>(false);
  const { setUserInfo } = useUserInfo();
  useEffect(() => {
    getUserInfo().then((data) => {
      setUserInfo(data);
      setIsLoad(true);
    });
  }, []);

  return (
    <div className="App">
      <BrowserRouter basename={basename}>
        <Suspense>
          {isLoad ? (
            <Routes>
              <Route path="*" element={<Navigate to="/" />} />
              <Route path="/" element={<Home />} />
            </Routes>
          ) : (
            <></>
          )}
        </Suspense>
      </BrowserRouter>
    </div>
  );
}

export default observer(App);
