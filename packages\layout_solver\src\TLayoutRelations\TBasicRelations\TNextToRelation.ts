import { HallwayWidth } from "@layoutai/basic_data";
import { TFigureElement } from "@layoutai/layout_scheme";
import { Vector3 } from "three";
import { I_LayoutRelation, TLayoutRelation } from "../TLayoutRelation";



export enum NeighborDirType // 邻居的朝向
{
    Dir_0 = 0, // 同朝向
    Dir_180 = 180,
    Dir_90 = 90,
    Dir_270 = -90,
}

export interface I_NeighborInfo {
    dir_type?: NeighborDirType | number;
    /**
     *  法向偏移距离
     * 
     *  先简单点, 使用eval表示法
     * 
     *  三种类型:
     *    ---> 直接是数值
     *    ---> 直接是百分比
     *    ---> 括号开始的:   
     *       --- 默认参数有:  
     *            ---  ml,md (主元的长 主元的深)
     *            ---  tl,td (主元的长 主元的深) 
     *       --- 0.5 * ml + 0.5 * td
     */
    nor_offset_dist?: string;
    /**
     *  切向偏移距离
     * 
     *  
     */
    dir_offset_dist?: string;

    /**
     *  关联长度
     */
    length_func ?: string;


    keep_u_dv_flag?: number;


    /**
     *  条件检查
     */
    check_conditions?: string[];

    /**
     *  阵列数量
     */
    array_num?: number;
    /**
     *  阵列方式:  "rect_around"|"ellipse_around"
     */
    array_method?: "rect_around" | "ellipse_around" | string;

    array_sideable?: boolean;

    /**
     *  两侧的留白
     */
    array_side_padding?: number;

    allowed_overlap?: boolean;

    array_depth_offset?:number;
    [key:string]:any;
}
export interface I_NextToRelation extends I_LayoutRelation {
    _parent_element_id?: number;
    _target_element_id?: number;
    _dir_side?: number;
    neighbor_info?: I_NeighborInfo;
}
export class TNextToRelation extends TLayoutRelation {
    _parent_element: TFigureElement;
    _target_element: TFigureElement;
    neighbor_info: I_NeighborInfo;

    _dir_side: number;
    static TypeName: string = "NextTo";

    _array_elements: TFigureElement[];

    constructor() {
        super();
        this.type = TNextToRelation.TypeName;
        this._parent_element = null;
        this._target_element = null;
        this._array_elements = null;
        this._dir_side = 1;
        this._order = 0;
        this.neighbor_info = {};
    }

    importJson(data: I_NextToRelation) {
        let t_data = data as I_NextToRelation;
        if (this._graph) {
            this._parent_element = this._graph._curr_figure_list.figure_elements[t_data._parent_element_id] || null;
            this._target_element = this._graph._curr_figure_list.figure_elements[t_data._target_element_id] || null;
        }
        this._dir_side = data._dir_side || 1;
        this.neighbor_info = t_data.neighbor_info;
        return this;
    }
    exportJson(): I_LayoutRelation {
        let res: I_NextToRelation = super.exportJson();

        if (this._graph) {
            res._parent_element_id = this._graph._curr_figure_list.figure_elements.indexOf(this._parent_element);
            res._target_element_id = this._graph._curr_figure_list.figure_elements.indexOf(this._target_element);
        }
        res.neighbor_info = {};
        res._dir_side = this._dir_side || 1;
        for (let key in this.neighbor_info) {
            res.neighbor_info[key] = this.neighbor_info[key];
        }
        return res;
    }

    apply(): void {
        if (!this._parent_element || !this._target_element) return;



        if (this.neighbor_info?.array_method) {
            this.applyArray();
            return;
        }

        let dir_type = this.neighbor_info.dir_type || NeighborDirType.Dir_0;

        let parent_rect = this._parent_element.rect;
        let target_rect = this._target_element.rect;

        let target_center = new Vector3();

        let nor_dir_y = parent_rect.dv.clone();
        let nor_dir_x = parent_rect._nor.clone();
        let angle = dir_type / 180 * Math.PI;

        let target_nor = nor_dir_x.clone().multiplyScalar(Math.cos(angle)).add(nor_dir_y.multiplyScalar(Math.sin(angle)));


        target_rect._nor.copy(target_nor);

        target_rect.updateRect();


        let dv = parent_rect.dv.clone();
        let nor = parent_rect._nor.clone();

        if(this.neighbor_info.length_func) // 如果长度设置了
        {
            let len = this.getFuncVal(this.neighbor_info.length_func,parent_rect.w);
            target_rect._w = len || target_rect.w;
            target_rect.updateRect();
        }
        dv.multiplyScalar(this.getFuncVal(this.neighbor_info.dir_offset_dist, parent_rect._w));
        nor.multiplyScalar(this.getFuncVal(this.neighbor_info.nor_offset_dist, parent_rect._h));


        target_center = parent_rect.rect_center.clone().add(dv).add(nor);

        target_rect.rect_center = target_center;
        // delete target_rect._u_dv;

        let keep_u_dv_flag = this.neighbor_info.keep_u_dv_flag || 1;
        target_rect._u_dv_flag = parent_rect._u_dv_flag * keep_u_dv_flag;

        let allowed_overlap = (this.neighbor_info.allowed_overlap === undefined) ? true : this.neighbor_info.allowed_overlap;

        if (!this._target_element?._solver_params?._init_nor) {
            if (!this._target_element._solver_params) {
                this._target_element._solver_params = {};
            }
            this._target_element._solver_params._init_nor = target_nor.clone();

            this._target_element._solver_params._init_position = target_center.clone();


            let min_dist = -HallwayWidth * 1.5;
            let max_dist = HallwayWidth * 1.5;

            if (!allowed_overlap) {
                let points = parent_rect.getRectPoints();
                let mp_dist = 9999;
                for (let p of points) {
                    let nor_dist = (p.clone().sub(target_center).dot(target_nor));

                    if (Math.abs(nor_dist) < Math.abs(mp_dist)) {
                        mp_dist = nor_dist;
                    }
                }
                if (mp_dist < 0) {
                    mp_dist += this._target_element.rect._h / 2.;
                    min_dist = HallwayWidth;
                }
                else {
                    mp_dist -= this._target_element.rect._h / 2.;
                    max_dist = mp_dist;
                }
            }


            this._target_element._solver_params._nor_offset_range = { min: min_dist, max: max_dist, weight: 2. };



            let len = -this._parent_element.rect._w / 4. * 2;
            this._target_element._solver_params._dir_offset_range = { min: -len, max: len, weight: 50 };
        }


    }

    applyArray() {
        if (!this.neighbor_info?.array_method) return [];

        if (this.neighbor_info.array_method == "rect_around") {
            let res: TFigureElement[] = [];

            let ml = this._parent_element?.params?.length || 0;
            let md = this._parent_element?.params?.depth || 0;

            let tl = this._target_element?.params?.length || 0;
            let td = this._target_element?.params?.depth || 0;
            let array_num = this.neighbor_info.array_num;
            let side_able = this.neighbor_info.array_sideable || false;
            let side_padding = this.neighbor_info.array_side_padding || 0;
        
            let side_depth_offset = this.neighbor_info.array_depth_offset || 210;
            ml = ml - side_padding;

            if (!array_num) {
                array_num = Math.floor((ml - 200) / tl) * 2;

            }

            if (array_num >= 6) array_num = 6;
            let d_y = md / 2 + side_depth_offset;
            let d_x = ml / 4;

            if (array_num <= 1) {
                d_x = 0;
            }
            else if (array_num <= 4) {
                d_x = ml / 4;
            }
            else if (array_num <= 6) {
                d_x = ml / 4;

            }


            let target_dv_list = [{ x: -d_x, y: d_y }, { x: d_x, y: d_y }, { x: -d_x, y: -d_y }, { x: d_x, y: -d_y }, { x: -(ml + side_padding + 200) / 2, y: 0 }, { x: (ml + side_padding + 200) / 2, y: 0 }];
            let target_nor_list = [{ x: 0, y: -1 }, { x: 0, y: -1 }, { x: 0, y: 1 }, { x: 0, y: 1 }, { x: 1, y: 0 }, { x: -1, y: 0 }];

            if (!side_able && array_num == 6) {
                d_x = ml / 3;
                target_dv_list = [{ x: -d_x, y: d_y }, { x: d_x, y: d_y }, { x: 0, y: d_y }, { x: -d_x, y: -d_y }, { x: 0, y: -d_y }, { x: d_x, y: -d_y }];
                target_nor_list = [{ x: 0, y: -1 }, { x: 0, y: -1 }, { x: 0, y: -1 }, { x: 0, y: 1 }, { x: 0, y: 1 }, { x: 0, y: 1 }];
            }
            if (!side_able && array_num == 6 && this._parent_element.category == "岛台") {
                d_x = ml / 2.5;

                target_dv_list = [{ x: d_x, y: d_y }, { x: d_x - tl - 50, y: d_y }, { x: d_x - 2 * tl - 100, y: d_y }, { x: d_x, y: -d_y }, { x: d_x - tl - 50, y: -d_y }, { x: d_x - 2 * tl - 100, y: -d_y }];
                target_nor_list = [{ x: 0, y: -1 }, { x: 0, y: -1 }, { x: 0, y: -1 }, { x: 0, y: 1 }, { x: 0, y: 1 }, { x: 0, y: 1 }];
            }


            for (let i = 0; i < array_num; i++) {
                let figure = new TFigureElement(this._target_element.exportJson());
                figure._array_ref_figure = this._target_element;

                let center_pos = this._parent_element.rect.unproject(target_dv_list[i]);
                let center_nor = this._parent_element.rect.unproject(target_nor_list[i]).clone().sub(this._parent_element.rect.unproject({ x: 0, y: 0 })).normalize();

                figure.rect._nor.copy(center_nor);

                figure.rect.rect_center = center_pos;

                res.push(figure);
            }
            this._array_elements = res;
            return res;
        }
        else if(this.neighbor_info.array_method == "ellipse_around")
        {
            this._array_elements =[];
            let res : TFigureElement[] = [];

            let ml = this._parent_element?.params?.length || 0;
            let md = this._parent_element?.params?.depth || 0;

            let tl = this._target_element?.params?.length || 0;
            let td = this._target_element?.params?.depth || 0;
            let array_num = this.neighbor_info.array_num;
            let side_able = this.neighbor_info.array_sideable || false;
            let side_padding = this.neighbor_info.array_side_padding || 0;

           
            let xl = ml / 2 + td / 2 - side_padding;
            if(!array_num)
            {
                array_num = Math.floor((ml - 200) / tl) *2;
                
            }
            

            
            let target_dv_list = [];
            let target_nor_list = [];

            let dv0 = new Vector3(1,0,0);
            let nor0 = dv0.clone().negate();
            for(let i=0; i < array_num; i++)
            {
                let angle = Math.PI * 2 / array_num * (i+0.5);

                target_dv_list.push(dv0.clone().applyAxisAngle({x:0,y:0,z:1} as any,angle).multiplyScalar(xl));
                target_nor_list.push(nor0.clone().applyAxisAngle({x:0,y:0,z:1} as any,angle).multiplyScalar(xl));
            }

            for(let i=0; i < array_num; i++)
            {
                let figure = new TFigureElement(this._target_element.exportJson());
                figure._array_ref_figure = this._target_element;

                let center_pos = this._parent_element.rect.unproject(target_dv_list[i]);
                let center_nor  = this._parent_element.rect.unproject(target_nor_list[i]).clone().sub( this._parent_element.rect.unproject({x:0,y:0})).normalize();

                figure.rect._nor.copy(center_nor);

                figure.rect.rect_center = center_pos;

                res.push(figure);
            }
            this._array_elements = res;
            return res
        }
        return [];

    }

    static extractRelation(parent_element: TFigureElement, target_element: TFigureElement, neighbor_info: I_NeighborInfo = null) {
        let relation = new TNextToRelation();
        relation._parent_element = parent_element;
        relation._target_element = target_element;
        let parent_rect = parent_element.rect;
        let target_rect = target_element.rect;

        relation._dir_side = parent_rect.dv.dot(target_rect.rect_center.clone().sub(parent_rect.rect_center)) >= 0 ? 1 : -1;

        if (neighbor_info) {
            relation.neighbor_info = neighbor_info;
        }
        else {
            let parent_rect = parent_element.rect;
            let target_rect = target_element.rect;


            let nor_dir_y = parent_rect.dv.clone();
            let nor_dir_x = parent_rect._nor.clone();


            let tx = target_rect._nor.dot(nor_dir_x);
            let ty = target_rect._nor.dot(nor_dir_y);



            let t_dv = target_rect.rect_center.clone().sub(parent_rect.rect_center.clone());
            let nor_dist = t_dv.dot(nor_dir_x);
            let dir_dist = t_dv.dot(nor_dir_y);

            // console.log(parent_element.category,target_element.category,nor_dist, dir_dist);

            let angle = Math.atan2(ty, tx) / Math.PI * 180;


            relation.neighbor_info = {
                dir_type: angle,
                allowed_overlap: true,
                dir_offset_dist: '' + dir_dist,
                nor_offset_dist: '' + nor_dist,
            }

        }

        return relation;
    }

    checkConditions() {
        if (this.neighbor_info.check_conditions) {

            let ml = this._parent_element?.params?.length || 0;
            let md = this._parent_element?.params?.depth || 0;

            let tl = this._target_element?.params?.length || 0;
            let td = this._target_element?.params?.depth || 0;
            for (let eval_str of this.neighbor_info.check_conditions) {
                try {
                    let val: boolean = eval(eval_str);

                    // console.log(val,ml,md,tl,td);
                    if (!val) {
                        return false;
                    }
                } catch (error) {

                }
            }
        }

        return true;
    }


    getFuncVal(func_text: string, refer_length: number = 0.) {
        if (!func_text) return 0;
        let t_dist = 0;
        if (func_text.indexOf("(") >= 0) {
            let ml = this._parent_element?.params?.length || 0;
            let md = this._parent_element?.params?.depth || 0;

            let tl = this._target_element?.params?.length || 0;
            let td = this._target_element?.params?.depth || 0;

            let dir_side = this._dir_side || 1;
            try {
                let val = eval(func_text);

                t_dist = val;

            } catch (error) {
            }

        }
        else if(func_text.indexOf("%")>=0)
        {
            let  tt = parseFloat(func_text.replace("%","")) / 100.0;
            t_dist = refer_length * tt;
        }
        else {
            t_dist = parseFloat(func_text);
        }
        return t_dist;
    }
}