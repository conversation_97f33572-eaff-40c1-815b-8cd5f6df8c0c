




import { EventName, LayoutAI_Commands, TBaseEntity, TFurnitureEntity } from "@layoutai/layout_scheme";
import { Vector3 } from "three";
import { I_MouseEvent, ZRect } from "@layoutai/z_polygon";
import { AI2BaseModeHandler } from "../AI2BaseModeHandler";
import { CadBaseSubHandler } from "./CadBaseSubHandler";


export class CombinationHandler extends CadBaseSubHandler {
    _previous_rect: ZRect;
    _start_pos: Vector3;
    _last_pos: Vector3;
    _draw_box_rect: ZRect;

    constructor(cad_mode_handler: AI2BaseModeHandler) {
        super(cad_mode_handler);
        this.name = 'CombinationHandler';
        this._previous_rect = null;

        this._last_pos = null;
        this._start_pos = null;
        this._draw_box_rect = null;  //框选的矩形

        this.group_rect = null;  // 组合的矩形
    }

    enter(state?: number): void {
        this._cad_mode_handler._is_moving_element = false;
    }

    onmousedown(ev: I_MouseEvent): void {

        let pos = { x: ev.posX, y: ev.posY, z: 0 };

        this._start_pos = new Vector3(ev.posX, ev.posY, 0); // 记录鼠标按下的位置
        this._draw_box_rect = new ZRect(1, 1);
        this._draw_box_rect.rect_center = this._start_pos;
        this.EventSystem.emit_M(EventName.SelectingTarget, null, ev._ev);
        this._cad_mode_handler._is_moving_element = true;


        this.updateSelectionState();

    }

    onmousemove(ev: I_MouseEvent): void {

        if (!this._draw_box_rect) return;
        let pos = { x: ev.posX, y: ev.posY, z: 0 };
        if (ev.buttons == 1) {
            if (!this._start_pos) {
                this._cad_mode_handler.runCommand(LayoutAI_Commands.LeaveSubHandler);
                return;
            }
            let current_pos = new Vector3(ev.posX, ev.posY, 0);
            this._draw_box_rect._w = ev.posX - this._start_pos.x;
            this._draw_box_rect._h = ev.posY - this._start_pos.y;
            this._draw_box_rect.rect_center = this._start_pos.clone().add(current_pos).multiplyScalar(0.5);
            this._draw_box_rect.updateRect();
            this.update();
        }
        if (ev.buttons == 0) {
            this.updateHoverRect(pos);
        }

    }

    onmouseup(ev: I_MouseEvent): void {

        let furniture_rects = this.container.getCandidateRects(["Furniture"]
            , { ignore_realtypes: ["Lighting", "Decoration"] }, { "Funiture": 10, "Door": 2, "Window": 2 }, this.container._drawing_layer_mode === "AIMatching");
        if (this._draw_box_rect && this._draw_box_rect.min_hh > 1) {
            this.selected_target.selected_combination_entitys = [];

            for (let rect of furniture_rects) {
                if (this._draw_box_rect && this._draw_box_rect.containsPoly(rect)) {
                    let entity = TBaseEntity.getEntityOfRect(rect) as TFurnitureEntity;
                    if(entity)
                    {
                        this.selected_target.selected_combination_entitys.push(entity);
                    }
                }
            }
        }
        else {
            if (!this.selected_target.selected_combination_entitys || this.selected_target.selected_combination_entitys.length == 0) {
                let current_rect = this.selected_target.selected_rect;
                if (current_rect) {
                    let current_entity = TBaseEntity.getEntityOfRect(current_rect);
                    if (current_entity?.type === "Furniture") {
                        {
                            let entity = current_entity as TFurnitureEntity;
                            this.selected_target.selected_combination_entitys = [entity];
                        }
                    }
                }

            }

            let rect = this.getTargetRectContainsPos(this._start_pos);
            // console.log(rect);

            if (furniture_rects.indexOf(rect) >= 0) {
                if (!this.selected_target.selected_combination_entitys) this.selected_target.selected_combination_entitys = [];
                let entity =TBaseEntity.getEntityOfRect(rect) as TFurnitureEntity;
                let id = this.selected_target.selected_combination_entitys.indexOf(entity);
                if (id < 0) this.selected_target.selected_combination_entitys.push(entity);
            }
        }


        this._draw_box_rect = null;

        this.combineSelectedCombinationRects();

        this.updateSelectionState();
        this.update();

        this._cad_mode_handler._is_moving_element = false;
        let rightTop = this.computeRightVerical();
        this.update();

        if (rightTop) {
            let _pp = this.painter.worldToCanvas(rightTop);
            let entity = TBaseEntity.getEntityOfRect(this.selected_target.selected_rect);
            this.EventSystem.emit_M(EventName.SelectingTarget, entity, ev._ev, _pp);
        }

        this._cad_mode_handler.runCommand(LayoutAI_Commands.LeaveSubHandler);
    }

    onwheel(ev: WheelEvent): void {

        // 
    }


    drawCanvas(): void {
        super.drawCanvas();
        let painter = this._cad_mode_handler.painter;
        painter.fillStyle = "#d5e4fb";
        painter.strokeStyle = "#147FFA";
        painter._context.lineWidth = 1;
        if (this._draw_box_rect) {
            this.painter.fillPolygon(this._draw_box_rect, 0.5);

            this.painter.strokePolygons([this._draw_box_rect]);
        }
    }
}