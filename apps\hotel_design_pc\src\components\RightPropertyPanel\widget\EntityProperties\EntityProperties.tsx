import React, { useEffect, useState, useRef } from 'react';
import { PanelContainer } from '@svg/antd-cloud-design';
import useStyles from '@/components/RightPropertyPanel/style';
import { observer } from "mobx-react-lite";
import { useStore } from '@/models';
import { useTranslation } from 'react-i18next';
import { PropertyPanel } from '@svg/antd-cloud-design';
import RowButton from '@/components/AttributeEdit/widget/RowButton/rowButton';
import RotateWidget from '@/components/AttributeEdit/widget/RotateWidget';
import ColorWidget from '@/components/AttributeEdit/widget/ColorWidget';
import SubAreaWidget from "@/components/AttributeEdit/widget/SubAreaWidget/subAreaWidget";
import { TBaseEntity } from '@layoutai/layout_scheme';

const EntityProperties: React.FC<{Entity:TBaseEntity}> = ({Entity}) => {
    if(!Entity?.type) return <></>
    
  const store = useStore();
  let properties = Entity.getUiProperties();

  let params = {
    mode: 'init', // 当前模式，支持edit ,hide 
    title: Entity.getTitle(), // 当前面板的标题
    properties: properties
}
  return (
     <PropertyPanel 
        key={store.homeStore.key}
        widgets={{ RowButton, RotateWidget,ColorWidget,SubAreaWidget}} 
        title={params.title} 
        draggable={false} 
        schema={params}
        top={48}
        framed={false}
        />
        );
};


export default observer(EntityProperties);
