import React, { useEffect, useRef } from 'react'
// import { TestMainPC } from '../../../test/TestMainPC';
import { appContext } from '../../../context/AppContext';

const Design2D: React.FC = () => {
    const containerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (containerRef.current) {
            let design2dCtx = appContext.design2DContext;
            if (design2dCtx) {
                design2dCtx.bindMainDiv(containerRef.current);
                // 启动渲染循环
                design2dCtx.object2DManager.updateObject2D();
                design2dCtx.canvas2DManager.startRender();
                // 初始化Design2D API中心
            }
        }

        // 清理函数
        return () => {
            if (appContext.design2DContext) {
                appContext.design2DContext.canvas2DManager.stopRender();
            }
        };
            
    }, []);


    return (
        <div
            ref={containerRef}
            className="canvas"
            style={{
                width: '100%',
                height: '100%',
            }}
        >
            {/* <button
                onClick={async () => {
                    await TestMainPC.testApplySeries();
                    if (appContext.design2DContext) {
                        appContext.design2DContext.object2DManager.updateObject2D();
                        appContext.design2DContext.canvas2DManager.updateCanvas(true);
                    }
                }}
                style={{
                    position: 'absolute',
                    bottom: '20px',
                    right: '20px',
                    padding: '8px 16px',
                    backgroundColor: '#1890ff',
                    color: '#fff',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer'
                }}
            >
                应用套系
            </button> */}
        </div>
    )
}

export default Design2D

