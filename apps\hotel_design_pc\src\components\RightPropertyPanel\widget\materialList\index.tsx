import { TRoomEntity,TRoom,TSeriesSample,LayoutAI_App,TFigureElement, EventName, g_FigureImagePaths, FigureCategoryManager, LayoutAI_Events} from '@layoutai/layout_scheme';
import { AI_PolyTargetType, I_MaterialMatchingItem } from '@layoutai/basic_data';
import { Image, Tabs } from '@svg/antd';
import { observer } from "mobx-react-lite";
import React, { useEffect, useReducer, useRef, useState } from 'react';
import useStyles from './style';
import { useStore } from '@/models';

interface RoomSeriesPlanProps {
  materialList: materialListProps[];
  multiExpand?: boolean;
}
interface materialListProps {
  label: string;
  figureList: figure[];
  init_expanded?: boolean;
}
interface figure {
  img: string;
  title: string;
  category: string;
}
interface Module {
  image: string;
  title: string;
  label: string;
}
interface FigureItem {
  image_path: string, room: TRoom,
  isFloor?: boolean, label: string,
  title: string, title2: string, title3: string,
  img: string, img1: string, img2: string,
  centerTitle: string, bottomTitle: string, checked?: boolean, area: string, isLocked?: boolean,
  figure_element?: TFigureElement, imgTitle?: string
};

const MaterialList: React.FC<RoomSeriesPlanProps> = ({ materialList, multiExpand = true }) => {
  const t = LayoutAI_App.t;
  const { styles } = useStyles();
  const [materialListInfo, setMaterialListInfo] = useState([]);
  const menuBoxRef = useRef(null);
  const [, forceUpdate] = useReducer(x => x + 1, 0);
  const store = useStore();
  let {setShowReplace} = store.homeStore;
  const handleItemClick = (subItem: FigureItem) => {
    if (subItem.figure_element) {
      LayoutAI_App.DispatchEvent(LayoutAI_Events.AIMatchingFigureSelected, subItem.figure_element);
      LayoutAI_App.emit_M(EventName.FigureElementSelected, subItem.figure_element);
      store.homeStore.selectEntity = subItem.figure_element;
      setShowReplace(true);
    }

  }

  const figureInfo = (itemLabel: string, subItem: FigureItem, subIndex: string) => {
    return <div
      key={"t_figure" + subIndex}
      className={`
        ${styles.figure_item} 
        ${styles.figure_item_qt}  
        ${["硬装", "定制", "软装"].includes(itemLabel) ? styles.figure_item_sp : "" /*[i18n:ignore]*/} 
        ${subItem.figure_element?.locked ? styles.figure_item_locked : ''} 
        ${subItem.checked ? styles.figure_item_checked : ''}`
      }
      onClick={() => {
        if (subItem.figure_element?.locked !== true && [t("硬装"), t("定制"), t("软装"), t("图例"), t("家具")].includes(itemLabel)) {
          handleItemClick(subItem)
        }
      }} >

      { !subItem.figure_element.haveDeletedMaterial() && !subItem.figure_element.haveMatchedMaterial2() && <div className={styles.delete_icon} /> }

      {subItem.figure_element?.locked && <div className={styles.mask_item_locked}></div>}

      <Image src={(`${subItem.image_path}`)} preview={false} />
      {subItem.imgTitle && <div className={"imgTitle"}>{subItem.imgTitle}</div>}
      <div style={{ width: '70%' }}>
        <div className={styles.category} title={subItem.title}>{t(subItem.title)} </div>
        <div className={styles.size}>{t(subItem.centerTitle)}</div>
        <div className={styles.size} title={t(subItem.bottomTitle)}>{t(subItem.bottomTitle)}</div>
      </div>
      {subItem.isFloor !== true &&
        <div style={{ position: "absolute", right: "0" }}>
          <div className={styles.lock_icon + " lock_icon iconfont iconunlock_fill " + (subItem.figure_element?.locked ? "iconlock_fill" : "")} onClick={(ev) => {
            if (subItem.room && subItem.room?.locked) return;
            if (subItem.figure_element) {
              subItem.figure_element.locked = !subItem.figure_element.locked;
              LayoutAI_App.emit(EventName.RoomMaterialsUpdated, true);
              LayoutAI_App.instance.update();
              ev.stopPropagation();

            }
          }}>
          </div>
        </div>

      }
    </div>
  }
  useEffect(() => {
    if (materialList.length > 0) {
      setMaterialListInfo(materialList);
    }
  }, [materialList])
  return (
    <div className={styles.root}>
      <div className={styles.menu_box}
        id='menu_box'
        ref={menuBoxRef}>
        <Tabs 
          className="menu" 
          items={materialListInfo.map((item) => ({
            key: item.label,
            label: t(item.label),
            children: (
              <div className={styles.figureListInfo}>
                {item.figureList.map((subItem: FigureItem, subIndex: number) => 
                  figureInfo(item.label, subItem, subIndex.toString())
                )}
              </div>
            )
          }))}
        />
      </div>
    </div>
  );
};


export default observer(MaterialList);
