import { I_FigureElement } from "@layoutai/basic_data";
import { I_DecorationRule, LayoutAI_App, LayoutAI_Configs, TFigureElement, TGraphBasicConfigs, TLayoutEntityContainer, TLayoutOptimizer, TRoom, WPolygon } from "@layoutai/layout_scheme";
import { Vec3toMeta, ZEdge, ZPolygon, ZRect, compareNames } from "@layoutai/z_polygon";
import { Box3, Vector3 } from "three";


export class TPostDecoratesLayout {

    constructor() {

    }
    /**
     * 后处理 饰品生成
     * @param room  
     */
    static post_add_decorations(room: TRoom, figure_elements: TFigureElement[] = null) {

        if (!figure_elements) {
            figure_elements = room._furniture_list;
        }
        if (!room.max_R_shape) {
            room.updateFeatures();
        }
        // 厨房的饰品要单独后处理
        if(compareNames([room.roomname],["厨房"])==1)
        {
            TPostDecoratesLayout.post_clean_decorations(room, figure_elements);
            let main_lights = TPostDecoratesLayout._post_add_main_lighting(room, figure_elements);

            figure_elements.push(...main_lights);

            // TPostDecoratesLayout.post_add_electricity_decorations(room,figure_elements);

            figure_elements.sort((a,b)=>{
                return a.default_drawing_order - b.default_drawing_order;
            });
            return;
        }
        if(figure_elements.length == 0) return;
        TPostDecoratesLayout.post_clean_decorations(room, figure_elements);
        TPostDecoratesLayout.post_add_on_table_decorations(room, figure_elements);
        TPostDecoratesLayout.post_add_wall_decorations(room, figure_elements);


        figure_elements.sort((a,b)=>{
            return a.default_drawing_order - b.default_drawing_order;
        })

    }


    static post_clean_decorations(room: TRoom, figure_elements: TFigureElement[] = null) {

        figure_elements = figure_elements = room._furniture_list;

        if(room._room_entity)
        {
            let decoration_entites = room._room_entity.furniture_entities.filter((entity)=>compareNames([entity.category],["筒灯","墙饰"]));
            if(LayoutAI_App.instance.layout_container)
            {
                (LayoutAI_App.instance.layout_container as TLayoutEntityContainer).removeFurnitureEntities(decoration_entites);
            }
            decoration_entites.forEach(entity=>entity.dispose());
            room._room_entity.decoration_elements.length = 0;
        }
        let decorations = figure_elements.filter(figure_element => figure_element._is_decoration === true || compareNames([figure_element.category],["筒灯","墙饰"]));
        
        if (decorations) {
            for (let fig of decorations) {
                let id = figure_elements.indexOf(fig);
                if (id >= 0) {
                    figure_elements.splice(id, 1);
                }
            }
        }
        figure_elements.forEach((ele)=>{
            if(ele.decorationElements)
            {
                ele.decorationElements.forEach((ele)=>{
                    ele.dispose3d();
                });
                ele.decorationElements =null;
            }
        });


    }
    static post_clean_electricity_decorations(room:TRoom,figure_elements:TFigureElement[] = null){
        let decorations = figure_elements.filter(figure_element =>figure_element._decoration_type==="Electricity" || compareNames([figure_element.category],["插座","开关"]));
        if (decorations) {
            decorations.forEach((ele)=>{
                let id = figure_elements.indexOf(ele);
                figure_elements.splice(id,1);
            })
        }

    }

    /**
     * 后处理 添加灯具
     * @param room  
     */
    static post_add_lighting(room: TRoom, figure_elements: TFigureElement[], options:{add_main_lights?:boolean, add_decoration?:boolean} = {add_main_lights:true, add_decoration:true}) {

        let main_lights = TPostDecoratesLayout._post_add_main_lighting(room, figure_elements);

        if(options.add_main_lights)
        {

            figure_elements.push(...main_lights);
        }


        if(options.add_decoration)
        {
            TPostDecoratesLayout._post_add_down_lighting(room,figure_elements);

            // let down_lights = TPostDecoratesLayout._post_add_decortaion_lighting(room, figure_elements, main_lights);

            // figure_elements.push(...down_lights);

            if(room._room_entity?.room_ceiling_entity)
            {
                room._room_entity.room_ceiling_entity.bindDownLights(figure_elements);
            }
        }



    }

    static _post_add_main_lighting(room: TRoom, figure_elements: TFigureElement[]) {

        if(!LayoutAI_Configs.Configs.is_post_add_main_lights){
            let main_lights = figure_elements.filter((ele)=>compareNames([ele.sub_category,ele.category],["主灯"]));
            main_lights.forEach((light)=>{
                figure_elements.splice(figure_elements.indexOf(light),1);
            })
            if(room._room_entity)
            {
                let main_lights = room._room_entity.furniture_entities.filter((ele)=>compareNames([ele.category],["主灯"]));
                if(main_lights)
                {
                    (LayoutAI_App.instance.layout_container as TLayoutEntityContainer).removeFurnitureEntities(main_lights);
                    main_lights.forEach((ele)=>ele.dispose());
                }

            }
            
            return [];
        }
        let has_light = false;
        for (let figure of figure_elements) {
            if (compareNames([figure.sub_category, figure.public_category, figure.category], ["主灯"])) {
                has_light = true;
            }
        }
        // 只处理没有添加灯具的空间
        if(has_light) return [];
        let main_lights: TFigureElement[] = [];
        let main_light_figure: I_FigureElement = {
            category: "主灯",
            sub_category: "主灯",
            _is_decoration: false,
            _decoration_type : "Lighting",
            min_z: 2500,
            max_z: 2600,
            params: {
                length: 700,
                depth: 700,
                height: 700
            }
        }

        let func_add_light = (light: TFigureElement) => {
            let id = main_lights.findIndex((ele) => ele.rect.rect_center.distanceTo(light.rect.rect_center) < 500);
            if (id < 0) main_lights.push(light);
        }
        if (compareNames([room.roomname], ["客餐厅"])) {
            // 沙发 和 电视柜 所构成的最大矩形

            let sofa_element: TFigureElement = null;
            let tv_element: TFigureElement = null;
            let dinning_table_element: TFigureElement = null;

            for (let ele of figure_elements) {
                if (compareNames([ele.sub_category, ele.public_category], ["多人沙发", "转角沙发", "直排沙发"])) {
                    sofa_element = ele;
                }
                if (compareNames([ele.sub_category, ele.public_category], ["电视柜"])) {
                    tv_element = ele;
                }
                if (compareNames([ele.sub_category, ele.public_category], ["餐桌"])) {
                    dinning_table_element = ele;
                }
            }

            if (sofa_element) {
                let bbox = new Box3();

                bbox.union(sofa_element.rect.computeBBox());

                if (tv_element) {
                    bbox.union(tv_element.rect.computeBBox());
                }
                else {
                    let pp = room.room_shape._poly.getRayIntersection(sofa_element.rect.rect_center, sofa_element.rect.nor);
                    if (pp && pp.point) {
                        bbox.expandByPoint(pp.point);
                    }
                }

                let sofa_main_light = new TFigureElement(main_light_figure);
                sofa_main_light.category = "客厅主灯";
                sofa_main_light.sub_category = "主灯";
                let b_center = bbox.getCenter(new Vector3());
                sofa_main_light.rect.nor = sofa_element.rect.nor;
                sofa_main_light.rect.rect_center = b_center;
                func_add_light(sofa_main_light);
            }

            if (dinning_table_element) {
                let dinning_table_light = new TFigureElement(main_light_figure);
                dinning_table_light.rect.nor = dinning_table_element.rect.nor;
                dinning_table_light.rect.rect_center = dinning_table_element.rect.rect_center;
                dinning_table_light.category = "餐厅主灯";
                dinning_table_light.sub_category = "主灯";
                func_add_light(dinning_table_light);

            }

        }
        else {
            let main_rect: ZRect = null;
            if (room.max_R_shape) {
                main_rect = room.max_R_shape.getRect();
            }
            if (room.max_L_shape) {
                let t_main_rect = ZRect.computeMainRect(room.max_L_shape._poly);
                if (t_main_rect.max_hh < main_rect.max_hh + 600) {
                    main_rect = t_main_rect;
                }

            }
            if (main_rect) {

                let room_main_light = new TFigureElement(main_light_figure);
                room_main_light.rect.nor = main_rect.nor;
                room_main_light.rect.rect_center = main_rect.rect_center;
                func_add_light(room_main_light);
            }
            else {
                // console.log(room);
            }
        }

        return main_lights;
    }

    /**
     * 沿着墙添加筒灯的逻辑
     * @param room  
     * @param figure_elements 
     */
    static _post_add_down_lighting(room : TRoom, figure_elements : TFigureElement[]) {

        if(compareNames([room.roomname],["卧室","客餐厅"])==0) return;

        let down_lights = figure_elements.filter((light)=>compareNames([light.category,light.sub_category],["筒灯","射灯"]));

        down_lights.forEach(ele=>{
            let id = figure_elements.indexOf(ele);
            if(id>=0) figure_elements.splice(id,1); 
        })


        let decoration_figure : I_FigureElement=  {
                    category : "筒灯",
                    public_category :"筒灯",
                    sub_category : "筒灯",
                    _is_decoration : true,
                    _decoration_type :"Lighting",
                    min_z : 2350,
                    max_z : 2450,
                    params : {
                        length : 100,
                        depth : 100,
                        height : 100
                    }
        };


        let target_poly : ZPolygon = null;

        if(compareNames([room.roomname],["客餐厅"]))
        {
            target_poly = room.room_shape._feature_shape._w_poly;
        }
        else {
            target_poly = room.max_R_shape._poly;

        }

        if(!target_poly) return;



        let edges = target_poly.edges;
        let down_light_distance = 1200;
        let end_distance = 450;
        let offset_len = 190;

        let main_background_wall_figures = figure_elements.filter(ele=>compareNames([ele.category,ele.public_category,ele.sub_category],["背景墙"])) || [];
        for(let edge of edges)
        {

            if(compareNames([room.roomname],["卧室"])) // 卧室-垂直于背景墙的边 就不用布置筒灯了
            {
                if(main_background_wall_figures[0])
                {
                    let back_nor = main_background_wall_figures[0].rect.nor;

                    if(Math.abs(edge.nor.dot(back_nor)) < 0.2)
                    {
                        continue;
                    }
                }
            }
            let win = WPolygon.getWindowOnEdge(edge);
            if(win) {
                if(win.type === "Hallway" || (win.type === "Door" && win.length < 2000) )
                {
                    continue;
                }

            }
            let front_edges = edges.filter(t_edge=>{
                if(t_edge.nor.dot(edge.nor)>-0.9) return false;
                let pp = edge.projectEdge2d(t_edge.center);
                if(pp.y > 0) return false;
                if(pp.x < 0 || pp.x > edge.length) return false;
                return true;
            
            });

            let depth = 5000;

            if(front_edges[0])
            {
                depth = Math.abs(edge.projectEdge2d(front_edges[0].center).y);
            }


            let center_x = edge.length / 2;

            let tx_list :number[] = [];

            let m_num = Math.round(edge.length / down_light_distance) + 1;
            let backgroud_wall_figures = main_background_wall_figures.filter( ele=>compareNames([ele.category,ele.public_category],["背景墙"]) 
            && Math.abs(edge.projectEdge2d(ele.rect.back_center).y) < 100);

            let chuanglian_figures = figure_elements.filter( ele=>compareNames([ele.category,ele.public_category],["窗帘"]) 
            && Math.abs(edge.projectEdge2d(ele.rect.back_center).y) < 100);



            if(chuanglian_figures.length > 0) continue;

            if(backgroud_wall_figures.length>0)
            {
                center_x = edge.projectEdge2d( backgroud_wall_figures[0].rect.back_center).x;
            }
            else {
                
            }

            if(edge.length < 2100 && edge.length > 1500)
            {
                if(depth > 1500)
                {

                    let figure_element = new TFigureElement(decoration_figure);
                    figure_element.rect.nor = edge.nor;
                    figure_element.rect.rect_center = edge.unprojectEdge2d({x:edge.length/2,y:-offset_len});
    
                    figure_elements.push(figure_element);
                }
                else{

                }

                continue;
            }

            for(let i=0; i < m_num; i++)
            {
                let tx =  down_light_distance/2+ down_light_distance * i;

        
                let n_x = center_x + tx;
                let p_x = center_x - tx;

                if(n_x <= edge.length - end_distance && n_x >= end_distance) 
                {
                    tx_list.push(n_x);
                }
                if(p_x >= end_distance && p_x <= edge.length - end_distance)
                {
                    tx_list.push(p_x);
                }
            }

            // console.log(edge._edge_id,edge.length,tx_list);

            for(let xx of tx_list)
            {
                let pos = edge.unprojectEdge2d({x:xx,y:-offset_len});

                let figure_element = new TFigureElement(decoration_figure);
                figure_element.rect.nor = edge.nor;
                figure_element.rect.rect_center = pos;

                figure_elements.push(figure_element);

                // console.log(figure_elements);

            }
            
        }

        

    }

    static _post_add_decoration_figures_by_target_element(rule: I_DecorationRule, rect: ZRect, layon_rect: ZRect = null) {
        let t_l = rect.length;
        let t_d = rect.depth;

        let m_l = rule.decoration_figure.params.length;
        let m_d = rule.decoration_figure.params.depth;

        let p_d = m_d;
        let p_l = m_l;
        let p_x = 0;
        if (layon_rect) {
            p_d = rect.project(layon_rect.rect_center).y;
            p_l = layon_rect.w;
            p_x = rect.project(layon_rect.rect_center).x;
        }
        let array_x_len = rule.array_x_len || 0;
        let array_y_len = rule.array_y_len || 0;
        let array_rowN = (eval(rule.array_rowN || '0'));
        let array_colN = (eval(rule.array_colN || '0'));
        array_x_len = array_x_len;
        array_y_len = array_y_len;
        let figures: TFigureElement[] = [];
        for (let i = 0; i < array_rowN; i++) {
            for (let j = 0; j < array_colN; j++) {
                let xx = (eval(rule.array_pos_x_func));
                let yy = (eval(rule.array_pos_y_func));

                let angle = (eval(rule.array_angle_func || '0'));

                let r_pos = rect.unproject({ x: xx, y: yy });
                let t_figure = new TFigureElement(rule.decoration_figure);
                t_figure._decoration_type = rule.decortation_type;
                t_figure.rect.nor = rect.nor.clone().negate();
                if(angle > 179)
                {
                    t_figure.rect.nor.negate();
                }
                t_figure.rect.rect_center = r_pos;
                figures.push(t_figure);
            }
        }
        return figures;
    }

    static _post_add_decortaion_lighting(room: TRoom, figure_elements: TFigureElement[], main_lights: TFigureElement[]) {
        let down_lights: TFigureElement[] = [];


        let func_add_light = (light: TFigureElement) => {
            // let id0 = main_lights.findIndex((ele)=>ele.rect.rect_center.distanceTo(light.rect.rect_center)<500);
            // if(id0>=0) return;
            let id = down_lights.findIndex((ele) => ele.rect.rect_center.distanceTo(light.rect.rect_center) < 500);
            if (id < 0) down_lights.push(light);
        }


        let light_rules = TGraphBasicConfigs.DecorationRules.filter((value, id, array) => { return value.decortation_type === "Lighting" });
        for (let rule of light_rules) {
            let i_light_figure = rule.decoration_figure;
            let elements = figure_elements.filter(ele => { return compareNames([ele.sub_category, ele.category, ele.public_category], rule.target_figure_categories, false) == 1 });
            for (let ele of elements) {
                let figures = TPostDecoratesLayout._post_add_decoration_figures_by_target_element(rule, ele.rect, null);
                for (let fi in figures) {
                    let fig = figures[fi];
                    let figure_has_in_room = figure_elements.find((value)=>compareNames([value.modelLoc],[fig.modelLoc],false) && value.rect.rect_center.distanceTo(fig.rect.rect_center)<(fig.rect.max_hh+300));
                    if(figure_has_in_room)
                    {
                        figure_has_in_room.rect.copy(fig.rect);
                        figure_has_in_room.rect.updateRect();
                        figure_has_in_room._is_decoration = fig._is_decoration;
                        figure_has_in_room._decoration_type = fig._decoration_type;
                        fig = figure_has_in_room;
                    }
                    else{
                        func_add_light(fig);
                    }
                }

            }
        }

        return down_lights;


    }

    /**
     *  后处理  添加墙画
     */
    static post_add_wall_decorations(room: TRoom, figure_elements: TFigureElement[]) {
        let onwall_rules = TGraphBasicConfigs.DecorationRules.filter((value, id, array) => { return value.decortation_type === "OnWall" });

        let wall_edges: ZEdge[] = [...room.room_shape._feature_shape._w_poly.edges];

        TLayoutOptimizer.pre_sub_room_edges_inwall(room.room_shape._feature_shape, wall_edges, room.roomname, ["Door", "Window", "Hallway"],[]);

        let sub_categories = [...TGraphBasicConfigs.MainCabinetsCategories,...TGraphBasicConfigs.OnWindowsEleCategories]
        for (let figure_ele of figure_elements) {
            if (compareNames([figure_ele.sub_category], sub_categories)) {
                let rect = figure_ele.rect.clone();
                TLayoutOptimizer.sub_edges_by_rect(rect, wall_edges, 50);
            }
        }

        let on_wall_figures: TFigureElement[] = [];
        let all_figure_elements : TFigureElement[] = [];
        figure_elements.forEach((ele)=>{
            if(ele.disassembledElements)
            {
                all_figure_elements.push(...ele.disassembledElements);
            }
            else{
                all_figure_elements.push(ele);
            }
        })

        for (let rule of onwall_rules) {
            let elements = all_figure_elements.filter(ele => { return compareNames([ele.sub_category, ele.category, ele.public_category], rule.target_figure_categories, false) == 1 });

            let min_dist = rule.target_wall_distance || 0;
            let min_length = rule.target_wall_min_length || 2000;
            for (let ele of elements) {
                
                let candidate_edges = wall_edges.filter((edge => {
                    if (edge.length < min_length) return false;

                    let check_edge_align = false;

                    for (let r_edge of ele.rect.edges) {
                        let t_dist = r_edge.projectEdge2d(edge.center).y;
                        if(t_dist < 0) continue;
                        if (r_edge.islayOn(edge, min_dist, 0.1) && edge.nor.dot(r_edge.nor)>0.9) {
                            min_dist = Math.min(min_dist,Math.abs(edge.projectEdge2d(r_edge.center).y));
                            check_edge_align = true;
                        }
                    }
                    return check_edge_align;
                }));
                candidate_edges.sort((a, b) => b.length - a.length);

                for (let c_edge of candidate_edges) {

                    let edge_rect = new ZRect(c_edge.length, 1);
                    edge_rect.nor = c_edge.nor.clone();
                    edge_rect.rect_center = c_edge.center;
                    let figures = TPostDecoratesLayout._post_add_decoration_figures_by_target_element(rule, edge_rect, ele.rect);

                    for(let fig of figures) // 设定父亲
                    {
                        fig.rect._attached_elements[TFigureElement.LayonElement] =  ele;
                        fig.rect._attached_elements[TFigureElement.ParentWallRect] = edge_rect;
                        fig._room = room;
                    }
                    on_wall_figures.push(...figures);
                    

                }

            }

        }


        if(room.decoration_elements)
        {
            room.decoration_elements.push(...on_wall_figures);
        }
        // figure_elements.push(...on_wall_figures);

    }

    /**
     * 后处理 添加台面装饰
     * @param room 
     * @param figure_elements 
     * @param attchedToTable 
     * @returns 
     */
    static post_add_on_table_decorations(room: TRoom, figure_elements: TFigureElement[], attchedToTable:boolean = true) {
        let ontable_rules = TGraphBasicConfigs.DecorationRules.filter((value, id, array) => { return value.decortation_type === "OnTable" });
        let allTableDecorationElements:TFigureElement[] = [];
        for (let rule of ontable_rules) {
            let tableElements = figure_elements.filter(ele => { return compareNames([ele.sub_category, ele.category, ele.public_category], rule.target_figure_categories, false) == 1 });
            for(let fe of figure_elements) {
                if (fe.disassembledElements) {
                    for(let disassemble of fe.disassembledElements) {
                        if (compareNames([disassemble.sub_category, disassemble.category, disassemble.public_category], rule.target_figure_categories, false) == 1) {
                            tableElements.push(disassemble);
                        }
                    }
                }
            }
            for (let table of tableElements) {
                let parent_elements = figure_elements.filter(parent_element => {
                    return compareNames([parent_element.sub_category, parent_element.category, parent_element.public_category], rule.target_parent_categories || [], false) == 1
                        &&
                        (parent_element.rect.distanceToPoint(table.rect.front_center) < (rule.target_parent_distance || -1000))
                });
                let parent_element: TFigureElement = parent_elements[0] || null;
                if (rule.target_parent_categories && !parent_element) continue;  // 如果设定了父亲 又没有找到，就跳过
                let tableDecorationElements = TPostDecoratesLayout._post_add_decoration_figures_by_target_element(rule, table.rect, parent_element?.rect || null);
                for(let fi in tableDecorationElements) // 设定父亲
                {
                    let tableDecoration = tableDecorationElements[fi];

                    // 为避免重复
                    let figure_has_in_room = figure_elements.find((value)=>compareNames([value.modelLoc],[tableDecoration.modelLoc],false) 
                            && value.rect.rect_center.distanceTo(tableDecoration.rect.rect_center)<(tableDecoration.rect.max_hh));
                    if(figure_has_in_room)
                    {
                        figure_has_in_room.rect.copy(tableDecoration.rect);
                        figure_has_in_room.rect.updateRect();
                        figure_has_in_room._is_decoration = tableDecoration._is_decoration;
                        figure_has_in_room._decoration_type = tableDecoration._decoration_type;
                        tableDecoration = figure_has_in_room;
                    }
                    tableDecoration.attchedToTargetElement = table;
                    tableDecoration.rect._attached_elements[TFigureElement.LayonElement] = parent_element || table;

                    if (attchedToTable) {
                        table.decorationElements = tableDecorationElements;
                    }
                }

                allTableDecorationElements.push(...tableDecorationElements);
            }
        }
        return allTableDecorationElements;
    }

    static _checkFigureOnWall(figure:TFigureElement,room:TRoom, on_wall_radio:number=0.8)
    {
        let _w_poly =room.room_shape._feature_shape._w_poly;

        let rect = figure.rect;
        let edges = _w_poly.edges.filter(edge=>{
            if(edge.islayOn(rect.backEdge,100,on_wall_radio))
            {
                return true;            
            }
            return false;
        });
        edges.sort((a,b)=>Math.abs(rect.project(a.center).y)- Math.abs(rect.project(b.center).y));

        return edges[0] || null;
    }
    static post_add_electricity_decorations(room:TRoom, figure_elements:TFigureElement[]){

        // 先清空 水电点位
        let decorations = figure_elements.filter(figure_element => figure_element._decoration_type==="Electricity");
        if (decorations) {
            for (let fig of decorations) {
                let id = figure_elements.indexOf(fig);
                if (id >= 0) {
                    figure_elements.splice(id, 1);
                }
            }
        }
        let rules = TGraphBasicConfigs.DecorationRules.filter((value, id, array) => { return value.decortation_type === "Electricity" });


        for (let rule of rules) {
            if(rule.target_window_realtypes) // 如果是考虑门窗的规则
            {
                if(rule.check_room_names)
                {
                    if(!compareNames([room.roomname],rule.check_room_names))
                    {
                        continue;
                    }
                }
                let doors = room.windows.filter((ele)=>compareNames([ele.type,ele.realType||""],rule.target_window_realtypes,false));

                for(let door of doors)
                {
                    let rect = door.rect;
                    if(!rect) continue;

                    let door_on_edge = room.room_shape._poly.edges.find((edge)=>{
                        if(edge.islayOn(rect.backEdge,rect.h+50,0.8))
                        {
                            return true;
                        }
                        return false;
                    });
                    if(!door_on_edge) continue;
                    if(door_on_edge.checkSameNormal(door.rect.nor.clone().negate(),false)){
                        continue;
                    }
                    let figures = TPostDecoratesLayout._post_add_decoration_figures_by_target_element(rule,rect);

                    figures = figures.filter((figure)=>{
                        let pp = door_on_edge.projectEdge2d(figure.rect.rect_center);
                        if(pp.x < 0 || pp.x > door_on_edge.length) return false;

                        figure.rect.back_center = door_on_edge.unprojectEdge2d({x:pp.x,y:0});
                        figure.rect.nor = door_on_edge.nor.negate();
                        figure.rect.updateRect();
                        return true;

                    });
                    figure_elements.push(...figures);
    
                }
            }
            else if(rule.target_figure_categories) // 考虑参考图元的规则
            {
                let elements = figure_elements.filter(ele => { return compareNames([ele.sub_category, ele.category, ele.public_category], rule.target_figure_categories, false) == 1 });
                for (let ele of elements) {
                    let parent_elements = figure_elements.filter(parent_element => {
                        return compareNames([parent_element.sub_category, parent_element.category, parent_element.public_category], rule.target_parent_categories || [], false) == 1 &&
                            (parent_element.rect.distanceToPoint(ele.rect.front_center) < (rule.target_parent_distance || -1000))
                    });
                    let parent_element: TFigureElement = parent_elements[0] || null;
                    if (rule.target_parent_categories && !parent_element) continue;  // 如果设定了父亲 又没有找到，就跳过
                    if(rule.target_alignment_on_parent)
                    {
                        let parent_rect = parent_element.rect;
                        let target_rect = ele.rect;

                        let pp = parent_rect.project(target_rect.rect_center);

                        if(parent_rect.orientation_z_nor.z < 0) pp.x = -pp.x;

                        if(rule.target_alignment_on_parent=="Left")
                        {
                            if(pp.x > 0) continue;
                        }
                        else if(rule.target_alignment_on_parent=="Right")
                        {
                            if(pp.x < 0) continue;
                        }

                    }
                    
                    let figures = TPostDecoratesLayout._post_add_decoration_figures_by_target_element(rule, ele?.matched_rect||ele.rect, parent_element?.matched_rect || parent_element?.rect || null);
                    for(let fi in figures) // 设定父亲
                    {
                        let fig = figures[fi];
    
                        fig.attchedToTargetElement= ele;
                        fig.rect._attached_elements[TFigureElement.LayonElement] = parent_element || ele;
                    }
    
                    // console.log(rule.decoration_name, figures);
                    figure_elements.push( ...figures.filter((figure)=>{
                        let wall_edge = TPostDecoratesLayout._checkFigureOnWall(figure,room);
                        if(!wall_edge) return false;
                        let win = WPolygon.getWindowOnEdge(wall_edge);
                        if(win)
                        {
                            if(win.type === "Window")
                            {
                                if(figure.min_z > 600)
                                {
                                    return false;
                                }
                            }
                            else{
                                return false;
                            }
                        }
                        return true;
    
                    }));
                }
            }


  
        }
    }


    /**
     * 调整饰品的高度和位置，或删除饰品素材
     * @param room  
     * @param figure_elements 
     */
    static post_adjust_decorations(room: TRoom, figure_elements: TFigureElement[] = null, params : {storey_height:number, floor_z:number} = {storey_height:2800, floor_z:0}) {

        if(compareNames([room.roomname],["厨房"])==1)
        {
            return;
        }
        let allFigureElements:TFigureElement[] = [];
        if (!figure_elements) { 
            allFigureElements.push(...room._furniture_list);
            if(room.decoration_elements)
            {
                allFigureElements.push(...room.decoration_elements);
            }
        }
        else allFigureElements.push(...figure_elements);

        

        let ceilingHeight = room.ceilingHeight;

        //调整卧室书房的主灯位置为吊顶区域中心点
        TPostDecoratesLayout.post_adjust_mainlights(room,allFigureElements,params);

        //找出组合图元里的饰品素材
        let memberElementsInGroup:TFigureElement[] = [];
        for(let fe of allFigureElements) {
            if (fe.disassembledElements) {
                for(let dfe of fe.disassembledElements) {
                    if (dfe.decorationElements) {
                        memberElementsInGroup.push(...dfe.decorationElements);
                    }
                }
            }
            if (fe.decorationElements) {
                memberElementsInGroup.push(...fe.decorationElements);
            }
        }
        allFigureElements.push(...memberElementsInGroup);

        //遍历房间的所有饰品图元
        for(let decorate_element of allFigureElements)
        {
            // 这段代码的作用是调整照明类饰品(如筒灯)的位置和高度
            // 主要逻辑如下:
            // 1. 首先检查是否为照明类饰品,并且有目标尺寸
            // 2. 找出所有覆盖该饰品中心点且放置位置Z轴高度小于500的其他图元(如地毯、窗帘、定制柜等)
            // 3. 按照高度从高到低排序这些覆盖图元
            // 4. 如果覆盖图元高度>=2000(比如窗帘、定制柜),则:
            //    - 将饰品投影到覆盖图元上
            //    - 调整饰品位置到覆盖图元中心往法线方向190毫米的位置
            // 5. 如果覆盖图元高度<2000,则保持饰品原位置不变
            // 6. 最后更新饰品的目标位置,保持z轴高度不变
            if(decorate_element._decoration_type==="Lighting")
            {
                if(!decorate_element?._matched_material?.targetSize) continue;

                let cover_figures = allFigureElements.filter(ele=>(ele!==decorate_element) && ele.rect.containsPoint(decorate_element.rect.rect_center) && (ele.min_z || 0)<500);
                if(cover_figures.length == 0) continue;
                cover_figures.sort((a,b)=>b.rect.min_hh - a.rect.min_hh);

                let target_figure = cover_figures[0];
                let target_rect = target_figure?.matchMainRect() || target_figure.rect;
                let height = cover_figures[0]._matched_material?.height || 0;
                let matched_rect = decorate_element.matched_rect || decorate_element.rect.clone();


                matched_rect.rect_center = decorate_element.rect.rect_center;
                
                decorate_element.matched_rect = matched_rect;
                let zval = decorate_element._matched_material.targetPosition.z;
                decorate_element._matched_material.targetPosition = Vec3toMeta(matched_rect.rect_center);
                decorate_element._matched_material.targetPosition.z = zval;
            }

            //如果是台面家具的饰品素材、或者是电视机
            if( decorate_element._decoration_type==="OnTable" 
            || compareNames([decorate_element.sub_category],["电视"],false))
            {
                if(!decorate_element?._matched_material?.targetSize) continue;

                let cover_figures = allFigureElements.filter(ele=>(ele!==decorate_element) && ele.rect.containsPoint(decorate_element.rect.rect_center) && (ele.min_z || 0)<500);

                
                if(cover_figures && cover_figures.length > 0)
                {
                    cover_figures.sort((a,b)=>b.rect.min_hh - a.rect.min_hh);
                    let height = cover_figures[0]._matched_material?.height || 0;

                    if(height > 2400)
                    {
                        if(decorate_element._matched_material.modelId)
                        {
                            decorate_element._matched_material.deleted_model_id = decorate_element._matched_material.modelId;
                            decorate_element._matched_material.modelId = null;
                        }
                        else{
                            if(!decorate_element._matched_material.modelId)
                            {
                                decorate_element._matched_material.modelId = decorate_element._matched_material.deleted_model_id;
                            }

                        }
                    }  
                    if(height>100)
                    {
                        if(compareNames([decorate_element.sub_category],["电视"],false))
                        {
                            decorate_element._matched_material.targetPosition.z = height + 200;
                        }
                    }
                }
            }
        }

        for(let rule of  TGraphBasicConfigs.DecorationRules)
        {
            let elements = allFigureElements.filter(ele => { return ele._is_decoration 
                && ele.category == rule.decoration_figure.category && ele._decoration_type !== "Electricity" });
            let last_model_id = "";
            for(let ele of elements)
            {   

                if(!ele?._matched_material?.targetSize) continue;
                ele._matched_material.modelId = ele._matched_material.modelId || ele._matched_material.deleted_model_id;
                let current_model_id = ele._matched_material.modelId;
                let storey_height = params.storey_height - ceilingHeight;
                
                if(rule.needs_different_material)
                {
                    if(ele._matched_material && ele._matched_material.modelId === last_model_id)
                    {
                        if(ele._candidate_materials)
                        {
                            let id = ele._candidate_materials.findIndex((val)=>val.modelId != last_model_id);

                            if(id >= 0)
                            {
                                let oldMaterial = ele._matched_material;
                                ele._matched_material = ele._candidate_materials[id];
                                ele._matched_material.figureElement = ele;
                                let r_center = ele._matched_rect.rect_center.clone();
                                ele._matched_rect._w = ele._matched_material?.originalLength || ele._matched_material.length; 
                                ele._matched_rect._h = ele._matched_material?.originalWidth || ele._matched_material.width; 
                                ele._matched_rect.rect_center = r_center;
                                ele._matched_material.targetSize = ele._matched_material.targetSize || {length:ele._matched_material.width,width:ele._matched_rect.h,height:ele._matched_material?.originalHeight||100};
                                ele._matched_material.targetSize.length = ele._matched_rect.w;
                                ele._matched_material.targetSize.width = ele._matched_rect.h;
                                ele._matched_material.targetSize.height = ele._matched_material?.originalHeight || 100;
                                ele._matched_material.targetPosition = oldMaterial.targetPosition;
                                ele._matched_material.targetRotation = oldMaterial.targetRotation;
                            }
                        }
                    }
         
                }

     
                
                let m_h = ele._matched_material.targetSize.height;
                let layon_element : TFigureElement = ele.rect._attached_elements[TFigureElement.LayonElement];
                let target_element : TFigureElement = ele.attchedToTargetElement;
                let p_h = (layon_element?._matched_material?.targetSize?.height || 0) + (layon_element?._matched_material?.targetPosition?.z || 0);
                let m_l = ele._matched_material.targetSize.length;
                let m_d = ele._matched_material.targetSize.width;

        
                last_model_id = current_model_id;
            
                if(layon_element?._matched_material?.targetSize && layon_element?.matched_rect
                    && target_element?._matched_material?.targetSize && target_element?.matched_rect)
                {

      
                    let i = 0;
                    let j = elements.indexOf(ele);
                    let array_x_len = rule.array_x_len || 0;
                    let array_y_len = rule.array_y_len || 0;

    

                    let rect = target_element?.matched_rect;
                    let p_d = m_d;
                    let p_l = m_l;
                    let p_x = 0;
                    let layon_rect = layon_element.matched_rect;
                    if (layon_rect) {
                        p_d = rect.project(layon_rect.rect_center).y;
                        p_x = rect.project(layon_rect.rect_center).x;
                        p_l = layon_rect.w;
                    }
             
                    if(rule.decortation_type==="OnWall")
                    {
                        rect = ele.rect._attached_elements[TFigureElement.ParentWallRect] || rect;
                    }
                    let t_l = rect.w;
                    let t_d = rect.h;
                    t_l = t_l; t_d = t_d;
                    let xx = (eval(rule.array_pos_x_func));
                    let yy = (eval(rule.array_pos_y_func));
                    let r_pos = rect.unproject({ x: xx, y: yy });
       
 

                    ele.matched_rect.rect_center = r_pos;
                    let zval = ele._matched_material?.targetPosition?.z || 0;
                    ele._matched_material.targetPosition = Vec3toMeta(ele.matched_rect.rect_center_3d);
                    ele._matched_material.targetPosition.x = r_pos.x;
                    ele._matched_material.targetPosition.y = r_pos.y;
                    ele._matched_material.targetPosition.z = zval;


                }


                
                let zval = eval(rule.adjust_pos_z_func || '0');

                if(!ele._matched_material.targetPosition)
                {
                    ele._matched_material.targetPosition = Vec3toMeta(ele.rect.rect_center);
                }

                ele._matched_material.targetPosition.z = zval;
                if(ele.matched_rect)
                {
                    ele.matched_rect.zval = zval;
                    ele.rect.zval = zval;
                }
         
                if(rule.decortation_type !== "Lighting")
                {
                    // console.log(ele,m_h,p_h, ele.matched_rect.zval);

                    // if(ele.rect.zval > 2000) // ele过高了， 则modelId不给布置
                    // {
                    //     ele._matched_material.modelId = null;
                    // }

                    if(rule.check_condition)
                    {
                        let m_z = ele._matched_material.targetPosition.z;
                        let res = eval(rule.check_condition);

                        if(!res)
                        {
                            ele._matched_material.deleted_model_id = ele._matched_material.modelId;
                            ele._matched_material.modelId = null;
                        }
                        else if(!ele._matched_material.modelId){
                            
                            ele._matched_material.modelId = ele._matched_material.deleted_model_id;
                        }
                    }
                }


            }

        }
        
    }

    //调整卧室书房的主灯位置为吊顶区域中心点
    static post_adjust_mainlights(room:TRoom,figure_elements:TFigureElement[]=null, params : {storey_height:number, floor_z:number} = {storey_height:2800, floor_z:8})
    {
        if(compareNames([room.roomname],["卧室","书房"]))
        {
            if (!figure_elements) figure_elements = room._furniture_list;
            let main_lights = figure_elements.filter((ele)=>compareNames([ele.category,ele.sub_category],["主灯"]));
            for(let light_element of main_lights)
            {
                if(room._ceilling_list)
                {
                    //找出最接近主灯的吊顶
                    let inside_ceilings = room._ceilling_list.filter((ceiling)=>ceiling.rect.containsPoint(light_element.rect.rect_center));
    
                    if(inside_ceilings.length == 0) continue;
                    const distToLight = (a:TFigureElement)=>{
                        return a.rect.rect_center.distanceTo(light_element.rect.rect_center);
                    }
                    if(inside_ceilings.length > 1)
                    {
                        inside_ceilings.sort((a,b)=>distToLight(a)-distToLight(b));
    
                    }
    
                    let target_ceiling = inside_ceilings[0];
                    //设置主灯的位置为吊顶中心
                    light_element.rect.rect_center = target_ceiling.rect.rect_center;
                    if(light_element.matched_rect)
                    {
                        light_element.matched_rect.rect_center = target_ceiling.rect.rect_center;
                    }
                
                    
                }
            }
        }
    }


    /**
     * 后处理背景墙
     * 2024.05.07: 暂时只处理客餐厅
     * @param room 
     * @param figure_elements 
     */
    static post_adjust_background_walls(room:TRoom, figure_elements:TFigureElement[] = null, params : {storey_height:number, floor_z:number} = {storey_height:2800, floor_z:0})
    {
        if (!figure_elements) figure_elements = room._furniture_list;

        let background_wall_elemenets = figure_elements.filter((val)=>compareNames([val.category],["背景墙"]));
        for(let ele of background_wall_elemenets)
        {
            if(ele?._matched_material?.targetSize)
            {
                ele._matched_material.targetSize.width = 50; // 固定成50
                ele._matched_material.targetSize.height = params.storey_height - (room.ceilingHeight || 200) - params.floor_z;
            }

            if(compareNames([room.roomname],["客餐厅"]) && ele.category==="背景墙")
            {
                let tv_cabinet_element = figure_elements.find((val)=>compareNames([val.sub_category],["电视柜"],false) && Math.abs(ele.rect.project(val.rect.back_center).y) < 200);
                let sofa_cabinet_element = figure_elements.find((val)=>compareNames([val.sub_category],["直排沙发","转角沙发"]) && Math.abs(ele.rect.project(val.rect.back_center).y) < 1000);

                if(tv_cabinet_element)
                {
                    ele.category = "电视背景墙";
                }
                else //if(sofa_cabinet_element)
                {
                    ele.category = "沙发背景墙";
                }
            }


        }

        if(compareNames([room.roomname],["客餐厅"])==0)
        {
            return;
        }


        
        // 只有客餐厅才会去处理
        let tv_cabinet_element = figure_elements.find((val)=>compareNames([val.sub_category],["电视柜"],false));
        let tv_background_wall_element = figure_elements.find((val)=>compareNames([val.sub_category],["电视背景墙"],false));

        if(tv_cabinet_element)
        {
            if(tv_background_wall_element)
            {
                if(tv_cabinet_element._matched_material?.targetSize && tv_background_wall_element._matched_material?.targetSize)
                {
                    if(tv_cabinet_element._matched_material.targetSize.height > 1400)
                    {
                        tv_background_wall_element._matched_material.deleted_model_id = tv_background_wall_element._matched_material.modelId;
                        tv_background_wall_element._matched_material.modelId = null;
                    }
                    else{
                        if(!tv_background_wall_element._matched_material.modelId)
                        {
                            tv_background_wall_element._matched_material.modelId = tv_background_wall_element._matched_material.deleted_model_id;       
                        }
                    }
                }
            }

        }


    }
}