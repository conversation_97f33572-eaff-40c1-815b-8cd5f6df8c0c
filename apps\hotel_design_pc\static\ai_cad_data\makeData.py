import os,json


dirname = os.path.dirname(__file__)
prefix = "./static/ai_cad_data"

list = []
for root,dirs,files in os.walk(dirname):
    for file in files:
        if file.endswith(".txt"):
            filename = os.path.join(root,file)
            
            json_filename = filename.replace(".txt",".json")

            if os.path.exists(json_filename):
                # print(json_filename)
                list.append({"txt_file":filename, "result_json":prefix+"/"+file.replace(".txt",".json")})
# print(list)

for data in list:
    fp = open(data['txt_file'],'r',encoding='utf-8')
    if fp:
        str = fp.read()
        # print(str)

        t_str = str.replace("'",'"')

        t_data = json.loads(t_str)

        # print(t_data)

        for key in t_data:
            data[key] = t_data[key]
# print(dirname)

for data in list:
    txt_file = data['txt_file']
    txt_file = txt_file.replace(dirname,"")
    txt_file = prefix+"/"+txt_file.replace("\\","")
    data['txt_file'] = txt_file


fp = open(os.path.join(dirname,"test_data.json"),'w',encoding='utf-8')
json.dump(list,fp=fp,ensure_ascii=False,indent=2)
fp.close()


# print(list)


