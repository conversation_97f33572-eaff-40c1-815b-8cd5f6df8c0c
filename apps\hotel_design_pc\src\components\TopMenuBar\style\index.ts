import { createStyles } from '@svg/antd/es/theme/utils';
export default createStyles(({ css }) => {
  // color: ${token.colorPrimary};
  return {
    container: css`
        position: fixed;
        top: 48px;
        right: 50%;
        transform: translate(50%);
        height: 40px;
        background: #fff;
        box-shadow: 0 2px 8px 0 rgba(0,0,0,.16);
        border-radius: 2px;
        font-size: 12px;
        font-weight: 400;
        line-height: 40px;
        z-index: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;
        text-align: center;
        padding: 0 12px;
        box-sizing: border-box;
    `,
    wallInput: css`
      .ant-select-selection-item
        {
            display: none;
        }
    `,
    label: css`
        margin: 0px 10px;
    `,
    popupClass: css`
        width: 100px !important;
    `
  }
});
