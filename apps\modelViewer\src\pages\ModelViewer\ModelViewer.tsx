
import "./index.less";

import { But<PERSON>, Segmented } from "antd";
import { observer } from "mobx-react";
import React, { useEffect, useRef, useState } from "react";
import { DoubleSide, Group, Mesh, MeshStandardMaterial, Object3D } from "three";

import { Cabinet3DApi, DesignMaterialService, GltfManager, I_CustomCabinetInfoItem, LayoutCustomCabinetInfoService, Model3dApi, Model3dViewer, SvgGltfCabinetNode, SvgGltfObjectNode, get_swj_xml_from_url } from "@layoutai/model3d_api";
import { getImgDomain } from "@svg/request";

import { MeshListItem } from "./MeshListItem";
import Object3dTreeItem from "./Object3dTreeItem";
import { compareNames } from "@layoutai/z_polygon";


/**
 * @description 模型查看器
 * 输入模型id，查看模型3D样式、节点树和网格列表
 */
const Model3DViewerPage: React.FC = () => {
  const mainDivRef = useRef(null);

  // 当前显示的3D模型节点
  const [currentModel, setCurrentModel] = useState<Object3D>(null);

  // true 显示模型节点树，false 显示模型网格列表
  const [isShowTree, setShowTree] = useState<boolean>(true);

  // localstorage 中的历史记录
  const [queryHistory, setQueryHistory] = useState<{ materialId: string, date: number }[]>([]);

  const [currentInfo,setCurrentInfo] = useState<{url:string,materialId:string,isGlbJson?:boolean,styleBrushId?:string}>({url:"",materialId:""});
  const [glbJsonUrlList,setGlbJsonUrlList] = useState<I_CustomCabinetInfoItem[]>([]);
  const [currentRightTabName,setCurrentRightTab]  = useState<string>("details");
  const rightPanelModeOptions = [
    {
      label:"模型",
      value:"details"
    },{
      label:"JSON列表",
      value:"jsonList"
    }
  ];

  // let simplierGlbUrl = "362488182.glb";

  const object_id = "LayoutAI_Model3DViewerPage";
  let simplierGlbUrl = "https://3vj-content.3vjia.com/ai/layout-design/cabinet-glb/258315629/258315629_2700X370X2400_1152079.glb?t=25611";

  const updateMeshCount = (object: Object3D) => {
    let counter = 0;
    if ((object as Mesh).isMesh) {
      counter += 1;
      let mesh = object as Mesh;
      if (mesh.geometry) {
        // mesh.visible = (mesh.geometry?.attributes?.position?.count || 0) == 16
      }
      if (mesh.material) {
        let material = mesh.material as MeshStandardMaterial;
        if (material instanceof Array) {
          material.forEach((ele) => {
            if (ele.side) ele.side = DoubleSide;
          })
        }
        else {
          if (material.side) {
            material.side = DoubleSide;
          }
        }

      }
    }
    if (object.children && object.children.length > 0) {
      object.children.forEach((child, index) => {
        counter += updateMeshCount(child);
      });
    }
    object.userData.meshesCount = counter;
    return counter;
  }
  const onLoadModel = async (group_node: Object3D) => {

    updateMeshCount(group_node);

    Model3dViewer.instance.setMainGroup(group_node as Group);

    setCurrentModel(group_node);
  }

  const exportModel = async () => {
    if (currentModel) {
      Model3dViewer.instance.downloadModel("test");
    }
  }

  const localStorageKey = {
    queryHistory: object_id + "_" + "QueryHistory"
  }
  const saveLocalHistory = (queryHistory: { materialId: string, date: number }[]) => {
    if (queryHistory) {
      if (localStorage) {
        localStorage.setItem(localStorageKey.queryHistory, JSON.stringify(queryHistory));
      }
    }
  }
  const loadLocalHistory = () => {
    if (localStorage) {
      try {
        let data = localStorage.getItem(localStorageKey.queryHistory);
        if (data) {
          let list = JSON.parse(data);
          if (list && list instanceof Array) {
            setQueryHistory(list);
          }
        }
      } catch (error) {

      }

    }
  }
  useEffect(() => {
    loadLocalHistory();
    if (mainDivRef) {
      Model3dViewer.instance.bindParentDiv(mainDivRef.current as HTMLDivElement);
      Model3dViewer.instance.startRender();
    }
  }, []);

  const loadGlbJsonUrl = async (materialId:string,jsonDataUrl:string)=>{
    if(!jsonDataUrl) return;
    let isValid = false;
    let url = "https://3vj-content.3vjia.com/" + jsonDataUrl;

    if (url.includes(".json")) {
      let data = await fetch(url).then(val => val.json()).catch(e => null);
      if (data) {

        setCurrentInfo({url:jsonDataUrl,materialId:materialId});
        isValid = true;
        let svgGltfCabinetNode = new SvgGltfCabinetNode(data);
        svgGltfCabinetNode.mergeBatchedNodes();

        setCurrentModel(svgGltfCabinetNode);

        await svgGltfCabinetNode.updateSolidModels();

        onLoadModel(svgGltfCabinetNode);
      }
    }
    else if(url.includes(".glb"))
    {
      
      let glbData = await GltfManager.getGltfObjectByUrl(jsonDataUrl,{needsSvgObject:true});

      if(glbData)
      {
        console.log(glbData);
        if(glbData instanceof SvgGltfObjectNode)
        {
            await glbData.updateTextures();
        }
        setCurrentInfo({materialId:materialId,url:glbData.userData.glbUrl||""});
        onLoadModel(glbData);
        return;
      }
    }
    return isValid;


  }
  const onLoadMaterial = async (materialId: string,jsonDataUrl:string = null) => {
    let res = await DesignMaterialService.getDesignMaterialInfoByIds([materialId]);
    let infoList = glbJsonUrlList;
    
    if(!glbJsonUrlList[0] || glbJsonUrlList[0]?.materialId !== materialId)
    {
        let data =    await LayoutCustomCabinetInfoService.querySimilarInfos({ "materialId": materialId, pageIndex: 1, pageSize: 50, "updateDateStart": "2025-06-16 00:05:12" }, { width: res[0].PICLength, depth: res[0].PICWidth, height: res[0].PICHeight });
        setGlbJsonUrlList(data);
        infoList = data;
    }
    if(jsonDataUrl)
    {
        await loadGlbJsonUrl(materialId,jsonDataUrl);
        return;
    }

    if (res[0]?.ContentUrl) {
      
      let glbData = await GltfManager.getGltfObjectByMaterialId(materialId,res[0].PICLength,res[0].PICWidth,res[0].PICHeight,res[0].PICLength,res[0].PICWidth,res[0].PICHeight);

      if(glbData)
      {
        if(glbData instanceof SvgGltfObjectNode)
        {
            await glbData.updateTextures();
        }
        setCurrentInfo({materialId:materialId,url:glbData.userData.glbUrl||""});
        onLoadModel(glbData);
        return;
      }


      if (infoList[0]) {
        let isValid = await loadGlbJsonUrl(materialId,infoList[0].valueUrl);
        if (!isValid) {

          let group = await new Promise<Group>((resolve, reject) => Model3dApi.MakeMesh3D_WithContentUrl(materialId, res[0].ContentUrl, "电视柜", resolve, false));
          onLoadModel(group);
        }
      }
      else{
          console.log(await Cabinet3DApi.parseCabinetDataFromContentUrl(res[0].ContentUrl));      
      }

    }
    else if (res[0].A3dSource) {
      setCurrentInfo({materialId:materialId,url:getImgDomain() + res[0].A3dSource});
      let group = await Model3dApi.MakeMesh3D_WithA3dSource(materialId, res[0].A3dSource, true, false);
      onLoadModel(group);
    }
    
  }
  const onSaveHistoryItem = (materialId: string) => {
    let history = [...queryHistory];
    let id = history.findIndex((item) => item.materialId == materialId);
    if (id >= 0) {
      history[id].date = new Date().getTime();
      history.sort((a, b) => b.date - a.date);
    }
    else {
      history = [{ materialId: materialId, date: new Date().getTime() }, ...queryHistory];
    }
    history.length = Math.min(30, history.length);
    saveLocalHistory(history);
    setQueryHistory(history);
  }
  const QueryMaterialIdInput = () => {
    const [showHistory, setShowHistory] = useState<boolean>(false);
    return <>
      <input id="designMaterialId" defaultValue={queryHistory[0]?.materialId || ""} autoComplete='off' onFocus={() => {
        setShowHistory(true);
      }} onBlur={() => {
        // setShowHistory(false);
      }}></input>
      <div id="queryHistoryList" className='queryHistoryList' style={{ display: showHistory ? "block" : "none" }}>
        {queryHistory.map((item, index) => <div key={"history_item" + index} className={"item"} onClick={() => {
          console.log(item.materialId);
          let history = [item,...queryHistory.filter((hisItem)=>hisItem!=item)];
          setQueryHistory(history);
          let designMaterialIdInput = document.getElementById("designMaterialId") as HTMLInputElement;
          designMaterialIdInput.value = item.materialId;

          setShowHistory(false);


        }}> {item.materialId}-{new Date(item.date).toLocaleString()}</div>)}
      </div>
      <Button onClick={async () => {
        let value = (document.getElementById("designMaterialId") as HTMLInputElement).value;

        const materialId = value;
        onSaveHistoryItem(materialId);
        onLoadMaterial(materialId);

      }}>查看模型</Button>

    </>
  }
  return (
    <>
      {/* <Button onClick={()=>loadModel()}>加载模型</Button> */}
      <div className='leftPanelContainer'>
        <QueryMaterialIdInput></QueryMaterialIdInput>
        <Button onClick={() => exportModel()}>下载模型</Button>
      </div>

      <div style={{ position: "absolute", left: 300 }}>
        <div id="validUrl" style={{ position: "absolute", left: 10, top: 10, zIndex: 3 }}>{currentInfo?.url||""}</div>
        <div id="Model3DViewer" className='main_3d_div' ref={mainDivRef}></div>
        <div className='modelTreeContainer'>
        <Segmented options={rightPanelModeOptions} defaultValue={currentRightTabName} onChange={(value) => setCurrentRightTab(value)}></Segmented>

          {
            currentRightTabName==="details" && <>
              <div className='tabTile' onClick={(ev) => { setShowTree(!isShowTree); }}>
                {isShowTree ? "树状" : "网格"}
              </div>
              {isShowTree && currentModel && <Object3dTreeItem object={currentModel as any} prefix={''} level={0}></Object3dTreeItem>}
              {!isShowTree && currentModel && <MeshListItem object={currentModel as any}></MeshListItem>}
            
            </>
          }

          {currentRightTabName=="jsonList" && <>
                  {glbJsonUrlList.map((item,index)=><div key={"item"+index} className='rowDiv' onClick={()=>{
                      if(!item.valueUrl.startsWith("http"))
                      {
                        item.valueUrl = "https://3vj-content.3vjia.com/" +item.valueUrl;
                      }
                      loadGlbJsonUrl(item.materialId,item.valueUrl);
                      
                  }}>
                      <span className='urlSpan'>{item.valueUrl.substring(item.valueUrl.lastIndexOf("/")+1)}{compareNames([item.valueUrl.substring(item.valueUrl.lastIndexOf("/")+1)],[currentInfo?.url||"---"])?"✔":""}</span>
                      <span className='urlUpdateDate'>{item.updateDate}</span>
                  </div>)}
              </>
            }

        </div>
      </div>
    </>

  );
};


export default observer(Model3DViewerPage);
