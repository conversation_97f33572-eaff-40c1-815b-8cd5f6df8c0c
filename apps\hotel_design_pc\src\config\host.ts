import { ENV, isLocal3d } from "./env";

const imgHostMap: any = {
  dev: 'https://hws-img3.admin.3vjia.com/',
  test: 'https://img3-test.3weijia.com/',
  hws: 'https://hws-img3.admin.3vjia.com/',
  pre: 'https://img3.admin.3vjia.com/',
  prod: 'https://img3.admin.3vjia.com/'
};
export const imgHostUrl = imgHostMap[ENV];

const devPluginMap: any = {
  dev: 'https://pre-dev-plugin.3vjia.com',
  test: 'https://pre-dev-plugin.3vjia.com',
  hws: 'https://hws-dev-plugin.3weijia.com',
  pre: 'https://pre-dev-plugin.3vjia.com',
  prod: 'https://dev-plugin.3vjia.com'
};
export const devPluginUrl = devPluginMap[ENV];

const magellanMap: any = {
  dev: 'https://magellan.3weijia.com',
  test: 'https://magellan-test.3weijia.com',
  hws: 'https://pre-magellan.3vjia.com',
  pre: 'https://pre-magellan.3vjia.com',
  prod: 'https://magellan.3vjia.com'
};
export const magellanUrl = magellanMap[ENV];

const materialMap: any = {
  dev: 'https://pre-www.3vjia.com/x/assets/plaza/tool',
  test: 'https://pre-www.3vjia.com/x/assets/plaza/tool',
  hws: 'https://hws-www.3weijia.com/x/assets/plaza/tool',
  pre: 'https://pre-www.3vjia.com/x/assets/plaza/tool',
  prod: 'https://www.3vjia.com/x/assets/plaza/tool'
};
export const materialUrl = materialMap[ENV];

const quanwuMap: any = {
  dev: 'https://pre-www.3vjia.com/x/assets/quanwu',
  test: 'https://pre-www.3vjia.com/x/assets/quanwu',
  hws: 'https://hws-www.3weijia.com/x/assets/quanwu',
  pre: 'https://pre-www.3vjia.com/x/assets/quanwu',
  prod: 'https://www.3vjia.com/x/assets/quanwu'
};
export const quanwuUrl = quanwuMap[ENV];

const gatewayMap: any = {
  dev: 'https://hws-magiccube-gateway.3weijia.com',
  test: 'https://hws-magiccube-gateway.3weijia.com',
  hws: 'https://hws-magiccube-gateway.3weijia.com',
  pre: 'https://pre-magiccube-gateway.3vjia.com',
  prod: 'https://magiccube-gateway.3vjia.com',
};

const openApiHostMap: any = {
  dev: 'https://hws-open.3weijia.com',
  test: 'https://open-test.3weijia.com',
  hws: 'https://hws-open.3weijia.com',
  pre: 'https://pre-open.3vjia.com',
  prod: 'https://open.3vjia.com',
}

const dpAiWebHostMap: any = isLocal3d() ? {
  dev: 'https://dev-hws-3d.3weijia.com',
  hws: 'https://dev-hws-3d.3weijia.com',
  pre: 'https://test.3vjia.com',
  prod: 'https://magiccube-gateway.3vjia.com',
} : {
  dev: 'https://hws-magiccube-gateway.3weijia.com',
  hws: 'https://hws-magiccube-gateway.3weijia.com',
  pre: 'https://pre-magiccube-gateway.3vjia.com',
  prod: 'https://magiccube-gateway.3vjia.com',
}

const cadParseApiMap: any = {
  dev: 'https://turing-service.3vjia.com/api/CADHouseIdentify/v1/predict',
  hws: 'https://turing-service.3vjia.com/api/CADHouseIdentify/v1/predict',
  pre: 'https://turing-service.3vjia.com/api/CADHouseIdentify/v1/predict',
  prod: 'https://turing-service.3vjia.com/api/CADHouseIdentify/v1/predict',
}

const predictHouseImitateApiMap: any = {
  dev:  'https://hws-open.3weijia.com/sd-biz/api/building/roommodel/v1/predict',
  hws:  'https://hws-open.3weijia.com/sd-biz/api/building/roommodel/v1/predict',
  pre:  'https://pre-open.3vjia.com/sd-biz/api/building/roommodel/v1/predict',
  prod: 'https://open.3vjia.com/sd-biz/api/building/roommodel/v1/predict',
}

const predictHouseImitateOpenApiMap: any = {
  dev:  'https://open.3vjia.com/sd-biz/api/building/roommodel/v1/predict',
  hws:  'https://open.3vjia.com/sd-biz/api/building/roommodel/v1/predict',
  pre:  'https://open.3vjia.com/sd-biz/api/building/roommodel/v1/predict',
  prod: 'https://open.3vjia.com/sd-biz/api/building/roommodel/v1/predict',
}

const xmlParse2JsonApiMap:any = isLocal3d() ? {
  dev:  'https://dev-hws-3d.3weijia.com/dp-ai-web/parseXml2Json',
  hws:  'https://dev-hws-3d.3weijia.com/dp-ai-web/parseXml2Json',
  pre:  'https://test.3vjia.com/dp-ai-web/parseXml2Json',
  prod:     'https://magiccube-gateway.3vjia.com/dp-ai-web/parseXml2Json',
} : {
  dev:  'https://pre-magiccube-gateway.3vjia.com/dp-ai-web/parseXml2Json',
  hws:  'https://pre-magiccube-gateway.3vjia.com/dp-ai-web/parseXml2Json',
  pre:  'https://pre-magiccube-gateway.3vjia.com/dp-ai-web/parseXml2Json',
  prod: 'https://magiccube-gateway.3vjia.com/dp-ai-web/parseXml2Json',
}

const AIDeskMap: any = {
  dev: 'https://hws-kuaida.3weijia.com',
  hws: 'https://hws-kuaida.3weijia.com',
  pre: 'https://pre-kuaida.3vjia.com',
  prod: 'https://kuaida.3vjia.com',
}

const staticMap: any = {
  dev: 'https://hws-static.3vjia.com/',
  hws: 'https://hws-static.3vjia.com/',
  pre: 'https://static.3vjia.com/',
  prod: 'https://static.3vjia.com/',
}

const hxMap: any = {
  dev: 'https://hws-dev-plugin.3weijia.com/hx-search-ui/new?model=modal&appId=sd-design-web',
  hws: 'https://hws-dev-plugin.3weijia.com/hx-search-ui/new?model=modal&appId=sd-design-web',
  pre: 'https://dev-plugin.3vjia.com/hx-search-ui/new?model=modal&appId=sd-design-web',
  prod: 'https://dev-plugin.3vjia.com/hx-search-ui/new?model=modal&appId=sd-design-web',

}
const aiCabinetMap: any = {
  dev: 'https://hws-aicabinet.3weijia.com',
  hws: 'https://hws-aicabinet.3weijia.com',
  pre: 'https://pre-aicabinet.3vjia.com',
  prod: 'https://aicabinet.3vjia.com',

}

const preorderMap: any = {
  dev: 'https://pre-work-sub.3vjia.com/quote-ui/bimPreQuoteWeb',
  hws: 'https://pre-work-sub.3vjia.com/quote-ui/bimPreQuoteWeb',
  pre: 'https://pre-work-sub.3vjia.com/quote-ui/bimPreQuoteWeb',
  prod: 'https://work-sub.3vjia.com/quote-ui/bimPreQuoteWeb',
}


const backgroundOrderMap: any = {
  dev: 'https://pre-work-sub.3vjia.com/quote-ui/bimPreQuote',
  hws: 'https://pre-work-sub.3vjia.com/quote-ui/bimPreQuote',
  pre: 'https://pre-work-sub.3vjia.com/quote-ui/bimPreQuote',
  prod: 'https://work-sub.3vjia.com/quote-ui/bimPreQuote',
}

const svj3dCloudDesignMap: any = {
  dev: isLocal3d() ? 'https://dev-hws-3d.3weijia.com/design' : 'https://hws-3d.3weijia.com/design?versionId=10000012',
  hws: 'https://hws-3d.3weijia.com/design?versionId=10000012',
  pre: 'https://pre-3d.3vjia.com/design?versionId=10000012',
  prod: 'https://3d.3vjia.com/design',
}

const caApiUrlMap: any = {
  dev: 'https://hws-ca-api.3weijia.com/3.0',
  hws: 'https://hws-ca-api.3weijia.com/3.0',
  pre: 'https://pre-ca-api.3vjia.com/3.0',
  prod: 'https://ca-api.3vjia.com/3.0'
}

const xrHostDomainMap: any = {
  hws: 'https://hws-xr.3weijia.com',
  pre: 'https://pre-xr.3vjia.com',
  prod: 'https://xr.3vjia.com'
};

const layoutHostDomainMap: any = {
  hws: 'https://hws-layoutai.3weijia.com/editor/',
  pre: 'https://pre-layoutai.3vjia.com/editor/',
  prod: 'https://layoutai.3vjia.com/editor/'
};
const workHostDomainMap: any = {
  hws: 'https://hws-layoutai.3weijia.com',
  pre: 'https://pre-layoutai.3vjia.com',
  prod: 'https://layoutai.3vjia.com'
};

const panoHostMap: any = {
  dev: 'https://hws-3vj-pano.oss-cn-shenzhen-internal.aliyuncs.com',
  hws: 'https://hws-3vj-pano.oss-cn-shenzhen-internal.aliyuncs.com',
  pre: 'https://test-3vj-pano.oss-cn-shenzhen-internal.aliyuncs.com',
  prod: 'https://3vj-pano.3vjia.com'
};

const openPluginUrlMap: any = {
  dev: 'https://pre-open-plugin.3vjia.com',
  hws: 'https://pre-open-plugin.3vjia.com',
  pre: 'https://pre-open-plugin.3vjia.com',
  prod: 'https://open-plugin.3vjia.com',
}

const imsApiMap: any = {
  dev: 'https://hws-imsapi.3weijia.com',
  hws: 'https://hws-imsapi.3weijia.com',
  pre: 'https://pre-imsapi.3vjia.com',
  prod: 'https://imsapi.3vjia.com',
}

const turingServiceHostMap: any = {
  dev: 'https://hws-turing-service.3weijia.com',
  hws: 'https://hws-turing-service.3weijia.com',
  pre: 'https://pre-turing-service.3vjia.com',
  prod: 'https://turing-service.3vjia.com'
};

const openGatewayMap: any = {
  dev: 'https://hws-open-gateway.3weijia.com',
  hws: 'https://hws-open-gateway.3weijia.com',
  pre: 'https://pre-open-gateway.3vjia.com',
  prod: 'https://open-gateway.3vjia.com',
}



export const turingServiceHost = turingServiceHostMap[ENV];
export const gatewayUrl = gatewayMap[ENV];
export const openApiHost = openApiHostMap[ENV];
export const dpAiWebHost = dpAiWebHostMap[ENV];
export const staticHost = staticMap[ENV];
export const cadParseApiUrl = cadParseApiMap[ENV];
export const predictHouseImitateApiUrl = predictHouseImitateApiMap[ENV];
export const predictHouseImitateOpenApiUrl = predictHouseImitateOpenApiMap[ENV];
export const xmlParse2JsonApiUrl = xmlParse2JsonApiMap[ENV];
export const AIDeskUrl = AIDeskMap[ENV];
export const hxUrl = hxMap[ENV];
export const aiCabinetUrl = aiCabinetMap[ENV];
export const preorderUrl = preorderMap[ENV];
export const backgroundOrderUrl = backgroundOrderMap[ENV];
export const svj3dCloudDesignUrl:string = svj3dCloudDesignMap[ENV];
export const caApiUrl = caApiUrlMap[ENV];
export const xrHostDomain = xrHostDomainMap[ENV];
export const layoutHostDomain = layoutHostDomainMap[ENV];
export const workDomainMap = workHostDomainMap[ENV];
export const panoHost = panoHostMap[ENV];
export const openPluginUrl = openPluginUrlMap[ENV];
export const imsApiUrl = imsApiMap[ENV];
export const openGatewayUrl = openGatewayMap[ENV];
