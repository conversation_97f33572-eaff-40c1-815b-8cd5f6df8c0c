
export type I_SdkMsgType = "Command" | "Event" | "Async" | "Connected" | "OpenRequest" | "IndexDB";

const APP_ID = "2320217937436";
const MagiccubeToken = "";
export const LayoutAI_MsgConfig = {
    APP_ID : APP_ID,
    MagiccubeToken :MagiccubeToken,
    headers : {
        'Content-Type': 'application/json',
        'sysCode':APP_ID,
        'Magiccube-App-Id': APP_ID,
        'Magiccube-Token':MagiccubeToken,
        'Joint-Token': null,
    },
    openRequest : null,
    magiccubeDpAiWebRequest : null
}
export interface I_LayoutAI_MsgData {
    msgId?: string;

    msgType?: I_SdkMsgType;
    command?: string;
    eventName?: string;

    eventData?: { [key: string]: any };
    /**
     *  异步事件ID
     */
    asyncId?: string;

    /**
     *  异步事件名称
     */
    asyncEventName?: string;
    /**
     * asyncState: 0: querying; 1: done; -1: error;
     */
    asyncState?: number;
    /**
     *  异步事件结果
     */
    asyncResult?: any;

    aysncError?: string;

    aysncTimeout?: number;

    /**
     *  传入headers
     */
    headers ?: any;

    [key: string]: any;
}

export class LayoutAI_MsgCenter {
    protected static _instance: LayoutAI_MsgCenter;


    protected targetWindow: Window;

    protected onMessage: (event: MessageEvent) => void;

    postMessage : (data:I_LayoutAI_MsgData)=>void = (data)=>{};

    constructor() {
    }

    static makeInstance() {
        if (LayoutAI_MsgCenter._instance) {
            LayoutAI_MsgCenter._instance.dispose();
        }

        LayoutAI_MsgCenter._instance = new LayoutAI_MsgCenter();

    }
    static get instance() {
        if (!LayoutAI_MsgCenter._instance) {
            LayoutAI_MsgCenter.makeInstance();
        }
        return LayoutAI_MsgCenter._instance;
    }

    init() {
        let scope = this;
        if (!this.onMessage) {
            this.onMessage = function (event: MessageEvent) {
                scope.handleMessage(event.data);
            }
            globalThis.addEventListener("message",this.onMessage,false);
        }

    }

    dispose() {
        if (this.onMessage) {
            globalThis.removeEventListener("message", this.onMessage, false);
        }
    }

    bindTargetWindow(targetWindow: Window) {
        if (targetWindow) {
            this.targetWindow = targetWindow;
        }
    }


    protected handleMessage(data: I_LayoutAI_MsgData = {}) {
        if (data.msgType === "Command") {
            this.processCommand(data);
        }
        else if (data.msgType === "Event") {
            this.handleEvent(data);
        }
        else if (data.msgType === "Async") {
            this.processAsyncMsg(data);
        }
        else if (data.msgType === "Connected") {
            this._onConnected(data);
        }
    }

    /**
     * 在派生类中多态处理命令
     * @param data  
     */
    protected processCommand(data: I_LayoutAI_MsgData) {

    }

    /**
     * 在派生类中多态处理事件
     * @param data  
     */
    protected handleEvent(data: I_LayoutAI_MsgData) {

    }

    /**
     * 在派生类中多态处理异步信息
     * @param data  
     */
    protected async processAsyncMsg(data: I_LayoutAI_MsgData) {

    }

    protected _onConnected(data: I_LayoutAI_MsgData) {
        console.log("Connected!@@@", data);
    }

    public postEventMessage(event_name: string, event_data: any) {
        let data: I_LayoutAI_MsgData = {
            msgType: "Event",
            eventName: event_name,
            eventData: event_data
        }
        this.postMessage(data);
    }

}