import React, { useEffect, useState } from "react";
import useStyles from "./style";
import { useTranslation } from "react-i18next";
import { observer } from "mobx-react-lite";
import { FigureDataList } from "@layoutai/design_2d";
import { message } from "@svg/antd";
import { FigureList } from "../../../../components";
import { appContext } from "../../../../context/AppContext";
import { Design2DStates } from "../../../../context/fsm/const/FSMConst";

/**
 * @description 户型编辑面板
 */

interface Module {
    image: string;
    png: string;
    title: string;
    label: string;
    icon: string;
}

const HouseLayoutMenu: React.FC = () => {
    const { t } = useTranslation();
    const { styles } = useStyles();
    const menuList = FigureDataList;
    const [figureDataList, setFigureDataList] = useState<Module[]>([]);
    const [structural, setStructural] = useState<Module[]>([]);

    const DrawCommands = {
        StraightWall: "StraightWall",
        RectangleWall: "RectangleWall",
        Square: "Square",
        Rectangle: "Rectangle",
        LShape: "LShape",
        Convex: "Convex",
        Concave: "Concave",
        Polygon: "Polygon",
    } as const;
    
    const DRAW_WALL_MENU = [
        // 画墙
        [
            {
                title: t("直墙"),
                icon: "iconstraightwall",
                command: DrawCommands.StraightWall,
            },
            {
                title: t("矩形墙"),
                icon: "icondrawroom",
                command: DrawCommands.RectangleWall,
            },
        ],
        // 从形状绘制
        [
            {
                title: t("正方形"),
                icon: "iconsquare",
                command: DrawCommands.Square,
            },
            {
                title: t("长方形"),
                icon: "iconrectangle1",
                command: DrawCommands.Rectangle,
            },
            {
                title: t("L形"),
                icon: "iconLshape",
                command: DrawCommands.LShape,
            },
            {
                title: t("凸形"),
                icon: "iconconvex",
                command: DrawCommands.Convex,
            },
            {
                title: t("凹形"),
                icon: "iconconcave",
                command: DrawCommands.Concave,
            },
            {
                title: t("多边形"),
                icon: "iconpolygon",
                command: DrawCommands.Polygon,
            },
        ],
    ];

    let allList: any = [];
    menuList.forEach((item) => {
        item.child.forEach((childItem) => {
            allList = allList.concat(childItem.figureList);
        });
    });

    useEffect(() => {
        updateCurrentMenuList();
    }, []);

    const drawWallHandle = (command: string) => {
        console.log("drawWallHandle: ", command);
        appContext.mainFSM.transitionTo(Design2DStates.IDLE2D);
        if (command === DrawCommands.StraightWall || command === DrawCommands.RectangleWall) {
            console.log("进入绘制矩形模式");
            appContext.mainFSM.transitionTo(Design2DStates.DRAW_RECT);

            message.info({
                duration: 1,
                content: t("点击右键可以退出户型绘制").toString(),
                style: {
                    marginTop: "4vh",
                    zIndex: 10,
                },
            });
        }
    };

    const updateCurrentMenuList = () => {
        // let mode = LayoutAI_App.instance._current_handler_mode;
        // if (mode === AI2DesignBasicModes.HouseDesignMode) {
        let t_menu_list = menuList.filter((data) => data.label.includes("户型")); //[i18n:ignore]
        let win_door_type = ["推拉门", "单开门", "一字窗", "飘窗", "双开门", "子母门", "门洞", "垭口", "栏杆"]; //[i18n:ignore]
        let structural_type = ["包管", "地台", "方柱", "横梁", "烟道"]; //[i18n:ignore]
        let win_door_list: any = t_menu_list[0].child[0]?.figureList.filter((data) => win_door_type.includes(data.title));
        setFigureDataList(win_door_list);
        let structural_list: any = t_menu_list[0].child[0]?.figureList.filter((data) => structural_type.includes(data.title));
        setStructural(structural_list);
        // }
    };

    return (
        <div className={styles.root}>
            <div className={styles.title}>{t("画墙") as string}</div>
            <div className={styles.itemBox}>
                {DRAW_WALL_MENU[0].map((item) => (
                    <div
                        className={styles.item}
                        key={item.command}
                        onMouseDown={() => {
                            drawWallHandle(item.command);
                        }}
                    >
                        <div className={styles.itemIcon}>
                            <svg className="icon" aria-hidden="true" style={{ width: 60, height: 60 }}>
                                <use xlinkHref={`#${item.icon}`}></use>
                            </svg>
                        </div>
                        <div className={styles.desc}>{item.title}</div>
                    </div>
                ))}
            </div>

            <div className={styles.title}>{t("从形状绘制") as string}</div>
            <div className={styles.itemBox}>
                {DRAW_WALL_MENU[1].map((item) => (
                    <div
                        className={styles.item}
                        key={item.command}
                        onMouseDown={() => {
                            drawWallHandle(item.command);
                        }}
                    >
                        <div className={styles.itemIcon}>
                            <svg className="icon" aria-hidden="true" style={{ width: 60, height: 60 }}>
                                <use xlinkHref={`#${item.icon}`}></use>
                            </svg>
                        </div>
                        <div className={styles.desc}>{item.title}</div>
                    </div>
                ))}
            </div>
            <div className={styles.title}>{t("门窗") as string}</div>
            <FigureList data={figureDataList} />
            <div className={styles.title}>{t("结构件") as string}</div>
            <FigureList data={structural} />
        </div>
    );
};

export default observer(HouseLayoutMenu);
