
import { I_Window } from "@layoutai/basic_data";
import { I_Range2D, I_SubSpacePoly, TBaseRoomToolUtil, TFigureElement, TGroupTemplate, TRoom, WPolygon } from "@layoutai/layout_scheme";
import { Vec3toMeta, ZEdge, ZPolygon, ZRect, compareNames } from "@layoutai/z_polygon";
import { Vector3 } from "three";
import { I_TinyLayoutLogicStep, I_TinyLayoutParams, layout_rect_name_dict } from "../../TinyLogicFlow/TinyLayoutLogicFlow";
import { TinyLogicFlow } from "../../TinyLogicFlow/TinyLogicFlow";
import { TSimpleRoomPartRelation } from "./TSimpleRoomPartRelation";



const xDir: Vector3 = new Vector3(1, 0, 0);
const yDir: Vector3 = new Vector3(0, 1, 0);

export interface I_TinyLivingRoomByPathParams extends I_TinyLayoutParams
{
    pathAreaInfo?: any;

    livingPolyInfo?: {
        subPoly: ZPolygon,
        subMainRect: ZRect,
    };

    diningPolyInfo?: {
        subPoly: ZPolygon,
        subMainRect: ZRect,
    }

    hallwayRects?: ZRect[];

    hallwayPathEdges?: ZEdge[];

    // 柜体数据
    allCabinetRects?: ZRect[];

    // 窗户
    windows?: I_Window[];

    // 门
    doors?: I_Window[];

    // 户型有效区域
    validAreas?: ZPolygon[];

    // 客厅区
    livingAreaRect?: ZRect;

    // 餐厅区
    diningAreaRect?: ZRect;

    // 电视柜区
    tv_cabinet_rect?: ZRect;

    // 沙发组合区
    sofa_group_rect?: ZRect;

    // 沙发背景墙区
    sofa_background_wall_rect?: ZRect;

    // 餐桌组合区
    dinning_table_rect?: ZRect;

    // 餐边柜区
    dinning_cabinet_rect?: ZRect;

    // 玄关柜
    entrance_cabinet_rect?: ZRect;

    // 沙发地毯
    sofa_carpet_rect?: ZRect;
}

// TODO 
// 1. 玄关柜提到前面进行布置，这个可以参考士玮那边的代码设计，这个目前没啥特别的难度（这个优先进行解决）
// 2. 多边形内取如果第一大矩形和第二大矩形大小相差不大，则第二大的矩形也会也会作为目标区域进行布置
// 2. 代码重新整理
// 3. 动线优化
export class TTinyLivingRoomByPathRelation extends TSimpleRoomPartRelation
{
    // 客餐厅布局逻辑流
    private _layoutFlow: TinyLogicFlow;

    // 背景墙厚度
    public static readonly backgroundWallDepth: number = 80;

    public static readonly bedRoomClusterDist: number = 1000;

    public static readonly hallwayDist: number = 900;

    public static readonly pathMinLen: number = 200;

    public static readonly onWallTol: number = 100;

    public static readonly cabinetDepth: number = 450;

    public static readonly pathAttachWallDistTol: number = 1000;

    public static readonly minLivingAreaMinLength: number = 1400;

    public static readonly minTvCabinetLen: number = TTinyLivingRoomByPathRelation.minLivingAreaMinLength;

    public static readonly maxTvCabinetLen: number = 4000;

    public static readonly tvCabinetFrontMinLen: number = this.minLivingAreaMinLength - this.cabinetDepth;

    // 电视柜前方到墙超过多少距离则沙发组合区不靠墙摆放
    public static readonly tvCabinetFrontToWallMaxLen: number = 4500;

    public static readonly tvCabinetToSofaGroupBackDist: number =3000;

    // 电视柜前方多少距离设置沙发组合区域
    public static readonly tvCabinetFrontBufferDistance: number = 300;

    // 餐边柜最小长度
    public static readonly diningCabinetMinLen: number = 900;

    // 餐边柜最大长度
    public static readonly diningCabinetMaxLen: number = 4000;

    // 餐边柜前方多少距离放置餐边柜, (目前无要求餐桌一定落于餐边柜前方)
    public static readonly diningCabinetFrontBufferDistance: number = 50;

    public static readonly diningCabinetFrontBufferMaxDistance: number = 2000;

    public static readonly diningGroupMinLen: number = 1000;

    public static readonly diningGroupMinDepth: number = 1500;

    public static readonly pointDistTol: number = 5;

    public static readonly entranceCabinetMinLen: number = 800;
    
    public static readonly entranceCabinetMaxLen: number = 2500;

    public static readonly entranceCabinetToEntranceDoorMaxDist: number = 3000;

    constructor() 
    {
        super(); 
        this.initLayoutFlow();
    }

    private initLayoutFlow()
    {
        this._layoutFlow = new TinyLogicFlow();
        this._layoutFlow._quiet = true;
        this._layoutFlow._root._description = "客餐厅布局_区域划分";
        let startFlowStep = this._initLayoutFlow();
        this._layoutFlow._root.loop_steps = [startFlowStep];
        this._max_attempt_num = 30;
    }

    public precompute()
    {
        this._candidate_figure_list = [];
        if (!this._room || !compareNames([this._room.roomname], ["客餐厅"])) return;
        if (!this._room.room_shape._feature_shape) {
            this._room.updateFeatures();
        }
        let params: I_TinyLivingRoomByPathParams = {
            _room: this._room,
            _w_poly: this._room.room_shape._feature_shape._w_poly,
        };
        this._layoutFlow.apply(this._layoutFlow._root, params);
        this.filterCandidates();
        this._candidate_figure_list = this._candidate_figure_list.sort((a, b) => b.debug_data._flow_score - a.debug_data._flow_score);
        this.postProcessLivingAndDiningLayout();
        // 需要做去重处理
        this._attempt_num = this._candidate_figure_list.length;
    }
    
    private filterCandidates()
    {
        // 对布局方案进行过滤，原有的客餐厅过滤方案是比较拉胯的，这里需要重写一版
        //  其实内部的核心是同一块区域的家具类型是否一致，并且是处于同一个区域模块的，即这两个的距离足够近切家具类型是一致
        let distTol: number = 1000;
        let schemeLen: number = this._candidate_figure_list.length;
        // 这里的本质上还是彼此之间进行互相剔除
        let repeatSchemeIndexs: number[] = [];
        for(let i = 0; i < schemeLen; ++i)
        {
            if (repeatSchemeIndexs.includes(i))
            {
                continue;
            }
            let currentScheme: any = this._candidate_figure_list[i];
            for(let j = i + 1; j < schemeLen; ++j)
            {
                let otherScheme: any = this._candidate_figure_list[j];
                if(isHighSimilarity(currentScheme, otherScheme, distTol))
                {
                    repeatSchemeIndexs.push(j);
                }
            }
        }
        this._candidate_figure_list = this._candidate_figure_list.filter((scheme, index) => { return !repeatSchemeIndexs.includes(index); });
    }

    private _initLayoutFlow(): I_TinyLayoutLogicStep
    {
        let multiPathStep: I_TinyLayoutLogicStep = {
            description: "计算过道多解(卧室集群以及更远的卧室门)",
            step: (params: I_TinyLivingRoomByPathParams) => {
                // LayoutGraphTestingHander._valid_polys = [];
                let pathAreaInfos: any = calPathsByDoor(params);
                if(!pathAreaInfos){
                    return null;
                }
                let results: I_TinyLivingRoomByPathParams[] = [];
                for(let pathAreaInfo of pathAreaInfos){
                    let pathResult: I_TinyLivingRoomByPathParams = {
                        pathAreaInfo: pathAreaInfo,
                    }
                    results.push(pathResult);
                }
                return {
                    results: results
                }
            }
        };

        let validSpaceAreaStep: I_TinyLayoutLogicStep = {
            description: "剔除过道区域后的有效空间区域",
            step: (params: I_TinyLivingRoomByPathParams) => {
                params.doors = params._room.windows.filter((win) => { return win.type === "Door"; });
                params.windows = params._room.windows.filter((win) => { return win.type === "Window"; });
                let wPoly: ZPolygon = params._room.room_shape._feature_shape._w_poly;
                params.allCabinetRects = WPolygon._computeValidOnWallCabinetRects(wPoly);
                params.validAreas = computeValidAreas(params);
                let subPolys: ZPolygon[] =  [];
                let pathAreaInfo: any = params.pathAreaInfo;
                if(pathAreaInfo)
                {
                    params.hallwayPathEdges = pathAreaInfo.pathEdges;
                    subPolys = params._room.room_shape._poly.clone().substract(pathAreaInfo.pathPolygon);
                    if(subPolys.length === 0){
                        return null;
                    }
                    // 设置区域内的过道区域，首先确保所有的过道区域都在区域内，然后同时反过来进行区域截取
                    params.hallwayRects = [];
                    let roomShapeRange: I_Range2D = TBaseRoomToolUtil.instance.getRange2dByPolygon(params._room.room_shape._poly);
                    for(let index = pathAreaInfo.pathRects.length - 1; index >= 0; index--){
                        let pathRect: ZRect = pathAreaInfo.pathRects[index];
                        let pathRange: I_Range2D = TBaseRoomToolUtil.instance.getRange2dByPolygon(pathRect);
                        pathRange.xMin = Math.max(pathRange.xMin, roomShapeRange.xMin);
                        pathRange.xMax = Math.min(pathRange.xMax, roomShapeRange.xMax);
                        pathRange.yMin = Math.max(pathRange.yMin, roomShapeRange.yMin);
                        pathRange.yMax = Math.min(pathRange.yMax, roomShapeRange.yMax);
                        if(index == 0 && index == pathAreaInfo.pathRects.length - 1)
                        {
                            for(let roomEdge of params._room.room_shape._poly.edges)
                            {
                                let roomEdgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(roomEdge);
                                if(TBaseRoomToolUtil.instance.isOverlayRange2ds(roomEdgeRange, pathRange))
                                {
                                    let roomEdgeXDot: number = roomEdge.nor.clone().dot(xDir);
                                    let roomEdgeYDot: number = roomEdge.nor.clone().dot(yDir);
                                    if(roomEdgeXDot > 0.9)
                                    {
                                        pathRange.xMax = Math.min(pathRange.xMax, roomEdgeRange.xMax);
                                    }
                                    if(roomEdgeXDot < -0.9)
                                    {
                                        pathRange.xMin = Math.max(pathRange.xMin, roomEdgeRange.xMin);
                                    }
                                    if(roomEdgeYDot > 0.9)
                                    {
                                        pathRange.yMax = Math.min(pathRange.yMax, roomEdgeRange.yMax);
                                    }
                                    if(roomEdgeYDot < -0.9)
                                    {
                                        pathRange.yMin = Math.max(pathRange.yMin, roomEdgeRange.yMin);
                                    }
                                }
                            }
                        }
                        let newPathRect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(pathRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(pathRange));
                        if(index != 0)
                        {
                            let prePathRect: ZRect = pathAreaInfo.pathRects[index - 1];
                            let subPathPolys: ZPolygon[] = newPathRect.substract(prePathRect);
                            if(subPathPolys.length > 0)
                            {
                                newPathRect = TBaseRoomToolUtil.instance.getMaxInnerRectByPolygon(subPathPolys[0]);
                            }
                            subPathPolys = null;
                        }
                        params.hallwayRects.push(newPathRect);
                    }
                     // TODO 这个是为了临时将过道区域显示出来
                    // LayoutGraphTestingHander._valid_polys.push(...subPolys);
                }
                else
                {
                    // roomPoly 一整个直接进行分割，这里涉及的分割包含阳角分割以及对半分, 但是需要比例均衡
                    subPolys = splitRoomPolyBestRatio(params._room.room_shape._poly, params);
                    logRoomAndPolys(params._room.room_shape._poly, subPolys, "绘制户型分割结果");
                }
                let newResults: I_TinyLivingRoomByPathParams[] = markLivingAndDingAreaResults(params, subPolys);
                if(newResults.length > 0)
                {
                    return {
                        results: newResults,
                    };
                }
                return null;
            }
        };

        let newLayoutEntranceCanbinetStep: I_TinyLayoutLogicStep = {
            description: "优先布置玄关柜",
            step: (params: I_TinyLivingRoomByPathParams) => {
                let entranceCabinetInfo: I_TinyLivingRoomByPathParams[] = layoutEntranceCabinet(params);
                if(entranceCabinetInfo)
                {
                    return {
                        results: entranceCabinetInfo,
                    };
                }
                return null;
            }
        }

        // 布置客厅区
        let layoutLivingStep: I_TinyLayoutLogicStep = {
            description: "客厅区布局",
            step: (params: I_TinyLivingRoomByPathParams) => {
                if(!params.livingPolyInfo){
                    return null;
                }
                let livingSubAreaInfo: I_TinyLivingRoomByPathParams[] = layouSubSpaceAreaByLivingArea(params.livingPolyInfo, params);
                if(livingSubAreaInfo)
                {
                    return {
                        results: livingSubAreaInfo,
                    };
                }
                return null; 
            }
        }

        // 布置餐厅区
        let layoutDiningStep: I_TinyLayoutLogicStep = {
            description: "餐厅区布局",
            step: (params: I_TinyLivingRoomByPathParams) => {
                if(!params.diningPolyInfo) 
                {
                    return null;
                }
                let diningSubAreaInfo: I_TinyLivingRoomByPathParams[] = layouSubSpaceAreaByDiningArea(params.diningPolyInfo.subMainRect, params);
                // 重新布置餐桌布局
                if(diningSubAreaInfo)
                {
                    return {
                        results: diningSubAreaInfo,
                    };
                }
                return null; 
            }
        }

        // 玄关柜布置，下面重新改造，重新布置玄关柜
        let layoutEntranceCanbinetStep: I_TinyLayoutLogicStep = {
            description: "布置玄关柜",
            step: (params: I_TinyLivingRoomByPathParams) => {
                let entranceCabinetInfo: I_TinyLivingRoomByPathParams[] = layoutSubSpaceAreaByEntranceCabinetArea(params);
                if(entranceCabinetInfo)
                {
                    return {
                        results: entranceCabinetInfo,
                    };
                }
                return null;
            }
        }

        let finallyStep: I_TinyLayoutLogicStep = {
            description: "合成最终布局",
            step: (params: I_TinyLivingRoomByPathParams) => {
                if(!params.sofa_group_rect)
                {
                    return null;
                }
                let t_group_templates: TGroupTemplate[] = [];
                let sub_area_list: I_SubSpacePoly[] = [];
                let name_dict = layout_rect_name_dict;
                for (let key in name_dict) {
                    if (params[key]) {
                        let group_space_category = (name_dict as any)[key];
                        if (key.endsWith("_area_poly"))
                        {
                            let poly = params[key] as ZPolygon;
                            sub_area_list.push({
                                area_name: group_space_category,
                                area_points: poly.positions.map((v) => Vec3toMeta(v))
                            })

                        }
                        else if (params[key] instanceof Array) {
                            let target_rects: ZRect[] = params[key];
                            target_rects.forEach((rect) => {
                                let group_tempalte = TGroupTemplate.makeGroupTemplateByGroupSpaceCategory(group_space_category, "客餐厅", rect, {needs_set_group_code:false,consider_depth:true});
                                if (group_tempalte && group_tempalte.current_s_group) {
                                    t_group_templates.push(group_tempalte);
                                }
                            })
                        }
                        else {
                             // TODO 这里调用其余的布局方案，emmm并且还得是TGroupTemplate数据类型
                            let group_tempalte = TGroupTemplate.makeGroupTemplateByGroupSpaceCategory(group_space_category, "客餐厅", params[key], {needs_set_group_code:false,consider_depth:true});
                            if (group_tempalte && group_tempalte.current_s_group) {
                                t_group_templates.push(group_tempalte);
                            }
                        }
                    }
                }

                if (t_group_templates.length > 0) {
                    // TODO 下面这里需要带出子区域评分信息，方便后续进行排序
                    this._candidate_figure_list.push({ /*sub_aeras: sub_area_list,*/ group_templates: t_group_templates,
                        debug_data: {
                            _flow_score: computeLivingRoomScore(params),
                            scheme_name: "逻辑-" + (params.rule_text || "动线分区布局"), 
                            livingSpace: params.livingPolyInfo.subMainRect, diningSpace: params.diningPolyInfo.subMainRect, hallwaySpace: params.hallwayRects, diningTableRect: params.dinning_table_rect}
                    });
                }

                return null;
            }
        }
        multiPathStep.loop_steps = [validSpaceAreaStep];
        validSpaceAreaStep.loop_steps = [layoutLivingStep];
        layoutLivingStep.loop_steps = [layoutDiningStep];
        layoutDiningStep.loop_steps = [layoutEntranceCanbinetStep];
        layoutEntranceCanbinetStep.loop_steps = [finallyStep];
        return multiPathStep;
    }

    private postProcessLivingAndDiningLayout()
    {
        this.postProcessDiningGroupLayout();
    }

    
    private postProcessDiningGroupLayout()
    {
        // 1. 这个处理餐厅区的餐桌组合的主图元与玄关柜重合或者还是在餐厅区中心点
        for(let candidateFigureInfo of this._candidate_figure_list)
        {
            let diningCabinetFigures: TFigureElement[] = getFigureFromCandidateFigureList(candidateFigureInfo, "餐边柜区");
            let diningTableFigures: TFigureElement[] = getFigureFromCandidateFigureList(candidateFigureInfo, "餐桌区");
            if(diningCabinetFigures.length == 0)
            {
                // 尽可能将餐桌组合移到区域中心
                let diningTableRange: any = TBaseRoomToolUtil.instance.getBoxRangByFigurs(diningTableFigures);
                let diningTableGroupRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(candidateFigureInfo.debug_data.diningTableRect);
                let diningTableCenter: Vector3 = TBaseRoomToolUtil.instance.getCenter3dByRange2d(diningTableRange);
                let diningTableGroupCenter: Vector3 = TBaseRoomToolUtil.instance.getCenter3dByRange2d(diningTableGroupRange);
                let subVector: Vector3 = diningTableGroupCenter.clone().sub(diningTableCenter);
                moveDiningTableFigures(subVector, diningTableRange, diningTableGroupRange, diningTableFigures);
            }
            else
            {
                let diningTableRange: any = TBaseRoomToolUtil.instance.getBoxRangByFigurs(diningTableFigures);
                let diningTableGroupRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(candidateFigureInfo.debug_data.diningTableRect);
                let diningCabinetFrontEdge: ZEdge = diningCabinetFigures[0].rect.frontEdge;
                let diningTableFigure: TFigureElement = diningTableFigures.find(figure => { return figure.sub_category.includes("餐桌")});
                let diningTableCenter: Vector3 = diningTableFigure.rect.rect_center.clone();
                let projectInfo: any = diningCabinetFrontEdge.projectEdge2d({x: diningTableCenter.x, y: diningTableCenter.y});
                let projectY: number = projectInfo.y;
                if(projectY > TTinyLivingRoomByPathRelation.diningCabinetFrontBufferMaxDistance)
                {
                    projectY = TTinyLivingRoomByPathRelation.diningCabinetFrontBufferMaxDistance;
                }
                let unprojectPoint: Vector3 = diningCabinetFrontEdge.unprojectEdge2d({x: diningCabinetFrontEdge.length / 2, y: projectY});
                let subVector: Vector3 = unprojectPoint.clone().sub(diningTableCenter);
                moveDiningTableFigures(subVector, diningTableRange, diningTableGroupRange, diningTableFigures);
            }
        }
    }
}

function calPathsByDoor(params: I_TinyLivingRoomByPathParams): any[]
{
    let room: TRoom = params._room;
    let roomPoly: ZPolygon = room.room_shape._poly;
    let doors: I_Window[] = room.windows.filter((win) => { return win.type === "Door"; });
    if(doors.length === 0){
        return null;
    }
    let entranceDoors:I_Window[] = doors.filter(door => door.room_names.includes("入户花园") || (door.room_names.length == 1 && door.room_names.includes("客餐厅")));
    let bedRoomDoors: I_Window[] = doors.filter(door => door.room_names.includes("卧室"));
    if(bedRoomDoors.length === 0 || entranceDoors.length == 0)
    {
        return null;
    }
    let pathInfos: any[] = getBedRoomClusterPointInfo(bedRoomDoors, entranceDoors, params._room);
    let pathPolyInfos: any[] = calPathAreas(roomPoly, pathInfos);
    return pathPolyInfos;
}

function getBedRoomClusterPointInfo(bedDoors: I_Window[], entranceDoors: I_Window[], room: TRoom): any[]
{
    let resultPathInfos: any[] = [];
    // 需要求出多动线解，即不单单是卧室集群同时还包含卧室最远点，我看看要如何进行拆分
    let bedCluster: Map<I_Window, I_Window[]> = new Map();
    for(let i = 0; i < bedDoors.length; ++i)
    {
        for(let j = i + 1; j < bedDoors.length; ++j)
        {
            let bedDist: number = TBaseRoomToolUtil.instance.calDistanceByPolygons(bedDoors[i].rect, bedDoors[j].rect);
            if(bedDist > 2000 || Math.abs(bedDoors[i].rect.nor.clone().dot(bedDoors[j].rect.nor)) > 0.9)
            {
                continue; 
            }
            let isFind: boolean = false;
            for(let entry of bedCluster.entries())
            {
                let doorCluster: I_Window[] = entry[1];
                if(doorCluster.includes(bedDoors[i]) || doorCluster.includes(bedDoors[j]))
                {
                    if(!doorCluster.includes(bedDoors[i]))
                    {
                        doorCluster.push(bedDoors[i]);
                    }
                    if(!doorCluster.includes(bedDoors[j]))
                    {
                        doorCluster.push(bedDoors[j]);
                    }
                    isFind = true;
                    break;
                }
            }
            if(!isFind)
            {
                bedCluster.set(bedDoors[i], [bedDoors[i], bedDoors[j]]);
            }
        }        
    }
    let farPoint: Vector3 = null;
    let farEntranceDoor: I_Window = null;
    let maxDist: number = null;
    let maxEntranceDoor: I_Window = null;
    let maxFarBedDoor:I_Window = null;
    for(let entranceDoor of entranceDoors)
    {
        for(let bedDoor of bedDoors)
        {
            let dist: number = entranceDoor.rect.rect_center.distanceTo(bedDoor.rect.rect_center);
            if(maxDist === null || dist > maxDist)
            {
                maxDist = dist;
                maxEntranceDoor = entranceDoor;
                maxFarBedDoor = bedDoor;
            }
        }
    }
    farPoint = moveBedDoorCenterPoint(room, maxFarBedDoor);
    farEntranceDoor = maxEntranceDoor;

    let clusterPoint: Vector3 = null;
    let clusterEntranceDoor: I_Window = null;
    if(bedCluster.size > 0)
    {
        let clusterPoints: Vector3[] = [];
        for(let doors of bedCluster.values())
        {
            let sumPoint: Vector3 = new Vector3(0, 0, 0);
            for(let door of doors)
            {
                let moveBedCenter: Vector3 = moveBedDoorCenterPoint(room, door);
                sumPoint.add(moveBedCenter);
            }
            sumPoint.multiplyScalar(1.0 / doors.length);
            clusterPoints.push(sumPoint);
        }
        let maxDist: number = null;
        let maxFarClusterPoint: Vector3 = null;
        let maxEntranceDoor: I_Window = null;
        for(let entranceDoor of entranceDoors)
        {
            for(let clusterPoint of clusterPoints)
            {
                let dist: number = entranceDoor.rect.rect_center.distanceTo(clusterPoint);
                if(maxDist === null || dist > maxDist)
                {
                    maxDist = dist;
                    maxFarClusterPoint = clusterPoint;
                    maxEntranceDoor = entranceDoor;
                }
            }
        }
        clusterPoint = maxFarClusterPoint;
        clusterEntranceDoor = maxEntranceDoor;
    }

    let isOnlyFar: boolean = false;
    if(farPoint && clusterPoint)
    {
        let distTol: number = 2000;
        let dist: number = farPoint.distanceTo(clusterPoint);
        if(dist < distTol)
        {
            isOnlyFar = true;
        }
    }

    if(farPoint && !isOnlyFar)
    {
        let subVec: Vector3 = farPoint.clone().sub(farEntranceDoor.rect.rect_center);
        let subDot: number = subVec.clone().dot(farEntranceDoor.rect.nor);
        let entranceDoorPathPoint: Vector3 = farEntranceDoor.rect.rect_center.clone().add(farEntranceDoor.rect.nor.clone().multiplyScalar((subDot > 0 ? 1 : -1) * TTinyLivingRoomByPathRelation.hallwayDist));
        resultPathInfos.push({
            pathVec: farPoint.clone().sub(entranceDoorPathPoint),
            entranceDoor: farEntranceDoor,
            pathStartPoint: entranceDoorPathPoint,
            pathEndPoint: farPoint
            });
    }
    if(clusterPoint)
    {
        let subVec: Vector3 = clusterPoint.clone().sub(clusterEntranceDoor.rect.rect_center);
        let subDot: number = subVec.clone().dot(clusterEntranceDoor.rect.nor);
        let entranceDoorPathPoint: Vector3 = clusterEntranceDoor.rect.rect_center.clone().add(clusterEntranceDoor.rect.nor.clone().multiplyScalar((subDot > 0 ? 1 : -1) * TTinyLivingRoomByPathRelation.hallwayDist));
        resultPathInfos.push({
            pathVec: clusterPoint.clone().sub(entranceDoorPathPoint),
            entranceDoor: clusterEntranceDoor,
            pathStartPoint: entranceDoorPathPoint,
            pathEndPoint: clusterPoint
            });
    }
    return resultPathInfos;
}

function calPathAreas(roomPoly: ZPolygon, pathInfos: any[]): any[]
{
    let results: any[] = [];
    for(let pathInfo of pathInfos)
    {
        // 1. 先对pathVec进行分解
        let pathResolves: any = parsePathVec(pathInfo, roomPoly);
        // 2. 判断哪条分解路径线最佳, 目前一个法向量是可以分解成两个方向的（至于这条路径下后续分解成多个转折角再说）
        let bestPath: Vector3[][] = getBestPathInRoom(roomPoly, pathResolves);
        if(!bestPath)
        {
            continue;
        }
        bestPath = modifyPathAttachWall(bestPath, roomPoly);
        let pathxs: number[][] = []; let pathys: number[][] = [];
        for(let pathItem of bestPath)
        {
            if(!pathItem)
            {
                continue;
            }
            pathxs.push([pathItem[0].x, pathItem[1].x]);
            pathys.push([pathItem[0].y, pathItem[1].y]);
        }
        let pathRects: ZRect[] = [];
        for(let bestPathItem of bestPath)
        {
            if(!bestPathItem)
            {
                continue;
            }
            let pathRect: ZRect = createRectByPath(bestPathItem, pathInfo.entranceDoor);
            if(pathRect)
            {
                expandPathWidthPathRects(pathRect, bestPathItem, roomPoly);
                pathRects.push(pathRect);
            }
        }
        let entranceDoorRect: ZRect = expandDoorRect(pathInfo.entranceDoor);
        if(pathRects.length == 0)
        {
            return null;
        }
        for(let pathIndex = 0; pathIndex < pathRects.length; ++pathIndex)
        {
            let nextPathRect: ZRect = null;
            if(pathIndex < pathRects.length - 1)
            {
                nextPathRect = pathRects[pathIndex + 1];
            }
            if(!nextPathRect)
            {
                continue;
            }
            let currentPathRect: ZRect = pathRects[pathIndex];
            let currentPathDir: Vector3 = bestPath[pathIndex][1].clone().sub(bestPath[pathIndex][0]).normalize();
            let currentPathRectRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(currentPathRect);
            let nextPathRectRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(nextPathRect);

            if(Math.abs(currentPathDir.clone().dot(nextPathRect.leftEdge.nor)) > 0.9)
            {
                currentPathRectRange.xMin = Math.min(currentPathRectRange.xMin, nextPathRectRange.xMin);
                currentPathRectRange.xMax = Math.max(currentPathRectRange.xMax, nextPathRectRange.xMax);
            }
            else if(Math.abs(currentPathDir.clone().dot(nextPathRect.frontEdge.nor)) > 0.9)
            {
                currentPathRectRange.yMin = Math.min(currentPathRectRange.yMin, nextPathRectRange.yMin);
                currentPathRectRange.yMax = Math.max(currentPathRectRange.yMax, nextPathRectRange.yMax);
            }
            let modifyCurrentPathRect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(currentPathRectRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(currentPathRectRange));
            currentPathRect.copy(modifyCurrentPathRect);
        }
        let unionPoly: ZPolygon[] = entranceDoorRect.union_polygons(pathRects);
        if(unionPoly.length > 0)
        {
            let bestPathEdges: ZEdge[] = [];
            for(let pathItem of bestPath)
            {
                if(!pathItem)
                {
                    continue;
                }
                let pathEdge: ZEdge = new ZEdge({pos: pathItem[0]}, {pos: pathItem[1]});
                pathEdge.computeNormal();
                bestPathEdges.push(pathEdge);
            }
            results.push({pathPolygon: unionPoly[0], pathRects: [entranceDoorRect, ...pathRects], pathEdges: bestPathEdges});
        }
    }
    if(results.length > 0)
    {
        return results;
    }
    return null;
}

function parsePathVec(pathInfo: any, roomPoly: ZPolygon): any
{
    let endNearWallEdge: ZEdge = null;
    let minDist: number = null;
    for(let wallEdge of roomPoly.edges)
    {
        let projectInfo: any = wallEdge.projectEdge2d({x: pathInfo.pathEndPoint.x, y: pathInfo.pathEndPoint.y});
        if(projectInfo.x < 0 || projectInfo.x > wallEdge.length)
        {
            continue;
        }
        let dist: number = Math.abs(projectInfo.y);
        if(minDist === null || dist < minDist)
        {
            minDist = dist;
            endNearWallEdge = wallEdge;
        }
    }

    let resolveX: Vector3 = xDir.clone().multiplyScalar(pathInfo.pathVec.clone().dot(xDir));
    let resolveY: Vector3 = yDir.clone().multiplyScalar(pathInfo.pathVec.clone().dot(yDir));
    let firstPath1: Vector3[] = [pathInfo.pathStartPoint, pathInfo.pathStartPoint.clone().add(resolveX)];
    if(firstPath1[0].distanceTo(firstPath1[1]) < TTinyLivingRoomByPathRelation.pathMinLen)
    {
        firstPath1 = null;
    }
    let firstPath2: Vector3[] = null;
    if(firstPath1)
    {
        firstPath2 = [firstPath1[1], pathInfo.pathEndPoint];
    }
    else
    {
        firstPath2 = [pathInfo.pathStartPoint, pathInfo.pathStartPoint.clone().add(resolveY)];
    }
    let firstPathDir: Vector3 = firstPath2[1].clone().sub(firstPath2[0]).normalize();
    if(Math.abs(firstPathDir.clone().dot(endNearWallEdge.nor)) > 0.9)
    {
        let firstIntersectInfo: any = roomPoly.getRayIntersection(firstPath2[1], firstPathDir);
        if(firstIntersectInfo.point)
        {
            firstPath2[1] = firstIntersectInfo.point;
        }
    }
    if(firstPath2[0].distanceTo(firstPath2[1]) < TTinyLivingRoomByPathRelation.pathMinLen)
    {
        firstPath2 = null;
    }
    let secondPath1: Vector3[] = [pathInfo.pathStartPoint, pathInfo.pathStartPoint.clone().add(resolveY)];
    if(secondPath1[0].distanceTo(secondPath1[1]) < TTinyLivingRoomByPathRelation.pathMinLen)
    {
        secondPath1 = null;
    }
    let secondPath2: Vector3[] = null;
    if(secondPath1)
    {
        secondPath2 = [secondPath1[1], pathInfo.pathEndPoint];
    }
    else
    {
        secondPath2 = [pathInfo.pathStartPoint, pathInfo.pathStartPoint.clone().add(resolveX)];
    }
    let secondPathDir: Vector3 = secondPath2[1].clone().sub(secondPath2[0]).normalize();
    if(Math.abs(secondPathDir.clone().dot(endNearWallEdge.nor)) > 0.9)
    {
        let secondIntersectInfo: any = roomPoly.getRayIntersection(secondPath2[1], secondPathDir);
        if(secondIntersectInfo.point)
        {
            secondPath2[1] = secondIntersectInfo.point;
        }
    }
    if(secondPath2[0].distanceTo(secondPath2[1]) < TTinyLivingRoomByPathRelation.pathMinLen)
    {
        secondPath2 = null;
    }
    let path1: Vector3[][] = [];
    if(firstPath1)
    {
        path1.push(firstPath1);
    }
    if(firstPath2)
    {
        path1.push(firstPath2);
    }
    let path2: Vector3[][] = [];
    if(secondPath1)
    {
        path2.push(secondPath1);
    }
    if(secondPath2)
    {
        path2.push(secondPath2);
    }
    let path: any = {
        path1: path1,
        path2: path2,
    };
    return path;
}

function getBestPathInRoom(roomPoly: ZPolygon, pathResolves: any): Vector3[][]
{
    let pathOverNum1: number = calPathOverRoomNum(roomPoly, pathResolves.path1);
    let pathOverNum2: number = calPathOverRoomNum(roomPoly, pathResolves.path2);
    let minOverNum: number = Math.min(pathOverNum1, pathOverNum2);
    let minOverPath: Vector3[][][] = [];
    if(pathOverNum1 == minOverNum)
    {
        minOverPath.push(pathResolves.path1);
    }
    if(pathOverNum2 == minOverNum)
    {
        minOverPath.push(pathResolves.path2)
    }
    if(minOverPath.length == 1 || minOverNum == 0)
    {
        return minOverPath[0];
    }
    let targetPaths: Vector3[][] = parseCandidatePaths(roomPoly, pathResolves);
    return targetPaths;
}

function calPathOverRoomNum(roomPoly: ZPolygon, path: Vector3[][]): number
{
    let pathOverNum1: number = 0;
    let pathOverNum2: number = 0;
    if(path[0])
    {
        let pathEdge1: ZEdge = new ZEdge({pos: path[0][0]}, {pos: path[0][1]});
        pathEdge1.computeNormal();
        pathOverNum1 = calOnePathOverRoomNum(roomPoly, pathEdge1);
        pathEdge1 = null;
    }
    if(path[1])
    {
        let pathEdge2: ZEdge = new ZEdge({pos: path[1][0]}, {pos: path[1][1]});
        pathEdge2.computeNormal();
        pathOverNum2 = calOnePathOverRoomNum(roomPoly, pathEdge2);
        pathEdge2 = null;
    }
    return Math.abs(pathOverNum1) + Math.abs(pathOverNum2);
}

function calOnePathOverRoomNum(roomPoly: ZPolygon, pathEdge: ZEdge): number
{
    let pathEdgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(pathEdge);
    let overNum: number = 0;
    for(let roomEdge of roomPoly.edges)
    {
        let dotVal: number = roomEdge.nor.clone().dot(pathEdge.dv);
        if(Math.abs(dotVal) < 0.9)
        {
            continue;
        }
        let roomEdgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(roomEdge);
        if(TBaseRoomToolUtil.instance.isOverlayRange2ds(pathEdgeRange, roomEdgeRange, false))
        {
            if(dotVal > 0)
            {
                overNum += 1;
            }
            else if(dotVal < 0)
            {
                overNum -= 1;
            }
        }
    }
    return overNum;
}

function expandDoorRect(door: I_Window): ZRect
{
    let doorRect: ZRect = door.rect.clone();
    let doorCenter: Vector3 = doorRect.rect_center.clone();
    doorRect.depth += (2 * TTinyLivingRoomByPathRelation.hallwayDist);
    doorRect.rect_center = doorCenter;
    return doorRect;
}

function createRectByPath(path: Vector3[], entranceDoor: I_Window): ZRect
{
    if(!path)
    {
        return null;
    }
    let dist: number = path[0].distanceTo(path[1]);
    if(dist < TTinyLivingRoomByPathRelation.pathMinLen)
    {
        return null;
    }
    let targetEdge: ZEdge = null;
    let dirVec: Vector3 = path[1].clone().sub(path[0]).normalize();
    if(entranceDoor)
    {
        let startPoint: Vector3 = path[0];
        let endPoint: Vector3 = path[1].clone().add(dirVec.clone().multiplyScalar(TTinyLivingRoomByPathRelation.hallwayDist / 2));
        targetEdge = new ZEdge({pos: startPoint}, {pos: endPoint});
        targetEdge.computeNormal();
    }
    else
    {
        let startPoint: Vector3 = path[0];
        let endPoint: Vector3 = path[1].clone().add(dirVec.clone().multiplyScalar(TTinyLivingRoomByPathRelation.hallwayDist * 100));
        targetEdge = new ZEdge({pos: startPoint}, {pos: endPoint});
        targetEdge.computeNormal();
    }
    let targetRect: ZRect = createRectByEdge(targetEdge);
    return targetRect;
}

function createRectByEdge(edge: ZEdge): ZRect
{
    let halfHallwayDist: number = TTinyLivingRoomByPathRelation.hallwayDist / 2;
    let edgeNor: Vector3 = edge.nor.clone();
    let xDot: number = edgeNor.clone().dot(xDir);
    let yDot: number = edgeNor.clone().dot(yDir);
    let edgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(edge);
    if(Math.abs(xDot) > 0.9)
    {
        edgeRange.xMax += halfHallwayDist;
        edgeRange.xMin -= halfHallwayDist;
    }
    else if(Math.abs(yDot) > 0.9)
    {
        edgeRange.yMax += halfHallwayDist;
        edgeRange.yMin -= halfHallwayDist;
    }
    let rect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(edgeRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(edgeRange));
    return rect;
}

function logRoomAndPolys(roomPoly: ZPolygon, polys: ZPolygon[], name: string) {
    console.log("---------------------" + name + "---------------------");
    let roomXs: number[] = [];
    let roomYs: number[] = [];
    for (let edge of roomPoly.edges) {
        roomXs.push(edge.v0.pos.x);
        roomXs.push(edge.v1.pos.x);
        roomYs.push(edge.v0.pos.y);
        roomYs.push(edge.v1.pos.y);
    }
    console.log("roomXs:  " + roomXs);
    console.log("roomYs:  " + roomYs);

    for(let i = 0; i < polys.length; ++i)
    {
        console.log("---------------------" + i + "---------------------");
        let polyXs: number[] = [];
        let polyYs: number[] = [];
        for (let edge of polys[i].edges) {
            polyXs.push(edge.v0.pos.x);
            polyXs.push(edge.v1.pos.x);
            polyYs.push(edge.v0.pos.y);
            polyYs.push(edge.v1.pos.y);
        }
        console.log("polyXs:  " + polyXs);
        console.log("polyYs:  " + polyYs);
    }
}

function logRoomAndPath(roomPoly: ZPolygon, pathResolves: any, name: string) {
    console.log("---------------------" + name + "---------------------");
    let roomXs: number[] = [];
    let roomYs: number[] = [];
    for (let edge of roomPoly.edges) {
        roomXs.push(edge.v0.pos.x);
        roomXs.push(edge.v1.pos.x);
        roomYs.push(edge.v0.pos.y);
        roomYs.push(edge.v1.pos.y);
    }
    console.log("roomXs:  " + roomXs);
    console.log("roomYs:  " + roomYs);

    if(pathResolves)
    {
        console.log("---------------------" + "path1" + "---------------------");
        let pathXs1: number[] = [];
        let pathYs1: number[] = [];
        for(let pathVecs of pathResolves.path1)
        {
            if(!pathVecs)
            {
                continue;
            }
            for(let vec of pathVecs)
            {
                pathXs1.push(vec.x);
                pathYs1.push(vec.y);
            }
        }
        console.log("pathXs: " + pathXs1);
        console.log("pathYs: " + pathYs1);


        console.log("---------------------" + "path2" + "---------------------");
        let pathXs2: number[] = [];
        let pathYs2: number[] = [];
        for(let pathVecs of pathResolves.path2)
        {
            if(!pathVecs)
            {
                continue;
            }
            for(let vec of pathVecs)
            {
                pathXs2.push(vec.x);
                pathYs2.push(vec.y);
            }
        }
        console.log("pathXs: " + pathXs2);
        console.log("pathYs: " + pathYs2);
    }
}

function moveBedDoorCenterPoint(room: TRoom, bedDoor: I_Window): Vector3
{
    let layonRoomEdge: ZEdge = null;
    for(let roomEdge of room.room_shape._poly.edges)
    {
        if(Math.abs(roomEdge.nor.clone().dot(bedDoor.rect.nor)) < 0.9)
        {
            continue;
        }
        if(TBaseRoomToolUtil.instance.edgeOnPolygon(roomEdge, bedDoor.rect, TTinyLivingRoomByPathRelation.onWallTol, 0.1))
        {
            layonRoomEdge = roomEdge;
            break;
        }
    }
    let bedDoorCenter: Vector3 = bedDoor.rect.rect_center.clone();
    if(layonRoomEdge)
    {
        let moveDir: Vector3 = layonRoomEdge.nor.clone().negate();
        bedDoorCenter.add(moveDir.multiplyScalar(TTinyLivingRoomByPathRelation.hallwayDist / 2));
    }
    return bedDoorCenter;
}

function modifyPathAttachWall(paths: Vector3[][], roomPoly: ZPolygon): Vector3[][]
{
    if(paths.length < 2)
    {
        return paths;
    }
    // 多路径贴墙并且保持适当距离处理，不当当只处理第一条路径线，同时应当处理其余路线并且当前路线的修正会对前后的路径线同步进行修正
    for(let pathIndex = 0; pathIndex < paths.length; pathIndex++)
    {
        let currentPath: Vector3[] = paths[pathIndex];
        if(!currentPath)
        {
            continue;
        }
        let prePath: Vector3[] = null;
        let nextPath: Vector3[] = null;
        if(pathIndex > 0)
        {
            prePath = paths[pathIndex - 1];
        }
        if(pathIndex < paths.length - 1)
        {
            nextPath = paths[pathIndex + 1];
        }
        // 检查当前的路径线是否需要修正
        let currentPathEdge: ZEdge = new ZEdge({pos: currentPath[0]}, {pos: currentPath[1]});
        currentPathEdge.computeNormal();
        let currentPathRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(currentPathEdge);
        // 获取最贴近此边的户型边
        let minRoomEdgeDist: number = null;
        let nearRoomEdge: ZEdge = null;
        for(let roomEdge of roomPoly.edges)
        {
            let dist: number = TBaseRoomToolUtil.instance.calDistance(roomEdge, currentPathEdge);
            let roomEdgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(roomEdge);
            if(pathIndex == 0 && nextPath)
            {
                let nextPathDir: Vector3 = nextPath[1].clone().sub(nextPath[0]).normalize();
                if(roomEdge.nor.clone().dot(nextPathDir) > 0.9)
                {
                    continue;
                } 
            }
            if(!TBaseRoomToolUtil.instance.isParallelTwoEdges(roomEdge, currentPathEdge)
                || dist > TTinyLivingRoomByPathRelation.pathAttachWallDistTol
                || !((currentPathRange.xMin < roomEdgeRange.xMax && currentPathRange.xMax > roomEdgeRange.xMin) 
                    || (currentPathRange.yMin < roomEdgeRange.yMax && currentPathRange.yMax > roomEdgeRange.yMin)))
            {
                continue;
            }
            if(minRoomEdgeDist === null || dist < minRoomEdgeDist)
            {
                minRoomEdgeDist = dist;
                nearRoomEdge = roomEdge;
            }
        }
        if(!nearRoomEdge)
        {
            continue;
        }
        let moveDir: Vector3 = nearRoomEdge.nor.clone();
        let moveLen: number = minRoomEdgeDist - TTinyLivingRoomByPathRelation.hallwayDist / 2;
        let currentStartPoint: Vector3 = currentPath[0].clone().add(moveDir.clone().multiplyScalar(moveLen));
        let currentEndPoint: Vector3 = currentPath[1].clone().add(moveDir.clone().multiplyScalar(moveLen));
        currentPath[0] = currentStartPoint;
        currentPath[1] = currentEndPoint;
        if(prePath)
        {
            prePath[1] = currentStartPoint;
        }
        if(nextPath)
        {
            nextPath[0] = currentEndPoint;
        }
    }
    return paths;
}

function getLivingSubPolyInfo(twoSpacePolyInfo: any[], params: I_TinyLivingRoomByPathParams): any
{
    let doors: I_Window[] = params.doors;
    let blaconyDoors: I_Window[] = doors.filter(door => door.type == "Door" && door.room_names.includes("阳台"));
    let kitchenDoors: I_Window[] = doors.filter(door => door.type == "Door" && door.room_names.includes("厨房"));
    let kitchenWindows: I_Window[] = params.windows.filter(door => door.type == "Window" && door.room_names.includes("厨房"));
    let entranceDoors: I_Window[] = doors.filter(door => door.type == "Door" && (door.room_names.includes("入户花园") || (door.room_names.length == 1 && door.room_names.includes("客餐厅"))));
    let livingSubPolyInfo: any[] = [];
    let kitchenSubPolyInfo: any[] = [];
    // 引入面积评分，面积评分这个反而是比较靠后的， 
    twoSpacePolyInfo.sort((firstSpaceInfo: any, secondSpaceInfo: any) => secondSpaceInfo.subMainRect.area - firstSpaceInfo.subMainRect.area);
    for(let index = 0; index < twoSpacePolyInfo.length; ++index)
    {
        let spacePolyInfo: any = twoSpacePolyInfo[index];
        let livingScore: number = 100;
        let kitchenScore: number = 100;
        // livingScore += (twoSpacePolyInfo.length - index);
        // kitchenScore += index;
        if(isLayonWindows(spacePolyInfo.subPoly, blaconyDoors).isLayon)
        {
            livingScore += 1;
            kitchenScore -= 1;
        }
        if(isLayonWindows(spacePolyInfo.subMainRect, [...kitchenDoors, ...kitchenWindows], false).isLayon)
        {
            livingScore -= 1;
            kitchenScore += 1;
        }
        // if(isLayonWindows(spacePolyInfo.subPoly, entranceDoors).isLayon)
        // {
        //     livingScore -= 1;
        //     kitchenScore -= 1;
        // }
        if(livingScore >= kitchenScore)
        {
            let livingScoreInfo: any = spacePolyInfo;
            livingScoreInfo.livingScore = livingScore;
            livingSubPolyInfo.push(livingScoreInfo);
        } 
        else
        {
            let kitchenScoreInfo: any = spacePolyInfo;
            kitchenScoreInfo.kitchenScore = kitchenScore;
            kitchenSubPolyInfo.push(kitchenScoreInfo);
        }
    }
    if(livingSubPolyInfo.length == 2 && livingSubPolyInfo[0].livingScore == livingSubPolyInfo[1].livingScore && params.hallwayPathEdges)
    {
        let isSucessUseHallwayPath: boolean = false;
        for(let hallwayIndex = 0; hallwayIndex < params.hallwayPathEdges.length; ++hallwayIndex)
        {
            if(!params.hallwayPathEdges[hallwayIndex] || params.hallwayPathEdges[hallwayIndex].length < 10)
            {
                continue;
            }
            let minProjectInfo1: any = getMinProjectOnHallwayInfo(livingSubPolyInfo[0].subMainRect, params.hallwayPathEdges[hallwayIndex]);
            if(!minProjectInfo1)
            {
                continue;
            }
            let minProjectInfo2: any = getMinProjectOnHallwayInfo(livingSubPolyInfo[1].subMainRect, params.hallwayPathEdges[hallwayIndex]);
            if(!minProjectInfo2)
            {
                continue;
            }
            let isPositive1: number = minProjectInfo1.y > 0 ? 1 : -1;
            let isPositive2: number = minProjectInfo2.y > 0 ? 1 : -1;
            let isSameSide: boolean = isPositive1 == isPositive2;
            if(!isSameSide)
            {
                break;
            }
            if(Math.abs(minProjectInfo1.y - minProjectInfo2.y) > 100)
            {
                continue;
            }
            if(Math.abs(minProjectInfo1.x) > Math.abs(minProjectInfo2.x))
            {
                isSucessUseHallwayPath = true;
                livingSubPolyInfo[0].livingScore += 10;
            }
            else if(Math.abs(minProjectInfo1.x) < Math.abs(minProjectInfo2.x))
            {
                isSucessUseHallwayPath = true;
                livingSubPolyInfo[1].livingScore += 10;
            }
        }
        if(!isSucessUseHallwayPath)
        {
            livingSubPolyInfo[0].livingScore += 2;
            livingSubPolyInfo[1].livingScore += 1;

        }
    }
    livingSubPolyInfo.sort((a, b) => {
        return b.livingScore - a.livingScore;
    });
    if(livingSubPolyInfo.length > 0)
    {
        return livingSubPolyInfo[0];
    }
    if(livingSubPolyInfo.length == 0 && kitchenSubPolyInfo.length > 0)
    {
        let maxKitchenSubPoly: any = null;
        let maxKitchenScore: number = null;
        for(let kitchenSubPolyItem of kitchenSubPolyInfo)
        {
            if(maxKitchenScore == null || maxKitchenScore < kitchenSubPolyItem.kitchenScore)
            {
                maxKitchenSubPoly = kitchenSubPolyItem;
                maxKitchenScore = kitchenSubPolyItem.kitchenScore;
            }
        }
        return twoSpacePolyInfo.find(spaceInfo => spaceInfo != maxKitchenSubPoly);
    }
    return null;
}

function getMinProjectOnHallwayInfo(rect: ZRect, hallwayEdge: ZEdge)
{
    let minX: number = null;
    let minY: number = null;
    let absMinX: number = null;
    let absMinY: number = null;
    for(let rectVertex of rect.vertices)
    {
        let projectInfo: any = hallwayEdge.projectEdge2d({x: rectVertex.pos.x, y: rectVertex.pos.y});
        if(absMinX === null || absMinX > Math.abs(projectInfo.x))
        {
            absMinX = Math.abs(projectInfo.x);
            minX = projectInfo.x;
        }
        if(absMinY === null || absMinY > Math.abs(projectInfo.y))
        {
            absMinY = Math.abs(projectInfo.y);
            minY = projectInfo.y;
        }
    }
    if(minX === null || minY === null)
    {
        return null;
    }
    return {x: minX, y: minY};
}

function isLayonWindows(subPoly: ZPolygon, windows: I_Window[], isExpandDoor: boolean = true): {isLayon: boolean, layonEdge: ZEdge}
{
    for(let subPolyEdge of subPoly.edges)
    {
        for(let window of windows)
        {
            // TODO 这里的window只限定前后边
            if(subPolyEdge.islayOn(window.rect.frontEdge, TTinyLivingRoomByPathRelation.onWallTol, 0.2) || 
                subPolyEdge.islayOn(window.rect.backEdge, TTinyLivingRoomByPathRelation.onWallTol, 0.2))
            {
                return {isLayon: true, layonEdge: subPolyEdge};
            }
        }
    }
    // 将门深度进行延申
    if(!isExpandDoor)
    {
        return {isLayon: false, layonEdge: null};
    }
    for(let subPolyEdge of subPoly.edges)
    {
        for(let window of windows)
        {
            if(window.type == "Door" && window.realType == "SingleDoor")
            {
                let windowRect: ZRect = window.rect.clone();
                let oldWindowRectCenter: Vector3 = windowRect.rect_center.clone();
                windowRect.depth += (2 * TTinyLivingRoomByPathRelation.hallwayDist);
                windowRect.rect_center = oldWindowRectCenter.clone();
                windowRect.updateRect();
                if(TBaseRoomToolUtil.instance.edgeOnPolygon(subPolyEdge, windowRect, TTinyLivingRoomByPathRelation.onWallTol, 0.001))
                {
                    return {isLayon: true, layonEdge: subPolyEdge};
                }
            }
        }

    }
    return {isLayon: false, layonEdge: null};
}

function layouSubSpaceAreaByLivingArea(livingPolyInfo: {subPoly: ZPolygon, subMainRect: ZRect}, params: I_TinyLivingRoomByPathParams): I_TinyLivingRoomByPathParams[]
{
    let livingAreaRect: ZRect = livingPolyInfo.subMainRect;
    let layonSpaceDoorOrWindows: I_Window[] = getLayonSpaceDoorOrWindows(livingPolyInfo.subPoly, params);
    let layonRoomEdges: ZEdge[] = getLayonCommonEdges(livingAreaRect, params._room); 
    let layonBlaconyDoors: I_Window[] = layonSpaceDoorOrWindows.filter(doorOrWindow => doorOrWindow.type == "Door" && doorOrWindow.room_names.includes("阳台"));
    let candidateTvCabinets: ZRect[] = params.allCabinetRects.filter(cabinetRect => cabinetRect.length >= TTinyLivingRoomByPathRelation.minTvCabinetLen);
    let layoutLivingAreaResults: I_TinyLivingRoomByPathParams[] = 
        getLayoutLivingAreaResults(livingAreaRect, candidateTvCabinets, layonBlaconyDoors, layonRoomEdges, layonSpaceDoorOrWindows, params._room.room_shape._poly);
    // 客厅区后处理
    postProcessLayoutLivingAreaResults(layoutLivingAreaResults, params);
    if(layoutLivingAreaResults.length == 0)
    {
        return null;
    }
    return layoutLivingAreaResults;
}

function getLayonSpaceDoorOrWindows(spaceArea: ZPolygon, params: I_TinyLivingRoomByPathParams): I_Window[]
{
    let doorOrWindows: I_Window[] = [...params.doors, ...params.windows];
    let layonDoorOrWindows: I_Window[] = [];
    for(let doorOrWindow of doorOrWindows)
    {
        if(TBaseRoomToolUtil.instance.isLayOnPolygons(spaceArea, doorOrWindow.rect, null, 0.1, TTinyLivingRoomByPathRelation.onWallTol))
        {
            layonDoorOrWindows.push(doorOrWindow);
        }
    }
    return layonDoorOrWindows;
}

function getLayonCommonEdges(spaceRect:ZRect, room: TRoom): ZEdge[]
{
    let layonRoomEdges: ZEdge[] = [];
    for(let spaceAreaEdge of spaceRect.edges)
    {
        for(let roomEdge of room.room_shape._feature_shape._w_poly.edges)
        {
            let dot: number = spaceAreaEdge.nor.clone().dot(roomEdge.nor);
            if(dot < 0.9)
            {
                continue;
            }
            let commonLayonEdge: ZEdge = roomEdge.computeLayOnEdge(spaceAreaEdge, TTinyLivingRoomByPathRelation.onWallTol);
            if(!commonLayonEdge)
            {
                continue;
            }
            layonRoomEdges.push(commonLayonEdge);
        }
    }
    return layonRoomEdges;
}

function getLayoutLivingAreaResults(
    livingAreaRect: ZRect, candidateTvCabinets: ZRect[], layonBlaconyDoors: I_Window[], 
    layonRoomEdges: ZEdge[], layonSpaceDoorOrWindows: I_Window[], roomPoly: ZPolygon): I_TinyLivingRoomByPathParams[]
{
    let targetResults: I_TinyLivingRoomByPathParams[] = [];
    let tvCabinetRects: ZRect[] = [];
    for(let tvCabinet of candidateTvCabinets)
    {
        if(!TBaseRoomToolUtil.instance.isOverlayByRects(livingAreaRect, tvCabinet))
        {
            continue;
        }
        // let isVerticalBlaconyDoor: boolean = false;
        // for(let blaconyDoor of layonBlaconyDoors)
        // {
        //     let dot: number = Math.abs(blaconyDoor.rect.nor.clone().dot(tvCabinet.nor));
        //     if(dot < 0.9)
        //     {
        //         isVerticalBlaconyDoor = true;
        //         break;
        //     }
        // }
        // if(!isVerticalBlaconyDoor)
        // {
        //     continue;
        // }
        for(let livingAreaEdge of livingAreaRect.edges)
        {
            let l_res: { layon_len?: number, ll?: number, rr?: number } = {};
            livingAreaEdge.islayOn(tvCabinet.backEdge, TTinyLivingRoomByPathRelation.onWallTol, 0.1, l_res);
            if(l_res.layon_len < TTinyLivingRoomByPathRelation.minTvCabinetLen)
            {
                continue;
            }
            else if(l_res.layon_len > TTinyLivingRoomByPathRelation.maxTvCabinetLen)
            {
                l_res.layon_len = TTinyLivingRoomByPathRelation.maxTvCabinetLen;
                let resCenter: number = (l_res.ll + l_res.rr) / 2.0;
                l_res.ll = resCenter - l_res.layon_len / 2.0;
                l_res.rr = resCenter + l_res.layon_len / 2.0;
            }
            let layonStartPoint: Vector3 = tvCabinet.backEdge.unprojectEdge2d({x: l_res.ll, y: 0});
            let layonEndPoint: Vector3 = tvCabinet.backEdge.unprojectEdge2d({x: l_res.rr, y: 0});
            let backCenter: Vector3 = layonStartPoint.clone().add(layonEndPoint).multiplyScalar(0.5);
            let newTvCabinetRect: ZRect = new ZRect(l_res.layon_len, tvCabinet.depth);
            newTvCabinetRect.nor = tvCabinet.nor.clone();
            newTvCabinetRect.back_center = backCenter;
            newTvCabinetRect.updateRect();
            tvCabinetRects.push(newTvCabinetRect);
        }
    }
    if(livingAreaRect.min_hh < TTinyLivingRoomByPathRelation.minLivingAreaMinLength)
    {
        return targetResults;
    }
    if(tvCabinetRects.length == 0)
    {
        let layoutSofaGroupInfos: I_TinyLivingRoomByPathParams[] = calLayoutSofaGroupWithoutTvCabinetInfo(livingAreaRect, layonRoomEdges, layonBlaconyDoors, layonSpaceDoorOrWindows);
        targetResults.push(...layoutSofaGroupInfos);
    }
    else
    {
        for(let tvCabinetRect of tvCabinetRects)
        {
            // 直接获取电视柜前方的边，并检查获取此边与所靠墙的距离，如果距离在范围内，则沙发可靠墙，否则沙发组合与电视柜保留一定的距离限制
            let cabinetFrontDistInfo: any = calCabinetFrontDist(tvCabinetRect, livingAreaRect, layonRoomEdges);
            if(cabinetFrontDistInfo == null)
            {
                continue;
            }
            if(cabinetFrontDistInfo.frontDist < TTinyLivingRoomByPathRelation.tvCabinetFrontMinLen)
            {
                // 直接放置沙发组合
                let layoutSofaGroupInfos: I_TinyLivingRoomByPathParams[] = calLayoutSofaGroupWithoutTvCabinetInfo(livingAreaRect, layonRoomEdges, layonBlaconyDoors, layonSpaceDoorOrWindows);
                targetResults.push(...layoutSofaGroupInfos);
            }
            else if(cabinetFrontDistInfo.frontDist > TTinyLivingRoomByPathRelation.tvCabinetFrontToWallMaxLen)
            {
                // 沙发组合不靠墙放置, 设定在电视柜前方一定的距离设置一定大小的矩形区域
                let layoutSofaGroupInfo: any = calLayoutSofaGroupWithoutWallInfo(tvCabinetRect, livingAreaRect);
                let result: I_TinyLivingRoomByPathParams = {
                    tv_cabinet_rect: layoutSofaGroupInfo.tvCabinetRect,
                    sofa_group_rect: layoutSofaGroupInfo.sofaGroupRect,
                    sofa_carpet_rect: layoutSofaGroupInfo.sofaCarpetRect,
                };
                targetResults.push(result);
            }
            else
            {
                // TODO 沙发组合靠墙，按照最佳的情况进行放置, 这个同样需要对其他情况筛选出沙发背靠最大墙的情况
                let sofaBackEdge: ZEdge = getValidSofaBackEdge(cabinetFrontDistInfo.frontEdge, layonSpaceDoorOrWindows, tvCabinetRect);
                if(sofaBackEdge.length < TTinyLivingRoomByPathRelation.minLivingAreaMinLength)
                {
                    sofaBackEdge = cabinetFrontDistInfo.frontEdge;
                }
                let layoutSofaGroupInfos: any[] = calLayoutSofaGroupInfos(sofaBackEdge, tvCabinetRect, livingAreaRect, layonRoomEdges, layonSpaceDoorOrWindows, roomPoly);
                for(let layoutSofaGroupInfo of layoutSofaGroupInfos)
                {
                    let result: I_TinyLivingRoomByPathParams = {
                        tv_cabinet_rect: layoutSofaGroupInfo.tvCabinetRect,
                        sofa_group_rect: layoutSofaGroupInfo.sofaGroupRect,
                        sofa_carpet_rect: layoutSofaGroupInfo.sofaCarpetRect,
                        sofa_background_wall_rect: layoutSofaGroupInfo.sofaBackGroundRect,
                    };
                    targetResults.push(result);
                }
            }
        }
    }
    return targetResults;
}

function getExpandDoorRects(doors: I_Window[], poly: ZPolygon): ZRect[]
{
    let expandDoorRects: ZRect[] = [];
    for(let door of doors)
    {
        let doorTempRect: ZRect = door.rect.clone();
        if(door.type != "Door")
        {
            continue;
        }
        if(door.realType == "SlidingDoor")
        {
            doorTempRect.depth += TTinyLivingRoomByPathRelation.hallwayDist / 2;
        }
        else if(door.realType == "SingleDoor")
        {
            // 单开门的需要看门朝向，当门朝向与所靠墙的方向一致时，则加半个过道距离即可，如果不一致则深度为门的长度
            let isSameNorLayonRoomEdge: boolean = calIsSameNorLayonRoomEdge(door, poly);
            if(!isSameNorLayonRoomEdge)
            {
                doorTempRect.depth += TTinyLivingRoomByPathRelation.hallwayDist;
            }
            else
            {
                doorTempRect.depth += door.rect.length * 2;
            }
        }
        doorTempRect.rect_center = door.rect.rect_center.clone();
        doorTempRect.updateRect();
        expandDoorRects.push(doorTempRect);
    }
    return expandDoorRects;
}

function calLayoutSofaGroupWithoutTvCabinetInfo(livingAreaRect: ZRect, layonRoomEdges: ZEdge[], layonBlaconyDoors: I_Window[], layonSpaceDoorOrWindows: I_Window[]): I_TinyLivingRoomByPathParams[]
{
    let layoutResults: I_TinyLivingRoomByPathParams[] = [];
    // 无电视柜时，沙发组合的摆放
    let layonDoors: I_Window[] = layonSpaceDoorOrWindows.filter(window => window.type == "Door");
    let layonWindows: I_Window[] = layonSpaceDoorOrWindows.filter(window => window.type == "Window");
    if(layonBlaconyDoors.length == 0)
    {
        // 无阳台门的情况下，则看区域那面是靠墙的，则沙发组合放置在靠墙的那一边
        let candidateSofaBackEdges: ZEdge[] = [];
        for(let livingAreaEdge of livingAreaRect.edges)
        {
            let isLayonDoor: boolean = isEdgeLayonDoorOrWindow(livingAreaEdge, layonDoors);
            if(isLayonDoor)
            {
                continue;
            }
            for(let layonRoomEdge of layonRoomEdges)
            {
                if(livingAreaEdge.nor.clone().dot(layonRoomEdge.nor) < 0.9)
                {
                    continue;
                }
                let commonLayonEdge: ZEdge = livingAreaEdge.computeLayOnEdge(layonRoomEdge, TTinyLivingRoomByPathRelation.backgroundWallDepth, 0.1);
                if(!commonLayonEdge)
                {
                    continue;
                }
                candidateSofaBackEdges.push(commonLayonEdge);
            }
        }
        for(let sofaBackEdge of candidateSofaBackEdges)
        {
            let sofaGroupNor: Vector3 = sofaBackEdge.nor.clone().negate();
            if(sofaBackEdge.length < TTinyLivingRoomByPathRelation.minLivingAreaMinLength)
            {
                continue;
            }
            // 检查
            let isLayonWindow: boolean = isEdgeLayonDoorOrWindow(sofaBackEdge, layonWindows);
            let dot: number = Math.abs(sofaGroupNor.clone().dot(livingAreaRect.nor));
            let sofaGroupDepth: number = dot > 0.9 ? livingAreaRect.depth : livingAreaRect.length;
            let sofaGroupLen: number = sofaBackEdge.length;
            let sofaGroupBackCenter: Vector3 = sofaBackEdge.center.clone();
            let sofaBackGroundRect: ZRect = null;
            if(!isLayonWindow)
            {
                sofaBackGroundRect = new ZRect(sofaGroupLen, TTinyLivingRoomByPathRelation.backgroundWallDepth);
                sofaBackGroundRect.nor = sofaGroupNor.clone();
                sofaBackGroundRect.back_center = sofaGroupBackCenter.clone();
                sofaBackGroundRect.updateRect();

                sofaGroupDepth -= TTinyLivingRoomByPathRelation.backgroundWallDepth;
                sofaGroupBackCenter = sofaGroupBackCenter.clone().add(sofaGroupNor.clone().multiplyScalar(TTinyLivingRoomByPathRelation.backgroundWallDepth));
            }
            let sofaGroupRect: ZRect = new ZRect(sofaGroupLen, sofaGroupDepth);
            sofaGroupRect.nor = sofaGroupNor.clone();
            sofaGroupRect.back_center = sofaGroupBackCenter;
            sofaGroupRect.updateRect();
            let result: I_TinyLivingRoomByPathParams = {
                sofa_background_wall_rect: sofaBackGroundRect,
                sofa_group_rect: sofaGroupRect,
                sofa_carpet_rect: sofaGroupRect.clone(),
            };
            layoutResults.push(result);
        }
    }
    else
    {
        // 存在阳台门的情况下，一般我们的沙发布置是面向阳台
        let livingAreaCenter: Vector3 = livingAreaRect.rect_center.clone();
        for(let blaconyDoor of layonBlaconyDoors)
        {
            let sofaGroupNor: Vector3 = blaconyDoor.rect.nor.clone();
            let blaconyDoorCenter: Vector3 = blaconyDoor.rect.rect_center.clone();
            let subVector: Vector3 = blaconyDoorCenter.clone().sub(livingAreaCenter);
            let dot: number = subVector.clone().dot(sofaGroupNor);
            if(dot < 0)
            {
                sofaGroupNor.negate();
            }
            let sofaBackEdge: ZEdge = livingAreaRect.edges.find(edge => edge.nor.clone().dot(sofaGroupNor) < -0.9);
            // TODO 这个还需要记录与阳台门的投影部分，如果投影部分过大或者过小都需要进行限制， 主要是以阳台门的中心的向两边进行扩散
            sofaBackEdge = calProjectBalconyDoorEdge(sofaBackEdge, blaconyDoor);
            let isHasSofaBackGround: boolean = false;
            for(let layonRoomEdge of layonRoomEdges)
            {
                if(sofaBackEdge.nor.clone().dot(layonRoomEdge.nor) < 0.9)
                {
                    continue;
                }
                let commonLayonEdge: ZEdge = sofaBackEdge.computeLayOnEdge(layonRoomEdge, TTinyLivingRoomByPathRelation.backgroundWallDepth, 0.1);
                if(!commonLayonEdge || commonLayonEdge.length < TTinyLivingRoomByPathRelation.minLivingAreaMinLength)
                {
                    continue;
                }
                if(!isEdgeLayonDoorOrWindow(sofaBackEdge, layonWindows))
                {
                    isHasSofaBackGround = true;
                }
                break;
            }
            let livingAreaDot: number = Math.abs(livingAreaRect.nor.clone().dot(sofaGroupNor));
            let sofaGroupLen: number = sofaBackEdge.length;
            let sofaGroupDepth: number = livingAreaDot > 0.9 ? livingAreaRect.depth : livingAreaRect.length;
            let sofaGroupBackCenter: Vector3 = sofaBackEdge.center.clone();
            let sofaBackGroundRect: ZRect = null;
            if(isHasSofaBackGround)
            {
                sofaBackGroundRect = new ZRect(sofaGroupLen, TTinyLivingRoomByPathRelation.backgroundWallDepth);
                sofaBackGroundRect.nor = sofaGroupNor.clone();
                sofaBackGroundRect.back_center = sofaGroupBackCenter.clone();
                sofaBackGroundRect.updateRect();

                sofaGroupDepth -= TTinyLivingRoomByPathRelation.backgroundWallDepth;
                sofaGroupBackCenter = sofaGroupBackCenter.clone().add(sofaGroupNor.clone().multiplyScalar(TTinyLivingRoomByPathRelation.backgroundWallDepth));
            }
            let sofaGroupRect: ZRect = new ZRect(sofaGroupLen, sofaGroupDepth);
            sofaGroupRect.nor = sofaGroupNor.clone();
            sofaGroupRect.back_center = sofaGroupBackCenter;
            sofaGroupRect.updateRect();
            let result: I_TinyLivingRoomByPathParams = {
                sofa_background_wall_rect: sofaBackGroundRect,
                sofa_group_rect: sofaGroupRect,
                sofa_carpet_rect: sofaGroupRect.clone(),
            };
            layoutResults.push(result);
        }
    }
    return layoutResults;
}

function calCabinetFrontDist(tvCabinetRect: ZRect, livingAreaRect: ZRect, layonRoomEdges: ZEdge[]): any
{
    let frontLivingAreaEdge: ZEdge = null;
    for(let livingAreaEdge of livingAreaRect.edges)
    {
        let dot: number = tvCabinetRect.frontEdge.nor.clone().dot(livingAreaEdge.nor);
        if(dot > 0.9 && TBaseRoomToolUtil.instance.edgeIsInFrontArea(tvCabinetRect.frontEdge, tvCabinetRect.backEdge, livingAreaEdge))
        {
            frontLivingAreaEdge = livingAreaEdge;
        }
    }
    let frontRoomEdge: ZEdge = null;
    for(let roomEdge of layonRoomEdges)
    {
        let dot: number = tvCabinetRect.frontEdge.nor.clone().dot(roomEdge.nor);
        if(dot > 0.9 && TBaseRoomToolUtil.instance.edgeIsInFrontArea(tvCabinetRect.frontEdge, tvCabinetRect.backEdge, roomEdge))
        {
            frontRoomEdge = roomEdge;
        }
    }
    let targetFrontEdge: ZEdge = null;
    let minDist: number = null;
    for(let frontEdge of [frontLivingAreaEdge, frontRoomEdge])
    {
        if(frontEdge == null)
        {
            continue;
        }
        let dist: number = TBaseRoomToolUtil.instance.calDistance(frontLivingAreaEdge, tvCabinetRect.frontEdge);
        if(targetFrontEdge == null || minDist > dist)
        {
            targetFrontEdge = frontEdge;
            minDist = dist;
        }
    }
    if(targetFrontEdge == null)
    {
        return null;
    }
    return {frontEdge: targetFrontEdge, frontDist: minDist};
}

function calLayoutSofaGroupWithoutWallInfo(tvCabinetRect: ZRect, livingAreaRect: ZRect): any
{
    let sofaGroupNor: Vector3 = tvCabinetRect.nor.clone().negate();
    let sofaGroupLen: number = tvCabinetRect.length;
    let sofaGroupDepth: number = TTinyLivingRoomByPathRelation.tvCabinetToSofaGroupBackDist;
    let sofaGroupBackCenter: Vector3 = tvCabinetRect.frontEdge.center.clone().add(tvCabinetRect.nor.clone().multiplyScalar(sofaGroupDepth));
    sofaGroupDepth -= TTinyLivingRoomByPathRelation.tvCabinetFrontBufferDistance;
    let sofaGroupRect: ZRect = new ZRect(sofaGroupLen, sofaGroupDepth);
    sofaGroupRect.nor = sofaGroupNor.clone();
    sofaGroupRect.back_center = sofaGroupBackCenter;
    sofaGroupRect.updateRect();
    let sofaCarpetRect: ZRect = sofaGroupRect.clone();
    return {
        sofaGroupRect: sofaGroupRect,
        sofaCarpetRect: sofaCarpetRect,
        tvCabinetRect: tvCabinetRect,
    }
}

function calLayoutSofaGroupInfos(
    sofaBackEdge: ZEdge, tvCabinetRect: ZRect, livingAreaRect: ZRect, layonRoomEdges: ZEdge[],
    layonSpaceDoorOrWindows: I_Window[], roomPoly: ZPolygon, sofaGroupExtendLenTol: number = 1500): any
{
    // 需要抽出来一个接口， 方便后续进行多种布局解
    let layoutLivingResults: any[] = [];
    let layoutLivingResult: any = getLayoutLivingSofaGroupInTvCabinetInfo(sofaBackEdge, tvCabinetRect, livingAreaRect, layonRoomEdges, layonSpaceDoorOrWindows);
    if(layoutLivingResult)
    {
        layoutLivingResults.push(layoutLivingResult);
    }
    let isLayonRoomEdgeHasOutSideCorner: boolean = isOutSideLayonRoomEdgeBySofaBackEdge(sofaBackEdge, roomPoly);
    let subLen: number = sofaBackEdge.length - tvCabinetRect.length;
    if(subLen < sofaGroupExtendLenTol && subLen > 10 && layoutLivingResult && isLayonRoomEdgeHasOutSideCorner)
    {
        let  otherLayoutLivingResult: any = getOtherLayoutLivingSofaGroupByModifyLen(sofaBackEdge, layoutLivingResult);
        if(otherLayoutLivingResult)
        {
            layoutLivingResults.push(otherLayoutLivingResult);
        }
    }
    return layoutLivingResults;
}

function getLayoutLivingSofaGroupInTvCabinetInfo(sofaBackEdge: ZEdge, tvCabinetRect: ZRect, livingAreaRect: ZRect, layonRoomEdges: ZEdge[], layonSpaceDoorOrWindows: I_Window[]): any
{
    let sofaGroupNor: Vector3 = sofaBackEdge.nor.clone().negate();
    let sofaGroupLayonRoomEdges: ZEdge[] = layonRoomEdges.filter(edge => edge.nor.clone().dot(sofaBackEdge.nor) > 0.9 && edge.islayOn(sofaBackEdge, TTinyLivingRoomByPathRelation.backgroundWallDepth, 0.1, null));
    let hasSofaBackGroud: boolean = sofaGroupLayonRoomEdges.length == 1;
    // 电视柜前方放置沙发组合,沙发组合放置在电视柜正前方
    let tvCabinetCenter: Vector3 = tvCabinetRect.rect_center.clone();
    let projectInfo: any = sofaBackEdge.projectEdge2d({x: tvCabinetCenter.x, y: tvCabinetCenter.y});
    let sofaBackGroupCenter: Vector3 = tvCabinetRect.length > sofaBackEdge.length ? sofaBackEdge.center.clone() : sofaBackEdge.unprojectEdge2d({x: projectInfo.x, y: 0});
    let sofaGroupLen: number = tvCabinetRect.length > sofaBackEdge.length ? sofaBackEdge.length : tvCabinetRect.length;
    let sofaGroupDepth: number = Math.abs(sofaGroupNor.clone().dot(livingAreaRect.nor)) > 0.9 ? livingAreaRect.depth : livingAreaRect.length;
    sofaGroupDepth -= (TTinyLivingRoomByPathRelation.cabinetDepth + TTinyLivingRoomByPathRelation.tvCabinetFrontBufferDistance);
    let sofaBackGroundRect: ZRect = null;
    if(hasSofaBackGroud)
    {
        sofaBackGroundRect = new ZRect(sofaGroupLen, TTinyLivingRoomByPathRelation.backgroundWallDepth);
        sofaBackGroundRect.nor = sofaGroupNor.clone();
        sofaBackGroundRect.back_center = sofaBackGroupCenter;
        sofaBackGroundRect.updateRect();
        // TODO 检查沙发背景墙是靠着窗，如果靠着窗则不存在背景墙
        let layonWindows: I_Window[] = layonSpaceDoorOrWindows.filter(doorOrWindow => doorOrWindow.type == "Window");
        let isLayonWindow: boolean = isEdgeLayonWindow(sofaBackGroundRect.backEdge, layonWindows);
        if(isLayonWindow)
        {
            sofaBackGroundRect = null;
        }
        else
        {
            sofaBackGroupCenter = sofaBackGroupCenter.clone().add(sofaGroupNor.clone().multiplyScalar(TTinyLivingRoomByPathRelation.backgroundWallDepth));
            sofaGroupDepth -= TTinyLivingRoomByPathRelation.backgroundWallDepth;
        }
    }
    // 设置放置沙发组合的深度限制
    let isNeedDeleteTvCabinet: boolean = false;
    if(sofaGroupDepth < TTinyLivingRoomByPathRelation.minLivingAreaMinLength)
    {
        // 剔除掉电视柜，直接放置沙发组合
        sofaGroupDepth += (TTinyLivingRoomByPathRelation.cabinetDepth + TTinyLivingRoomByPathRelation.tvCabinetFrontBufferDistance);
        isNeedDeleteTvCabinet = true;
    }
    let sofaGroupRect: ZRect = new ZRect(sofaGroupLen, sofaGroupDepth);
    sofaGroupRect.nor = sofaGroupNor.clone();
    sofaGroupRect.back_center = sofaBackGroupCenter;
    sofaGroupRect.updateRect();
    let sofaCarpetRect: ZRect = sofaGroupRect.clone();
    tvCabinetRect = isNeedDeleteTvCabinet? null : tvCabinetRect;
    return {
        sofaGroupRect: sofaGroupRect,
        sofaCarpetRect: sofaCarpetRect,
        sofaBackGroundRect: sofaBackGroundRect,
        tvCabinetRect: tvCabinetRect
    };
}

function getOtherLayoutLivingSofaGroupByModifyLen(sofaBackEdge: ZEdge, layoutLivingResult: any): any
{
    let otherLayoutLivingResult: any = {};
    let modifyRectLenToNewRect = (sofaBackEdge: ZEdge, sourceRect: ZRect) =>
    {
        let sofaBackLen: number = sofaBackEdge.length;
        let sofaBackCenter: Vector3 = sofaBackEdge.center;
        let xDot: number = Math.abs(sofaBackEdge.nor.clone().dot(xDir));
        let yDot: number = Math.abs(sofaBackEdge.nor.clone().dot(yDir));
        let otherRect: ZRect = sourceRect.clone();
        let oldOtherRectCenter: Vector3 = otherRect.rect_center.clone();
        otherRect.length = sofaBackLen;
        if(xDot > 0.9)
        {
            otherRect.rect_center = new Vector3(oldOtherRectCenter.x, sofaBackCenter.y, 0);
        }
        if(yDot > 0.9)
        {
            otherRect.rect_center = new Vector3(sofaBackCenter.x, oldOtherRectCenter.y, 0);
        }
        return otherRect;
    };
    if(layoutLivingResult.sofaGroupRect)
    {
        let otherSofaGroupRect: ZRect = modifyRectLenToNewRect(sofaBackEdge, layoutLivingResult.sofaGroupRect);
        otherLayoutLivingResult.sofaGroupRect = otherSofaGroupRect;
    }
    if(layoutLivingResult.sofaCarpetRect)
    {
        let otherSofaCarpetRect: ZRect = modifyRectLenToNewRect(sofaBackEdge, layoutLivingResult.sofaCarpetRect);
        otherLayoutLivingResult.sofaCarpetRect = otherSofaCarpetRect;
    }
    if(layoutLivingResult.sofaBackGroundRect)
    {
        let otherSofaBackGroundRect: ZRect = modifyRectLenToNewRect(sofaBackEdge, layoutLivingResult.sofaBackGroundRect);
        otherLayoutLivingResult.sofaBackGroundRect = otherSofaBackGroundRect;
    }
    if(layoutLivingResult.tvCabinetRect)
    {
        otherLayoutLivingResult.tvCabinetRect = layoutLivingResult.tvCabinetRect;
    }
    return otherLayoutLivingResult;
}

function isEdgeLayonWindow(edge: ZEdge, windows: I_Window[]): boolean
{
    for(let window of windows)
    {
        let dot: number = Math.abs(edge.nor.clone().dot(window.nor));
        if(dot > 0.9 && isEdgeLayonDoorOrWindow(edge, [window]))
        {
            return true;
        }
    }
    return false;
}

function isEdgeLayonDoorOrWindow(sourceEdge: ZEdge, doorOrWindows: I_Window[]): boolean
{
    // 防止门侧边影响，最好是直接获取门的前后边检查是否layon
    for(let doorOrWindow of doorOrWindows)
    {
        if(sourceEdge.islayOn(doorOrWindow.rect.frontEdge, TTinyLivingRoomByPathRelation.onWallTol, 0.5, null) || sourceEdge.islayOn(doorOrWindow.rect.backEdge, TTinyLivingRoomByPathRelation.onWallTol, 0.5, null))
        {
            return true;
        }
    }
    return false;
}

function calProjectBalconyDoorEdge(sofaBackEdge: ZEdge, blaconyDoor: I_Window): ZEdge
{
    let projectSofaBackEdge: ZEdge = null;
    let blaconyFrontEdge: ZEdge = blaconyDoor.rect.frontEdge;
    let blaconyCenter: Vector3 = blaconyDoor.rect.rect_center.clone();
    let projectv0: any = sofaBackEdge.projectEdge2d({x: blaconyFrontEdge.v0.pos.x, y: blaconyFrontEdge.v0.pos.y});
    let projectv1: any = sofaBackEdge.projectEdge2d({x: blaconyFrontEdge.v1.pos.x, y: blaconyFrontEdge.v1.pos.y});
    let projectCenter: any = sofaBackEdge.projectEdge2d({x: blaconyCenter.x, y: blaconyCenter.y});
    if(projectv0.x < 0)
    {
        projectv0.x = 0
    }
    else if(projectv0.x > sofaBackEdge.length)
    {
        projectv0.x = sofaBackEdge.length;
    }
    if(projectv1.x < 0)
    {
        projectv1.x = 0
    }
    else if(projectv1.x > sofaBackEdge.length)
    {
        projectv1.x = sofaBackEdge.length;
    }
    if(projectCenter.x < 0)
    {
        projectCenter.x = 0
    }
    else if(projectCenter.x > sofaBackEdge.length)
    {
        projectCenter.x = sofaBackEdge.length;
    }
    let projectLen: number = Math.abs(projectv0.x - projectv1.x);
    if(projectLen < TTinyLivingRoomByPathRelation.minLivingAreaMinLength)
    {
        let sofaBackCenetrVal: number = sofaBackEdge.length / 2;
        projectv0.x = sofaBackCenetrVal - TTinyLivingRoomByPathRelation.minLivingAreaMinLength / 2;
        projectv1.x = sofaBackCenetrVal + TTinyLivingRoomByPathRelation.minLivingAreaMinLength / 2;
        if(projectv0.x < 0)
        {
            projectv0.x = 0;
        }
        else if(projectv0.x > sofaBackEdge.length)
        {
            projectv0.x = sofaBackEdge.length;
        }

        if(projectv1.x < 0)
        {
            projectv1.x = 0;
        }
        else if(projectv1.x > sofaBackEdge.length)
        {
            projectv1.x = sofaBackEdge.length;
        }
    }
    else if(projectLen > TTinyLivingRoomByPathRelation.maxTvCabinetLen)
    {
        projectv0.x = projectCenter.x - TTinyLivingRoomByPathRelation.maxTvCabinetLen / 2;
        projectv1.x = projectCenter.x + TTinyLivingRoomByPathRelation.maxTvCabinetLen / 2;
    }

    let unprojectV0: Vector3 = sofaBackEdge.unprojectEdge2d({x: projectv0.x, y: 0});
    let unprojectV1: Vector3 = sofaBackEdge.unprojectEdge2d({x: projectv1.x, y: 0});
    if(projectv0.x > projectv1.x)
    {
        let tempUnproject: Vector3 = unprojectV0.clone();
        unprojectV0 = unprojectV1.clone();
        unprojectV1 = tempUnproject.clone();
    }

    projectSofaBackEdge = new ZEdge({pos: unprojectV0}, {pos: unprojectV1});
    projectSofaBackEdge.computeNormal();
    return projectSofaBackEdge;
}

function layouSubSpaceAreaByDiningArea(diningArea: ZRect, params: I_TinyLivingRoomByPathParams): any
{
    // 1. 优先确定餐边柜，寻找在餐厅区内的餐边柜
    let diningCabinetRects: ZRect[] = [];
    for(let diningCabinet of params.allCabinetRects)
    {
        if(!TBaseRoomToolUtil.instance.isOverlayByRects(diningArea, diningCabinet) || diningCabinet.length < TTinyLivingRoomByPathRelation.diningCabinetMinLen)
        {
            continue;
        }
        for(let diningAreaEdge of diningArea.edges)
        {
            let l_res: { layon_len?: number, ll?: number, rr?: number } = {};
            diningAreaEdge.islayOn(diningCabinet.backEdge, TTinyLivingRoomByPathRelation.onWallTol, 0.1, l_res);
            if(Object.keys(l_res).length == 0 || l_res.layon_len < TTinyLivingRoomByPathRelation.diningCabinetMinLen)
            {
                continue;
            }
            else if(l_res.layon_len > TTinyLivingRoomByPathRelation.diningCabinetMaxLen)
            {
                l_res.layon_len = TTinyLivingRoomByPathRelation.diningCabinetMaxLen;
                let resCenter: number = (l_res.ll + l_res.rr) / 2.0;
                l_res.ll = resCenter - l_res.layon_len / 2.0;
                l_res.rr = resCenter + l_res.layon_len / 2.0;
            }
            let layonStartPoint: Vector3 = diningCabinet.backEdge.unprojectEdge2d({x: l_res.ll, y: 0});
            let layonEndPoint: Vector3 = diningCabinet.backEdge.unprojectEdge2d({x: l_res.rr, y: 0});
            let backCenter: Vector3 = layonStartPoint.clone().add(layonEndPoint).multiplyScalar(0.5);
            let newTvCabinetRect: ZRect = new ZRect(l_res.layon_len, diningCabinet.depth);
            newTvCabinetRect.nor = diningCabinet.nor.clone();
            newTvCabinetRect.back_center = backCenter;
            newTvCabinetRect.updateRect();
            diningCabinetRects.push(newTvCabinetRect);
        }
    }
    let validPolys: ZPolygon[] = params.validAreas;
    let intersectDiningAreas: ZPolygon[] = diningArea.clone().intersect_polygons(validPolys);
    let maxDiningAreaRect: ZRect = null;
    let maxDiningArea: number = null;
    for(let intersectArea of intersectDiningAreas)
    {
        let intersectAreaRect: ZRect = TBaseRoomToolUtil.instance.getMaxInnerRectByPolygon(intersectArea);
        if(maxDiningArea == null || maxDiningArea < intersectAreaRect.area)
        {
            maxDiningArea = intersectAreaRect.area;
            maxDiningAreaRect = intersectAreaRect;
        }
    }
    if(maxDiningAreaRect)
    {
        diningArea = maxDiningAreaRect;
    }
    
    let layoutDiningAreaResults: I_TinyLivingRoomByPathParams[] = getLayoutDiningAreaResults(diningArea, diningCabinetRects, params);
    if(layoutDiningAreaResults.length == 0)
    {
        return null;
    }
    // 尽可能扩大餐桌区特别是沿着餐椅朝向的边，没有靠墙的一侧去侵占过道（只局限于原有的餐桌区特别小的情况下，解决这个的原因是因为经常出现单边餐椅不好看的情况下）
    // 所以想要扩到餐桌区的深度（如果允许扩大的情况下， 目前因为椅子是活动家具，所以不用考虑）
    
    return layoutDiningAreaResults;
}

function getLayoutDiningAreaResults(diningAreaRect: ZRect, candidateCabinets: ZRect[], params: I_TinyLivingRoomByPathParams): I_TinyLivingRoomByPathParams[]
{
    let layoutDiningResults: I_TinyLivingRoomByPathParams[] = [];
    for(let diningCabinet of candidateCabinets)
    {
        let expandDiningCabinetRect: ZRect = diningCabinet.clone();
        let oldExpandDiningCabinetRectCenter: Vector3 = expandDiningCabinetRect.rect_center.clone();
        expandDiningCabinetRect.depth += (TTinyLivingRoomByPathRelation.diningCabinetFrontBufferDistance * 2);
        expandDiningCabinetRect.rect_center = oldExpandDiningCabinetRectCenter;
        expandDiningCabinetRect.updateRect();
        let expandDiningCabinetRange: I_Range2D = TBaseRoomToolUtil.instance.getRange2dByPolygon(expandDiningCabinetRect);
        let diningCabinetNor: Vector3 = diningCabinet.nor.clone();
        let diningCabinetNorXDot: number = diningCabinetNor.dot(xDir);
        let diningCabinetNorYDot: number = diningCabinetNor.dot(yDir);
        let diningAreaRange: I_Range2D = TBaseRoomToolUtil.instance.getRange2dByPolygon(diningAreaRect);
        if(diningCabinetNorXDot > 0.9)
        {
            diningAreaRange.xMin = Math.max(expandDiningCabinetRange.xMax, diningAreaRange.xMin);
        }
        if(diningCabinetNorXDot < -0.9)
        {
            diningAreaRange.xMax = Math.min(expandDiningCabinetRange.xMin, diningAreaRange.xMax);
        }
        if(diningCabinetNorYDot > 0.9)
        {
            diningAreaRange.yMin = Math.max(expandDiningCabinetRange.yMax, diningAreaRange.yMin);
        }
        if(diningCabinetNorYDot < -0.9)
        {
            diningAreaRange.yMax = Math.min(expandDiningCabinetRange.yMin, diningAreaRange.yMax);
        }
        let currentDiningAreaRect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(diningAreaRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(diningAreaRange));
        let diningGroupRect: ZRect = null;
        let currentDiningCabinet: ZRect = null;
        if(currentDiningAreaRect.min_hh < Math.min(TTinyLivingRoomByPathRelation.diningGroupMinLen, TTinyLivingRoomByPathRelation.diningGroupMinDepth))
        {
            diningGroupRect = diningAreaRect.clone();
        }
        else
        {
            diningGroupRect = currentDiningAreaRect.clone();
            currentDiningCabinet = diningCabinet.clone();
        }
        let diningGroupRectNor: Vector3 = diningGroupRect.nor.clone();
        let tDiningGroupRectNor: Vector3 = new Vector3(diningGroupRectNor.y, diningGroupRectNor.x, 0);
        let diningGroupRectNors: Vector3[] = [diningGroupRectNor, tDiningGroupRectNor];
        let currentDiningGroups: ZRect[] = [];
        for(let i = 0; i < diningGroupRectNors.length; ++i)
        {
            let diningGroupCenter: Vector3 = diningGroupRect.rect_center.clone();
            let diningGroupLen: number =  diningGroupRect.length;
            let diningGroupDepth: number =  diningGroupRect.depth;
            if(i == 1)
            {
                let tempLen: number = diningGroupLen;
                diningGroupLen = diningGroupDepth;
                diningGroupDepth = tempLen;
            }
            let currentDiningGroupRect: ZRect = new ZRect(diningGroupLen, diningGroupDepth);
            currentDiningGroupRect.nor = diningGroupRectNors[i];
            currentDiningGroupRect.rect_center = diningGroupCenter.clone();
            currentDiningGroupRect.updateRect();
            currentDiningGroups.push(currentDiningGroupRect);
        }
        for(let currentDiningGroup of currentDiningGroups)
        {
            let result: I_TinyLivingRoomByPathParams = {
                dinning_cabinet_rect: currentDiningCabinet,
                dinning_table_rect: currentDiningGroup
            }
            layoutDiningResults.push(result);
        }
    }
    if(layoutDiningResults.length == 0)
    {
        let currentDiningGroups: ZRect[] = [];
        let diningGroupRectNor: Vector3 = diningAreaRect.nor.clone();
        let tDiningGroupRectNor: Vector3 = new Vector3(diningGroupRectNor.y, diningGroupRectNor.x, 0);
        let diningGroupRectNors: Vector3[] = [diningGroupRectNor, tDiningGroupRectNor];
        for(let i = 0; i < diningGroupRectNors.length; ++i)
        {
            let diningGroupCenter: Vector3 = diningAreaRect.rect_center.clone();
            let diningGroupLen: number =  diningAreaRect.length;
            let diningGroupDepth: number =  diningAreaRect.depth;
            if(i == 1)
            {
                let tempLen: number = diningGroupLen;
                diningGroupLen = diningGroupDepth;
                diningGroupDepth = tempLen;
            }
            let currentDiningGroupRect: ZRect = new ZRect(diningGroupLen, diningGroupDepth);
            currentDiningGroupRect.nor = diningGroupRectNors[i];
            currentDiningGroupRect.rect_center = diningGroupCenter.clone();
            currentDiningGroupRect.updateRect();
            currentDiningGroups.push(currentDiningGroupRect);
        }
        for(let currentDiningGroup of currentDiningGroups)
        {
            let result: I_TinyLivingRoomByPathParams = {
                dinning_table_rect: currentDiningGroup
            }
            layoutDiningResults.push(result);
        }
    }
    return layoutDiningResults;
}

function isOutSideCorner(edge1: ZEdge, edge2: ZEdge): boolean
{
    let dotValue: number = edge1.dv.clone().dot(edge2.nor);
    if(dotValue < -0.9)
    {
        return true;
    }
    return false;
}

function createSplitEdgeRect(edge: ZEdge, wPoly: ZPolygon, isStart: boolean): ZRect
{
    let edgePoint: Vector3 = isStart ? edge.v0.pos.clone() : edge.v1.pos.clone();
    let dir: Vector3 = isStart? edge.dv.clone().negate() : edge.dv.clone();
    let splitRect: ZRect = getSplitRectInPoly(wPoly, edgePoint, dir, edge.nor);
    return splitRect;
}

function getSplitRectInPoly(wPoly: ZPolygon, startPoint: Vector3, dir: Vector3, nor: Vector3): ZRect
{
    // TODO 这里重新确定startPoint
    let reCalStartPointInfo: {edge: ZEdge, point: Vector3} = wPoly.getRayIntersection(startPoint, dir.clone().negate());
    if(!reCalStartPointInfo.point)
    {
        return null;
    }
    if(reCalStartPointInfo.point.distanceTo(startPoint) > 1)
    {
        startPoint = reCalStartPointInfo.point.clone();
    }
    let rayStartPoint: Vector3 = startPoint.clone().add(dir.clone().multiplyScalar(0.1));
    let res: {edge: ZEdge, point: Vector3} = wPoly.getRayIntersection(rayStartPoint, dir);
    if (!res || !res.edge)
    {
        return null;
    }
    let startPoint1: Vector3 = startPoint.clone().add(dir.clone().multiplyScalar(-0.1));
    let edgeEndPoint: Vector3 = res.point.clone();
    edgeEndPoint.add(dir.clone().multiplyScalar(0.1));
    let splitDistTol: number = 10;
    let backCenter: Vector3 = startPoint1.clone().add(edgeEndPoint).multiplyScalar(0.5).add(nor.clone().multiplyScalar(splitDistTol / 2));
    let rectLen: number = startPoint1.distanceTo(edgeEndPoint);
    let splitRect: ZRect = new ZRect(rectLen, splitDistTol);
    splitRect.nor = nor.clone().negate();
    splitRect.back_center = backCenter;
    splitRect.updateRect();
    return splitRect;
}

function splitgetValidSplitTwoSubArea(spacePoly: ZPolygon, splitRect: ZRect): any[]
{
    if(!splitRect)
    {
        return null;
    }
    let subSpacePoly: ZPolygon[] = spacePoly.clone().substract(splitRect);
    let subSpacePolyInfos: any[] = [];
    for(let subSpace of subSpacePoly)
    {
        let subSpaceRect: ZRect = TBaseRoomToolUtil.instance.getMaxInnerRectByPolygon(subSpace);
        if(!subSpaceRect)
        {
            continue;
        }
        if(subSpaceRect.min_hh / subSpaceRect.max_hh < 0.2 || subSpaceRect.min_hh < Math.min(TTinyLivingRoomByPathRelation.minLivingAreaMinLength, TTinyLivingRoomByPathRelation.diningGroupMinLen))
        {
            continue;
        }
        let subSpaceInfo: any = {
            subPoly: subSpace,
            subMainRect: subSpaceRect,
        }
        subSpacePolyInfos.push(subSpaceInfo);
    }
    if(subSpacePolyInfos.length < 2)
    {
        return null;
    }
    // 检查分割出来的两个区域大小是否均衡且长宽比比较合适、
    subSpacePolyInfos.sort((a, b) => {
        return b.subMainRect.area - a.subMainRect.area;
    });
    let minArea: number = Math.min(subSpacePolyInfos[0].subMainRect.area, subSpacePolyInfos[1].subMainRect.area);
    let maxArea: number = Math.max(subSpacePolyInfos[0].subMainRect.area, subSpacePolyInfos[1].subMainRect.area)
    if(minArea / maxArea < 0.3)// || subSpacePolyInfos[0].subMainRect.min_hh / subSpacePolyInfos[0].subMainRect.max_hh < 0.35 || subSpacePolyInfos[1].subMainRect.min_hh / subSpacePolyInfos[1].subMainRect.max_hh < 0.35)
    {
        return null;
    }
    return [subSpacePolyInfos[0], subSpacePolyInfos[1]];
}

function getLayonSplitDoors(poly: ZPolygon, doors: I_Window[]): I_Window[]
{
    let splitDoors: I_Window[] = doors.filter(door => (door.realType == "SlidingDoor" && (door.room_names.includes("阳台") || door.room_names.includes("厨房"))) || 
        ((door.room_names.length == 1 && door.room_names.includes("客餐厅")) || door.room_names.includes("入户花园")) || door.room_names.includes("卧室"));
    // 检查是否有门会靠着区域
    let targetSplitDoors: I_Window[] = [];
    for(let splitDoor of splitDoors)
    {
        if(isLayonWindows(poly, [splitDoor]).isLayon)
        {
            targetSplitDoors.push(splitDoor);
        }
    }
    return targetSplitDoors;
}

function getSplitAreaByDoors(poly: ZPolygon, doors: I_Window[]): any[]
{
    let splitAreas: any[] = [];
    for(let door of doors)
    {
        let splitDoorNor: Vector3 = door.rect.nor.clone();
        for(let polyEdge of poly.edges)
        {
            if(Math.abs(polyEdge.nor.clone().dot(splitDoorNor)) < 0.9)
            {
                continue;
            }
            if(!(polyEdge.islayOn(door.rect.frontEdge, door.rect.depth) || polyEdge.islayOn(door.rect.backEdge, door.rect.depth)))
            {
                continue;
            }
            let layonPolyEdgeNor: Vector3 = polyEdge.nor.clone().negate();
            let splitDoorPoint1: Vector3 = door.rect.frontEdge.v0.pos.clone();
            let splitDoorPoint2: Vector3 = door.rect.frontEdge.v1.pos.clone();
            let splitDoorCenter: Vector3 = door.rect.frontEdge.center.clone();

            splitDoorPoint1 = getProjectEdgePoint(splitDoorPoint1, polyEdge);
            splitDoorPoint2 = getProjectEdgePoint(splitDoorPoint2, polyEdge);
            splitDoorCenter = getProjectEdgePoint(splitDoorCenter, polyEdge);

            splitDoorPoint1.add(layonPolyEdgeNor.clone().multiplyScalar(0.1));
            splitDoorPoint2.add(layonPolyEdgeNor.clone().multiplyScalar(0.1));
            splitDoorCenter.add(layonPolyEdgeNor.clone().multiplyScalar(0.1));

            let splitDoorRect1: ZRect = getSplitRectInPoly(poly, splitDoorPoint1, layonPolyEdgeNor, polyEdge.dv);
            let splitDoorRect2: ZRect = getSplitRectInPoly(poly, splitDoorPoint2, layonPolyEdgeNor, polyEdge.dv);
            let splitDoorRect3: ZRect = getSplitRectInPoly(poly, splitDoorCenter, layonPolyEdgeNor, polyEdge.dv);
            let splitTwoAreas1: any = splitgetValidSplitTwoSubArea(poly, splitDoorRect1);
            let splitTwoAreas2: any = splitgetValidSplitTwoSubArea(poly, splitDoorRect2);
            let splitTwoAreas3: any = splitgetValidSplitTwoSubArea(poly, splitDoorRect3);   
            if(splitTwoAreas1)
            {
                splitAreas.push(splitTwoAreas1);
            }
            if(splitTwoAreas2)
            {
                splitAreas.push(splitTwoAreas2);
            }
            if(splitTwoAreas3)
            {
                splitAreas.push(splitTwoAreas3);
            }
        }
    }
    // 选取比例最佳的分割
    let maxRatio: number = null;
    for(let tempSplitAreas of splitAreas)
    {
        let ratio: number = Math.min( tempSplitAreas[0].min_hh / tempSplitAreas[0].max_hh, tempSplitAreas[1].min_hh / tempSplitAreas[1].max_hh);
        if(ratio == null || ratio > maxRatio)
        {
            maxRatio = ratio;
        }
    }
    let targetSplitAreas: any[] = [];
    for(let tempSplitAreas of splitAreas)
    {
        let ratio: number = Math.min( tempSplitAreas[0].min_hh / tempSplitAreas[0].max_hh, tempSplitAreas[1].min_hh / tempSplitAreas[1].max_hh);
        if(ratio > maxRatio * 0.8)
        {
            targetSplitAreas.push(tempSplitAreas);
        }
    }
    return targetSplitAreas;
}

function getProjectEdgePoint(point: Vector3, edge: ZEdge): Vector3
{
    let projectInfo: any = edge.projectEdge2d({x: point.x, y: point.y});
    let projectPoint: Vector3 = edge.unprojectEdge2d({x: projectInfo.x, y: 0});
    return projectPoint;
}

function postProcessLayoutLivingAreaResults(results: I_TinyLivingRoomByPathParams[], params: I_TinyLivingRoomByPathParams)
{
    postProcessAddTvCabinet(results, params);
}

function postProcessAddTvCabinet(results: I_TinyLivingRoomByPathParams[], params: I_TinyLivingRoomByPathParams)
{
    // 1. 沙发背景墙沿着墙缩短
    for(let result of results)
    {
        let room: TRoom = params._room;
        let sofaBackGroundWall: ZRect = result.sofa_background_wall_rect;
        if(sofaBackGroundWall)
        {
            let sofaBackGroundWallRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(sofaBackGroundWall);
            let sofaBackGroundWallNor: Vector3 = sofaBackGroundWall.nor.clone();
            let sofaBackGroundBackEdge: ZEdge = sofaBackGroundWall.backEdge;
            let layonRoomEdge: ZEdge = TBaseRoomToolUtil.instance.edgeOnPolygon(sofaBackGroundBackEdge, room.room_shape._poly, 5, 0.1);
            if(layonRoomEdge)
            {
                let layonRoomEdgeDir: Vector3 = layonRoomEdge.dv.clone();
                let layonRoomEdgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(layonRoomEdge);
                let layonRoomEdgeXDot: number = Math.abs(layonRoomEdgeDir.clone().dot(xDir));
                let layonRoomEdgeYDot: number = Math.abs(layonRoomEdgeDir.clone().dot(yDir));
                if(layonRoomEdgeXDot > 0.9)
                {
                    sofaBackGroundWallRange.xMin = Math.max(layonRoomEdgeRange.xMin, sofaBackGroundWallRange.xMin);
                    sofaBackGroundWallRange.xMax = Math.min(layonRoomEdgeRange.xMax, sofaBackGroundWallRange.xMax);
                }
                if(layonRoomEdgeYDot > 0.9)
                {
                    sofaBackGroundWallRange.yMin = Math.max(layonRoomEdgeRange.yMin, sofaBackGroundWallRange.yMin);
                    sofaBackGroundWallRange.yMax = Math.min(layonRoomEdgeRange.yMax, sofaBackGroundWallRange.yMax);
                }
            }
            let newSofaBackGroundWallRect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(sofaBackGroundWallRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(sofaBackGroundWallRange));
            if(Math.abs(newSofaBackGroundWallRect.nor.clone().dot(sofaBackGroundWallNor)) > 0.9)
            {
                newSofaBackGroundWallRect.nor = sofaBackGroundWallNor.clone();
            }
            else
            {
                let newSofaBackGroundLen: number = newSofaBackGroundWallRect.length;
                let newSofaBackGroundDepth: number = newSofaBackGroundWallRect.depth;
                let newSofaBackGroundCenter: Vector3 = newSofaBackGroundWallRect.rect_center;
                newSofaBackGroundWallRect.length = newSofaBackGroundDepth;
                newSofaBackGroundWallRect.depth = newSofaBackGroundLen;
                newSofaBackGroundWallRect.nor = sofaBackGroundWallNor.clone();
                newSofaBackGroundWallRect.rect_center = newSofaBackGroundCenter;
            }
            newSofaBackGroundWallRect.updateRect();
            sofaBackGroundWall.copy(newSofaBackGroundWallRect);
        }
    }

    // 2. 检查沙发组合前侧是否存在电视柜
    let allCabinetRects: ZRect[] = params.allCabinetRects;
    for(let result of results)
    {
        let tvCabinetRect: ZRect = result.tv_cabinet_rect;
        if(tvCabinetRect)
        {
            continue;
        }
        let sofaGroupRect: ZRect = result.sofa_group_rect;
        let sofaGroupRectRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(sofaGroupRect);
        for(let cabinetRect of allCabinetRects)
        {
            let cabinetRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(cabinetRect);
            if(!((cabinetRange.xMin < sofaGroupRectRange.xMax && cabinetRange.xMax > sofaGroupRectRange.xMin) 
                || (cabinetRange.yMin < sofaGroupRectRange.yMax && cabinetRange.yMax > sofaGroupRectRange.yMin)) 
                || !TBaseRoomToolUtil.instance.polygonIsInFrontArea(sofaGroupRect.frontEdge, sofaGroupRect.backEdge, cabinetRect)
                || TBaseRoomToolUtil.instance.isOverlayByRects(cabinetRect, params.diningPolyInfo.subMainRect))
            {
                continue;
            }
            let cabinetXDot: number = Math.abs(cabinetRect.nor.clone().dot(xDir));
            let cabinetYDot: number = Math.abs(cabinetRect.nor.clone().dot(yDir));
            let tempCabinetRectCenter: Vector3 = TBaseRoomToolUtil.instance.getCenter3dByRange2d(cabinetRange);
            if(cabinetXDot > 0.9)
            {
                cabinetRange.yMin = Math.max(sofaGroupRectRange.yMin, cabinetRange.yMin);
                cabinetRange.yMax = Math.min(sofaGroupRectRange.yMax, cabinetRange.yMax);
            }
            if(cabinetYDot > 0.9)
            {
                cabinetRange.xMin = Math.max(sofaGroupRectRange.xMin, cabinetRange.xMin);
                cabinetRange.xMax = Math.min(sofaGroupRectRange.xMax, cabinetRange.xMax);
            }
            let newCabinetRectCenter: Vector3 = TBaseRoomToolUtil.instance.getCenter3dByRange2d(cabinetRange);
            let newCabinetRect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(cabinetRange, newCabinetRectCenter);
            if(Math.abs(newCabinetRect.nor.clone().dot(cabinetRect.nor)) > 0.9)
            {
                newCabinetRect.nor = cabinetRect.nor.clone();
            }
            else
            {
                let newCabinetLen: number = newCabinetRect.length;
                let newCabinetDepth: number = newCabinetRect.depth;
                newCabinetRect.length = newCabinetDepth;
                newCabinetRect.depth = newCabinetLen;
                newCabinetRect.nor = cabinetRect.nor.clone();
            }
            newCabinetRect.rect_center = newCabinetRectCenter;
            newCabinetRect.updateRect();
            result.tv_cabinet_rect = newCabinetRect;
            break;
        }
    }
}

function parseCandidatePaths(roomPoly: ZPolygon,  candidatePath: any): Vector3[][]
{
    // 获取第一条路径线上在户型数据内最长的路径方案
    let initPathEdges: Vector3[][] = getInitPath(roomPoly, candidatePath);
    if(initPathEdges == null)
    {
        return null;
    }
    let secondPaths: Vector3[][] = [candidatePath.path1[1], candidatePath.path2[1]];
    let newPaths: Vector3[][] = findNewPathByRoom(initPathEdges, roomPoly, secondPaths);
    return newPaths;
}

function getInitPath(roomPoly: ZPolygon,  candidatePath: any): Vector3[][]
{
    let innerFirstPathLen1: number = calInnerRoomPathLen(roomPoly, candidatePath.path1[0]);
    let innerFirstPathLen2: number = calInnerRoomPathLen(roomPoly, candidatePath.path2[0]);
    if(innerFirstPathLen1 != null && innerFirstPathLen2 != null)
    {
        if(innerFirstPathLen1 > innerFirstPathLen2)
        {
            return candidatePath.path1;
        }
        return  candidatePath.path2;
    }
    else if(innerFirstPathLen1 != null)
    {
        return candidatePath.path1;
    }
    else if(innerFirstPathLen2 != null)
    {
        return candidatePath.path2;
    }
    return null;
}

function calInnerRoomPathLen(roomPoly: ZPolygon, firstPath: Vector3[]): number
{
    let tempEdge = new ZEdge({pos: firstPath[0]}, {pos: firstPath[1]});
    tempEdge.computeNormal();
    let rayIntersectInfo: any = roomPoly.getRayIntersection(tempEdge.v0.pos, tempEdge.dv);
    if(rayIntersectInfo == null || rayIntersectInfo.point == null)
    {
        return null;
    }
    let toRoomLen: number = tempEdge.v0.pos.distanceTo(rayIntersectInfo.point);
    return toRoomLen;
}

function findNewPathByRoom(initPaths: Vector3[][], roomPoly: ZPolygon, secondPaths: Vector3[][]): Vector3[][]
{
    if(initPaths.length < 2)
    {
        return initPaths;
    }
    let secondPathEdges: ZEdge[] = [];
    for(let secondPath of secondPaths)
    {
        let secondPathEdge: ZEdge = new ZEdge({pos: secondPath[0]}, {pos: secondPath[1]});
        secondPathEdge.computeNormal();
        secondPathEdges.push(secondPathEdge);
    }
    let secondPathEdge1: ZEdge = new ZEdge({pos: initPaths[1][0]}, {pos: initPaths[1][1]});
    secondPathEdge1.computeNormal();
    let pathDirs: Vector3[] = [initPaths[0][1].clone().sub(initPaths[0][0]).normalize(), initPaths[1][1].clone().sub(initPaths[1][0]).normalize()];
    let index: number = 0;
    let recordRoomEdges: ZEdge[] = [];
    let newPathEdges: Vector3[][] = [];
    let startPoint: Vector3 = initPaths[0][0].clone();
    while(true)
    {
        if(index > 20)
        {
            break;
        }
        let rayDir: Vector3 = pathDirs[index % 2];
        let singlePath :Vector3[] = calPathByRayAndRoom(startPoint, rayDir, roomPoly, recordRoomEdges);
        if(singlePath == null)
        {
            break;
        }
        let singlePathEdge: ZEdge = new ZEdge({pos: singlePath[0]}, {pos: singlePath[1]});
        singlePathEdge.computeNormal();
        let singlePathEdgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(singlePathEdge);
        let isOverlaySecondPath: boolean = false;
        for(let secondPathEdge of secondPathEdges)
        {
            let secondPathEdgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(secondPathEdge);
            if(TBaseRoomToolUtil.instance.isOverlayRange2ds(secondPathEdgeRange, singlePathEdgeRange, true))
            {
                // 直接计算这两条线之间的距离
                let intersectInfo: any = singlePathEdge.checkIntersection(secondPathEdge, 0);
                if(intersectInfo)
                {
                    singlePathEdge = null;
                    singlePath[1] = intersectInfo.point.clone();
                    newPathEdges.push(singlePath);
                    newPathEdges.push([intersectInfo.point.clone(), secondPathEdge.v1.pos.clone()]);
                }
                isOverlaySecondPath = true;
                break;
            }
        }
        if(isOverlaySecondPath)
        {
            break;
        }
        singlePathEdge = null;
        startPoint = singlePath[1].clone();
        newPathEdges.push(singlePath);
        index += 1;
    }
    if(index > 20)
    {
        return initPaths;
    }
    return newPathEdges;
}

function calPathByRayAndRoom(startPoint: Vector3, rayDir: Vector3, roomPoly: ZPolygon, recordRoomEdges: ZEdge[]): Vector3[]
{
    let intersectInfo: any = roomPoly.getRayIntersection(startPoint, rayDir);
    let tempEdge: ZEdge = new ZEdge({pos: startPoint}, {pos: intersectInfo.point});
    tempEdge.computeNormal();
    // 先找到投影点在上面的边，并找出此点关联的边的法向是否与此射线方向是逆向的
    let endPoint: Vector3 = null;
    let minDist: number = null;
    let minCurrentRoomEdge: ZEdge = null;
    for(let i = 0; i < roomPoly.edges.length;  ++i)
    {
        let currentRoomEdge: ZEdge = roomPoly.edges[i];
        // TODO 这里需要修正下边是否对着边，目前暂时没写
        if(Math.abs(rayDir.clone().dot(currentRoomEdge.dv.clone())) < 0.9 
            || recordRoomEdges.includes(currentRoomEdge) 
            || Math.abs(currentRoomEdge.nor.clone().dot(startPoint.clone().sub(currentRoomEdge.v0.pos))) > TTinyLivingRoomByPathRelation.pathAttachWallDistTol * 2)
        {
            continue;
        }
        let preRoomEdge: ZEdge = roomPoly.edges[(i - 1 + roomPoly.edges.length) % roomPoly.edges.length];
        let nextRoomEdge: ZEdge = roomPoly.edges[(i + 1) % roomPoly.edges.length];
        let projectInfo0: any = tempEdge.projectEdge2d({x: currentRoomEdge.v0.pos.x, y: currentRoomEdge.v0.pos.y});
        let projectInfo1: any = tempEdge.projectEdge2d({x: currentRoomEdge.v1.pos.x, y: currentRoomEdge.v1.pos.y});
        
        if(projectInfo0.x >= 0 && projectInfo0.x <= tempEdge.length && preRoomEdge.nor.clone().dot(rayDir) < -0.9 && !recordRoomEdges.includes(preRoomEdge))
        {
            if(minDist == null || minDist > projectInfo0.x)
            {
                minDist = projectInfo0.x;
                minCurrentRoomEdge = currentRoomEdge;
            }  
        }
        if(projectInfo1.x >= 0 && projectInfo1.x <= tempEdge.length && nextRoomEdge.nor.clone().dot(rayDir) < -0.9 &&!recordRoomEdges.includes(nextRoomEdge))
        {
            if(minDist == null || minDist > projectInfo1.x)
            {
                minDist = projectInfo1.x;
                minCurrentRoomEdge = currentRoomEdge;
            }
        }
    }
    if(!minCurrentRoomEdge)
    {
        let cutDist: number = tempEdge.length - TTinyLivingRoomByPathRelation.hallwayDist / 2;
        if(cutDist < 0)
        {
            cutDist = tempEdge.length / 2;
        }
        endPoint = tempEdge.unprojectEdge2d({x: cutDist, y: 0});
    }
    else
    {
        let extencDist = minDist + TTinyLivingRoomByPathRelation.hallwayDist / 2;
        if(extencDist > tempEdge.length)
        {
             extencDist = (tempEdge.length + minDist)/ 2;
        }
        endPoint = tempEdge.unprojectEdge2d({x: extencDist, y: 0});
        recordRoomEdges.push(minCurrentRoomEdge);
    }
    tempEdge = null;
    if(endPoint)
    {
        return [startPoint, endPoint];
    }
    return null;
}

function layoutSubSpaceAreaByEntranceCabinetArea(params: I_TinyLivingRoomByPathParams): I_TinyLivingRoomByPathParams[]
{
    let allCabinetRects: ZRect[] = params.allCabinetRects;
    let allLayoutRects: ZRect[] = parseLayoutRects(params);
    let layoutEntranceResults: I_TinyLivingRoomByPathParams[] = [];
    let entranceDoors: I_Window[] = params.doors.filter(door => door.type == "Door" 
        && (door.room_names.includes("入户花园") || (door.room_names.length == 1 && door.room_names.includes("客餐厅"))));
    let candidateEntranceCabinetInfos: {rect: ZRect, nearEntranceDoorDist: number}[] = [];
    for(let cabinetRect of allCabinetRects)
    {
        let toEntranceDoorDistInfo: any = getMinDistToEntranceDoors(cabinetRect, entranceDoors);
        if(toEntranceDoorDistInfo.minDist > TTinyLivingRoomByPathRelation.entranceCabinetToEntranceDoorMaxDist)
        {
            continue;
        }
        let cabinetNor: Vector3 = cabinetRect.nor;
        let subPolys: ZPolygon[] = cabinetRect.substract_polygons(allLayoutRects);
        if(subPolys.length == 1)
        {
            let maxSubInnerRect: ZRect = TBaseRoomToolUtil.instance.getMaxInnerRectByPolygon(subPolys[0]);
            let subPolyRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(maxSubInnerRect);
            let xLen = subPolyRange.xMax - subPolyRange.xMin;
            let yLen = subPolyRange.yMax - subPolyRange.yMin;
            let xDot: number = Math.abs(cabinetNor.clone().dot(xDir));
            let yDot: number = Math.abs(cabinetNor.clone().dot(yDir));
            let  subCabinetLen: number = null; let subCabinetDepth: number = null;
            if(xDot > 0.9)
            {
                subCabinetLen = yLen;
                subCabinetDepth = xLen;
            }
            else if(yDot > 0.9)
            {
                subCabinetLen = xLen;
                subCabinetDepth = yLen;
            }
            if(subCabinetLen == null || subCabinetDepth == null 
                || subCabinetLen <= TTinyLivingRoomByPathRelation.entranceCabinetMinLen)
            {
                continue;
            }
            let oldCenter: Vector3 = TBaseRoomToolUtil.instance.getCenter3dByRange2d(subPolyRange);
            let subCabinetRect: ZRect = new ZRect(subCabinetLen, subCabinetDepth);
            subCabinetRect.nor = cabinetNor.clone();
            subCabinetRect.rect_center = oldCenter;
            subCabinetRect.updateRect();
            if(subCabinetLen > TTinyLivingRoomByPathRelation.entranceCabinetMaxLen)
            {
                cutEntranceCabinet(subCabinetRect, toEntranceDoorDistInfo.nearEntranceDoor, params._room);
            }
            candidateEntranceCabinetInfos.push({rect: subCabinetRect, nearEntranceDoorDist: toEntranceDoorDistInfo.minDist});
        }
    }
    candidateEntranceCabinetInfos.sort((cabinetInfo1, cabinetInfo2) => cabinetInfo1.nearEntranceDoorDist - cabinetInfo2.nearEntranceDoorDist);
    // 备选柜体检查是否会挡过道，过滤掉挡过道的
    let blockRects: ZRect[] = getBlockRects(params);
    for(let entranceCabinetInfo of candidateEntranceCabinetInfos)
    {
        let isBlockHallway: boolean = isCabinetBlockHallway(entranceCabinetInfo.rect, blockRects, params._room);
        if(isBlockHallway)
        {
            continue;
        }
        let result: I_TinyLivingRoomByPathParams = {
            entrance_cabinet_rect: entranceCabinetInfo.rect,
        };
        layoutEntranceResults.push(result);
    }
    if(layoutEntranceResults.length == 0)
    {
        return null;
    }
    return layoutEntranceResults;
}

// 这块主要是迁移士玮那边的
function layoutEntranceCabinet(params: I_TinyLivingRoomByPathParams): I_TinyLivingRoomByPathParams[]
{
    let allCabinets: ZRect[] = params.allCabinetRects;
    let entranceDoorRects: ZRect[] = params.doors.filter(door => door.type == "Door" && (door.room_names.length == 1 && door.room_names.includes("客餐厅"))).map(door => door.rect);

    if (!allCabinets || allCabinets.length == 0) return null;
    if (!entranceDoorRects || entranceDoorRects.length) return null;

    let candidateEntranceRects: ZRect[] = [];
    let distLevel = [200, 2000];
    let hasFound = false;
    // 定义两种级别的距离限制，找出在此距离的限制下最接近柜体的入户门，调整柜体方向并将加入到玄关柜列表中，当距离小的级别找到玄关柜列表时，则停止后续距离限制大的查找
    for (let levelDist of distLevel) {
        allCabinets.forEach((rect) => {
            let positions = [...rect.positions];
            let minDist = levelDist;
            let tDist = minDist;
            let tDoor: ZRect = null;
            let entranceRect: ZRect = null;
            let targetDoor: ZRect = null;
            entranceDoorRects.forEach((door) => {
                positions.forEach(p => {
                    let dist = door.distanceToPoint(p);
                    if (dist < tDist) {
                        tDist = dist;
                        tDoor = door;
                    }
                });
            });
            if (tDist < minDist) {
                entranceRect = rect.clone();
                targetDoor = tDoor;
            }
            if (entranceRect && targetDoor) {
                let xx = entranceRect.project(targetDoor.rect_center).x;
                if (xx > 0) {
                    entranceRect.u_dv = entranceRect.dv.negate();
                    entranceRect.updateRect();
                }
                candidateEntranceRects.push(entranceRect);
                hasFound = true;
            }
        });
        if (hasFound) break;
    }
    candidateEntranceRects.sort((a, b) => a.w - b.w);
    // 玄关柜确定之后则会将此柜从备选柜体列中剔除，同时如果玄关柜长度过长则会将其拆分为两个柜，一个柜为玄关柜，一个加入到备选柜体中
    let entranceCabinetResults: I_TinyLivingRoomByPathParams[] = [];
    candidateEntranceRects.forEach((entranceRect) => {
        let targetCandidateRects = [...allCabinets];
        let id = targetCandidateRects.indexOf(entranceRect);
        targetCandidateRects.splice(id, 1);
        // 这里使用了魔法数字
        if (entranceRect.w > 2700) {
            // 拆分成两个柜
            let backEdge = entranceRect.backEdge;
            let rect0 = entranceRect.clone();
            let rect1 = entranceRect.clone();

            rect0._w = 1500;
            rect1._w = entranceRect._w - rect0.w;

            rect0.back_center = backEdge.unprojectEdge2d({ x: rect0.w / 2, y: 0 });
            rect1.back_center = backEdge.unprojectEdge2d({ x: rect0.w + rect1.w / 2, y: 0 });

            rect0.updateRect();
            rect1.updateRect();

            entranceRect.copy(rect0);

            targetCandidateRects.push(rect1);
        }

        for(let entranceRect of targetCandidateRects)
        {
            let entranceCabinetResult: I_TinyLivingRoomByPathParams = {
                entrance_cabinet_rect: entranceRect,
            };
            entranceCabinetResults.push(entranceCabinetResult);
        }
    })
    return entranceCabinetResults;
}

function parseLayoutRects(params: I_TinyLivingRoomByPathParams): ZRect[]
{
    let layoutRects: ZRect[] = [];
    let sofaGroupRect: ZRect = params.sofa_group_rect;
    let sofaBackGroundRect: ZRect = params.sofa_background_wall_rect;
    let tvCabinetRect: ZRect = params.tv_cabinet_rect;
    let diningTableRect: ZRect = params.dinning_table_rect;
    let diningCabinetRect: ZRect = params.dinning_cabinet_rect;
    
    if(sofaGroupRect)
    {
        layoutRects.push(sofaGroupRect);
    }
    if(sofaBackGroundRect)
    {
        layoutRects.push(sofaBackGroundRect);
    }
    if(tvCabinetRect)
    {
        layoutRects.push(tvCabinetRect);
    }
    if(diningTableRect)
    {
        layoutRects.push(diningTableRect);
    }
    if(diningCabinetRect)
    {
        layoutRects.push(diningCabinetRect);
    }
    let doors: I_Window[] = params.doors;
    let doorAndWindowExpandRects: ZRect[] = getExpandDoorRects(doors, params._room.room_shape._poly);
    layoutRects.push(...doorAndWindowExpandRects);
    return layoutRects;
}

function computeValidAreas(params: I_TinyLivingRoomByPathParams): ZPolygon[]
{
    let expandoorRects: ZRect[] = getExpandDoorRects(params.doors, params._room.room_shape._poly);
    let subPolys: ZPolygon[] = params._room.room_shape._poly.substract_polygons(expandoorRects);
    return subPolys;
}

function calIsSameNorLayonRoomEdge(door: I_Window, polygon: ZPolygon): boolean
{
    let isSameNor: boolean = false;
    let doorNor: Vector3 = door.rect.nor.clone();
    let doorLayonEdges: ZEdge[] = [door.rect.frontEdge, door.rect.backEdge];
    for(let doorLayonEdge of doorLayonEdges)
    {
        let layonPolyEdge: ZEdge = TBaseRoomToolUtil.instance.edgeOnPolygon(doorLayonEdge, polygon);
        if(!layonPolyEdge)
        {
            continue;
        }
        isSameNor = layonPolyEdge.nor.clone().dot(doorNor) > 0.9;
        break;
    }
    return isSameNor;
}

function expandPathWidthPathRects(pathRect: ZRect, pathVec: Vector3[], roomPoly: ZPolygon, attachWallTol: number = 500)
{
    // 路径线两边都满足贴墙范围的情况下则过道直接进行扩充
    let pathDir: Vector3 = pathVec[1].clone().sub(pathVec[0]).normalize();
    let pathEdges: ZEdge[] = pathRect.edges.filter(edge => Math.abs(edge.dv.clone().dot(pathDir)) > 0.9);
    if(pathEdges.length < 2)
    {
        return;
    }
    let pathAttachInfo: Map<ZEdge, number> = new Map();
    for(let pathEdge of pathEdges)
    {
        let pathEdgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(pathEdge);
        let minDist: number = null;
        for(let roomEdge of roomPoly.edges)
        {
            let roomEdgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(roomEdge);
            if(!(pathEdge.nor.clone().dot(roomEdge.nor) > 0.9 
                && ((pathEdgeRange.xMin < roomEdgeRange.xMax && pathEdgeRange.xMax > roomEdgeRange.xMin) || (pathEdgeRange.yMin < roomEdgeRange.yMax && pathEdgeRange.yMax > roomEdgeRange.yMin))))
            {
                continue;
            }
            let dist: number = TBaseRoomToolUtil.instance.calDistance(pathEdge, roomEdge);
            if(minDist == null || minDist > dist)
            {
                minDist = dist;
            }
        }
        pathAttachInfo.set(pathEdge, minDist);
    }
    let isAllAttachWall: boolean = true;
    for(let dist of pathAttachInfo.values())
    {
        if(dist > attachWallTol)
        {
            isAllAttachWall = false;
            break;
        }
    }
    if(!isAllAttachWall)
    {
        return;
    }
    let pathRectRange: I_Range2D = TBaseRoomToolUtil.instance.getRange2dByPolygon(pathRect);
    for(let entry of pathAttachInfo.entries())
    {
        let pathRectEdge: ZEdge = entry[0];
        let dist: number = entry[1];
        let pathNor: Vector3 = pathRectEdge.nor.clone();
        let xDot: number = pathNor.clone().dot(xDir);
        let yDot: number = pathNor.clone().dot(yDir);
        if(xDot > 0.9)
        {
            pathRectRange.xMax += dist;
        }
        else if(xDot < -0.9)
        {
            pathRectRange.xMin -= dist;
        }

        if(yDot > 0.9)
        {
            pathRectRange.yMax += dist;
        }
        else if(yDot < -0.9)
        {
            pathRectRange.yMin -= dist;
        }
    }
    let expandWidthPathRect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(pathRectRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(pathRectRange));
    pathRect.copy(expandWidthPathRect);
}

function isEntranceDoorsFacePolygon(doors: I_Window[], rect: ZRect): boolean
{
    let rectRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(rect);
    for(let door of doors)
    {
        let doorRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(door.rect);
        if((doorRange.xMin < rectRange.xMax && doorRange.xMax > rectRange.xMin) || (doorRange.yMin < rectRange.yMax && doorRange.yMax > rectRange.yMin))
        {
            return true;
        }
    }
    return false;
}

function getDiningCabinetNum(diningSpacePoly: ZPolygon, params: I_TinyLivingRoomByPathParams): number
{
    let cabinetNum: number = 0;
    let allCabinetRects: ZRect[] = params.allCabinetRects;
    for(let cabinetRect of allCabinetRects)
    {
        for(let polyEdge of diningSpacePoly.edges)
        {
            if(cabinetRect.backEdge.islayOn(polyEdge, 5, 0.1))
            {
                cabinetNum += 1;
                break;
            }
        }
    }
    return cabinetNum;
}

function getFigureFromCandidateFigureList(candidateFigureList: any, groupCode: string = null): TFigureElement[]
{
    let targetFigures: TFigureElement[] = [];
    for(let groupTemplate of candidateFigureList.group_templates)
    {
        if(groupCode)
        {
            if(groupTemplate.group_space_category != groupCode)
            {
                continue;
            }
            targetFigures.push(...groupTemplate.current_s_group.figure_elements);
        }
        else
        {
            targetFigures.push(...groupTemplate.current_s_group.figure_elements);
        }
        if(groupCode && groupTemplate.group_space_category )
        groupTemplate.current_s_group.main_figure;
    }
    return targetFigures;
}

function moveDiningTableFigures(subVector: Vector3, diningTableRange: any, diningTableGroupRange: any, diningTableFigures: TFigureElement[])
{
    let subVectorNormal: Vector3 = subVector.clone().normalize();
    let xDot: number = subVectorNormal.dot(xDir);
    let yDot: number = subVectorNormal.dot(yDir);
    let targetMoveVec: Vector3 = null;
    if(xDot > 0)
    {
        let subVecLen: number = Math.abs(subVector.dot(xDir));
        let figureXMax: number = diningTableRange.xMax + subVecLen;
        let moveLen : number = figureXMax - diningTableGroupRange.xMax;
        if(moveLen < 0)
        {
            moveLen = subVecLen;
        }
        else
        {
            moveLen = subVecLen - moveLen;
        }
        targetMoveVec = xDir.clone().multiplyScalar(moveLen);
    }
    else if(xDot < 0)
    {
        let subVecLen: number = Math.abs(subVector.dot(xDir));
        let figureXMin: number = diningTableRange.xMin - subVecLen;
        let moveLen : number = diningTableGroupRange.xMin - figureXMin;
        if(moveLen < 0)
        {
            moveLen = subVecLen;
        }
        else
        {
            moveLen = subVecLen - moveLen;
        }
        targetMoveVec = xDir.clone().multiplyScalar(-moveLen);
    }

    if(yDot > 0)
    {
        let subVecLen: number = Math.abs(subVector.dot(yDir));
        let figureYMax: number = diningTableRange.yMax + subVecLen;
        let moveLen : number = figureYMax - diningTableGroupRange.yMax;
        if(moveLen < 0)
        {
            moveLen = subVecLen;
        }
        else
        {
            moveLen = subVecLen - moveLen;
        }
        targetMoveVec = yDir.clone().multiplyScalar(moveLen);
    }
    else if(yDot < 0)
    {
        let subVecLen: number = Math.abs(subVector.dot(yDir));
        let figureYMin: number = diningTableRange.yMin - subVecLen;
        let moveLen : number = diningTableGroupRange.yMin - figureYMin;
        if(moveLen < 0)
        {
            moveLen = subVecLen;
        }
        else
        {
            moveLen = subVecLen - moveLen;
        }
        targetMoveVec = yDir.clone().multiplyScalar(-moveLen);
    }
    // 同步进行移动
    if(!targetMoveVec)
    {
         return;
    }
    for(let figure of diningTableFigures)
    {
        let rectCenter: Vector3 = figure.rect.rect_center.clone().add(targetMoveVec);
        figure.rect.rect_center = rectCenter;
        figure.updateFigure();
    }
}

function getBlockRects(params: I_TinyLivingRoomByPathParams): ZRect[]
{
    let sofaGroupRect: ZRect = params.sofa_group_rect;
    let tvCabinetRect: ZRect = params.tv_cabinet_rect;
    let diningTableRect: ZRect = params.dinning_table_rect;
    let diningCabinetRect: ZRect = params.dinning_cabinet_rect;
    let blockRects: ZRect[] = [];
    if(sofaGroupRect)
    {
        blockRects.push(sofaGroupRect);
    }
    if( tvCabinetRect)
    {
        blockRects.push(tvCabinetRect);
    }
    if(diningTableRect)
    {
        let cutDiningTableRect: ZRect = diningTableRect.clone();
        cutDiningTableRect.depth = cutDiningTableRect.depth - 1000;
        cutDiningTableRect.updateRect();
        blockRects.push(cutDiningTableRect);
    }
    if(diningCabinetRect)
    {
        blockRects.push(diningCabinetRect);
    }
    return blockRects;
}

function isCabinetBlockHallway(cabinetRect: ZRect, blockRects: ZRect[], room: TRoom, minHallwayDist: number = 650): boolean
{
    let isBlock: boolean = false;
    let minFrontDist: number = null;
    let cabinetFrontEdge: ZEdge = cabinetRect.frontEdge;
    let cabinetBackEdge: ZEdge = cabinetRect.backEdge;
    for(let blockRect of blockRects)
    {
        if(!TBaseRoomToolUtil.instance.polygonIsInFrontArea(cabinetFrontEdge, cabinetBackEdge, blockRect))
        {
            continue;
        }
        let dist: number = TBaseRoomToolUtil.instance.calDistanceBetweenEdgeAndPolygon(cabinetFrontEdge, blockRect);
        if(minFrontDist == null || minFrontDist > dist)
        {
            minFrontDist = dist;
        }
    }
    let cabinetFrontEdgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(cabinetFrontEdge);
    for(let roomEdge of room.room_shape._poly.edges)
    {
        let roomEdgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(roomEdge);
        if(roomEdge.nor.clone().dot(cabinetFrontEdge.nor) > 0.9 
            && ((cabinetFrontEdgeRange.xMin < roomEdgeRange.xMax && cabinetFrontEdgeRange.xMax > roomEdgeRange.xMin) || (cabinetFrontEdgeRange.yMin < roomEdgeRange.yMax && cabinetFrontEdgeRange.yMax > roomEdgeRange.yMin)))
        {
            let dist: number = TBaseRoomToolUtil.instance.calDistance(cabinetFrontEdge, roomEdge);
            if(minFrontDist == null || minFrontDist > dist)
            {
                minFrontDist = dist;
            }
        }
    }
    if(minFrontDist && minFrontDist < minHallwayDist)
    {
        isBlock = true;
    }
    return isBlock;
}

function getMinDistToEntranceDoors(cabinetRect: ZRect, entranceDoors: I_Window[]): any
{
    let minDistEntranceDoor: I_Window = null;
    let minDist: number = null;
    for(let entranceDoor of entranceDoors)
    {
        let dist: number = TBaseRoomToolUtil.instance.calDistanceByPolygons(entranceDoor.rect, cabinetRect);
        if(minDist == null || minDist > dist)
        {
            minDist = dist;
            minDistEntranceDoor = entranceDoor;
        }
    }
    return {minDist: minDist, nearEntranceDoor: minDistEntranceDoor};
}

function cutEntranceCabinet(cabinetRect: ZRect,  entranceDoor: I_Window, room: TRoom)
{
    // 柜体长度过长则需要进行裁剪此柜体
    let cabinetRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(cabinetRect);
    let entranceDoorRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(entranceDoor.rect);
    let cabinetNor: Vector3 = cabinetRect.nor.clone();
    let xDot: number = Math.abs(cabinetNor.clone().dot(xDir));
    let yDot: number = Math.abs(cabinetNor.clone().dot(yDir));
    let isNearMinForCabinetRange = (cabinetRange: any, entranceDoorRange: any, isX: boolean) => {
        if(isX)
        {
            let minMinDist: number = Math.min(Math.abs(cabinetRange.xMin - entranceDoorRange.xMin), Math.abs(cabinetRange.xMin - entranceDoorRange.xMax));
            let minMaxDist: number = Math.min(Math.abs(cabinetRange.xMax - entranceDoorRange.xMin), Math.abs(cabinetRange.xMax - entranceDoorRange.xMax));
            if(minMinDist < minMaxDist)
            {
                return true;
            }
        }
        else
        {
            let minMinDist: number = Math.min(Math.abs(cabinetRange.yMin - entranceDoorRange.yMin), Math.abs(cabinetRange.yMin - entranceDoorRange.yMax));
            let minMaxDist: number = Math.min(Math.abs(cabinetRange.yMax - entranceDoorRange.yMin), Math.abs(cabinetRange.yMax - entranceDoorRange.yMax));
            if(minMinDist < minMaxDist)
            {
                return true;
            }
        }
        return false;
    }
    if(xDot > 0.9)
    {
        // 修正Y方向上的数据
        let isNearYMin: boolean = isNearMinForCabinetRange(cabinetRange, entranceDoorRange, false);
        if(isNearYMin)
        {
            // ymin固定
            cabinetRange.yMax = cabinetRange.yMin + TTinyLivingRoomByPathRelation.entranceCabinetMaxLen;
        }
        else
        {
            // ymax固定
            cabinetRange.yMin = cabinetRange.yMax - TTinyLivingRoomByPathRelation.entranceCabinetMaxLen;
        }
    }
    if(yDot > 0.9)
    {
        // 修正X方向上的数据
        let isNearXMin: boolean = isNearMinForCabinetRange(cabinetRange, entranceDoorRange, true);
        if(isNearXMin)
        {
            // xmin固定
            cabinetRange.xMax = cabinetRange.xMin + TTinyLivingRoomByPathRelation.entranceCabinetMaxLen;
        }
        else
        {
            // xmax固定
            cabinetRange.xMin = cabinetRange.xMax - TTinyLivingRoomByPathRelation.entranceCabinetMaxLen;
        }
    }
    let newCabinetRect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(cabinetRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(cabinetRange));
    let newCabinetCenter: Vector3 = newCabinetRect.rect_center.clone();
    if(Math.abs(cabinetNor.clone().dot(newCabinetRect.nor)) > 0.9)
    {
        newCabinetRect.nor = cabinetNor.clone();
    }
    else
    {
        let newCabinerLen: number = newCabinetRect.length;
        let newCabinerDepth: number = newCabinetRect.depth;
        
        newCabinetRect.length = newCabinerDepth;
        newCabinetRect.depth = newCabinerLen;
        newCabinetRect.nor = cabinetNor.clone();
    }
    newCabinetRect.rect_center = newCabinetCenter;
    newCabinetRect.updateRect();
    cabinetRect.copy(newCabinetRect);    
}

function getValidSofaBackEdge(sofaBackEdge: ZEdge, layonDoorOrWindows: I_Window[], tvCabinet: ZRect): ZEdge
{
    let layonDoors: I_Window[] = layonDoorOrWindows.filter(window => window.type == "Door");
    let splitPairs: number[][] = []
    for(let layonDoor of layonDoors)
    {
        let layonDoorEdge: ZEdge = TBaseRoomToolUtil.instance.edgeOnPolygon(sofaBackEdge, layonDoor.rect, 100, 0.01);
        if(!layonDoorEdge)
        {
            continue;
        }
        // 将门的在此边上的投影点提取出来
        let layonDoorV0: Vector3 = layonDoorEdge.v0.pos;
        let layonDoorV1: Vector3 = layonDoorEdge.v1.pos;
        let projectInfo1: any = sofaBackEdge.projectEdge2d({x: layonDoorV0.x, y: layonDoorV0.y});
        let projectInfo2: any = sofaBackEdge.projectEdge2d({x: layonDoorV1.x, y: layonDoorV1.y});
        let splitXs: number[] = [];
        if(projectInfo1.x < 0)
        {
            splitXs.push(0);
        }
        else if(projectInfo1.x > sofaBackEdge.length)
        {
            splitXs.push(sofaBackEdge.length);
        }
        else
        {
            splitXs.push(projectInfo1.x);
        }

        if(projectInfo2.x < 0)
        {
            splitXs.push(0);
        }
        else if(projectInfo2.x > sofaBackEdge.length)
        {
            splitXs.push(sofaBackEdge.length);
        }
        else
        {
            splitXs.push(projectInfo2.x);
        }
        
        splitXs.sort((x1, x2) => x1 - x2);
        splitPairs.push(splitXs);
    }
    splitPairs.sort((pair1, pair2) => pair1[0] - pair2[0]);
    // 开始寻找分割
    let splitEdges: ZEdge[] = [];
    let startX: number = 0;
    let endX: number = sofaBackEdge.length;
    for(let index = 0; index < splitPairs.length; ++index)
    {
        let split1: number = splitPairs[index][0];
        let split2: number = splitPairs[index][1];
        let nextSplit1: number = null;
        let nextSplit2: number = null;
        if(index != splitPairs.length - 1)
        {
            nextSplit1 = splitPairs[index + 1][0];
            nextSplit2 = splitPairs[index + 1][1];
            endX = nextSplit1;
        }
        if(split1 - startX > 0.5)
        {
            let unProjectVec1: Vector3 = sofaBackEdge.unprojectEdge2d({x: startX, y: 0});
            let unProjectVec2: Vector3 = sofaBackEdge.unprojectEdge2d({x: split1, y: 0});
            let splitEdge: ZEdge = new ZEdge({pos: unProjectVec1}, {pos: unProjectVec2});
            splitEdge.computeNormal();
            splitEdges.push(splitEdge);
        }
        if(endX - split2 > 0.5)
        {
            let unProjectVec1: Vector3 = sofaBackEdge.unprojectEdge2d({x: split2, y: 0});
            let unProjectVec2: Vector3 = sofaBackEdge.unprojectEdge2d({x: endX, y: 0});
            let splitEdge: ZEdge = new ZEdge({pos: unProjectVec1}, {pos: unProjectVec2});
            splitEdge.computeNormal();
            splitEdges.push(splitEdge);
        }
        if(nextSplit2)
        {
            startX = nextSplit2;
            endX = sofaBackEdge.length;
        }
    }
    splitEdges.sort((edge1, edge2) => edge2.length - edge1.length);
    if(tvCabinet) // TDOO 这个还需要添加
    {
        let tvCabinetCenter: Vector3 = tvCabinet.rect_center.clone();
        for(let splitEdge of splitEdges)
        {
            let projectInfo: any = splitEdge.projectEdge2d({x: tvCabinetCenter.x, y: tvCabinetCenter.y});
            if(projectInfo.x < 0 || projectInfo.x > splitEdge.length)
            {
                continue;
            }
            return splitEdge;
        }
    }
    if(splitEdges.length == 0)
    {
        return sofaBackEdge;
    }
    return splitEdges[0];
}

function isOutSideLayonRoomEdgeBySofaBackEdge(sofaBackEdge: ZEdge, roomPoly: ZPolygon): boolean
{
    for(let index = 0; index < roomPoly.edges.length; ++index)
    {
        let roomEdge: ZEdge = roomPoly.edges[index];
        if(roomEdge.nor.clone().dot(sofaBackEdge.nor) < 0.9 || !roomEdge.islayOn(sofaBackEdge, 10, 0.1))
        {
            continue;
        }
        let preRoomEdge: ZEdge = roomPoly.edges[(index - 1 + roomPoly.edges.length) % roomPoly.edges.length];
        let nextRoomEdge: ZEdge = roomPoly.edges[(index + 1) % roomPoly.edges.length];
        let isPreOutSide: boolean = isOutSideCorner(roomEdge, preRoomEdge);
        let isNextOutSide: boolean = isOutSideCorner(nextRoomEdge, roomEdge);
        return isPreOutSide || isNextOutSide;
    }
    return false;
}

function calAreaInfoForSpaceAreas(allSpacePolys: any[][]): any[][]
{
    let allSpaceAreaInfos: any[] = [];
    for(let spacePolys of allSpacePolys)
    {
        if(!spacePolys)
        {
            continue;
        }
        let spaceAreaInfos: any[] = [];
        for(let spacePoly of spacePolys)
        {
            let spaceArea: number = TBaseRoomToolUtil.instance.calPolygonArea(spacePoly.subPoly);
            let spaceAreaInfo: any = {
                spacePoly: spacePoly,
                spaceArea: spaceArea
            }
            spaceAreaInfos.push(spaceAreaInfo);
        }
        allSpaceAreaInfos.push(spaceAreaInfos);
    }
    return allSpaceAreaInfos;
}

function isLayonOrFaceKitchenDoor(poly: ZPolygon, params: any, isFace: boolean = true): boolean
{
    let kitchenWindows: I_Window[] = params.windows.filter((door: any) => door.type == "Window" && door.room_names.includes("厨房"));
    let kitchenDoors: I_Window[] = params.doors.filter((door: any) => door.type == "Door" && door.room_names.includes("厨房"));
    let isLayonKitchen: boolean = false;
    let layonInfo: {isLayon: boolean, layonEdge: ZEdge} = isLayonWindows(poly, [...kitchenDoors, ...kitchenWindows], false);
    if(layonInfo.isLayon)
    {
        let isFindNearEdge: boolean = false;
        for(let polyEdge of poly.edges)
        {
            if(layonInfo.layonEdge == polyEdge)
            {
                continue;
            }
            if(layonInfo.layonEdge.islayOn(polyEdge, TTinyLivingRoomByPathRelation.minLivingAreaMinLength, 0.1))
            {
                isFindNearEdge = true;
                break;
            }
        }
        isLayonKitchen = !isFindNearEdge;
    }
    return isLayonKitchen || (isFace && isFaceWindows(poly, [...kitchenDoors, ...kitchenWindows]));
}

function isFaceWindows(poly: ZPolygon, windows: I_Window[]): boolean
{
    let polyRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(poly);
    for(let window of windows)
    {
        let windowRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(window.rect);
        if((windowRange.xMax > polyRange.xMin && windowRange.xMin < polyRange.xMax) || (windowRange.yMax > polyRange.yMin && windowRange.yMin < polyRange.yMax))
        {
            return true;
        }
    }
    return false;
}

function computeLivingRoomScore(params: I_TinyLivingRoomByPathParams): number
{
    // 这里目前暂餐厅区靠近厨房门分值会高，客厅区靠近阳台门分值会高
    let kitchenWindowOrDoors: I_Window[] = params.doors.filter(win => win.room_names.includes("厨房"));
    kitchenWindowOrDoors.push(...params.windows.filter(win => win.room_names.includes("厨房")));
    let blaconyDoors: I_Window[] = params.doors.filter(win => win.room_names.includes("阳台"));
    let diningTableGroup: ZRect = params.dinning_table_rect;
    let sofaGroup: ZRect = params.sofa_group_rect;
    let minDistToKitchen: number = null;
    if(diningTableGroup)
    {
        for(let kitchenWin of kitchenWindowOrDoors)
        {
            let dist: number = TBaseRoomToolUtil.instance.calDistanceByPolygons(diningTableGroup, kitchenWin.rect);
            if(minDistToKitchen == null || minDistToKitchen > dist)
            {
                minDistToKitchen = dist;
            }
        }

    }
    let minDistToBlacony: number = null;
    if(sofaGroup)
    {
        for(let blaconyDoor of blaconyDoors)
        {
            let dist: number = TBaseRoomToolUtil.instance.calDistanceByPolygons(sofaGroup, blaconyDoor.rect);
            if(minDistToBlacony == null || minDistToBlacony > dist)
            {
                minDistToBlacony = dist;
            }
        }
    }
    let score: number = 0;
    if(minDistToKitchen)
    {
        score += 1 / minDistToKitchen;
    }
    else if(diningTableGroup)
    {
        let entranceDoors: I_Window[] = params.doors.filter(door => 
            (door.room_names.includes("入户花园") || (door.room_names.length == 1 && door.room_names.includes("客餐厅"))));
        let minDistToEntrance: number = null;
        for(let entranceDoor of entranceDoors)
        {
            let dist: number = entranceDoor.rect.rect_center.distanceTo(diningTableGroup.rect_center);
            if(minDistToEntrance == null || minDistToEntrance > dist)
            {
                minDistToEntrance = dist;
            }
        }
        if(minDistToEntrance)
        {
            score += 1 / minDistToEntrance;
        }
    }
    if(minDistToBlacony)
    {
        score += 1 / minDistToBlacony;
    }
    return score;
}

function isHighSimilarity(scheme1: any, scheme2: any, distTol: number): boolean
{
    let groupTemplates1: TGroupTemplate[] = scheme1.group_templates;
    let groupTemplates2: TGroupTemplate[] = scheme2.group_templates;
    if(groupTemplates1.length != groupTemplates2.length)
    {
        return false;
    }
    else
    {
        // 需要确保所有的类型都是一直的然后再检查家具具体类型不一致
        let sameCodeNum: number = 0;
        for(let groupTemplate of groupTemplates1)
        {
            let otherSameGroupCodeTemoplate: TGroupTemplate = groupTemplates2.find(otherGroupTemplate => otherGroupTemplate.group_space_category == groupTemplate.group_space_category);
            if(!otherSameGroupCodeTemoplate)
            {
                return false;
            }
            if(groupTemplate.seed_figure_group.group_code == otherSameGroupCodeTemoplate.seed_figure_group.group_code && groupTemplate._target_rect.rect_center.distanceTo(otherSameGroupCodeTemoplate._target_rect.rect_center) < distTol)
            {
                sameCodeNum += 1;
            }
        }
        let ratio: number = sameCodeNum / groupTemplates1.length;
        if(ratio >= 0.9)
        {
            return true;
        }
    }
    return false;
}

// 对于没有动线的区域分割应当是存在策略地进行区域分割，
function markLivingAndDingAreaResults(params: I_TinyLivingRoomByPathParams, subPolys: ZPolygon[]): I_TinyLivingRoomByPathParams[]
{
    if(!subPolys || subPolys.length == 0)
    {
        return null;
    }
    subPolys.sort((poly1: ZPolygon, poly2: ZPolygon) => TBaseRoomToolUtil.instance.calPolygonArea(poly2) - TBaseRoomToolUtil.instance.calPolygonArea(poly1));
    let isMaxSplit: boolean = subPolys.length == 1;
    let candidateSubPolys: ZPolygon[] = [];
    if(subPolys.length > 1)
    {
        let firstArea: number = TBaseRoomToolUtil.instance.calPolygonArea(subPolys[0]);
        let secondArea: number = TBaseRoomToolUtil.instance.calPolygonArea(subPolys[1]);
        let maxInnerArea: number = TBaseRoomToolUtil.instance.getMaxInnerRectByPolygon(subPolys[0]).area;
        let secondMaxInnerRect: ZRect = TBaseRoomToolUtil.instance.getMaxInnerRectByPolygon(subPolys[1]);
        if(secondArea / firstArea < 0.18 ||(secondArea / firstArea < 0.3 &&  maxInnerArea / firstArea < 0.8))
        {
            isMaxSplit = true;
            if((isLayonOrFaceKitchenDoor(subPolys[1], params, false) && secondArea / firstArea >= 0.18) || secondMaxInnerRect.area / maxInnerArea > 0.4)
            {
                isMaxSplit = false;
            }
        }
        else
        {
            if(!isMaxSplit)
            {
                let isMaxAreaLayonKitchen = (isLayonOrFaceKitchenDoor(subPolys[0], params, false) && secondArea / firstArea < 0.5) ;
                if(isMaxAreaLayonKitchen)
                {
                    isMaxSplit = true;
                }
            }
        }
        if(secondMaxInnerRect.min_hh / secondMaxInnerRect.max_hh < 0.2)
        {
            isMaxSplit = true;
        }
        if(!isMaxSplit)
        {
            candidateSubPolys = subPolys.slice(0, 2);
        }
    }
    if(isMaxSplit)
    {
        candidateSubPolys = [];
        // 对最大区域进行阳角切割区域以及对半分
        // 这里判断是否对区域进行阳角分割还是对半分割主要看这个区域的最大矩形是否与多边形的差距过大
        let maxInnerRect: ZRect = TBaseRoomToolUtil.instance.getMaxInnerRectByPolygon(subPolys[0]);
        let maxInnerRectArea: number = maxInnerRect.area;
        let polyArea: number = TBaseRoomToolUtil.instance.calPolygonArea(subPolys[0]);
        let isSucessSplitByOutSideCorner: boolean = false;
        if(maxInnerRectArea / polyArea < 0.9)
        {
            // 阳角分割
            let splitOutSideCorner: ZPolygon[] = splitAreaByOutSideCorner(subPolys[0]);
            if(splitOutSideCorner.length > 0)
            {
                isSucessSplitByOutSideCorner = true;
                candidateSubPolys.push(...splitOutSideCorner);
            }
        }
        if(maxInnerRectArea / polyArea > 0.9 || !isSucessSplitByOutSideCorner)
        {
            // 打印日志
            // logRoomAndPolys(params._room.room_shape._poly, [subPolys[0]], "绘制最大区域");

            // 对半分
            let splitHalfPolyInfos: ZPolygon[] = splitHalfPoly(subPolys[0]);
            if(splitHalfPolyInfos)
            {
                candidateSubPolys.push(...splitHalfPolyInfos);
            }
        }
    }
    let candidateAreaInfos: any[] = [];
    for(let candidateSubPoly of candidateSubPolys)
    {
        let mainRect: ZRect = TBaseRoomToolUtil.instance.getMaxInnerRectByPolygon(candidateSubPoly);
        candidateAreaInfos.push({
            subPoly: candidateSubPoly,
            subMainRect: mainRect,
        });
    }
    logRoomAndPolys(params._room.room_shape._poly, candidateSubPolys, "绘制最大区域");
    let results: I_TinyLivingRoomByPathParams[] = [];
    let livingSubPolyInfo: any = getLivingSubPolyInfo(candidateAreaInfos, params);
    let diningSubPolyInfo: any = candidateAreaInfos.find(info => livingSubPolyInfo.subPoly != info.subPoly);
    let result: I_TinyLivingRoomByPathParams = {
        livingPolyInfo: {
            subPoly: livingSubPolyInfo.subPoly,
            subMainRect: livingSubPolyInfo.subMainRect,
        },
        diningPolyInfo: {
            subPoly: diningSubPolyInfo.subPoly,
            subMainRect: diningSubPolyInfo.subMainRect,
        }
    }
    results.push(result);
    return results;
}

function splitHalfPoly(spaceAreaPoly: ZPolygon): ZPolygon[]
{
    let spaceMainRect: ZRect = TBaseRoomToolUtil.instance.getMaxInnerRectByPolygon(spaceAreaPoly);
    let spaceEdge1: ZEdge = spaceMainRect.leftEdge;
    let spaceEdge2: ZEdge = spaceMainRect.frontEdge;
    let bestSplitEdge: ZEdge = spaceEdge1;
    if(spaceEdge2.length > spaceEdge1.length)
    {
        bestSplitEdge = spaceEdge2;
    }
    let spaceEdgeNegateNor: Vector3 = bestSplitEdge.nor.clone().negate();
    let spaceEdgeCenter: Vector3 = bestSplitEdge.center.clone();
    let splitRect: ZRect = getSplitRectInPoly(spaceAreaPoly, spaceEdgeCenter, spaceEdgeNegateNor, bestSplitEdge.dv);
    // logRoomAndPolys(spaceAreaPoly, [splitRect], "绘制分割区域");
    let splitTwoAreas: any = splitgetValidSplitTwoSubArea(spaceAreaPoly, splitRect);
    if(!splitTwoAreas)
    {
        return null;
    }
    return [splitTwoAreas[0].subPoly, splitTwoAreas[1].subPoly];
}

function splitAreaByOutSideCorner(spaceAreaPoly: ZPolygon): ZPolygon[]
{
    let spaceAreas: ZPolygon[] = [];
    let minLen: number = null;
    for(let i = 0; i < spaceAreaPoly.edges.length; ++i)
    {
        let nextIndex: number = (i + 1) % spaceAreaPoly.edges.length;
        if(!isOutSideCorner(spaceAreaPoly.edges[i], spaceAreaPoly.edges[nextIndex]))
        {
            continue;
        }
        let spaceEdgeRect1: ZRect = createSplitEdgeRect(spaceAreaPoly.edges[i], spaceAreaPoly, false);
        let spaceEdgeRect2: ZRect = createSplitEdgeRect(spaceAreaPoly.edges[nextIndex], spaceAreaPoly, true);
        let bestSpaceEdgeRect: ZRect = null;
        if(spaceEdgeRect1)
        {
            bestSpaceEdgeRect = spaceEdgeRect1;
        }
        else
        {
            bestSpaceEdgeRect = spaceEdgeRect2;
        }
        if(spaceEdgeRect1 && spaceEdgeRect2)
        {
            if(spaceEdgeRect1.max_hh > spaceEdgeRect2.max_hh)
            {
                bestSpaceEdgeRect = spaceEdgeRect2;
            }
        }

        // logRoomAndPolys(spaceAreaPoly, [bestSpaceEdgeRect], "绘制分割区域");

        let splitTwoAreas: any[] = splitgetValidSplitTwoSubArea(spaceAreaPoly, bestSpaceEdgeRect);
        if(splitTwoAreas)
        {
            if(minLen == null || minLen > bestSpaceEdgeRect.max_hh)
            {
                minLen = bestSpaceEdgeRect.max_hh;
                spaceAreas = [splitTwoAreas[0].subPoly, splitTwoAreas[1].subPoly];
            }
        }
        else
        {
            let otheSpaceEdgeRect: ZRect = [spaceEdgeRect1, spaceEdgeRect2].find(rect => rect != bestSpaceEdgeRect);
            splitTwoAreas = splitgetValidSplitTwoSubArea(spaceAreaPoly, otheSpaceEdgeRect);
            if(splitTwoAreas)
            {
                if(minLen == null || minLen > otheSpaceEdgeRect.max_hh)
                {
                    minLen = otheSpaceEdgeRect.max_hh;
                    spaceAreas = [splitTwoAreas[0].subPoly, splitTwoAreas[1].subPoly];
                }
            }
        }
    }
    return spaceAreas;
}

function splitRoomPolyBestRatio(roomPoly: ZPolygon, params: I_TinyLivingRoomByPathParams): ZPolygon[]
{
    return [roomPoly];
}