
import { AI2DesignBasicModes, CadBatchDrawingLayerType, CadDrawingLayerType, DefaultFigureXml, EventName, FigureDataList, FloorImagePaths, IndexedDBService, ModelLocPublicCategoryMap, TAppManagerBase, TCadCabinetLayer, TCadCeilingLayer, TCadCopyImageLayer, TCadElectricityDrawingLayer, TCadFloorDrawingLayer, TCadFurnitureLayer, TCadOutLineLayer, TCadRoomDecoratesLayer, TCadRoomLightingLayer, TCadRoomNameLayer, TCadRoomStrucureLayer, TCadSubRoomAreaDrawingLayer, TDrawingBatchLayer, TDrawingLayer, TExportCadDrawingLayer, TExtDrawingDrawingLayer, TEzdxfDataDrawingLayer, TGroupTemplate, TLayoutSolverBase, TR<PERSON>rLayer, TSerialSizeRangeDB, TSeriesFigureGroupDB, g_FigureImagePaths } from "@layoutai/layout_scheme";
import { compareNames, loadImage } from "@layoutai/z_polygon";
import { AI2BaseModeHandler } from "./AI2BaseModeHandler";
import { AICadEditModeHandler } from "./AICadEditModeHandler";
import {TSwjLayoutGraphSolver} from "@layoutai/layout_solver"

/**
 *  AI空间设计
 */
export class AISpaceDesignManager extends TAppManagerBase
{
    static AppName: string = 'AISpaceDesign';
    _visible_layers: TDrawingLayer[];

    constructor()
    {
        super(AISpaceDesignManager.AppName);
        this._layout_graph_solver = TLayoutSolverBase.instance || new TSwjLayoutGraphSolver();
    }
    public init(): void {
        super.init();
    
        this.initLayers();
      }
    
      initElements(canvas: HTMLCanvasElement = null) {
        super.initElements(canvas);
        this.drawing_layers = {};
      }
    
      /**
       *  初始化图层
       */
      initLayers() {
        // 目前只初始化两个图层
    
        this.drawing_layers = {};
        this.drawing_layers[CadDrawingLayerType.CadFloorDrawing] = new TCadFloorDrawingLayer(this);
        this.drawing_layers[CadDrawingLayerType.CadEzdxfDrawing] = new TEzdxfDataDrawingLayer(this); // ezdxf json数据层
    
        this.drawing_layers[CadDrawingLayerType.CadFurniture] = new TCadFurnitureLayer(this); //
        this.drawing_layers[CadDrawingLayerType.CadCabinet] = new TCadCabinetLayer(this); //
        this.drawing_layers[CadDrawingLayerType.CadRoomStrucure] = new TCadRoomStrucureLayer(this); //
        this.drawing_layers[CadDrawingLayerType.CadDecorates] = new TCadRoomDecoratesLayer(this); //
        this.drawing_layers[CadDrawingLayerType.CadElectricity] = new TCadElectricityDrawingLayer(this); //
        this.drawing_layers[CadDrawingLayerType.CadLighting] = new TCadRoomLightingLayer(this); //
        this.drawing_layers[CadDrawingLayerType.CadCeiling] = new TCadCeilingLayer(this); //
    
        this.drawing_layers[CadDrawingLayerType.CadOutLine] = new TCadOutLineLayer(this); //
        this.drawing_layers[CadDrawingLayerType.CadRoomName] = new TCadRoomNameLayer(this); //
        this.drawing_layers[CadDrawingLayerType.CadSubRoomAreaDrawing] =
          new TCadSubRoomAreaDrawingLayer(this);
    
    
        this.drawing_layers[CadDrawingLayerType.ExtDrawingDrawing] = new TExtDrawingDrawingLayer(this);
        this.drawing_layers[CadDrawingLayerType.CadCopyImageDrawing] = new TCadCopyImageLayer(this);
        this.drawing_layers[CadDrawingLayerType.RulerDrawing] = new TRulerLayer(this);
        this.drawing_layers[CadDrawingLayerType.CadEzdxfDrawing].visible = false;
        this.drawing_layers[CadDrawingLayerType.CadSubRoomAreaDrawing].visible = false;
        this.drawing_layers[CadDrawingLayerType.CadLighting].visible = false;
        this.drawing_layers[CadDrawingLayerType.CadDecorates].visible = false;
    
        this.drawing_layers[CadDrawingLayerType.ExportCadDrawing] = new TExportCadDrawingLayer(this);
        this.drawing_layers[CadDrawingLayerType.ExportCadDrawing].visible = false;
        // 拆改图层
        this.drawing_layers[CadDrawingLayerType.Remodeling] = new TDrawingLayer(
          CadDrawingLayerType.Remodeling,
          this
        );
    
        this._batch_drawing_layers = {};
        this._batch_drawing_layers[CadBatchDrawingLayerType.AICadDefaultBatch] = new TDrawingBatchLayer(
          CadBatchDrawingLayerType.AICadDefaultBatch,
          [
            this.drawing_layers[CadDrawingLayerType.CadCopyImageDrawing],
            this.drawing_layers[CadDrawingLayerType.CadFloorDrawing],
            this.drawing_layers[CadDrawingLayerType.CadFurniture],
            this.drawing_layers[CadDrawingLayerType.CadCabinet],
            this.drawing_layers[CadDrawingLayerType.CadRoomStrucure],
            this.drawing_layers[CadDrawingLayerType.CadDecorates],
            this.drawing_layers[CadDrawingLayerType.CadElectricity],
            this.drawing_layers[CadDrawingLayerType.CadLighting],
            this.drawing_layers[CadDrawingLayerType.CadCeiling],
            this.drawing_layers[CadDrawingLayerType.CadOutLine],
            this.drawing_layers[CadDrawingLayerType.CadRoomName],
            this.drawing_layers[CadDrawingLayerType.CadSubRoomAreaDrawing],
            this.drawing_layers[CadDrawingLayerType.CadEzdxfDrawing],
            this.drawing_layers[CadDrawingLayerType.RulerDrawing],
            this.drawing_layers[CadDrawingLayerType.CadDimensionWallElement],
            this.drawing_layers[CadDrawingLayerType.CadDimensionOutterWallElement]
          ].filter((layer)=>layer),
          this
        );
    
        this._batch_drawing_layers[CadBatchDrawingLayerType.ExtDrawingBatch] = new TDrawingBatchLayer(
          CadBatchDrawingLayerType.ExtDrawingBatch,
          [
            this.drawing_layers[CadDrawingLayerType.CadSubRoomAreaDrawing],
            this.drawing_layers[CadDrawingLayerType.ExtDrawingDrawing]
          ],
          this
        );
    
        let t_count = Object.keys(this.drawing_layers).length;
        let t_i = 0;
        for (let key in this.drawing_layers) {
          this.drawing_layers[key].z_index = -t_count + t_i;
          t_i++;
        }
    
        this.updateVisibleLayers();
      }
    
      initHandlers() {
        this.handlers = {};
    
        this.handlers[AI2DesignBasicModes.AiCadMode] = new AICadEditModeHandler(this);

    
        for (let key in this.handlers) {
          this.handlers[key].name = key;
        }
      }
    
      updateVisibleLayers() {
        this._visible_layers = [];
        for (let key in this.drawing_layers) {
          if (this.drawing_layers[key].visible) {
            this._visible_layers.push(this.drawing_layers[key]);
          }
        }
      }
    
      protected async preparePatterns() {
        await this.painter.prepareDefaultBlocks("./static/db/ezdxf_blocks_default.json");
    
        let pattern_img_paths = g_FigureImagePaths;
        this.painter._svg_pattern_dict = {};
        let figure_xml: string = DefaultFigureXml;
        if (figure_xml) {
          let xml_document = new DOMParser().parseFromString(figure_xml, 'application/xml');
    
          let svg_elements = xml_document.getElementsByTagName('svg');
    
          for (let svg_element of svg_elements) {
            let figure_name = svg_element.getAttribute('figure_name');
            if (figure_name) {
              let elements = this.painter.computeSvgDrawingElement(svg_element);

              this.painter._svg_pattern_dict[figure_name] = {
                drawing_elements: elements
              };
            }
          }
        }
        for (let key in pattern_img_paths) {
          let data = pattern_img_paths[key];
          if (!data.img_path || data.img_path.length == 0) continue;
          if (this.painter._svg_pattern_dict[key] || this.painter._svg_pattern_dict[data.modelLoc])
            continue;
    
          let img = await loadImage(data.img_path);
    
          if (this.painter != null && img) {
            this.painter.setPattern(key, img, 'no-repeat');
          }
        }
        let pattern_floorimg_paths: { [key: string]: { img_path?: string } } = FloorImagePaths;
    
        for (let key in pattern_floorimg_paths) {
          let data = pattern_floorimg_paths[key];
          if (!data.img_path || data.img_path.length == 0) continue;
    
          let img = await loadImage(data.img_path);
    
          if (this.painter != null && img) {
            this.painter.setPattern(key, img, 'repeat');
          }
        }
        this.update();
      }
    
      onLayerVisibilityChanged(): void {
        let state: { [key: string]: boolean } = {};
    
        for (let layer_name in this.drawing_layers) {
          let layer = this.drawing_layers[layer_name];
          state[layer_name] = layer.visible;
        }
    
        this.EventSystem.emit_M(EventName.SwitchDrawingLayer, state);
      }
      _updateGroupTemplateImages(
        data_list: {
          image?: string;
          group_code?: string;
          title?: string;
          length?: number;
          depth?: number;
        }[]
      ) {
        if (!this.painter) return;
        let ts = this.painter.exportTransformData();
        let main_canvas = this.painter._canvas;
        let canvas: HTMLCanvasElement = document.createElement('canvas');
    
        this.painter.bindCanvas(canvas);
        for (let data of data_list) {
          if (data.group_code && !data.image) {
            let res = TGroupTemplate.getGroupTemplateImageByGroupCode(
              data.group_code,
              this.painter,
              canvas,
              data.length,
              data.depth
            );
            data.image = res.img_path;
    
            TGroupTemplate.GroupCodeUiInfo[data.group_code] = {
              image: data.image,
              title: data.title,
              default_length: data.length,
              default_depth: data.depth
            };
          }
        }
    
        this.painter.bindCanvas(main_canvas);
        this.painter.importTransformData(ts);
      }
    
      /**
       *  数据准备
       */
      async prepare(): Promise<void> {
        let model_loc_map = new ModelLocPublicCategoryMap();
        await model_loc_map.prepare_fromAiDesk();

        await IndexedDBService.instance.openDB();
        
        await TSerialSizeRangeDB.LoadSeries();
    
        await TSeriesFigureGroupDB.LoadSeries();
    
        await this.preparePatterns().then(() => {
          // 绘制组合模板的图片
    
          let group_template_data_list: any = [];
    
          let visit_data_list = (
            s_data_list: { label: string; child: []; figureList: { group_code?: string }[] }[]
          ) => {
            if (!s_data_list) return;
            for (let data of s_data_list) {
              if (!data) {
                console.log(s_data_list, data);
                continue;
              }
              if (data.figureList) {
                for (let figure of data.figureList) {
                  if (figure.group_code) {
                    group_template_data_list.push(figure);
                  }
                }
              }
              visit_data_list(data.child);
            }
          };
          visit_data_list(FigureDataList as any);
          this._updateGroupTemplateImages(group_template_data_list);
        });
    
        if (this._current_handler) {
          await this._current_handler.prepare(true);
        }
        await super.prepare();
      }
    
      /**
       *  绘制函数
       */
      onDraw() {
        if (!this.painter) return;
        this.painter.clean();
    
        this.painter.enter_drawpoly();
        if (this._current_handler) {
          this._current_handler.drawCanvas();
        }
    
        this.painter.leave_drawpoly();
      }


    
}