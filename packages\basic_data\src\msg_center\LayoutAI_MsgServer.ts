import { I_LayoutAI_MsgData, LayoutAI_MsgCenter } from "./LayoutAI_MsgCenter";

export type AsyncProcessor = (data?: any) => Promise<any>;

/**
 *   作为layoutAI端的处理器
 */
export class LayoutAI_MsgServer extends LayoutAI_MsgCenter {
    protected _async_processors: { [key: string]: AsyncProcessor };

    protected _commands_dict: { [key: string]: () => void };

    protected _async_queue: { msgData: I_LayoutAI_MsgData }[];

    protected _active_data: I_LayoutAI_MsgData = null;

    postMessage : (data:I_LayoutAI_MsgData)=>void = (data)=>{};
    constructor() {
        super();
        this._async_processors = {};
        this._commands_dict = {};
        this._async_queue = [];

        this.initDefaultProcessors();
    }
    init() {
        super.init();
        if (window.parent) {
            let data: I_LayoutAI_MsgData = {
                msgType: "Connected"
            }
            window.parent.postMessage(data, "*");
        }
    }
    addAsyncProcessor(name: string, processor: AsyncProcessor) {
        this._async_processors[name] = processor;
    }
    addCommand(name: string, func: () => void) {
        this._commands_dict[name] = func;
    }
    initDefaultProcessors() {

    }
    static makeInstance(force = false, callback: () => void = null): void {

        if (force || !LayoutAI_MsgCenter._instance) {
            LayoutAI_MsgCenter._instance = new LayoutAI_MsgServer();
            if (callback) {
                callback();
            }
        }
    }

    protected processCommand(data: I_LayoutAI_MsgData) {
        if (this._commands_dict[data.command]) {
            this._commands_dict[data.command]();
        }
        else {

        }

    }

    protected handleEvent(data: I_LayoutAI_MsgData) {

    }

    public async processAsyncMsg(data: I_LayoutAI_MsgData) {
        this._async_queue.push({ msgData: data });
        this.startProcessAsyncMsg();
    }

    protected startProcessAsyncMsg() {
        if (this._active_data) return;
        if (this._async_queue[0]) {
            this._exactProcessAsyncMsg(this._async_queue[0].msgData);
        }
    }
    protected onDoneMsgData(data: I_LayoutAI_MsgData) {
        if (this._active_data) this._active_data = null;
        if (this._async_queue.length > 0) {
            this._async_queue.splice(0, 1);

            this.startProcessAsyncMsg();
        }


    }
    protected async _exactProcessAsyncMsg(data: I_LayoutAI_MsgData) {
        let result_data: I_LayoutAI_MsgData = {
            msgType: "Async",
            asyncId: data.asyncId,  // 共享同一个id
            asyncEventName: data.asyncEventName,
            asyncResult: null
        }
        this._active_data = data;
        if (this._async_processors[data.asyncEventName]) {

            try {
                let res = await (this._async_processors[data.asyncEventName](data));
                result_data.asyncResult = res;
                result_data.asyncState = 1;
            } catch (error: any) {
                console.log(error);
                result_data.aysncError = "Error: In Process!";
                result_data.asyncState = -1;
            }
            this.onDoneMsgData(result_data);
            this.postMessage(result_data);
        }
        else {
            result_data.aysncError = "Error: No Processor!---" + data.asyncEventName;
            result_data.asyncState = -1;
            this.onDoneMsgData(result_data);
            this.postMessage(result_data);
        }
    }
}