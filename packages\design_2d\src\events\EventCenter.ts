import { IEventCenter } from './IEventCenter';
import { CanvasEventType } from './const/CanvasEventType';

/**
 * 事件数据接口
 */
export interface IEventData {
    [key: string]: any;
}

/**
 * 事件中心
 * 负责处理自定义事件和canvas事件的分发
 */
export class EventCenter implements IEventCenter {
    private _canvas: HTMLCanvasElement | null = null;
    private _isInit: boolean = false;
    private _canvasEventListeners: Map<string, Set<(event: Event) => void>> = new Map();
    private _eventHandlers: Map<string, (event: Event) => void> = new Map();
    
    // 自定义事件监听器
    private _customEventListeners: Map<string, Set<(data?: IEventData) => void>> = new Map();

    constructor() {
        // 移除对 EventEmitter 的继承
    }

    /**
     * 初始化事件中心
     * @param canvas HTMLCanvasElement
     */
    public init(canvas: HTMLCanvasElement): void {
        if (this._isInit) {
            return;
        }
        this._isInit = true;
        this._canvas = canvas;
        this._setupCanvasEventListeners();
    }

    /**
     * 设置Canvas事件监听器
     */
    private _setupCanvasEventListeners(): void {
        if (!this._canvas) return;

        // 鼠标事件
        this._addCanvasEventListener('mousedown', CanvasEventType.MOUSE_DOWN);
        this._addCanvasEventListener('mousemove', CanvasEventType.MOUSE_MOVE);
        this._addCanvasEventListener('mouseup', CanvasEventType.MOUSE_UP);
        this._addCanvasEventListener('click', 'click');
        this._addCanvasEventListener('dblclick', 'dblclick');
        this._addCanvasEventListener('contextmenu', CanvasEventType.CONTEXT_MENU);

        // 滚轮事件
        this._addCanvasEventListener('wheel', CanvasEventType.WHEEL);

        // 键盘事件
        this._addCanvasEventListener('keydown', CanvasEventType.KEY_DOWN);
        this._addCanvasEventListener('keyup', CanvasEventType.KEY_UP);
        this._addCanvasEventListener('keypress', CanvasEventType.KEY_PRESS);

        // 焦点事件
        this._addCanvasEventListener('focus', 'focus');
        this._addCanvasEventListener('blur', 'blur');

        // 触摸事件
        this._addCanvasEventListener('touchstart', CanvasEventType.TOUCH_START);
        this._addCanvasEventListener('touchmove', CanvasEventType.TOUCH_MOVE);
        this._addCanvasEventListener('touchend', CanvasEventType.TOUCH_END);
    }

    /**
     * 添加Canvas事件监听器
     */
    private _addCanvasEventListener(eventName: string, eventType: string): void {
        if (!this._canvas) return;

        const handler = (event: Event) => {
            this.emit(eventType, event);
        };

        this._canvas.addEventListener(eventName, handler);
        this._eventHandlers.set(eventName, handler);
    }

    /**
     * 移除Canvas事件监听器
     */
    private _removeCanvasEventListeners(): void {
        if (!this._canvas) return;

        this._eventHandlers.forEach((handler, eventName) => {
            this._canvas?.removeEventListener(eventName, handler);
        });
        this._eventHandlers.clear();
    }

    /**
     * 监听Canvas事件
     * @param eventType 事件类型
     * @param handler 事件处理函数
     */
    public onCanvas<T extends Event>(eventType: string, handler: (event: T) => void): void {
        const handlers = this._canvasEventListeners.get(eventType) || new Set();
        handlers.add(handler as any);
        this._canvasEventListeners.set(eventType, handlers);

        // 监听事件中心的事件
        this.on(eventType, handler as any);
    }

    /**
     * 移除Canvas事件监听
     * @param eventType 事件类型
     * @param handler 事件处理函数
     */
    public offCanvas<T extends Event>(eventType: string, handler: (event: T) => void): void {
        const handlers = this._canvasEventListeners.get(eventType);
        if (handlers) {
            handlers.delete(handler as any);
        }
        this.off(eventType, handler as any);
    }

    /**
     * 发送自定义事件
     * @param eventName 事件名称
     * @param data 事件数据
     */
    public emit(eventName: string, data?: IEventData): boolean {
        const listeners = this._customEventListeners.get(eventName);
        if (listeners) {
            for(let listener of listeners) {
                try {
                    listener(data);
                } catch (error) {
                    console.error(`Error in event listener for ${eventName}:`, error);
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 监听自定义事件
     * @param eventName 事件名称
     * @param listener 监听器函数
     */
    public on(eventName: string, listener: (data?: IEventData) => void): void {
        let listeners = this._customEventListeners.get(eventName);
        if (!listeners) {
            listeners = new Set();
            this._customEventListeners.set(eventName, listeners);
        }
        listeners.add(listener);
    }

    /**
     * 移除事件监听
     * @param eventName 事件名称
     * @param listener 监听器函数
     */
    public off(eventName: string, listener?: (data?: IEventData) => void): void {
        if (listener) {
            const listeners = this._customEventListeners.get(eventName);
            if (listeners) {
                listeners.delete(listener);
                if (listeners.size === 0) {
                    this._customEventListeners.delete(eventName);
                }
            }
        } else {
            this._customEventListeners.delete(eventName);
        }
    }

    /**
     * 一次性监听事件
     * @param eventName 事件名称
     * @param listener 监听器函数
     */
    public once(eventName: string, listener: (data?: IEventData) => void): void {
        const onceListener = (data?: IEventData) => {
            listener(data);
            this.off(eventName, onceListener);
        };
        this.on(eventName, onceListener);
    }

    /**
     * 移除所有事件监听
     */
    public removeAllListeners(): void {
        this._customEventListeners.clear();
    }

    /**
     * 销毁事件中心
     */
    public dispose(): void {
        this._removeCanvasEventListeners();
        this.removeAllListeners();
        this._canvasEventListeners.clear();
        this._isInit = false;
        this._canvas = null;
    }
}

// 默认会监听的canvas事件
export const defaultCanvasEventListeners = [
    CanvasEventType.MOUSE_UP,
    CanvasEventType.MOUSE_DOWN,
    CanvasEventType.MOUSE_MOVE,
    CanvasEventType.WHEEL,

    CanvasEventType.TOUCH_START,
    CanvasEventType.TOUCH_MOVE,
    CanvasEventType.TOUCH_END,

    CanvasEventType.KEY_DOWN,
    CanvasEventType.KEY_UP,
    CanvasEventType.KEY_PRESS,
]