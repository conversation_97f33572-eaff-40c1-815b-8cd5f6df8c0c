/**
 * 厨房基本逻辑: 
 * 基本约束：  
    --- 人要有900mm的活动空间---会比较舒适

    硬约束：
    1、炉灶不可以靠窗，因为要放烟机吊柜---（实在都是窗的，就算了）
    2、炉灶、水槽是必备项

    软约束：
    1、水槽尽可能近窗 （阳光烘干？跟马桶一样？）
    2、炉灶尽可能近烟道， 实际上是 烟机要近烟道；
    3、冰箱在新的布局中，最好有。
    4、铺满墙原则（减少空间浪费）

    厨房动线：  
    --- 取（冰箱、收纳高柜---至少900mm）
    --- 洗（米箱200mm->水槽650mm - 900mm， 以750mm算)
    --- 切（工具拉篮地柜等）--- 最少350mm宽  要放下一个砧板（一般300mm*300mm)
    --- 炒（炉灶地柜 --- 双炉灶一般要700mm）
    --- 盛 （剩余300mm能有空间最佳，也可以没有）

    ---> 900 + 200 + 750 + 350 + 700 + 300 = 3200mm  带冰箱-->最优动线长

    ---> 900 + 650 + 350 + 700 = 2600mm  带冰箱->最少动线长（无盛菜、无米箱）

    ---> 超出该值，先用柜子枚举扩大适配。 

    ---> 还不足以贴满墙？：  
        --->  按老关的视频和世文的规则，是补收纳高柜为主
        --->  不能补收纳高柜的地方， 补收纳地柜或吊柜。
    厨房的准出评分器：
    1、 空间干涉、挡门； 不能有柜子和柜子的干涉等。
    2、 除了转角柜，其他地柜面前(900mm）不能有柜子； 转角柜，要留出至少一半以上且350mm以上的开门区域。
    3、炉灶地柜不能在窗下，因为放不了烟机。

    厨房的推荐评分器：
    1、动线效率（  max(100%, 3200/（取、洗、切、炒 的路径总长度））
    2、空间利用率：  收纳柜面积 / 厨房总面积  
    3、是否有冰箱？
 */
import { I_OnLineFigureInterval, I_OnWall_DP_Data, TFigureElement, TGroupTemplate, TLayoutOptmizerOnWallRules, TRoom } from "@layoutai/layout_scheme";
import { ZEdge, ZPolygon, ZRect, compareNames, range_substract } from "@layoutai/z_polygon";
import { Vector3 } from "three";
    
type TriNeighborType = "Water"|"Fire"|"Ice"|"None";
interface I_TableTopSubRange {
    ll:number,
    rr:number,
    depth:number,
    start_corner?:boolean,
    end_corner?:boolean,
    start_neighbor ?: TriNeighborType,
    end_neighbor ?: TriNeighborType,
    front_sub_edge ?:ZEdge,
    front_table_edge?:ZEdge};
/**
 *   厨房布局的补全功能
 *    --->  确定好厨房三角:  冰箱、水槽、炉灶
 *      --->  基于厨房三角补全其他地柜
 *      --->  补全吊柜
 * 
 * 厨房：
 */

export interface I_CabinetIntervalData {
    name : string;
    length : number;
    center_x : number;
}

export interface I_CabinetIntervalArrayData {
    area_name ?: string;
    total_length : number;    
    intervals : I_CabinetIntervalData[];
}
export class TPostLayoutKitchenPart 
{
    private static _instance : TPostLayoutKitchenPart = null;

    static PropTableSubEdgeData = "PropTableSubEdgeData";


    private special_area_cabinet_array_dict : {[key:string]:{[key:string]:{[key:number]:I_CabinetIntervalArrayData}}} = null;

    tabletop_depth :number = 580;
    constructor()
    {
        this.special_area_cabinet_array_dict = {};

        this.initCabinetArrayDict();
    }

    static get instance()
    {
        if(!TPostLayoutKitchenPart._instance)
        {
            TPostLayoutKitchenPart._instance = new TPostLayoutKitchenPart();
        }
        return TPostLayoutKitchenPart._instance;
    }

    // 初始化区域地柜序列
    initCabinetArrayDict()
    {   
        this.addCabinetArrayToDict("主操作区地柜",{
            "工具地柜阵列":["工具拉篮地柜","消毒地柜","调味拉篮地柜","双门地柜","双门地柜","单门地柜"],
            "开门地柜阵列":["单门地柜","双门地柜","双门地柜","单门地柜"]


        });
        this.addCabinetArrayToDict("洗菜区地柜",{
            "抽屉地柜阵列":["抽屉地柜","双门地柜","双门地柜","单门地柜"],
            "开门地柜阵列":["单门地柜","双门地柜","双门地柜","单门地柜"]
        });
        this.addCabinetArrayToDict("基础阵列地柜",{
            "开门地柜阵列":["单门地柜","双门地柜","双门地柜","单门地柜"],
            "补板填充阵列":["地柜收口板","地柜收口板"]
        });

        this.addCabinetArrayToDict("基础阵列吊柜",{
            "开门吊柜阵列":["吊柜","吊柜","吊柜","吊柜","吊柜"]
        });

        console.log(this.special_area_cabinet_array_dict);
    }

    private addCabinetArrayToDict(area_name:string, array_group_names:{[key:string]:string[]})
    {
        this.special_area_cabinet_array_dict[area_name] = {};

        for(let name in array_group_names)
        {
            let ans_list : {[key:number]:I_CabinetIntervalArrayData} = {};

            this.special_area_cabinet_array_dict[area_name][name] = ans_list;

            let intervals :I_OnLineFigureInterval[] = [];

            for(let template_name of array_group_names[name])
            {
                intervals.push(TLayoutOptmizerOnWallRules.make_interval_of_group_category(template_name,"厨房"));
            }  
            let result_dp_data :{result:I_OnWall_DP_Data[]} = {result:[]};
            TLayoutOptmizerOnWallRules.optimize_online_intervals_dynamic_programming(intervals,5000,true,10,result_dp_data);

            if(result_dp_data.result)
            {

                for(let data of result_dp_data.result)
                {
                    let interval_array_data :I_CabinetIntervalArrayData={
                        area_name : name,
                        total_length : 0,
                        intervals:[]
                    }
                    if(data.curr_sum_length_error < 0.5)
                    {
                        for(let i in data.selected_lengths)
                        {
                            let t_l_data = data.selected_lengths[i];
                            let group_name = intervals[i]?.name;
                            if(!group_name) continue;
                            if(t_l_data.length > 1.)
                            {
                                interval_array_data.intervals.push({
                                    length : t_l_data.length,
                                    center_x : t_l_data.center_x,
                                    name : group_name
                                })
                            }
                        }
                    }
                    if(interval_array_data.intervals.length > 0)
                    {
                        interval_array_data.intervals.forEach((interval)=>interval_array_data.total_length+=interval.length);
                        ans_list[~~interval_array_data.total_length] = (interval_array_data);
                    }

                }

  
            }

        }
        // console.log(this.special_area_cabinet_array_dict);
    }

    // 初始化猜测厨房三点布局， 并更新为布局方案列表
    initGuessTriLayout(room:TRoom)
    {
        // 默认布局

        //

    }

    /**
     * 补全厨房的动态组合模板
     * ../../../../../..param room 
     * ../../../../../..param group_templates 
     */
    postInfillLayout(room:TRoom,group_templates:TGroupTemplate[]=null,fill_tabletop_only:boolean=false)
    {
        if(!group_templates)
        {
            group_templates = TGroupTemplate.extractGroupTemplates(room._furniture_list,"厨房");
        }

        let tri_templates = this.getTriTemplates(room,group_templates);
        console.log(tri_templates);
        if(!tri_templates) return null;
        // ice_template._target_rect = ice_template.current_s_group.group_rect.clone();
        // fire_template._target_rect = fire_template.current_s_group.group_rect.clone();
        // water_template._target_rect = water_template.current_s_group.group_rect.clone();

        let w_poly = room.room_shape?._feature_shape?._w_poly; 
        if(!w_poly) return null;

        // 找到合适的 连续铺满的边
        let wall_edges = this.getAttachedWallEdgeSubSet(w_poly,tri_templates);

        if(!wall_edges || wall_edges.length ==0) return null;
        
        // 先定台面---即操作面

        // 台面的深度是600mm
      
        this.postInfillTableTop(room,tri_templates,wall_edges);

        if(!fill_tabletop_only)
        {
            return   this.postInfillCabinets(room,tri_templates,wall_edges,group_templates);
        }
        else{
            return null;
        }
    }



    /**
     * 填充台面
     * ../../../../../..param room  
     * ../../../../../..param group_templates 
     */
    postInfillTableTop(room:TRoom,tri_templates:TGroupTemplate[]=null,wall_edges:ZEdge[]=null)
    {
        let tabletop_depth = this.tabletop_depth;

        let points : Vector3[] = [];
        wall_edges.forEach((edge)=>points.push(edge.v0.pos.clone(),edge.v1.pos.clone()));
        tri_templates.forEach((template)=>points.push(...template._target_rect.positions));

        let main_rect = ZRect.fromPoints(points, tri_templates[0]._target_rect.nor); // 主矩形

        if(main_rect.w < main_rect.h)
        {
            main_rect.swapWidthAndHeight();
        }

        // 从水槽\灶台起始, 往两边延伸台面线,
        let table_top_rects : ZRect[] = [];

        tri_templates.forEach(template=>{
            let back_edge = template._target_rect.backEdge;

            let t_edge = room.room_shape._poly.edges.find(edge=>{
                if(edge.islayOn(back_edge,100,0.6))
                {
                    return true;
                }
                else{
                    return false;
                }
            })
            let rect = new ZRect(10000,tabletop_depth);
            rect.nor = back_edge.nor.clone().negate();
            rect.back_center = back_edge.center;
            if(t_edge)
            {
                let pp = t_edge.projectEdge2d(back_edge.center);
                rect.back_center = t_edge.unprojectEdge2d({x:pp.x,y:0});
            }

            rect.updateRect();

            let t_rect = rect.intersect_rect(main_rect);

            if(t_rect){
                rect = ZRect.fromPoints(t_rect.positions,rect.nor);
                
            }
            table_top_rects.push(rect);
        });


        let init_table_top_polys = table_top_rects[0].union_polygons(table_top_rects);

        let table_top_polys :ZPolygon[] = [];

        let cutted_polys : ZPolygon[] = [];

        for(let win of room.windows)
        {
            if(win.rect && win.type === "Door")
            {
   
                let rect = win.rect.clone();
                rect._w = Math.min(rect.w, 900);
                rect._h += 900;

                rect.updateRect();
                rect.reOrderByOrientation();
                cutted_polys.push(rect);
            }
        }

        if(tri_templates[2])
        {
            let ice_rect = tri_templates[2]._target_rect.clone();
            ice_rect._w += 100;
            ice_rect.updateRect();
            cutted_polys.push(ice_rect);
        }


        for(let poly of init_table_top_polys)
        {
            let res_polys = poly.substract_polygons(cutted_polys);

            if(res_polys) table_top_polys.push(...res_polys);
        }
        
        room._tabletop_list = [];


        for(let poly of table_top_polys)
        {
            let ele = TFigureElement.createSimple("橱柜台面");
            ele.rect.copy(ZRect.computeMainRect(poly));
            if(ele.rect.min_hh < 100) continue;
            ele._polygon = poly;
            room._tabletop_list.push(ele);


            poly.edges.forEach((edge)=>{
                for(let rect of table_top_rects)
                {
                    if(rect.frontEdge.islayOn(edge,100,0.2) && rect.frontEdge.checkSameNormal(edge.nor,false))
                    {
                        edge._ex_props[TFigureElement.IsTableTopFrontEdge] = 1;
                    }
                }
            })
        }


    }
    getNeighborType(template:TGroupTemplate) : TriNeighborType
    {
        if(template.group_code.indexOf("冰箱")>=0)
        {
            return "Ice";
        }
        else if(template.group_code.indexOf("炉灶")>=0)
        {
            return "Fire";
        }
        else if(template.group_code.indexOf("水槽")>=0)
        {
            return "Water";
        }
        else {
            return "None";
        }
    }

    postInfillCabinets(room:TRoom,tri_templates:TGroupTemplate[],wall_edges:ZEdge[],src_group_templates:TGroupTemplate[])
    {
        let table_top_front_edges:ZEdge[] = [];

        if(!room._tabletop_list) return [];

        room._tabletop_list.forEach(table_top=>{
            table_top._polygon.edges.forEach((edge)=>{
                if(edge._ex_props[TFigureElement.IsTableTopFrontEdge])
                {
                    table_top_front_edges.push(edge);
                }
            })
        });


        let sub_edges:ZEdge[] = this._computeTableTopSubEdges(table_top_front_edges,wall_edges);

        let cabinet_sub_ranges = this._computeTableTopSubRanges(sub_edges,tri_templates);

        // console.log(cabinet_sub_ranges);

        






        let group_templates = src_group_templates.filter((group_template)=>!compareNames([group_template.group_space_category],["吊柜"]))    
        group_templates = this.postResortOnEdgeCabinets(room,group_templates,sub_edges,tri_templates)
        let score0 = this.computeFillingScore(room,group_templates,sub_edges);

        if(score0 < 0.9 || true)
        {
           let floor_group_templates = this._fillFloorCabinets(cabinet_sub_ranges,room,tri_templates,wall_edges);
           group_templates = [...floor_group_templates];
           console.log(floor_group_templates);
           group_templates = this.postResortOnEdgeCabinets(room,group_templates,sub_edges,tri_templates)
        }

        return group_templates;
        
    }

    postResortOnEdgeCabinets(room:TRoom, group_templates:TGroupTemplate[],sub_edges:ZEdge[],tri_templates:TGroupTemplate[])
    {

        let ans_group_templates : TGroupTemplate[] = [];


        for(let sub_edge of sub_edges)
        {
            let on_edge_templates = group_templates.filter((gt)=>{

                if(gt.group_space_category.includes("冰箱")) return false;
                if(gt._target_rect.checkSameNormal(sub_edge.nor,false))
                {
                    let pp = sub_edge.projectEdge2d(gt._target_rect.rect_center);
                    if(Math.abs(pp.y) > 600) return false;

                    if(pp.x < -gt._target_rect.w/2-100 || pp.x > sub_edge.length + gt._target_rect.w/2+100) return false;

                    return true;
                }

                return false;
            });

            if(on_edge_templates.length == 0) continue;
            on_edge_templates.sort((a,b)=>{
                let ax = sub_edge.projectEdge2d(a._target_rect.frontEdge.center).x;
                let bx =  sub_edge.projectEdge2d(b._target_rect.frontEdge.center).x;
                return ax - bx;
            });
            let lx = -1;

            let on_edge_intervals : I_OnLineFigureInterval[] = [];
            
            let start_x = 0;
            let end_x = 0;
            let start_y = 0;
            let start_nor :Vector3=  sub_edge.nor.clone();
            let start_depth = 0;
            let total_length = 0;
            for(let group_template of on_edge_templates)
            {
                let pp = sub_edge.projectEdge2d(group_template._target_rect.rect_center);
                let ww = group_template._target_rect.w;
                if(lx < 0)
                {
                    let xx = pp.x;
                    lx = xx - group_template._target_rect.w/2;
                    // start_y = pp.y;
                    start_x = lx;
                    start_nor = group_template._target_rect.nor.clone();
                    start_depth = group_template._target_rect.h;
                }


                let pos = sub_edge.unprojectEdge2d({x:lx + ww/2,y:pp.y});
                group_template._target_rect.rect_center = pos;
                group_template.updateByTargetRect();
                lx += ww;
                end_x = lx;
                total_length += ww;
                if(group_template.current_s_group)
                {
                    for(let figure_ele of group_template.current_s_group.figure_elements)
                    {
                        // ans_group_templates.push(TGroupTemplate.makeGroupTemplateByGroupSpaceCategory(figure_ele.sub_category,"厨房",figure_ele.rect));

                        if(figure_ele.sub_category.includes("收口板")) continue;
                        let interval = TLayoutOptmizerOnWallRules.make_interval_of_group_category(figure_ele.sub_category,"厨房");
                        interval.target_length = figure_ele.rect.w;
                        interval.target_center_x = sub_edge.projectEdge2d(figure_ele.rect.rect_center).x - start_x;
                        // interval.center_diff_weight = 1;
                        interval.length_differ_weight = 1;
                        if(compareNames([interval.name],["炉灶","水槽"]))
                        {
                            interval.length_differ_weight = 10;
                        }
                        interval.u_dv_flag = group_template._target_rect.dv.dot(sub_edge.dv);
                        on_edge_intervals.push(interval);
                    }
                }   
            }

            // if(on_edge_intervals[1] && compareNames([on_edge_intervals[1].name],["转角"]))
            // {
            //     start_x -= 30;
            // }
            // else{
            //     start_x = 0;
            // }

            // if(on_edge_intervals[on_edge_intervals.length-1] && 
            //     compareNames([on_edge_intervals[on_edge_intervals.length-1].name],["转角"]))
            // {
            //     end_x += 30;
            // }
            // else{
            //     end_x = sub_edge.length;
            // }

            end_x = Math.min(sub_edge.length + 350,end_x);
            start_x = Math.max(-350,start_x);

            total_length = end_x - start_x;

            

            if(on_edge_intervals[0] && !on_edge_intervals[0].name.includes("转角"))
            {
                let start_board_interval = TLayoutOptmizerOnWallRules.make_interval_of_group_category("地柜收口板","厨房");
                on_edge_intervals = [start_board_interval,...on_edge_intervals];
            }



            if(on_edge_intervals[on_edge_intervals.length-1] && !on_edge_intervals[on_edge_intervals.length-1].name.includes("转角"))
            {
                let end_board_interval = TLayoutOptmizerOnWallRules.make_interval_of_group_category("地柜收口板","厨房");
                end_board_interval.u_dv_flag = -1;
                on_edge_intervals.push(end_board_interval);
            }


            let diff_val = TLayoutOptmizerOnWallRules.optimize_online_intervals_dynamic_programming(on_edge_intervals,total_length,true,10);

            let sum_val = 0;
            on_edge_intervals.forEach((val)=>sum_val+=val.result_length);
            let ll_x = start_x;

            start_y = -(this.tabletop_depth) + 560 / 2;
            for(let interval of on_edge_intervals)
            {
                let rect = new ZRect(interval.result_length,start_depth);
                rect.nor = start_nor;
                rect.u_dv = sub_edge.dv.clone().multiplyScalar(  interval.u_dv_flag || 1);
                rect.rect_center = sub_edge.unprojectEdge2d({x:ll_x + interval.result_length/2,y:start_y});

                ll_x += interval.result_length;
                if(interval.result_length > 1)
                {
                    ans_group_templates.push(TGroupTemplate.makeGroupTemplateByGroupSpaceCategory(interval.name,"厨房",rect));
                }
            }

        }


        tri_templates.forEach(t_group_template=>{
            let in_ans_template = ans_group_templates.find((val)=>val.group_space_category==t_group_template.group_space_category);
            if(!in_ans_template)
            {
                ans_group_templates.push(t_group_template);
            }
        })


        return ans_group_templates;
    }

    computeFillingScore(room:TRoom, group_templates:TGroupTemplate[],sub_edges:ZEdge[])
    {
        
        let score_len = 0;
        let sum_length = 0;
        for(let sub_edge of sub_edges)
        {
            let on_edge_templates = group_templates.filter((gt)=>{

                if(gt.group_space_category.includes("冰箱")) return false;
                if(gt._target_rect.checkSameNormal(sub_edge.nor,false))
                {
                    let pp = sub_edge.projectEdge2d(gt._target_rect.rect_center);
                    if(Math.abs(pp.y) > 600) return false;

                    if(pp.x < -gt._target_rect.w/2-100 || pp.x > sub_edge.length + gt._target_rect.w/2+100) return false;

                    return true;
                }

                return false;
            });
            on_edge_templates.forEach(gt=>score_len+=gt._target_rect.w);
            sum_length += sub_edge.length;
        }

        return score_len / (sum_length+0.0001);
    }


    private _fillFloorCabinets(cabinet_sub_ranges:I_TableTopSubRange[], room:TRoom,tri_templates:TGroupTemplate[],wall_edges:ZEdge[])
    {
        let group_templates :TGroupTemplate[] = [...tri_templates];

        // 转角柜要特殊处理
        let corner_templates : TGroupTemplate[] = [];

        let candidate_main_area_group_names = ["主操作区地柜","洗菜区地柜","基础阵列地柜"];
        let candidate_main_area_index = 0;
        for(let range_data of cabinet_sub_ranges)
        {
            let t_edge = range_data.front_sub_edge;
            let depth = range_data.depth;
            let sub_ll = range_data.ll;

            let sub_rr = range_data.rr;

            let intervals : I_OnLineFigureInterval[] = [];

            let start_intervals : I_OnLineFigureInterval[] =[];

            let end_intervals : I_OnLineFigureInterval[] = [];

            let main_intervals : I_OnLineFigureInterval[] = [];
            let simple_cabinet_length = sub_rr - sub_ll;

            let main_area_name = candidate_main_area_group_names[candidate_main_area_index];
            if(candidate_main_area_index < candidate_main_area_group_names.length-1)
            {
                candidate_main_area_index++;
            }
            

             main_intervals.push(TLayoutOptmizerOnWallRules.make_interval_of_group_category(main_area_name,"厨房"));
            
            if(range_data.start_corner)
            {
                start_intervals.push(TLayoutOptmizerOnWallRules.make_interval_of_group_category("转角自由单门地柜","厨房"));
                simple_cabinet_length -= 350;
            }
            else if(range_data.start_neighbor ==="None")
            {
  
                start_intervals.push(TLayoutOptmizerOnWallRules.make_interval_of_group_category("自由收口地柜","厨房"));
                
                simple_cabinet_length -= 30;
            }
            if(range_data.start_neighbor === "Water")
            {
                start_intervals.push(TLayoutOptmizerOnWallRules.make_interval_of_group_category("米箱地柜","厨房"));
            }
         
         
            start_intervals.forEach(interval=>interval.is_at_start = true);

         
            if(range_data.end_corner)
            {

                end_intervals.push(TLayoutOptmizerOnWallRules.make_interval_of_group_category("转角自由单门地柜","厨房"));
                simple_cabinet_length -= 350;
                
                
            }
            else if(range_data.end_neighbor ==="None")
            {

                end_intervals.push(TLayoutOptmizerOnWallRules.make_interval_of_group_category("自由收口地柜","厨房"));
                              
                simple_cabinet_length -= 30;
            }



            end_intervals.forEach(interval=>interval.is_at_end=true);
            
            simple_cabinet_length = Math.max(0,simple_cabinet_length);


            intervals.push(...start_intervals,...main_intervals,...end_intervals);

            let error_num =  TLayoutOptmizerOnWallRules.optimize_online_intervals_dynamic_programming(intervals,sub_rr-sub_ll,true,10);
         
            let result_intervals :I_OnLineFigureInterval[] = [];
            for(let interval of intervals)
            {
                if(interval.is_at_end)
                {
                    interval.u_dv_flag = -1;
                }
                if(this.special_area_cabinet_array_dict[interval.name])
                {
   
                    let res_intervals = this._makeIntervalsOfArrayData(interval);
                    result_intervals.push(...res_intervals);
                }
                else {
                    result_intervals.push(interval);
                }

            }


            for(let interval of result_intervals)
            {
                if(interval.result_length < 1.) continue;
                
                let rect = new ZRect(interval.result_length,this.tabletop_depth);
                let pos = t_edge.unprojectEdge2d({x:sub_ll+interval.center_x,y:-this.tabletop_depth});
                rect.nor = t_edge.nor;
                rect._h = 560;
                rect.back_center = pos;
                rect._u_dv_flag = interval.u_dv_flag || 1;
                rect.updateRect();
                let group_template = TGroupTemplate.makeGroupTemplateByGroupSpaceCategory(interval.name,"厨房",rect);

                if(interval.name === "转角自由单门地柜")
                {
                    corner_templates.push(group_template);

                }
                else{
                    group_templates.push(group_template);

                }

            }
        }


        let result_corner_tempaltes = this._postProcessCornerCabinets(corner_templates,room);

        group_templates.push(...result_corner_tempaltes);
        // 特殊处理转角地柜
        return group_templates;

    }

    private _makeIntervalsOfArrayData(src_interval:I_OnLineFigureInterval)
    {
        let name = src_interval.name;
        if(!this.special_area_cabinet_array_dict[name]) return [];

        let intervals : I_OnLineFigureInterval[] = [];

        for(let key in this.special_area_cabinet_array_dict[name])
        {
            let data = this.special_area_cabinet_array_dict[name][key];

            let t_length = Math.floor(src_interval.result_length / 50+0.1) * 50;

            let array_data = data[t_length];

            if(!array_data)
            {
                t_length = Math.floor(src_interval.result_length / 10+0.1) * 10;
                array_data = data[t_length];
            }
            if(array_data)
            {
                for(let arr_ele of array_data.intervals)
                {
                    let interval :I_OnLineFigureInterval = {
                        name:arr_ele.name,
                        length_values:[arr_ele.length],
                        target_length : arr_ele.length,
                        result_length : arr_ele.length,
                        length_differ_weight : 0.1,
                        center_x : arr_ele.center_x + (src_interval.center_x - src_interval.result_length/2)
                    }
                    intervals.push(interval);
                }

                break;
            }
        }

        return intervals;
    }


    private _postProcessCornerCabinets(corner_templates:TGroupTemplate[],room:TRoom)
    {
        // 找到转角的对
        let corner_pairs : {group_templates:TGroupTemplate[]}[] = [];

        corner_templates.sort((a,b)=>b._target_rect.w - a._target_rect.w);

        let in_pair_group : TGroupTemplate[] = [];

        for(let i=0; i < corner_templates.length; i++)
        {
            let corner0 = corner_templates[i];
            let rect0 = corner0._target_rect;
            let pair :  {group_templates:TGroupTemplate[]} = {group_templates:[corner0]};
            
            for(let j=i+1;j < corner_templates.length; j++)
            {
                let corner1 = corner_templates[j];
                let rect1 = corner1._target_rect;

                if(rect0.checkSameNormal(rect1.nor)) continue; // 同法向，无需考虑
                let pp = rect0.project(rect1.front_center);

                if(Math.abs(pp.y) > 600) continue;
                if(Math.abs(pp.x) < rect0.w/2 + 300)
                {
                    pair.group_templates.push(corner1);
                }
            }
            if(pair.group_templates.length >=2)
            {
                in_pair_group.push(...pair.group_templates);
            }
            else if(pair.group_templates.length ==1)
            {
                if(in_pair_group.includes(pair.group_templates[0]))
                {
                    continue;
                }
            }

            corner_pairs.push(pair);
        }
        let cutted_rects : ZRect[] = [];

        room.room_shape._feature_shape._w_poly.edges.forEach((edge)=>{
            let rect = new ZRect(edge.length, 240);
            rect.nor = edge.nor;
            rect.back_center = edge.center;
            rect.updateRect();
            cutted_rects.push(rect);
        })

        cutted_rects.forEach((rect)=>rect.reOrderByOrientation(true));


        let try_extend_rect_func = (rect:ZRect)=>{
            let extend_len = 300;
            let pos = rect.unproject({x:extend_len/2,y:0});
            rect._w = rect.w+ extend_len;
            rect.rect_center = pos;
            let nor = rect.nor.clone();
            let u_dv_flag = rect.u_dv_flag;
            cutted_rects.forEach(c_rect=>{
                let r_rect = rect.clip_side_rect(c_rect,nor);
                if(r_rect)
                {
                    rect.copy(ZRect.fromPoints(r_rect.positions,nor,u_dv_flag));
                }
            });
            return rect;
        }
        let extend_templates : TGroupTemplate[] = [];
        // console.log(corner_templates.length, corner_pairs);

        for(let pair of corner_pairs)
        {
            if(pair.group_templates.length >= 1)
            {
                let target_id = 0;
                let max_extend_len = 0;
                for(let group_template of pair.group_templates)
                {
                    let extend_rect = group_template._target_rect.clone();
                    extend_rect = try_extend_rect_func(extend_rect);
                    group_template._target_rect._attached_elements["ExtendRect"] = extend_rect;
                    let extend_len = extend_rect.w - group_template._target_rect.w;

                    if(group_template._target_rect.w > 300)  // 保证有开门的位置
                    {
                        if(extend_len > max_extend_len)
                        {
                            target_id = pair.group_templates.indexOf(group_template);
                            max_extend_len = extend_len; //  减少空间浪费
                        }
                    }
                }

                for(let i in pair.group_templates)
                {
                    let group_template = pair.group_templates[i];
                    if(target_id == ~~i)
                    {
                        group_template._target_rect.copy(  group_template._target_rect._attached_elements["ExtendRect"]);
                        group_template.updateByTargetRect();
                        extend_templates.push(group_template);
                    }
                    else{
                        if(group_template._target_rect._attached_elements['ExtendRect'])
                        {
                            delete group_template._target_rect._attached_elements['ExtendRect'];
                        }
                    }
                }


                
                
            }
        }
        let result_group_templates : TGroupTemplate[] = [];

        corner_templates.forEach((group_template)=>{
            if(extend_templates.includes(group_template) && group_template._target_rect.w > (350 -0.1))
            {
                let target_rect = group_template._target_rect;


                let res_group_template = TGroupTemplate.makeGroupTemplateByGroupSpaceCategory("转角自由地柜","厨房",target_rect);
                result_group_templates.push(res_group_template);
            }
            else{
                let target_rect = group_template._target_rect;


                let res_group_template = TGroupTemplate.makeGroupTemplateByGroupSpaceCategory("自由收口地柜","厨房",target_rect);
                result_group_templates.push(res_group_template);

   
            }
        })


        return result_group_templates;
    }

    private _computeTableTopSubEdges(table_top_front_edges:ZEdge[],wall_edges:ZEdge[])
    {
        let sub_edges:ZEdge[] = [];

        for(let front_edge of table_top_front_edges)
        {
             let pairs:I_TableTopSubRange[] = [];

             let start_corner = false;
             let end_corner = false;
             if(table_top_front_edges.includes(front_edge.prev_edge))
             {
                start_corner = true;
             }
             if(table_top_front_edges.includes(front_edge.next_edge))
             {
                end_corner = true;
             }

             for(let w_edge of wall_edges)
             {
                if(w_edge.islayOn(front_edge,600+100,0.001))
                {
                    let depth = -front_edge.projectEdge2d(w_edge.center).y;
                    if(depth < 0) continue;
                    let ll = front_edge.projectEdge2d(w_edge.v0.pos).x;
                    let rr = front_edge.projectEdge2d(w_edge.v1.pos).x;

                    if(ll > rr) {
                        let tmp = ll; ll = rr; rr = tmp;
                    }

                    ll = Math.max(ll,0);
                    rr = Math.min(rr,front_edge.length);

            
                
                    pairs.push({ll:ll,rr:rr,depth:depth,front_table_edge:front_edge});
                }
             }  

             pairs.sort((a,b)=>a.ll-b.ll);
            for(let pi in pairs)
            {
                
                let pair = pairs[pi];
                let p_start_corner = false;
                let p_end_corner = false;
                if(~~pi == 0)
                {
                    p_start_corner = start_corner;
                }
                if(~~pi == pairs.length-1)
                {
                    p_end_corner = end_corner;
                }
                pair.start_corner = p_start_corner;
                pair.end_corner = p_end_corner;

                let sub_edge = front_edge.makeSubEdge(pair.ll,pair.rr);
                sub_edge._attached_elements[TPostLayoutKitchenPart.PropTableSubEdgeData] = pair;
                sub_edges.push(sub_edge);
            }
        }

        return sub_edges;

    }
    private _computeTableTopSubRanges(sub_edges:ZEdge[], tri_templates:TGroupTemplate[])
    {

        let cabinet_sub_ranges :I_TableTopSubRange[] = [];


        for(let sub_edge of sub_edges)
        {

            let tri_templates_pairs :number[][] = [];
            for(let template of tri_templates)
            {
                if(sub_edge.islayOn(template._target_rect.frontEdge,100,0.5))
                {
                    let ll = sub_edge.projectEdge2d(template._target_rect.frontEdge.v0.pos).x;
                    let rr = sub_edge.projectEdge2d(template._target_rect.frontEdge.v1.pos).x;

                    if(ll > rr) {
                        let tmp = ll; ll = rr; rr = tmp;
                    }
                    
                    tri_templates_pairs.push([ll,rr]);
                }
            }

            let res_pairs = range_substract(sub_edge.length, tri_templates_pairs);
            let sub_edge_range = sub_edge._attached_elements[TPostLayoutKitchenPart.PropTableSubEdgeData];
        
            res_pairs.sort((a,b)=>a[0]-b[0]);
            for(let pair of res_pairs)
            {
                let ll = pair[0];
                let rr = pair[1];
                let is_start_corner = false;
                let is_end_corner = false;

                if(pair[0] < 10 && (sub_edge_range?.start_corner))
                {
                    is_start_corner = true;
                }
                if(pair[1] > sub_edge.length - 10 && (sub_edge_range?.end_corner))
                {
                    is_end_corner = true;
                }
                let range_data:I_TableTopSubRange = {
                    ll:ll,
                    rr:rr,depth:(sub_edge_range?.depth || this.tabletop_depth),
                    start_corner:is_start_corner,
                    end_corner:is_end_corner,
                    front_sub_edge:sub_edge,
                    front_table_edge:sub_edge_range?.front_table_edge
                };

                let start_type :TriNeighborType = "None";
                let end_type :TriNeighborType = "None";
                for(let template of tri_templates)
                {
                    if(template._target_rect.checkSameNormal(sub_edge.nor))
                    {
                        let pp = sub_edge.projectEdge2d(template._target_rect.frontEdge.center);

                        if(Math.abs(pp.y) > 500) continue;
                        let ww = template._target_rect.w;

                        let xx = pp.x;

                        if(xx < ll && Math.abs(ll-xx) < ww/2+10)
                        {
                            start_type = this.getNeighborType(template);
                        }
                        if(xx > rr && Math.abs(xx - rr) < ww/2+10)
                        {
                            end_type = this.getNeighborType(template);
                        }

                    }
                }
                range_data.start_neighbor = start_type;
                range_data.end_neighbor = end_type;
                cabinet_sub_ranges.push(range_data);
                
            }
        }


        let sub_range_score = (sub_range:I_TableTopSubRange)=>{

            let weight = 1.;

            if(sub_range.start_neighbor ==="Water" || sub_range.end_neighbor==="Water")
            {
                 weight *= 10;
            }
            let len = sub_range.rr - sub_range.ll;

            return len * weight;
        }
        cabinet_sub_ranges.sort((a,b)=>sub_range_score(b)-sub_range_score(a));

        return cabinet_sub_ranges;
    }
    getTriTemplates(room:TRoom,group_templates:TGroupTemplate[] = null)
    {
        if(!group_templates)
        {
            group_templates = TGroupTemplate.extractGroupTemplates(room._furniture_list,"厨房");
        }
        let ice_template = group_templates.find((ele)=>compareNames([ele.group_code],["冰箱"]));
        let fire_template = group_templates.find((ele)=>compareNames([ele.group_code],["炉灶地柜"]));
        let water_template = group_templates.find((ele)=>compareNames([ele.group_code],["水槽地柜"]));


        if(  !fire_template || !water_template) return null;
        let tri_templates = [water_template,fire_template];
        if(ice_template)
        {
            tri_templates.push(ice_template);
        }
        return tri_templates;
    }

    private getAttachedWallEdgeSubSet(w_poly:ZPolygon,tri_templates:TGroupTemplate[])
    {
        let attached_edges = w_poly.edges.filter((edge)=>{
            for(let template of tri_templates)
            {
                if(edge.checkSameNormal(template._target_rect.nor.clone().negate(),false) && 
                    edge.islayOn(template._target_rect.backEdge,100,0.2)
                )
                {
                    return true;
                }
            }
            return false;
        });

        if(attached_edges.length == 0) return null;
        attached_edges.sort((a,b)=>a._edge_id - b._edge_id); 

        // 要最小的连续墙

        let wall_subset0 :ZEdge[] =[];
        let start_edge_id = attached_edges[0]._edge_id;
        let end_edge_id = attached_edges[attached_edges.length-1]._edge_id;

        for(let i=start_edge_id; i<=end_edge_id; i++)
        {
            wall_subset0.push(w_poly.edges[i]);
        }

        let second_edge_id = attached_edges[1]?._edge_id || start_edge_id;


        let wall_subset1 : ZEdge[] = [];
        for(let i=end_edge_id; i<=second_edge_id+w_poly.edges.length; i++)
        {
            let ii = i % w_poly.edges.length;
            wall_subset1.push(w_poly.edges[ii]);
        }

        let sum0 = 0; wall_subset0.forEach((edge)=>sum0+=edge.length);
        let sum1 = 0; wall_subset1.forEach((edge)=>sum1+=edge.length);

        let using_sub_set = sum0 < sum1?wall_subset0:wall_subset1;

        return using_sub_set;
    }

}