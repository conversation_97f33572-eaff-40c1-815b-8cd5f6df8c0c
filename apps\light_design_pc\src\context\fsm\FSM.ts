import type { IState } from "./interfaces/IState";
import { ANY_STATE } from "./const/FSMConst";

/**
 * 基础FSM实现 
 */
export abstract class FSM implements IState {
  public name: string;
  public parent?: FSM;
  public currentState: IState | null = null;
  public states: Map<string, IState> = new Map();
  public defaultState: string | null = null;
  public transitions: Map<string, Set<string>> = new Map();

  constructor(name: string) {
    this.name = name;
  }

  // 进入状态
  onEnter(data?: any): void {
    if (this.states.size > 0 && !this.currentState) {
      const defaultState = this.defaultState ?
        this.states.get(this.defaultState) :
        Array.from(this.states.values())[0];

      if (defaultState) {
        this.transitionTo(defaultState.name, data);
      }
    }
  }

  onExit(data?: any): void {
    this.currentState?.onExit?.(data);
    this.currentState = null;
  }

  // 状态管理
  addState(state: IState): void {
    state.parent = this;
    this.states.set(state.name, state);
  }

  removeState(stateName: string): void {
    this.states.delete(stateName);
  }


  setDefaultState(stateName: string): void {
    if (this.states.has(stateName)) {
      this.defaultState = stateName;
    }
  }

  getDefaultState(): IState | null {
    return this.defaultState ? this.states.get(this.defaultState) || null : null;
  }

  // 转换方法
  transitionTo(stateName: string, data?: any): boolean {
    const result = this.findStateAndCheckTransition(stateName);
    if (!result.found) {
      return false;
    }

    // 如果找到了目标状态，直接执行切换（让子FSM自己处理）
    if (result.targetState) {
      return this._executeTransition(result.targetState, data);
    }

    return false;
  }

  canTransitionTo(stateName: string): boolean {
    const result = this.findStateAndCheckTransition(stateName);
    return result.found && result.canTransition;
  }

  // 转换规则管理
  addTransition(from: string, to: string): void {
    if (from === ANY_STATE || to === ANY_STATE) {
      this._addWildcardTransition(from, to);
      return;
    }

    if (!this.transitions.has(from)) {
      this.transitions.set(from, new Set());
    }
    this.transitions.get(from)!.add(to);
  }

  removeTransition(from: string, to: string): void {
    const transitionSet = this.transitions.get(from);
    if (transitionSet) {
      transitionSet.delete(to);
      if (transitionSet.size === 0) {
        this.transitions.delete(from);
      }
    }
  }

  /**
   * 查找状态并检查转换
   * @param stateName 状态名称
   * @returns 查找结果
   */
  findStateAndCheckTransition(stateName: string): {
    found: boolean;
    canTransition: boolean;
    targetState: IState | null;
  } {
    // 检查当前FSM
    const state = this.states.get(stateName);
    if (state) {
      return {
        found: true,
        canTransition: this._checkTransition(stateName),
        targetState: state
      };
    }

    // 递归查找子FSM
    for (const [_, childState] of this.states) {
      if (this.isFSM(childState)) {
        const result = (childState as FSM).findStateAndCheckTransition(stateName);
        if (result.found) {
          return result;
        }
      }
    }

    return { found: false, canTransition: false, targetState: null };
  }

  /**
 * 查找包含指定状态的FSM
 */
  public findFSMContainingState(stateName: string): FSM | null {
    // 检查当前FSM
    if (this.states.has(stateName)) {
      return this;
    }

    // 递归查找子FSM
    for (const [_, childState] of this.states) {
      if (this.isFSM(childState)) {
        const found = (childState as FSM).findFSMContainingState(stateName);
        if (found) {
          return found;
        }
      }
    }

    return null;
  }

  /**
   * 获取当前叶子状态
   * @returns 当前叶子状态
   */
  getCurrentLeafState(): IState {
    if (!this.currentState) return this;

    if (this.isFSM(this.currentState)) {
      return (this.currentState as FSM).getCurrentLeafState();
    }

    return this.currentState;
  }

  public isFSM(state: IState): state is FSM {
    return 'states' in state && 'transitions' in state;
  }

  // 获取状态的转换
  private _getTransitions(from: string): string[] {
    return Array.from(this.transitions.get(from) || []);
  }
  // 检查状态的转换
  private _checkTransition(stateName: string): boolean {
    if (!this.currentState) return true;

    const currentLeafState = this.getCurrentLeafState();
    const allowedTransitions = this._getTransitions(currentLeafState.name);
    return allowedTransitions.includes(stateName);
  }

  // 执行状态的转换 
  private _executeTransition(targetState: IState, data?: any): boolean {
    this.currentState?.onExit?.(data);
    this.currentState = targetState;
    this.currentState.onEnter?.(data);
    return true;
  }

  // 添加通配符转换
  private _addWildcardTransition(from: string, to: string): void {
    const stateNames = Array.from(this.states.keys());

    if (from === ANY_STATE && to === ANY_STATE) {
      // 从任何状态到任何状态
      stateNames.forEach(fromState =>
        stateNames.forEach(toState =>
          fromState !== toState && this.addTransition(fromState, toState)
        )
      );
    } else if (from === ANY_STATE) {
      // 从任何状态到指定状态
      stateNames.forEach(fromState =>
        fromState !== to && this.addTransition(fromState, to)
      );
    } else if (to === ANY_STATE) {
      // 从指定状态到任何状态
      stateNames.forEach(toState =>
        from !== toState && this.addTransition(from, toState)
      );
    }
  }


} 