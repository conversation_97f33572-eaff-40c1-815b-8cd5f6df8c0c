import { I_XmlCWardrobeGroupEntity, I_XmlEntityBase } from "../IWardrobeEntityInterface";
import { XmlEntityBase } from "../XmlEntityBase";
import { XmlStdWardrobeEntity } from "./XmlStdWardrobeEntity";


export class XmlCWardrobeGroupEntity extends XmlStdWardrobeEntity implements I_XmlCWardrobeGroupEntity {
    newRelatedIdsS?: string;
    oldRelatedIDS?: string;

    constructor(data?: Partial<I_XmlCWardrobeGroupEntity>) {
        super(data);
    }
}

XmlEntityBase.Generators["CWardrobeGroupEntity"] = (data:I_XmlEntityBase)=>new XmlCWardrobeGroupEntity(data);