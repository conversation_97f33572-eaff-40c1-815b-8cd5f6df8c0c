import { I_XmlCFuntorEntity, I_XmlEntityBase } from "../IWardrobeEntityInterface";
import { XmlEntityBase } from "../XmlEntityBase";
import { XmlCWhSpacePartComponent } from "./XmlCWhSpacePartComponent";

export class XmlCFuntorEntity extends XmlEntityBase implements I_XmlCFuntorEntity {

    constructor(data?: Partial<I_XmlCFuntorEntity>) {
        super(data);

    }
}

XmlEntityBase.Generators["CFunctorEntity"] = (data:I_XmlEntityBase)=>new XmlCFuntorEntity(data);