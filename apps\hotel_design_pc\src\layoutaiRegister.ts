

// 一些LayoutAI相关的全局注册

import { LayoutAI_App } from "@layoutai/layout_scheme";
import { AISpaceDesignManager } from "./apps/AISpaceDesignManager";

LayoutAI_App.t = (s:string)=>{return s;}

LayoutAI_App.RegisterApp(AISpaceDesignManager.AppName, () => { return new AISpaceDesignManager() });

LayoutAI_App.NewApp(AISpaceDesignManager.AppName);

// LayoutAI_App.createScene3dManager = ()=>{
//     return new Scene3DManager(LayoutAI_App.instance as TAppManagerBase);
// }

if (globalThis) {
  (globalThis as any).LayoutAI_App = LayoutAI_App;
}