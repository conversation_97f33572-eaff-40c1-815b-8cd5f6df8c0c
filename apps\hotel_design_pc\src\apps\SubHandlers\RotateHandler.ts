

import { LayoutAI_Commands, TBaseEntity, TBaseGroupEntity, TFurnitureEntity, T_MoveOperationInfo } from "@layoutai/layout_scheme";
import { Vector3 } from "three";
import { I_MouseEvent, ZRect } from "@layoutai/z_polygon";
import { AI2BaseModeHandler } from "../AI2BaseModeHandler";
import { CadBaseSubHandler } from "./CadBaseSubHandler";




export class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> extends CadBaseSubHandler
{
    operation_info: T_MoveOperationInfo;
    target_rect: ZRect;
    _origin_shape_rect: ZRect;
    pos: Vector3;
    constructor(cad_mode_handler: AI2BaseModeHandler)
    {
        super(cad_mode_handler);
        this.operation_info = null;
        this.name = LayoutAI_Commands.Transform_Rotate;
        this.target_rect = null;
        this._origin_shape_rect = null;
        this.pos = new Vector3(0, 0, 0);
    }

    onmousedown(ev: I_MouseEvent): void {
        if(!this.selected_target.selected_transform_element || !this.selected_target.selected_transform_element._target_rect)
        {
            this._cad_mode_handler.runCommand(LayoutAI_Commands.LeaveSubHandler);
            return;
        }
        if(this.selected_target && this.selected_target.selected_rect)
        {
            this._cad_mode_handler._is_moving_element = true;
        } 
        else 
        {
            this._cad_mode_handler._is_moving_element = false;
        }
        this._origin_shape_rect = this.selected_target.selected_rect.clone();
        this.pos = new Vector3(this.selected_target.selected_rect.leftEdge.v0.x, this.selected_target.selected_rect.leftEdge.v0.y, 0);
        let pp = this.selected_target.selected_rect.project(this.pos);
    }
    onmousemove(ev: I_MouseEvent): void {
        if(ev.buttons != 1) return;
        if (this.selected_target.selected_rect?._attached_elements["FigureElement"]?.locked) {
            return;
        }

        this.target_rect = this.selected_target.selected_rect;
        let r_center = this.target_rect.rect_center;
        // 计算鼠标位置和目标中心之间的角度
        this.pos = new Vector3(ev.posX, ev.posY, 0);
        let pp = this.target_rect.project(this.pos);
        let angle = Math.atan2(pp.y, pp.x) - Math.PI / 2;


        let adsorb_angle = 10;
        // if(Math.abs(pp.y) < this.target_rect.h + 50)
        // {
        //     adsorb_angle = 45;
        // }
        let entity = TBaseEntity.getEntityOfRect(this.target_rect);
        let result = this.adsorptionRect(Math.atan2(this.pos.y - r_center.y, this.pos.x - r_center.x) * (180 / Math.PI), adsorb_angle);
        if (result) {
            let {x, y} = result;
            this.target_rect.nor = new Vector3(x, y, 0);
            // if(entity.match_rect)
            // {
            //     entity.match_rect.nor = new Vector3(x, y, 0);
            // }
        } else 
        {
            let axis = new Vector3(0, 0, 1);
            this.target_rect.nor.applyAxisAngle(axis, this.target_rect.u_dv_flag * angle); 
            // if(entity.match_rect)
            // {
            //     entity.match_rect.nor.applyAxisAngle(axis, this.target_rect.u_dv_flag * angle);
            // }
        }
        this.target_rect.rect_center = r_center;

        // if(entity)
        // {
        //     if(entity.match_rect)
        //     {
        //         entity.rect.rect_center = this.target_rect.rect_center.clone();
        //         entity.match_rect.rect_center = this.target_rect.rect_center.clone();
        //     }
        //     entity.update();
        // }
        // if(!this.operation_info)
        {
            this.operation_info = new T_MoveOperationInfo(this.manager);
        }
        if(this.selected_target.selected_rect.ex_prop['poly_target_type'] == 'Group' || this.selected_target.selected_rect.ex_prop['poly_target_type'] == 'BaseGroup')
        {
            let entity = TBaseEntity.getEntityOfRect(this.target_rect) as TBaseGroupEntity;
            if (entity._rect != this.target_rect)
            {  
                entity._rect.nor = this.target_rect.nor;
                if (entity.shouldAlignToBackCenter())
                {
                    entity._rect.back_center = this.target_rect.back_center;
                }
                else
                {
                    entity._rect.rect_center = this.target_rect.rect_center;
                }
                entity._rect.updateRect();
            }
            let combinationRects: TFurnitureEntity[] = entity.recursivelyAddCombinationRects();
            this.operation_info._history_info.previous_in_group_rects = combinationRects.map((entity) => {
                entity.rect.clone();
                return entity;
            });
            entity.rotateAllEntityMembersAlignToGroup();
            this.operation_info._history_info.current_in_group_rects = combinationRects.map((entity) => {
                entity.rect.clone();
                return entity;
            });
            this.operation_info.target_in_group_entitys = combinationRects;
        }
        
        
        this.operation_info._history_info.id = 0;
        this.operation_info._furniture_entities = this.furniture_entities;

        this.updateTransformElements();
        this.update();        

    }

    adsorptionRect(angle: number,adosrb_tol : number = 5) {
        let adsorptionAngles = [0, 45, 90, 135, 180, -180, -135, -90, -45];
        // 找到最接近的吸附角度
        let closest = adsorptionAngles.reduce((prev, curr) =>
            Math.abs(curr - angle) < Math.abs(prev - angle) ? curr : prev
        );
        if( Math.abs(closest - angle) < adosrb_tol)
        {
            let angleInRadians = closest * (Math.PI / 180);
            let x = 1 * Math.cos(angleInRadians);
            let y = 1 * Math.sin(angleInRadians);
            return {x,y};
        } 
    }
    onmouseup(ev: I_MouseEvent): void {
        if(!this.target_rect) {
            this._cad_mode_handler.runCommand(LayoutAI_Commands.LeaveSubHandler);
            return;
        };
        this.updateAttributes("edit");
        this.operation_info.target_rect = this.target_rect;
        this.operation_info._history_info.previous_rect = this._origin_shape_rect.clone();
        this.operation_info._history_info.current_rect = this.target_rect.clone();
        this.manager.appendOperationInfo(this.operation_info);
        this._cad_mode_handler._is_moving_element = false;
        this._cad_mode_handler.runCommand(LayoutAI_Commands.LeaveSubHandler);
        this.update();
    }

    drawCanvas(): void {
        super.drawCanvas();
        this.painter.fillStyle = '#147FFA'
        // #region : 画旋转控件
        const inradius = this.painter._p_sc < 0.4 ? 800 : 320 / this.painter._p_sc;    // 内径
        const ringWidth = 160;  // 环宽
        const exradius = inradius + ringWidth;  // 外径
        const center = this.selected_target.selected_rect.rect_center;

        // 画内外圈
        this.painter.drawPointCircle(center, exradius, 1);
        this.painter.drawPointCircle(center, inradius, 1);

        for (let angle = 0; angle < 360; angle += 45) {
            const radian = angle * (Math.PI / 180);
            const start = new Vector3(center.x + inradius * Math.cos(radian), center.y + inradius * Math.sin(radian));
            const end = new Vector3(center.x + exradius * Math.cos(radian), center.y + exradius * Math.sin(radian));
            this.painter.drawLineSegment(start, end, '#000');
        }
        
        // 画扇区
        const result = this.adsorptionRect(Math.atan2(this.pos.y - center.y, this.pos.x - center.x) * (180 / Math.PI), 10);
        if (result) {
            this.painter.drawCircleQuarter(center, exradius, -(Math.atan2(result.x, result.y) - Math.PI / 4), false, 'rgba(91, 174, 252, 0.8)')
            this.painter.drawCircleQuarter(center, inradius, -(Math.atan2(result.x, result.y) - Math.PI / 4), false, 'rgba(255, 255, 255, 0.4)')
        } else {
            this.painter.drawCircleQuarter(center, exradius, Math.atan2(this.pos.y - center.y, this.pos.x - center.x) - Math.PI / 4, false, 'rgba(91, 174, 252, 0.8)')
            this.painter.drawCircleQuarter(center, inradius, Math.atan2(this.pos.y - center.y, this.pos.x - center.x) - Math.PI / 4, false, 'rgba(255, 255, 255, 0.4)')
        }
        // #endregion
    }
}