import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ token, css }) => {


  return {
    root: css``,
    leftPanel: css`
        position :absolute;
        left:0;
        top:0;
        width: 400px;
        height:100%;
        z-index:5;
        background:#fff;
    `,
    result_list: css`
    overflow-y: auto;
    background: #fff;
    .room_ele_div {
      width: 250px;
      height: 300px;
      margin-left: 10px;
      margin-top: 10px;
      float: left;
      border: 1px solid #000;
      overflow: hidden;
      text-align: center;
      cursor: pointer;
      &:hover {
        background: rgba(127, 127, 255, 0.5);
      }
    }
    canvas {
      width: 250px;
      height: 250px;
    }
  `,
    rightPanel: css`
        position :absolute;
        right:0;
        top:0;
        width: 300px;
        height:100%;
        z-index:5;
        background:#fff;
        .title {
          color: #aaa;
          font-family: PingFang SC;
          font-weight: semibold;
          font-size: 16px;
          line-height: 24px;
          font-weight: 600;
          background: #F2F3F5;
         width: 100%;
         height: 40px;
         display: -webkit-box;
         display: -webkit-flex;
         display: -ms-flexbox;
         display: flex;
         -webkit-align-items: center;
         -webkit-box-align: center;
         -ms-flex-align: center;
         align-items: center;
         padding-left: 16px;
         margin-bottom: 10px;
         cursor:pointer;
         .active {
            color:#282828;
         }
       }
       select {
        margin-top:10px;
      }
      .row_div {
        width:100%;
        padding:5px;
        margin-top:5px;
        .label_div {
            width:80px;
            height:20px;
            float:left;
        }
        input {
          width:130px;
          margin-left:5px;
        }
        button {
          width:100px;
          margin-left:5px;
        }
        select {
          width:200px;
          margin-left:5px;
          margin-top:0px;
        }
        textarea {
          width:200px;
          height:150px;
          margin-left:5px;
          margin-top:0px;
        }
        .labelList {
          width:200px;
          height:150px;
          margin-left:5px;
          margin-top:0px;
          overflow-y:auto;
          border:1px solid;
        }
        .labelListRow {
            width:100%;
            height:20px;
            line-height:20px;
        }
        .labelListRow.checked {
          background:rgba(127,127,255,0.5);
        }
      }
    `,
    returnListBtn:css`
    `,
    progressInfo:css`
      position: absolute;
      top: 17%;
      width: 100%;
      height: 0;
      background-color: rgba(0, 0, 0, 0.3); /* 设置背景颜色为半透明的黑色 */
      z-index: 999; /* 确保蒙层在其他元素之上 */
    `,
    startRunningBtn:css`
      float:right;
      margin-right:15px;
      padding:2px;
      border-radius:2px;
      cursor:pointer;
      &:hover {
        background :#adf;
      }
      &.isRunning {
        background :#adf;
      }
    `,
    houseSchemeList:css`
      position : absolute;
      left:0;
      top:0;
      width:100%;
      height:100%; 
      overflow:hidden;
      background:#fff;
      .listDiv {
        height:calc(100% - 35px);
        overflow-y:auto;
      }
      .dataset_title {
        width:95%;
        height:35px;
        line-height:25px;
        padding-left:2%;
        padding-top:5px;
      }
      .scheme_row_div {
          width:95%;
          height:45px;
          line-height:20px;
          padding-left:5%;
          border-bottom:1px dashed #adf;
          cursor:pointer;
          user-select:text;
      }
      .scheme_row_div:hover {
         background :#efefef;
      }
      .scheme_row_div.highlight {
        background :#efefef;
      }
      .scheme_row_div.active {
        background :#ccefef;
      }


    `,
    houseSchemeTestingPanel:css`
      overflow-y:auto;
      .title {
         color: #282828;
         font-family: PingFang SC;
         font-weight: semibold;
         font-size: 16px;
         line-height: 24px;
         font-weight: 600;
         background: #F2F3F5;
        width: 100%;
        height: 40px;
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-align-items: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        padding-left: 16px;
        margin-bottom: 10px;
      }
    .content {
      position: relative;
      overflow-y: scroll;
        .info_text {
          display: -webkit-box;
          display: -webkit-flex;
          display: -ms-flexbox;
          display: flex;
          -webkit-box-pack: justify;
          -webkit-justify-content: space-between;
          justify-content: space-between;
          padding: 5px 12px;
          user-select:text;
          select {
             height:100%;
             margin:0;
          }
        }
        .info_text:hover {
          background: #eee;
          cursor:pointer;
        }
        .sub_info_text {
          padding-left: 15px;
        }
    }
    `,
    layoutScoreContent:css`
      font-size:13px;
      width:100%;
      .room_title {
        height:30px;
        font-size:14px;
        padding-left:10px;
        margin-top:10px;
        margin-bottom:5px;
        border-bottom:1px dashed;
      }
      .layoutScoreRow{
        height:20px;
        line-height:20px;
        .title {
          font-weight:700;
          padding-left:10px;
          float:left;
        }
        .score {
          float:right;
          padding-right:20px;
        }
      }
    `,
    schemeLayoutScorePopUp:css`
      position:absolute;
      min-width:200px;
      min-height:200px;
      top:0px;
      left:310px;

      .layoutScoreCard {
        border-radius: 2px;
        box-shadow: 0px 5px 24px 0px #00000055;
      }
    `,
    layoutScoreCard:css`
      color:#147ffa;
      float:left;
      width:240px;
      padding:10px;
      padding-bottom:20px;
      background:#fff;
      .layout_score_row {
        width:100%;
        min-height:20px;     
        float:left;    
      }
      .sub_layout_score {
        width:100%;
        min-height:20px;
        float:left;
        margin-bottom:5px;
        float:left;
      }
      .score_level_0 {
         line-height:30px;
         font-size : 15px;
         font-weight:700;
      }
      .score_level_1 {
        line-height:20px;
        font-size : 12px;
        font-weight:500;
      }
      .score_level_2 {
        line-height:20px;
        font-size : px;
        font-weight:500;
        padding-left:10px;
      }
      .score_label {
        float : left;
        padding-left:5px;
      }
      .div_Score {
        float:right;
        padding-right:5px;
      }
      .div_Grade {
        color : #bd761d;
        font-size:13px;
        font-style:italic;
        margin-left:5px;
      }
    `
  }
});
