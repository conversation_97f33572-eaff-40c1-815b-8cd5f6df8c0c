
import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  return {
    figure: css`
      display: flex;
      // justify-content: space-evenly;
      flex: 1;
      flex-wrap: wrap;
      max-height: calc(100vh - 300px);
      /* padding-top: 16px; */
      overflow-y: scroll;
      padding-left: 0px;
      transition: all .3s;
      &::-webkit-scrollbar {
        width: 0px;
      }
      .fold
      {
        width: 100%;
        border-radius: 4px;
        background: #F4F5F5;
        height: 24px;
        line-height: 24px;
        font-weight: 600;
        padding: 0 5px;
        margin: 8px 0 4px 0;
      }
      .content {
        display: flex;
        flex-wrap: wrap;
        max-height: 1000px; /* 你可能需要根据你的内容来调整这个值 */
        overflow: hidden;
        transition: max-height 0.3s ease-in-out; /* 这将添加过渡动画 */
      }

      .collapsed {
        max-height: 0;
      }
      .item {
        flex-grow: 1;
        max-width: 30%;
        min-width: 85px;
        margin: 4px;
        cursor: pointer;
        transition: box-shadow 0.3s ease;
        user-select:none;
        .image {
          height: 92px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;
          overflow:hidden;
          transition: all .3s;
          .ant-image-img {
            vertical-align: middle;
            user-select:none;
            pointer-events: none; 

          }
          .structure-image.ant-image-img {
            vertical-align: middle;
            user-select:none;
            pointer-events: none; 
            height: 60px;
            width: 60px;
          }
          .group_image.ant-image-img {
            vertical-align: middle;
            user-select:none;
            pointer-events: none; 
          }

        }

        .title {
          color: #000000;
          font-size: 12px;
          padding: 10px 0;
          height: 40px;
          line-height: 20px;
          text-align: center;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .mobile
      {
        max-width: 45% !important; 
      }
      .item:hover {
          /* box-shadow: 3px 3px 8px 0px rgba(0, 0, 0, 0.12); */
           
            background-color: #DDDFE4;
          
        }
    `,
    mobile: css`
      /* height: calc(100vh - 370px) !important; */
    `
  };
});
