import { IEntitySubArea, RoomEntityCategory } from "@layoutai/design_domain";
import { IType2UITypeDict } from "@layoutai/basic_data";
import { TPainter } from "../draw/TPainter";
import { Object2DBase } from "./Object2DBase";
import { Vector3 } from "three";

/**
 * 分区 2D 对象
 */
export class SubArea2D extends Object2DBase {

    constructor(uuid: string) {
        super(uuid);
    }

    public get subArea(): IEntitySubArea | undefined {
        return this.entity as IEntitySubArea;
    }

    public update(): any | undefined {
        if (!this.subArea) {
            console.error("分区实体不存在，无法更新");
            return undefined;
        }
        return this.subArea;
    }

    public hitTest(point: Vector3): boolean {
        if(!this.subArea?.rect) {
            return false;
        }
        return this.subArea.rect.containsPoint(point);
    }
    
    public render(painter: TPainter): void {
        if (!this.subArea)  return;
        let color = this.subArea.color;
        let rect = this.subArea.rect;
        let space_area_type = this.subArea.spaceAreaType;

        painter.fillStyle = color;
        painter.fillPolygon(rect, 0.2);
        painter.strokeStyle = color;
        painter.strokePolygons([rect]);

        let text = IType2UITypeDict[space_area_type] || RoomEntityCategory.Untitled;
        painter.drawText(text, rect.rect_center, 0, painter.clientScale > 1.5 ? 750 : 570, 10, true, true)
    }
} 