import { ZEdge } from "@layoutai/z_polygon";

import { TFeatureShape, TFigureElement } from "@layoutai/layout_scheme";
import { TLayoutRelation } from "../TLayoutRelation";


export class TEleToWallRelation extends TLayoutRelation
{
    wall_id : number;

    _face_angle : number;

    _nor_dist_to_wall : number;
    _dir_dist_on_wall : number;
    _m_rect_p_id : number;

    _figure_ele : TFigureElement;

    constructor()
    {
        super();
        this._figure_ele  = null;
        this.wall_id = 0;
        this._face_angle = 0;
        this._nor_dist_to_wall = 0;
        this._dir_dist_on_wall = 0;
        this._m_rect_p_id = 0;
    }

    get wall_edge()
    {
        return this._layout_f_shape?._contours._polygons[0].edges[this.wall_id];
    }
    makeRelation(edge:ZEdge, ele: TFigureElement)
    {
        if(!this._layout_f_shape) return;
        let rect = ele.rect;
        this.wall_id = this._layout_f_shape._contours.edges.indexOf(edge);
        if(this.wall_id < 0) return;

        let min_dist = 999999;

        let points = rect.getRectPoints();
        let m_p_id = 0;
        for(let i=0; i < 4; i++)
        {
            let pos = points[i];
            let pp = edge.projectEdge2d(pos);
            if(Math.abs(pp.y) < min_dist)
            {
                min_dist = pp.y;
                m_p_id = i;
            }
        }

        let nor = rect._nor;

        let a_y = nor.dot(edge.nor);
        let a_x = nor.dot(edge.dv);
        this._m_rect_p_id = m_p_id;
        this._face_angle = Math.atan2(a_y,a_x);
    
        let pp = edge.projectEdge2d(rect._back_center);
        this._nor_dist_to_wall = pp.y;
        this._dir_dist_on_wall = pp.x / edge.length;


        this._figure_ele = ele;


    }

    apply(): void {
        let wall_edge = this.wall_edge;
        if(!wall_edge) {
            console.log("WallEdge not Found!");
             return;
        }
        let a_x = Math.cos(this._face_angle);
        let a_y = Math.sin(this._face_angle);
        let nor = wall_edge.nor.clone().multiplyScalar(a_y).add(wall_edge.dv.clone().multiplyScalar(a_x));
        nor.normalize();

        let rect =this._figure_ele.rect;
        rect._nor.copy(nor);
        rect.updateRect();

        let point =  rect.getRectPoints()[this._m_rect_p_id];

        if(!point)
        {
            console.log("Point Error");
            return;
        }

        // let cp = wall_edge.projectEdge2d(rect.rect_center);
        // let dist = this._nor_dist_to_wall -  (cp.y);

        let xx = this.wall_edge.length * this._dir_dist_on_wall;
        let tpp = wall_edge.unprojectEdge2d({x:xx,y:this._nor_dist_to_wall});

        
        rect._back_center.copy(tpp);
        rect.updateRect();
    }

    static extract(feature_shape:TFeatureShape, element : TFigureElement) : TEleToWallRelation[]
    {
        let rect = element.rect.clone();
        let ele_wall_relations : TEleToWallRelation[] = [];
        for(let edge of feature_shape._contours._polygons[0].edges)
        {
            let ele_relation = new TEleToWallRelation();
            ele_relation._layout_f_shape = feature_shape;
            ele_relation._src_layout_f_shape = feature_shape;
            ele_relation.makeRelation(edge,element);
            ele_wall_relations.push(ele_relation);
        }

        ele_wall_relations.sort((a,b)=>Math.abs(a._nor_dist_to_wall) - Math.abs(b._nor_dist_to_wall));

        let ans_relations : TEleToWallRelation[] = [];

        for(let relation of ele_wall_relations)
        {
            if(ans_relations.length == 0)
            {
                ans_relations.push(relation);
                break;
            }
            else{
                if(Math.abs(ans_relations[0].wall_edge.nor.dot(relation.wall_edge.nor)) < 0.5)
                {
                    ans_relations.push(relation);
                    break;
                }
            }
            
        }

        return ans_relations;
    }
}