import { createStyles } from '@svg/antd/es/theme/utils';

const useStyles = createStyles(({ css }) => ({
    container: css`
        width:100%;
        height:100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    `,
    text: css`
        color: #000000;
        font-family: PingFang SC;
        font-weight: 600;
        font-size: 14px;
        line-height: 1.67;
        letter-spacing: 0px;
        text-align: center;
    `,
    
    rotating: css`
        @keyframes rotating {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }
        animation: rotating 2s linear infinite;
    `,
}));

export default useStyles;
