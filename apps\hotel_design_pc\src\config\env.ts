const envTypes = [
  'dev',
  'test',
  'hws',
  'pre',
  'prod',
] as const;

export type EnvType = typeof envTypes[number];

const devHosts = ['test.3vjia.com', 'test.3weijia.com', 'test.aihouse.com', 'dev-hws-3d.3weijia.com', 'autotest-hws-3d.3weijia.com', 'plugin.3weijia.com', 'plugin.3vjia.com'];
const local3dHost = ['local3d.3vjia.com'];
const prodHosts = ['3d.3vjia.com', 'www.aihouse.com', 'aihouse.3vjia.com', 'layoutai.3vjia.com'];
// 独立站点判断环境变量（因为本项目是通过pass平台直接发build包，不能通过node区分环境变量）
const hostname: string = window?.location?.hostname || '';

let env: EnvType = 'prod';
let appId = '';
let importType = '';
let cadFile = '';
let id = '';
let roomTemplate_RoomId = '';
let miniAPP = false;
let isStandaloneWebsite: boolean = false;   //判断是否是独立站点
let isDreamerMiniApp: boolean = false; // 是否是梦想家上的小程序
let isDebugModeWebsite = 0;
let schemeId = '';
let mode = '';
let from = '';
let editHxId = '';
let createHxId = '';
let magiccubeToken = '';
let designerId = '';
let designerOpenId = '';
let visitorOpenId = '';
let source = '';

if (devHosts.includes(hostname)) {
  env = 'dev';
} else if (prodHosts.includes(hostname)) {
  env = 'prod'
} else if (hostname.indexOf('hws') > -1) {
  env = 'hws';
} else if (hostname.indexOf('pre') > -1) {
  env = 'pre';
} else if (hostname.indexOf('test') > -1) {
  env = 'test';
}

// 获取URL参数，环境参数appEnv优先级高
if (window?.URLSearchParams) {
  const urlParams = new URLSearchParams(window.location.search);
  const route = window.location.pathname;
  appId = urlParams.get('appId') || '';
  importType = urlParams.get('importType') || '';
  miniAPP = urlParams.get('importType') === 'aiDesk' || (route.includes('dreamer')) || (route.includes('Dreamer')) ? true : false;
  isDreamerMiniApp = (route.includes('dreamer')) || (route.includes('Dreamer'));
  mode = urlParams.get('mode') || '';
  from = urlParams.get('from') || '';
  designerId = urlParams.get('designerId') || '';
  designerOpenId = urlParams.get('designerOpenId') || '';
  visitorOpenId = urlParams.get('visitorOpenId') || '';
  source = urlParams.get('source') || '';
  editHxId = urlParams.get('editHxId') || '';
  createHxId = urlParams.get('createHxId') || '';
  cadFile = urlParams.get('cadFile') || '';
  id = urlParams.get('id');
  roomTemplate_RoomId = urlParams.get("room_id");
  isDebugModeWebsite = (urlParams.get("debug") == "true" || urlParams.get("debug") == "1") ? 1 : 0;
  schemeId = urlParams.get("schemeId");
  isStandaloneWebsite = window.location.hostname.includes('local3d') || window.location.hostname.includes('layoutai') || (route == '/Dreamer') || (route == '/Training') ? true : false;
  const appEnv = urlParams.get('appEnv') as EnvType;
  magiccubeToken = urlParams.get("magiccubeToken");
  if (envTypes.includes(appEnv)) {
    env = appEnv;
  }
}

export function GetAppId() {
  const urlParams = new URLSearchParams(window.location.search);
  let appId = urlParams.get('appId') || '';
  return appId;
}

export const app_Id = appId;
export const import_Type = importType;
export const mini_APP = miniAPP;
export const cad_file = cadFile;
export const designer_id = designerId;
export const designer_open_id = designerOpenId;
export const visitor_open_id = visitorOpenId;

export const is_standalone_website: boolean = isStandaloneWebsite;  //判断独立站点
export const is_dreamer_mini_App = isDreamerMiniApp;
export const is_debugmode_website = isDebugModeWebsite;
export const EnvParams = {
  id: id,
  roomTemplate_RoomId: roomTemplate_RoomId
}
export const ENV: EnvType = env;
export const _magiccubeToken = magiccubeToken;

let isLocal3dEnv: boolean = false;
// let isLocal3dEnv: boolean = process.env.LOCAL3D != null && process.env.LOCAL3D == "true";

export const isLocal3d = () => {
  return isLocal3dEnv;
}

export const scheme_Id = schemeId;

export const mode_type = mode;
export const _from = from;
export const _editHxId = editHxId;
export const _createHxId = createHxId;
export const _source = source;