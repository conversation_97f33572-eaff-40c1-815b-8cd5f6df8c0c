import React, { useEffect, useState } from "react";
import useStyles from "./style";
import { useTranslation } from "react-i18next";
import { observer } from 'mobx-react-lite';
import { useStore } from "../../store/RootStore";
import { TopMenu } from "@svg/antd-cloud-design";
import type { ITopMenuItem, TopMenuType } from "@svg/antd-cloud-design/lib/TopMenu";

/**
 * @description 顶部菜单
 */

// 顶部菜单配置
interface TopMenuProps {
    handler: ((cmd: string) => void) | null;
    title: React.ReactNode;
    create3DLayout: (() => void) | null;
}

// 顶部菜单项配置
interface ICommandTopMenuItem extends ITopMenuItem {
    command_name?: string;
    titleCn?: string;
    onClick?: () => void;
    subList?: ICommandTopMenuItem[];
}

const LayoutTopMenu: React.FC<TopMenuProps> = ({ handler, title, create3DLayout }) => {
    const { t } = useTranslation() as any;
    const { styles } = useStyles();
    const store = useStore();
    const designMode = store.homeStore.designMode; // 模式

    const [btnList, setBtnList] = useState<ICommandTopMenuItem[]>([]); // 主按钮
    const [rightBtnList, setRightBtnList] = useState<any[]>([]); // 右侧按钮

    let list: ICommandTopMenuItem[] = [];
    let display_btnList: ICommandTopMenuItem[] = []; // "显示"按钮的子列表

    useEffect(() => {
        let topMenuList = list.concat();
        topMenuList = [
            {
                id: "fileBtn",
                title: t("文件"),
                titleCn: "文件", //[i18n:ignore]
                icon: "iconfile",
                type: "label" as TopMenuType,
                subList: [
                    {
                        id: "create",
                        title: t("新建"),
                        titleCn: "新建", //[i18n:ignore]
                        type: "label" as TopMenuType,
                    },
                    {
                        id: "openImitateImage",
                        title: t("导入临摹图"),
                        titleCn: "导入临摹图", //[i18n:ignore]
                        // command_name: ,
                        type: "label" as TopMenuType,
                    },
                    {
                        id: "openSwjFileByBuildingId",
                        title: t("搜索户型库"),
                        titleCn: "搜索户型库", //[i18n:ignore]
                        // command_name: ,
                        type: "label" as TopMenuType,
                    },
                ],
            },
            {
                id: "saveHouseScheme",
                title: t("保存"),
                titleCn: "保存", //[i18n:ignore]
                icon: "iconsave",
                type: "label" as TopMenuType,
                subList: [
                    {
                        id: "saveAsNewHouseScheme",
                        title: t("另存为"),
                        titleCn: "另存为", //[i18n:ignore]
                        type: "label" as TopMenuType,
                    },
                ],
            },
            {
                id: "undoBtn",
                title: t("撤销"),
                titleCn: "撤销", //[i18n:ignore]
                icon: "iconundo",
                type: "label" as TopMenuType,
                // disabled: ,
                // command_name: ,
            },
            {
                id: "redoBtn",
                title: t("恢复"),
                titleCn: "恢复", //[i18n:ignore]
                icon: "iconredo",
                type: "label" as TopMenuType,
                // disabled: ,
                // command_name: ,
            },
            {
                id: "toolBtn",
                title: t("显示"),
                titleCn: "显示", //[i18n:ignore]
                icon: "icondisplay",
                subList: display_btnList,
            },
        ];
        setBtnList(topMenuList.filter((item) => item !== null));
    }, [designMode]);

    // 添加菜单项
    let menuItemKeySet = new Set();
    const addKeysToMenuItems = (items: ITopMenuItem[]): ITopMenuItem[] => {
        return items
            .map((item) => {
                // 使用id作为key
                const itemWithKey = { ...item, key: item?.id };
                if (itemWithKey.key == null) {
                    return itemWithKey;
                } else if (menuItemKeySet.has(itemWithKey.key)) {
                    console.error("Duplicate ITopMenuItem id:", item);
                } else {
                    menuItemKeySet.add(itemWithKey.key);
                }
                // 递归处理子菜单
                if (item.subList) {
                    itemWithKey.subList = addKeysToMenuItems(item.subList);
                }
                return itemWithKey;
            })
            .filter((item) => item.key != null); // 过滤掉空值
    };

    // 点击菜单项
    const onMenuItemClick = async (id: string, item: ICommandTopMenuItem) => {
        console.log(id, item);
    };

    return (
        <div className={styles.topmenuInfo}>
            <TopMenu
                logo={
                    !store.userStore.aihouse
                        ? "https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/logo.png"
                        : "https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/en_logo.png"
                }
                title={<>{title}</>}
                centerBtnList={addKeysToMenuItems(btnList)}
                rightBtnList={addKeysToMenuItems(rightBtnList)}
                onBtnClick={onMenuItemClick}
            />
        </div>
    );
};

export default observer(LayoutTopMenu);
