import { I_XmlCSwingdoorLeafEntity, I_XmlEntityBase } from "../IWardrobeEntityInterface";
import { XmlCDoorBoardEntityBase } from "./XmlCDoorBoardEntityBase";
import { XmlCHandlePartEntity } from "./XmlCHandlePartEntity";
import { XmlEntityBase } from "../XmlEntityBase";


export class XmlCSwingdoorLeafEntity extends XmlEntityBase implements I_XmlCSwingdoorLeafEntity {
    isFunctorSyncB?: boolean;
    placeRuleS?: string;
    isQuoteB?: boolean;
    isBoardPointEditUnlimitedB?: boolean;
    isQuoteSplitN?: number;
    isOutsourcingN?: number;
    ocTypeS?: string;
    standardCategoryS?: string;
    isSplitN?: number;
    isSetThI_XmlCkN?: number;
    canReplaceMaterialB?: boolean;
    canSetHandleSizeB?: boolean;
    leafTypeN?: number;
    openDirectionN?: number;
    openPositionN?: number;
    hingeMountN?: number;
    connectDoorIdS?: string;
    Visible?: {
        MiddleVariable?: {
            nameS?: string;
            valueS?: string;
            valueExpressionS?: string;
        };
    };
    CWhMaterialComponent?: Record<string, unknown>;

    declare Children?: {
        CDoorBoardEntityBase?: XmlCDoorBoardEntityBase[];
        CHandlePartEntity?: XmlCHandlePartEntity[];
    };
    EdgeComponent?: {
        edgeBandModeN?: number;
        edgeBandPlanInfoS?: string;
        edgeModifyByUserN?: number;
    };

    constructor(data?: Partial<I_XmlCSwingdoorLeafEntity>) {
        super(data);
        this.EdgeComponent = data.EdgeComponent || {};
        this.CWhMaterialComponent = data.CWhMaterialComponent || {};
        this.Visible = data.Visible || {};
        this.ocTypeS = data.ocTypeS || "";
        this.standardCategoryS = data.standardCategoryS || "";
        this.isFunctorSyncB = data.isFunctorSyncB ?? false;
        this.canReplaceMaterialB = data.canReplaceMaterialB ?? false;
        this.canSetHandleSizeB = data.canSetHandleSizeB ?? false;     
        this.isQuoteB = data.isQuoteB ?? false;
        this.isSplitN = data.isSplitN ?? 0;
        this.isSetThI_XmlCkN = data.isSetThI_XmlCkN ?? 0;
        this.placeRuleS = data.placeRuleS ?? "";
        this.openDirectionN = data.openDirectionN ?? 0;
        this.openPositionN = data.openPositionN ?? 0;
        this.leafTypeN = data.leafTypeN ?? 0;
    }
}
XmlEntityBase.Generators["CSwingdoorLeafEntity"] = (data:I_XmlEntityBase)=>new XmlCSwingdoorLeafEntity(data);