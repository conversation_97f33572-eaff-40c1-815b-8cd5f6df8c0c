import { useState, useEffect } from 'react';
import { appContext } from '../context/AppContext';
import type { StateChangeEvent } from '../context/fsm/FSM';

/**
 * 自定义Hook：监听FSM状态变化
 * 替代定时器轮询的优雅解决方案
 */
export function useFSMState() {
  const [currentState, setCurrentState] = useState<string>(
    appContext.mainFSM.getCurrentLeafState().name
  );

  useEffect(() => {
    const handleStateChange = (event: StateChangeEvent) => {
      console.log('FSM状态变化:', event);
      const newState = appContext.mainFSM.getCurrentLeafState().name;
      setCurrentState(newState);
    };

    // 添加状态变化监听器
    appContext.mainFSM.addStateChangeListener(handleStateChange);

    return () => {
      // 清理监听器
      appContext.mainFSM.removeStateChangeListener(handleStateChange);
    };
  }, []);

  return {
    currentState,
    fsm: appContext.mainFSM
  };
}

/**
 * 自定义Hook：监听特定FSM的状态变化
 */
export function useSpecificFSMState(fsmName: string) {
  const [currentState, setCurrentState] = useState<string>('');

  useEffect(() => {
    const handleStateChange = (event: StateChangeEvent) => {
      // 只监听特定FSM的状态变化
      if (event.toState.includes(fsmName.toLowerCase())) {
        setCurrentState(event.toState);
      }
    };

    appContext.mainFSM.addStateChangeListener(handleStateChange);

    return () => {
      appContext.mainFSM.removeStateChangeListener(handleStateChange);
    };
  }, [fsmName]);

  return currentState;
}
