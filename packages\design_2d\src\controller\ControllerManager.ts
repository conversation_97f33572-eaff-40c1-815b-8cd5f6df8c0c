import { IControllerBase } from "../types/IControllerBase";
import { IEventCenter } from "../events/IEventCenter";
import { CanvasEventType } from "../events/const/CanvasEventType";
import { CanvasTransformCtrl } from "./ctrls/CanvasTransformCtrl";
import { ControllerType } from "./ControllerType";
import { CanvasLMBMoveCtrl } from "./ctrls/CanvasLMBMoveCtrl";
import { Design2DContext } from "../Design2DContext";

/**
 * 控制器管理器
 * 负责管理特定事件中心的控制器
 */
export class ControllerManager {
    private _context: Design2DContext;
    private _controllers: Map<string, IControllerBase> = new Map();
    private _activeControllers: Set<IControllerBase> = new Set();
    private _isInit: boolean = false;

    constructor(context: Design2DContext) {
        this._context = context;
    }

    /**
     * 获取事件中心
     */
    private get eventCenter(): IEventCenter {
        if (!this._context) {
            throw new Error('Context not set. Call setContext() first.');
        }
        return this._context.eventCenter;
    }

    /**
     * 初始化控制器管理器
     */
    public init(): void {
        if (this._isInit) {
            return;
        }
        this._isInit = true;
        
        // 注册默认控制器
        this._registerDefaultControllers();
        
        this._setupEventListeners();
    }

    /**
     * 注册默认控制器
     */
    private _registerDefaultControllers(): void {
        // 创建并注册画布变换控制器
        this.createAndRegisterCtrl(ControllerType.CANVAS_TRANSFORM, CanvasTransformCtrl);
        
        // 默认激活画布变换控制器
        this.activateCtrl(ControllerType.CANVAS_TRANSFORM);

        // 创建并注册画布左键移动控制器
        this.createAndRegisterCtrl(ControllerType.CANVAS_LMB_MOVE, CanvasLMBMoveCtrl);

        // 默认激活画布左键移动控制器
        // this.activateCtrl(ControllerType.CANVAS_LMB_MOVE);
    }

    /**
     * 设置事件监听器
     */
    private _setupEventListeners(): void {
        // 监听Canvas事件
        this.eventCenter.onCanvas<MouseEvent>(CanvasEventType.MOUSE_DOWN, (event: MouseEvent) => {
            for (const controller of this._activeControllers) {
                controller.onMouseDown(event);
            }
        });

        this.eventCenter.onCanvas<MouseEvent>(CanvasEventType.MOUSE_MOVE, (event: MouseEvent) => {
            for (const controller of this._activeControllers) {
                controller.onMouseMove(event);
            }
        });

        this.eventCenter.onCanvas<MouseEvent>(CanvasEventType.MOUSE_UP, (event: MouseEvent) => {
            for (const controller of this._activeControllers) {
                controller.onMouseUp(event);
            }
        });

        this.eventCenter.onCanvas<WheelEvent>(CanvasEventType.WHEEL, (event: WheelEvent) => {
            for (const controller of this._activeControllers) {
                controller.onWheel(event);
            }
        });
    }


    /**
     * 创建并注册控制器
     * @param ctrlType 控制器类型
     * @param CtrlClass 控制器类
     */
    public createAndRegisterCtrl<T extends IControllerBase>(
        ctrlType: string, 
        CtrlClass: new (context: Design2DContext) => T
    ): T {
        if (this._controllers.has(ctrlType)) {
            console.warn(`控制器 ${ctrlType} 已存在，将被覆盖`);
            this.unregisterCtrl(ctrlType);
        }
        
        const controller = new CtrlClass(this._context);
        this._controllers.set(ctrlType, controller);
        return controller;
    }

    /**
     * 注销控制器
     * @param ctrlType 控制器类型
     */
    public unregisterCtrl(ctrlType: string): boolean {
        const ctrl = this._controllers.get(ctrlType);
        if (ctrl) {
            this.deactivateCtrl(ctrlType);
            ctrl.dispose();
            return this._controllers.delete(ctrlType);
        }
        return false;
    }

    /**
     * 激活控制器
     * @param ctrlType 控制器类型
     */
    public activateCtrl(ctrlType: string): boolean {
        const controller = this._controllers.get(ctrlType);
        if (!controller) {
            console.warn(`控制器 ${ctrlType} 不存在`);
            return false;
        }

        if (!this._activeControllers.has(controller)) {
            this._activeControllers.add(controller);
            controller.activate();
        }
        return true;
    }

    /**
     * 停用控制器
     * @param ctrlType 控制器类型
     */
    public deactivateCtrl(ctrlType: string): boolean {
        const controller = this._controllers.get(ctrlType);
        if (controller && this._activeControllers.has(controller)) {
            this._activeControllers.delete(controller);
            controller.deactivate();
            return true;
        }
        return false;
    }

    /**
     * 获取指定控制器
     * @param ctrlType 控制器类型
     */
    public getCtrl(ctrlType: string): IControllerBase | null {
        return this._controllers.get(ctrlType) || null;
    }

    /**
     * 检查控制器是否激活
     * @param ctrlType 控制器类型
     */
    public isCtrlActive(ctrlType: string): boolean {
        const controller = this._controllers.get(ctrlType);
        return controller ? this._activeControllers.has(controller) : false;
    }

    /**
     * 获取所有激活的控制器
     */
    public getActiveControllers(): IControllerBase[] {
        return Array.from(this._activeControllers);
    }

    /**
     * 获取所有注册的控制器
     */
    public getAllControllers(): IControllerBase[] {
        return Array.from(this._controllers.values());
    }

    /**
     * 清除所有控制器
     */
    public clearAllCtrl(): void {
        for (const controller of this._activeControllers) {
            controller.deactivate();
        }
        for (const controller of this._controllers.values()) {
            controller.dispose();
        }
        this._activeControllers.clear();
        this._controllers.clear();
    }

    /**
     * 销毁管理器
     */
    public dispose(): void {
        this.clearAllCtrl();
        this._isInit = false;
    }
}
