import { IMidWare, MidWare, setupRoyscene } from "@clouddesign/royscene_threejs_extend";
import { MeshName, RootGroupName, UserDataKey } from "@layoutai/model3d_api";
import { EnvType, getImgDomain } from "@svg/request";
import { dvosNetAPI } from "@clouddesign/design-designmaterial";
import { Light, Matrix4, Mesh, Object3D, Scene, WebGLRenderer } from "three";
import Cookies from "js-cookie";
import { RoyMsgEngine } from "./RoySceneEngine";

/**
* @description 3D工具类，不用依赖其它本地文件
* <AUTHOR>
* @date 2025-06-25
* @lastEditTime 2025-06-25 14:45:03
* @lastEditors xuld
*/
class Utils3D {
    static disposeObject(object: Object3D) {
        if ((object as Mesh).isMesh) {
            let mesh = (object as Mesh);
            if (mesh.geometry) {
                mesh.geometry.dispose();
            }
            if (mesh.material) {
                if (mesh.material instanceof Array) {
                    mesh.material.forEach((m) => {
                        (m.dispose && m.dispose());
                    })
                }
                else {
                    mesh.material.dispose();
                }
            }
        }
        if (object.children) {
            object.children.forEach((obj) => Utils3D.disposeObject(obj));
        }
    }
}


export interface IRoySceneExportResult {
    success: boolean; // 是否成功
    msg: string; // 错误信息
    roySceneId: string; // 场景id
    roySceneSchemeId: string; // 方案id
    roySceneVersion: number; // 版本号
}

/**
* @description 导出RoyScene的类
* <AUTHOR>
* @date 2024-12-23
* @lastEditTime 2024-12-23 11:19:28
* @lastEditors xuld
*/
export class RoySceneExporter {

    // 缓存导出结果
    public static exportSceneInfo: IRoySceneExportResult = {
        success: false,
        msg: '',
        roySceneId: '',
        roySceneSchemeId: '',
        roySceneVersion: 0
    }

    /**
    * @description 导出Scene
    * @param oriScene 要导出的 js 的Scene
    * @param schemeId 方案 id
    * @param isShow 是否显示
    * @return IRoySceneExportResult
    */
    public async exportScene(oriScene: Scene, schemeId: string): Promise<IRoySceneExportResult> {
        let render = new WebGLRenderer();
        RoySceneExporter.initEnv(render);

        const midWare = new MidWare();

        this.travelNode(oriScene, "", midWare, false);

        midWare.process();

        const rsJson = midWare.encodeToRoySceneData({
            authoringTool: "ai_layout"
        });
        console.log("导出RoyScene完成");
        const res = await midWare.splitBuffer({ royScene: rsJson });
        if (res) {
            console.log("提取buffer成功");
        } else {
            console.log("提取buffer失败");
            return { success: false, msg: "提取buffer失败", roySceneId: '', roySceneSchemeId: '', roySceneVersion: 0 };
        }
        const rsRes = await midWare.fullUpload(rsJson, schemeId, 0);
        if (rsRes.success) {
            console.log("上传RoyScene成功");
        } else {
            console.log("上传RoyScene失败");
        }
        RoySceneExporter.exportSceneInfo = {
            success: rsRes.success,
            msg: rsRes.msg,
            roySceneId: rsRes.roySceneId,
            roySceneSchemeId: rsRes.roySceneSchemeId,
            roySceneVersion: rsRes.roySceneVersion
        }
        console.log(JSON.stringify(rsRes));

        // 释放内存，防止泄漏（多次导出白屏或崩溃）
        render.clear();
        render.dispose();
        render.forceContextLoss();
        Utils3D.disposeObject(midWare.getObject3D());

        return RoySceneExporter.exportSceneInfo;
    }

    public getRoySceneUrl()
    {
        let info = RoySceneExporter.exportSceneInfo;
        const url = RoyMsgEngine.getUrl({
            workMode: 'svgRoyScene',
            schemeId: '',
            id: info.roySceneId,
            version: info.roySceneVersion
          });
          return url;
      
    }
    private canExportNode(obj: Object3D): boolean {
        // 灯光节点总时导出，因为显示出来会计算光照，比较卡
        // if (obj.name === RootGroupName.AILightsOffline) {
        //     return true;
        // }

        // // 夜间模式下，不导出白天灯光
        // if (obj.name === RootGroupName.DayLights) {
        //     let lightMode = LayoutAI_App.instance.scene3D.getLightMode();
        //     return lightMode !== SceneLightMode.Night;
        // }

        // // 夜间模式下，导出夜间灯光
        // if (obj.name === RootGroupName.NightLights) {
        //     let lightMode = LayoutAI_App.instance.scene3D.getLightMode();
        //     return lightMode === SceneLightMode.Night;
        // }

        // if (!obj.visible) {
        //     console.warn("忽略隐藏节点:", obj.name);
        // }

        return obj.visible;
    }

    private travelNode(obj: Object3D, parentName: string, midWare: IMidWare, isInRoot: boolean): void {
        if (!this.canExportNode(obj)) {
            return;
        }

        // 导出当前节点
        const ptlName = this.exportNode(obj, parentName, midWare, isInRoot);
        if (!ptlName) {
            return;
        }

        let isChildInRoot = false;
        if (obj.type === "Group" && obj.name === RootGroupName.Root) {
            isChildInRoot = true;
        }

        if (obj.name === MeshName.WinDoor) {
            // 门窗的子节点是确定的，不需要遍历子节点，需要跳过白模，导出成品模型
            this._exportModel(obj, midWare, parentName);
        } else if (obj.name === MeshName.Furniture) {
            if (this._isCustomCabinet(obj)) {
                // 定制柜当mesh导出
                for (let i = 0; i < obj.children.length; i++) {
                    this.travelNode(obj.children[i] as Object3D, ptlName, midWare, isChildInRoot);
                }
            } else if (this._isGroupModel(obj)) {
                let furnitureNode = obj;
                let modelNode = furnitureNode.children.find(child => child.type === "Group");
                if (!modelNode) {
                    console.error("成品组合家具节点没有找到模型节点", furnitureNode.name);
                    return;
                }
                const modelPtlName = this.exportNode(modelNode, ptlName, midWare, false);
                // 成品模型组合, 导出模型组合材质
                for (let i = 0; i < modelNode.children.length; i++) {
                    this._exportModel(modelNode.children[i] as Object3D, midWare, modelPtlName);
                }
            } else {
                // 家具的子节点是确定的，不需要遍历子节点，需要跳过白模，导出成品模型
                this._exportModel(obj, midWare, parentName);
            }
        } else if (obj.name === MeshName.BaseGroup) {
            // 组合的子节点还是成品
            this._exportModel(obj, midWare, parentName);
        } else if (obj.name === MeshName.LightModel) {
            // 灯光模型
            this._exportModel(obj, midWare, parentName);
        } else if (obj.userData[UserDataKey.IsModel]) {
            // 无实体的成品模型
            this._exportModel(obj, midWare, parentName);
        } else {
            // 遍历子节点
            for (let i = 0; i < obj.children.length; i++) {
                this.travelNode(obj.children[i] as Object3D, ptlName, midWare, isChildInRoot);
            }
        }
    }

    // 判断是否是定制柜
    private _isCustomCabinet(obj: Object3D): boolean {
        for (let child of obj.children) {
            if (child.type === "Group" && child.userData[UserDataKey.FurnitureType] === MeshName.CustomModel) {
                return true;
            }
        }
        return false;
    }

    // 判断是否是成品模型组合
    private _isGroupModel(obj: Object3D): boolean {
        for (let child of obj.children) {
            if (child.type === "Group" && child.userData[UserDataKey.FurnitureType] === MeshName.GroupModel) {
                return true;
            }
        }
        return false;
    }

    private _exportModel(obj: Object3D, midWare: IMidWare, parentName: string) {
        if (obj.userData[UserDataKey.IsModel]) {
            let mid = obj.userData[UserDataKey.MaterialId];
            let ptlNameGroup = this.toSceneNode(obj, midWare, parentName);
            let userData = null;
            // 模型 IES 光
            let modelLights = obj?.userData[UserDataKey.ModelLights];
            if (modelLights && modelLights.length > 0) {
                userData = {
                    lights: modelLights
                };
            }
            // 模型 mesh 光
            let meshLight = obj?.userData[UserDataKey.MeshLight];
            if (meshLight) {
                userData = {
                    light: meshLight
                };
            }
            this.toCloudModel(mid, midWare, ptlNameGroup, userData);
            // console.log("export model", mid);
            return;
        }


        for (let child of obj.children) {
            if (!child.visible) {
                continue;
            }
            if (child.type === "Group") {
                let groupNode = child;
                // 飘窗高度特殊处理
                let mid = groupNode.userData[UserDataKey.MaterialId];
                let ptlNameGroup;
                if (mid == "24661567") {
                    // 把飘窗的高度偏移到1517
                    ptlNameGroup = this.toBayWindowSceneNode(obj, groupNode as Object3D, midWare, parentName);
                } else {
                    ptlNameGroup = this.toSceneNode(groupNode as Object3D, midWare, parentName);
                }
                this.toCloudModel(mid, midWare, ptlNameGroup);
            }
            else if (child.type === "Mesh") {
                // 白模
                console.log("skip model child type sample");
            } else {
                console.log("skip model child type", child.type);
            }
        }
    }

    /**
    * @description 导出当前节点
    * @param obj 当前节点
    * @param parentName 父节点名称
    * @param midWare 导出器
    * @param isInRoot 是否在根节点中
    * @return 当前节点的ptlName
    */
    private exportNode(obj: Object3D, parentName: string, midWare: IMidWare, isInRoot: boolean): string {
        // console.log(obj.type, obj.name);

        // TODO 为了测试方便，跳过天花、地板和内墙
        // if (obj.name === MeshName.Ceiling
        //     || obj.name === MeshName.Floor
        //     || obj.name === MeshName.InnerWall) {
        //     console.warn("skip:", obj.name);
        //     return "";
        // }

        let ptlName = "";
        switch (obj.type) {
            case "Object3D":
            case "Scene":
            case "Group":
                if (isInRoot) {
                    // 导出白名单中的Group
                    const validGroups = [
                        RootGroupName.DayLights,
                        RootGroupName.Walls,
                        RootGroupName.Rooms,
                        RootGroupName.Windows,
                        RootGroupName.Furniture,
                        RootGroupName.AILightsRT,
                        RootGroupName.AILightsOffline,
                    ];
                    if (validGroups.includes(obj.name)) {
                        ptlName = this.toSceneNode(obj as Object3D, midWare, parentName);
                    } else {
                        console.warn("忽略Group:", obj.name);
                    }
                } else {
                    ptlName = this.toSceneNode(obj as Object3D, midWare, parentName);
                }
                break;
            case "Mesh":
                if (obj.name === MeshName.Sky) {
                    // 忽略天空盒
                    console.warn("忽略Sky:", obj.name);
                } else if (obj.name === MeshName.WinDoor) {
                    // 门窗的节点 mesh 导出位 node，子节点才是具体的内容
                    ptlName = this.toSceneNode(obj as Object3D, midWare, parentName);
                } else if (obj.name === MeshName.FigureSample || obj.name === MeshName.BoxSegment) {
                    // 忽略门窗下的白模节点
                    console.warn("忽略:", obj.name);
                } else {
                    let mesh = obj as Mesh;
                    let geo = mesh.geometry;
                    if (geo.index && geo.attributes.position && geo.attributes.normal && geo.attributes.uv) {
                        if (geo.attributes.position.array.buffer.byteLength == 0) {
                            console.warn("几何顶点数为0");
                        }
                        ptlName = this.toCustomModel(mesh, midWare, parentName);
                    } else {
                        if (!geo.index) {
                            console.error("mesh 没有 index", mesh.name);
                        }
                        if (!geo.attributes.position) {
                            console.error("mesh 没有 position", mesh.name);
                        }
                        if (!geo.attributes.normal) {
                            console.error("mesh 没有 normal", mesh.name);
                        }
                        if (!geo.attributes.uv) {
                            console.error("mesh 没有 uv", mesh.name);
                        }
                    }
                }
                break;
            case "DirectionalLight":
            case "PointLight":
            case "RectAreaLight":
                let light = obj as Light;
                this.toLight(light, midWare, parentName);
                break;
            default:
                console.warn("未知对象类型:", obj.type);
                break;
        }
        return ptlName;
    }

    private getTransform(obj: Object3D): any {
        return {
            position: { x: obj.position.x, y: obj.position.y, z: obj.position.z },
            rotation: { x: obj.quaternion.x, y: obj.quaternion.y, z: obj.quaternion.z, w: obj.quaternion.w },
            scale: { x: obj.scale.x, y: obj.scale.y, z: obj.scale.z }
        };
    }

    private toSceneNode(obj: Object3D, midWare: IMidWare, parentName: string): string {
        let transform = this.getTransform(obj);
        let root = midWare.createRoySceneNode(parentName, transform);
        return root.ptlName;
    }

    // 飘窗位置和高度特殊处理，参考 DesignXmlMaker 的飘窗处理
    private toBayWindowSceneNode(entityObj: Object3D, obj: Object3D, midWare: IMidWare, parentName: string): string {
        let win: any = entityObj.userData['EntityOfMesh'];
        let rect = win.rect;
        let t_center = rect.rect_center;
        let wall_rect = win._wall_rect;
        if(wall_rect)
        {
            let pp =wall_rect.project(t_center);
            t_center =wall_rect.unproject({ x: pp.x, y: 0 });    
        }


        let scaleZ = win.height / 2034;
        let z = obj.position.z + 1017 * scaleZ;
        let transform = {
            position: { x: t_center.x, y: t_center.y, z: z },
            rotation: { x: obj.quaternion.x, y: obj.quaternion.y, z: obj.quaternion.z, w: obj.quaternion.w },
            scale: { x: obj.scale.x, y: obj.scale.y, z: obj.scale.z }
        };
        let root = midWare.createRoySceneNode(parentName, transform);
        return root.ptlName;
    }

    private toCustomModel(mesh: Mesh, midWare: IMidWare, parentName: string): string {
        let transform = this.getTransform(mesh);
        let userData: any = null;
        if (mesh.name === MeshName.Wall) {
            userData = {
                entityType: "mWall",
                modelType: "innerWall",
            }
        }
        else if (mesh.name === MeshName.Ceiling || mesh.name === MeshName.InnerWall) {
            userData = {
                entityType: "ceiling",
                modelType: "ceiling",
            }
        }
        else if (mesh.name === MeshName.Floor) {
            userData = {
                entityType: "floor",
                modelType: "floorSlab",
            }
        }
        let model = midWare.createCustomModel(mesh, parentName, transform, userData);
        return model.ptlName;
    }

    private toCloudModel(materialId: string, midWare: IMidWare, parentName: string, userData?: any): string {
        let transform = {
            position: { x: 0, y: 0, z: 0 },
            rotation: { x: 0, y: 0, z: 0, w: 1 },
            scale: { x: 1, y: 1, z: 1 }
        };
        let model = midWare.createCloudModel(materialId, parentName, transform, userData);
        return model.ptlName;
    }

    private toLight(light: Light, midWare: IMidWare, parentName: string): string {
        let transform = this.getTransform(light);
        // 导出插件有 bug，会修改 light 的父节点，先克隆一个
        let lightClone = light.clone();

        let ptlName = midWare.createLight(lightClone, parentName, transform);
        return ptlName;
    }

    private static initEnv(renderer: WebGLRenderer) {
        let env :EnvType = "hws";
        if (window.location.href.includes("3vjia.com")) {
            env = "pre"
        }

        //配置接口调用
        let token = Cookies.get("Magiccube-Token") || "";
        dvosNetAPI.setNetConfig({ env: env, token: token });

        //配置royscene空间到应用空间的适配变换，比如大小以及y-up等
        let mtx = new Matrix4();
        mtx.multiply(new Matrix4().makeRotationX(-Math.PI / 2));
        mtx.multiply(new Matrix4().makeScale(0.1, 0.1, 0.1));

        setupRoyscene({
            renderer: renderer as any,
            assetDomain: getImgDomain() + '/',
            adjustTransform: mtx,
            env: env
        });
    }
}

// 放入全局对象，方便调试
(globalThis as any).RoySceneExporter = RoySceneExporter;