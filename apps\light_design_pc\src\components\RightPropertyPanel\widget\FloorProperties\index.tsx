import React, { useEffect, useState } from "react";
import useStyles from "./style";
import { useTranslation } from "react-i18next";
import { observer } from "mobx-react-lite";
import type { IEntityRoom } from "@layoutai/design_domain";

/**
 * @description 楼层信息内容
 */

interface RoomSeriesPlanProps {
    roomInfos: IEntityRoom[];
    showLayoutList: boolean;
}

const FloorProperties: React.FC<RoomSeriesPlanProps> = ({ roomInfos, showLayoutList }) => {
    const { t } = useTranslation();
    const { styles } = useStyles();

    const [area, setArea] = useState(0);
    const [bedroomNum, setBedroomNum] = useState(0);
    const [livingRoomNum, setRivingRoomNum] = useState(0);
    const [toiletNum, setToiletNum] = useState(0);
    const [storeyHeight, setStoreyHeight] = useState(0);

    useEffect(() => {
        if (!roomInfos) return;
        let area = 0;
        let bedroomCount = 0;
        let livingRoomCount = 0;
        let toiletCount = 0;
        let storeyHeight = 0;

        roomInfos.forEach((room: IEntityRoom) => {
            area += room.area;
            if (room.name.includes("室") || room.name.includes("卧")) {
                bedroomCount++;
            }
            if (room.name.includes("厅")) {
                livingRoomCount++;
            }
            if (room.name.includes("厕所") || room.name.includes("卫生间")) {
                toiletCount++;
            }
            storeyHeight = Math.max(room.height, storeyHeight);
        });
        setArea(parseFloat(area.toFixed(2)));
        setBedroomNum(bedroomCount);
        setRivingRoomNum(livingRoomCount);
        setToiletNum(toiletCount);
        setStoreyHeight(storeyHeight);
    }, [roomInfos]);

    const modifyingStoreyHeight = (valueStr: string) => {
        const storeyHeight: number = Number(valueStr);
        setStoreyHeight(storeyHeight);
    };

    const modifiedStoreyHeight = (valueStr: string) => {
        if (!roomInfos) return;
        const storeyHeight: number = Number(valueStr);
        if (storeyHeight >= 2200 && storeyHeight <= 6000) {
            // (LayoutAI_App.instance as TAppManagerBase).layout_container._storey_height = storeyHeight;
            roomInfos.forEach((room: IEntityRoom) => {
                room.setHeight(storeyHeight);
            });
        } else {
            setStoreyHeight(roomInfos[0].height);
        }
    };

    return (
        <div className={styles.root}>
            <div className={styles.rootItem}>
                <div style={{ textAlign: "left" }}>{t("房屋使用面积")}</div>
                <div>{area}m²</div>
            </div>
            <div className={styles.rootItem}>
                <div style={{ textAlign: "left" }}>{t("户型")}</div>
                <div style={{ textAlign: "end" }}>
                    {bedroomNum} {t("室")} {livingRoomNum} {t("厅")} {toiletNum} {t("卫")}
                </div>
            </div>
            <div className={styles.rootItem}>
                <div style={{ textAlign: "left" }}>{t("户型朝向")}</div>
                <div>
                    <select>
                        <option>{t("未知")}</option>
                        <option>{t("南")}</option>
                        <option>{t("北")}</option>
                        <option>{t("东")}</option>
                        <option>{t("西")}</option>
                        <option>{t("东南")}</option>
                        <option>{t("东北")}</option>
                        <option>{t("西北")}</option>
                        <option>{t("西南")}</option>
                    </select>
                </div>
            </div>
            <div className={styles.rootItem}>
                <div>{t("当前层高")}</div>
                <div>
                    <input
                        value={storeyHeight}
                        type="number"
                        max="6000"
                        min="2200"
                        step={10}
                        onChange={(ev) => { modifyingStoreyHeight(ev.currentTarget.value)} }
                        onBlur={(ev) => { modifiedStoreyHeight(ev.currentTarget.value)} }
                    ></input>
                    <span className="unit">mm</span>
                </div>
            </div>
            <div className={styles.line}></div>
        </div>
    );
};

export default observer(FloorProperties);
