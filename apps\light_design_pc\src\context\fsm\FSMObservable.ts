import { Subject, Observable, BehaviorSubject } from 'rxjs';
import { distinctUntilChanged, map, filter } from 'rxjs/operators';
import type { StateChangeEvent } from './FSM';

/**
 * FSM响应式状态管理
 * 使用RxJS Observable提供更强大的状态监听能力
 */
export class FSMObservable {
  private stateChangeSubject = new Subject<StateChangeEvent>();
  private currentStateSubject = new BehaviorSubject<string>('');

  constructor() {
    // 监听状态变化并更新当前状态
    this.stateChangeSubject.subscribe(event => {
      this.currentStateSubject.next(event.toState);
    });
  }

  /**
   * 发送状态变化事件
   */
  public emitStateChange(event: StateChangeEvent): void {
    this.stateChangeSubject.next(event);
  }

  /**
   * 获取状态变化流
   */
  public getStateChanges(): Observable<StateChangeEvent> {
    return this.stateChangeSubject.asObservable();
  }

  /**
   * 获取当前状态流
   */
  public getCurrentState(): Observable<string> {
    return this.currentStateSubject.asObservable().pipe(
      distinctUntilChanged() // 只在状态真正变化时发出
    );
  }

  /**
   * 监听特定状态的变化
   */
  public onStateEnter(stateName: string): Observable<StateChangeEvent> {
    return this.stateChangeSubject.pipe(
      filter(event => event.toState === stateName)
    );
  }

  /**
   * 监听特定状态的退出
   */
  public onStateExit(stateName: string): Observable<StateChangeEvent> {
    return this.stateChangeSubject.pipe(
      filter(event => event.fromState === stateName)
    );
  }

  /**
   * 监听状态模式匹配
   */
  public onStatePattern(pattern: RegExp): Observable<StateChangeEvent> {
    return this.stateChangeSubject.pipe(
      filter(event => pattern.test(event.toState))
    );
  }

  /**
   * 获取状态名称流（只返回状态名称）
   */
  public getStateNames(): Observable<string> {
    return this.stateChangeSubject.pipe(
      map(event => event.toState),
      distinctUntilChanged()
    );
  }
}

// 创建全局实例
export const fsmObservable = new FSMObservable();
