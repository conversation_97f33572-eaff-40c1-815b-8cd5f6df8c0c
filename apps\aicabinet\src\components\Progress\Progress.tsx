import React, { useEffect, useState } from 'react';
import useStyles from './style';
// import { useTranslation } from "react-i18next";
import { Progress } from '@svg/antd'
import { observer } from 'mobx-react';
import aiDrawImg from '@/assets/aidraw_logo.png';

interface ProgressComponentProps {
    onFinish: () => void;
    title: string;
    step: number;
}
const ProgressComponent: React.FC<ProgressComponentProps> = ({ onFinish, title, step }) => {
    // const { t } = useTranslation();
    const { styles } = useStyles();
    const [progress, setProgress] = useState(0);

    useEffect(() => {
        const interval = setInterval(() => {
            setProgress(prev => {
                const nextProgress = Math.min(prev + step, 100);
                if (nextProgress >= 100) {
                    clearInterval(interval);
                }
                return nextProgress;
            });
        }, 100);

        return () => clearInterval(interval);
    }, []);

    useEffect(() => {
        if (progress >= 100) {
            onFinish();
        }
    }, [progress, onFinish]);

    return (
        <div className={styles.container}>
            <img className={styles.rotating} src={aiDrawImg} alt="" style={{ height: '48px'}} />
            <Progress 
                style={{ width: '200px', marginTop: '20px', marginBottom: '12px' }}
                percent={progress}
                strokeColor={{ from: '#B963F0', to: '#5D42FB' }}
                trailColor="#eaeaea"
                showInfo={false}
            />
            <span className={styles.text}>{title}</span>
        </div>
    );
};

export default observer(ProgressComponent); 