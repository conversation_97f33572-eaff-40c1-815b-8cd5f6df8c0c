import i18n from 'i18next';
import { initReactI18next, useTranslation as useI18nTranslation } from 'react-i18next';
import { getBrowserLang } from '@/utils';
import zh_CN from './zh-CN';
import en_US from './en-US';

// 加载远程数据
// getLangData().then((res: any) => {
//   // 没有使用命名空间
//   i18n.addResourceBundle(res.lang, 'translation', res.data)
//   i18n.changeLanguage(res.lang)
// })


/**
 * @description 描述
 * @param  {number} xxx - 名称
 * @return {number} xxx - 名称
 */
export const injectI18n = async () => {
  i18n
    .use(initReactI18next) // passes i18n down to react-i18next
    .init({
      // 设置语言数据
      resources: {
        zh_CN: {
          translation: {
            ...zh_CN,
          }
        },
        en_US: {
          translation: {
            ...en_US,
          }
        },
      },
      debug: true,
      lng: getBrowserLang(),
      interpolation: {
        escapeValue: false // react already safes from xss
      }
    });
};

/**
 * @description 描述
 * @param  {number} xxx - 名称
 * @return {number} xxx - 名称
 */
export function useTranslation(nameSpace?: string) {
  const { t } = useI18nTranslation();
  /**
   * @description 描述
   * @param  {number} xxx - 名称
   * @return {number} xxx - 名称
   */
  return {
    t: (name: string) => {
      if (nameSpace) {
        return t(`${nameSpace}.${name}`);
      } else {
        return t(`${name}`);
      }
    }
  };
}
