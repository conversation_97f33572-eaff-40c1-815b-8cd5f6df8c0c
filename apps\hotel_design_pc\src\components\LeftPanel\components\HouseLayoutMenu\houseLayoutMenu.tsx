import { EventName, FigureDataList, LayoutAI_App, LayoutAI_Commands, LayoutAI_Events } from '@layoutai/layout_scheme';
import { useStore } from '@/models';
import { message } from '@svg/antd';
import { observer } from "mobx-react-lite";
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import useStyles from './style';
import FigureList from '../FigureList/figureList';

interface Module {
  image: string;
  png: string;
  title: string;
  label: string;
  icon: string;
}

/**
 * @description 按钮组件
 */
const HouseLayoutMenu: React.FC = () => {
  const { t } = useTranslation()
  const { styles } = useStyles();
  const [figureDataList, setFigureDataList] = useState<Module[]>([]);
  const [structural, setStructural] = useState<Module[]>([]);
  const menuList = FigureDataList;
  const store = useStore();
  let allList: any = [];

  const DRAW_WALL_MENU = [
    // 画墙
    [{
      title: t('直墙'),
      icon: 'iconstraightwall',
      command: LayoutAI_Commands.DrawStraightWall,
    },
    {
      title: t('矩形墙'),
      icon: 'icondrawroom',
      command: LayoutAI_Commands.DrawRectangleWall,
    }],
    // 从形状绘制
    [{
      title: t('正方形'),
      icon: 'iconsquare',
      command: "square",
    },
    {
      title: t('长方形'),
      icon: 'iconrectangle1',
      command: "rectangle",
    },
    {
      title: t('L形'),
      icon: 'iconLshape',
      command: "LShape",
    },
    {
      title: t('凸形'),
      icon: 'iconconvex',
      command: "convex",
    },
    {
      title: t('凹形'),
      icon: 'iconconcave',
      command: "concave",
    },
    {
      title: t('多边形'),
      icon: 'iconpolygon',
      command: "polygon",
    },
  ]]

  menuList.forEach((item) => {
    item.child.forEach((childItem) => {
      allList = allList.concat(childItem.figureList);
    });
  });
  
  const drawWallHandle = (command: string) => {
    if (command === LayoutAI_Commands.DrawStraightWall
      || command === LayoutAI_Commands.DrawRectangleWall
    ) {
      LayoutAI_App.RunCommand(command);
      message.info({
        duration: 1,
        content: t('点击右键可以退出户型绘制').toString(),
        style: {
          marginTop: '4vh',
          zIndex: 10,
        }});
    } else {
      LayoutAI_App.DispatchEvent(LayoutAI_Events.DrawShapeWall, command);
    }
  }
  const object_id = "FiguresMenu";

  const updateCurrentMenuList = () => {

    let mode = LayoutAI_App.instance._current_handler_mode;
    if (mode === "HouseDesign") {
      let t_menu_list = menuList.filter((data) => data.label.includes("户型")); //[i18n:ignore]
      let win_door_type = ['推拉门','单开门','一字窗','飘窗','双开门','子母门','门洞','垭口','栏杆']; //[i18n:ignore]
      let structural_type = ['包管','地台', '方柱', '横梁', '烟道']; //[i18n:ignore]
      let win_door_list: any = t_menu_list[0].child[0]?.figureList.filter((data) => win_door_type.includes(data.title));
      setFigureDataList(win_door_list);
      let structural_list: any = t_menu_list[0].child[0]?.figureList.filter((data) => structural_type.includes(data.title));
      setStructural(structural_list)
    }
  }
  useEffect(() => {

    LayoutAI_App.on_M(EventName.AIDesignModeChanged, object_id, () => {

      updateCurrentMenuList();
    });

    updateCurrentMenuList();

  }, []);


  return (
    <div className={styles.root}>
      <div className={styles.title}>{t('画墙')}</div>
      <div className={styles.itemBox}>
        {DRAW_WALL_MENU[0].map((item) => (
          <div className={styles.item} key={item.command}
            onMouseDown={() => { drawWallHandle(item.command) }}
          >
            <div className={styles.itemIcon}>
              <svg className="icon" aria-hidden="true" style={{ width: 60 ,height:60 }}>
                <use xlinkHref={`#${item.icon}`}></use>
              </svg>
            </div>
            <div className={styles.desc}>{item.title}</div>
          </div>
        ))}
      </div>


      <div className={styles.title}>{t('从形状绘制')}</div>
      <div className={styles.itemBox}>
        {DRAW_WALL_MENU[1].map((item) => (
          <div className={styles.item} key={item.command}
            onMouseDown={() => { drawWallHandle(item.command) }}
          >
            <div className={styles.itemIcon}>
              <svg className="icon" aria-hidden="true" style={{ width: 60,height:60 }}>
              <use xlinkHref={`#${item.icon}`}></use>
              </svg>
            </div>
            <div className={styles.desc}>{item.title}</div>
          </div>
        ))}
      </div>
      <div className={styles.title}>{t('门窗')}</div>
      <FigureList data={figureDataList} />
      <div className={styles.title}>{t('结构件')}</div>
      <FigureList data={structural} />
    </div>
  );
};
export default observer(HouseLayoutMenu);
