import { I_XmlEntityBase } from "./IWardrobeEntityInterface";
import { NumberVariable } from "./NumberVariable";
import { StringVariable } from "./StringVariable";
import { XmlCWhSpacePartComponent } from "./XmlEntities/XmlCWhSpacePartComponent";


export class XmlEntityBase implements I_XmlEntityBase {
    EntityName?: string;
    uidN?: number;
    uidS?: string;
    nameS?: string;
    visibleB?: boolean;
    visibleExpS ?: string;
    DATAFORM?: string;
    nameExpS?: string;
    materialIdS?: string;
    typeS?: string;
    partNumberS?: string;
    subTypeS?: string;
    VariableComponet?: {
        NumberVariable?: NumberVariable[];
        StringVariable?: StringVariable[];
    };
    CWhSpacePartComponent?: XmlCWhSpacePartComponent;

    Children?: {
        [key:string]:XmlEntityBase[];
    };
    [key:string]:any;
    protected _valuesObj : {[key:string]:number|string};

    protected _parent: XmlEntityBase = null;

    _rawData : I_XmlEntityBase;
    public static Generators : {[key:string]:(data:I_XmlEntityBase)=>XmlEntityBase} = {}; 

    public static DefaultEntityName = "Base";
    public static GenerateEntity(key:string,data:I_XmlEntityBase) {
        if(XmlEntityBase.Generators[key])
        {
            return XmlEntityBase.Generators[key](data);
        }
        else if(XmlEntityBase.Generators[XmlEntityBase.DefaultEntityName])
        {
            return XmlEntityBase.Generators[XmlEntityBase.DefaultEntityName](data);
        }
        else{
            return new XmlEntityBase(data);
        }
    }
    constructor(data?: I_XmlEntityBase) {
        this.initAttributes(data);
        this.initChildren(data);

    }
    protected initAttributes(data ?: I_XmlEntityBase)
    {
        data = data || {};
        this._rawData = data;
        if (data) {
            for(let key in data)
            {
                this[key] = data[key];
            }
        }
        this.CWhSpacePartComponent = null;
        if(data.CWhSpacePartComponent)
        {
            this.CWhSpacePartComponent = new XmlCWhSpacePartComponent(data.CWhSpacePartComponent);
        }
    }
    protected initChildren(data ?: I_XmlEntityBase)
    {
        data = data || {};
        if (data.VariableComponet?.NumberVariable) {
            this.VariableComponet = this.VariableComponet || {};
            this.VariableComponet.NumberVariable = data.VariableComponet.NumberVariable.map(
                nv => new NumberVariable(nv)
            );
        }
        if (data.VariableComponet?.StringVariable) {
            this.VariableComponet = this.VariableComponet || {};
            this.VariableComponet.StringVariable = data.VariableComponet.StringVariable.map(
                nv => new StringVariable(nv)
            ); 
        }

        if(data.Children)
        {
            this.Children = {};
            for (const key in data.Children) {
                if (Array.isArray(data.Children[key])) {
                    this.Children[key] = (data.Children[key] as any[]).map(item => {
                        let res = XmlEntityBase.GenerateEntity(key,item);
                        res.parent = this;
                        return res;
                    });
                }
            }
        }
        this._valuesObj = {};
    }
    
    public get parent(): XmlEntityBase {
        return this._parent;
    }
    public set parent(value: XmlEntityBase) {
        this._parent = value;
    }
    updateValueObject()
    {
        if(this.VariableComponet)
        {
            if(this.VariableComponet.NumberVariable)
            {
                this.VariableComponet.NumberVariable.forEach((val)=>{
                    this._valuesObj[val.nameS] = val.valueN || 0;
                });
            }
            if(this.VariableComponet.StringVariable)
            {
                this.VariableComponet.StringVariable.forEach((val)=>{
                    this._valuesObj[val.nameS] = val.valueS || '';
                });
            }
        }
        return this._valuesObj;

    }
    get valuesObj()
    {
        if(!this._valuesObj) {
            this._valuesObj = {};
        }
        return this._valuesObj;
    }
}
