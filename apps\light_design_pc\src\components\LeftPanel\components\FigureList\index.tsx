import React, { useEffect } from "react";
import useStyles from "./style";
import { useTranslation } from "react-i18next";
import { observer } from "mobx-react-lite";
import { DragEventListener } from "@svg/antd-cloud-design";

/**
 * @description 缩略图展示列表（用于户型编辑面板中门窗、结构件部分）
 */

interface Module {
    image: string;
    png: string;
    title: string;
    label: string;
    group_code?: string;
    icon: string;
}

interface FigureListProps {
    data: Module[];
}

const FigureList: React.FC<FigureListProps> = ({ data }) => {
    const { t } = useTranslation();
    const { styles } = useStyles();
    const scrollContainerRef = React.useRef<HTMLDivElement>(null);

    useEffect(() => {
        const dragEventListener = new DragEventListener({
            // 展示缩略图
            isShowThumbnail: true,
            container: document.getElementById("side_pannel") as HTMLElement,
            // 打印
            log: false,
        });
        dragEventListener.bindDrag();
        return () => {
            dragEventListener.unbindDrag();
        };
    }, []);

    useEffect(() => {
        if (scrollContainerRef.current) {
            scrollContainerRef.current.scrollTop = 0; // 将滚动位置设置为 0
        }
    }, [data]);

    return (
        <div className={styles.figure} ref={scrollContainerRef}>
            {data.map((item, index) => (
                <div
                    key={index}
                    className="item"
                    onPointerDown={(ev) => {
                        let label = item.title;
                        if (item.group_code) {
                            label = "GroupTemplate:" + item.group_code;
                            // setLabel("GroupTemplate:"+item.group_code)
                        }

                        if (
                            item.title.includes("单开门") ||
                            item.title.includes("推拉门") ||
                            item.title.includes("一字窗") ||
                            item.title.includes("飘窗")
                        ) {
                            /*[i18n:ignore]*/
                            // LayoutAI_App.DispatchEvent(LayoutAI_Events.SelectedFurniture, label);
                            return;
                        } else {
                            // LayoutAI_App.DispatchEvent(LayoutAI_Events.SelectedFurniture, label);
                        }
                    }}
                >
                    <div className="image">
                        <svg className="icon" aria-hidden="true" style={{ width: 60 }}>
                            <use xlinkHref={`#${item.icon}`}></use>
                        </svg>
                    </div>
                    <div className="title">{t(item.title)}</div>
                </div>
            ))}
        </div>
    );
};

export default observer(FigureList);
