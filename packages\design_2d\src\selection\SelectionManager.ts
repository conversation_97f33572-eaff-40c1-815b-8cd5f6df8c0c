import { Design2DContext } from '../Design2DContext';
import { Object2DBase } from '../object2d/Object2DBase';
import { Vector2, Vector3 } from "three";
import { CoordinateTransformer } from '../utils/CoordinateTransformer';

/**
 * 选中管理器
 * 负责管理2D对象的选中状态
 */
export class SelectionManager {
    private _selectedObjects: Set<Object2DBase> = new Set();
    private _hoveredObject: Object2DBase | null = null;
    private _context: Design2DContext;

    constructor(context: Design2DContext) {
        this._context = context;
    }

    /**
     * 命中测试
     */
    public hitTest(point: Vector3): Object2DBase[] {
        const objects = this._context.object2DManager.getOrderedObjects().reverse();
        const result: Object2DBase[] = [];
        let globalPoint = CoordinateTransformer.canvasToGlobal(point, this._context);
        for (const obj of objects) {
            if (!obj.isInteractive) {
                continue;
            }

            // 击中区域是世界坐标系 将画布点转换为世界坐标点
            if (obj.hitTest(globalPoint)) {
                result.push(obj);
            }
        }
        return result;
    }

    /**
     * 获取当前选中的对象（单选模式）
     */
    public getSelectedObject(): Object2DBase | null {
        return this._selectedObjects.size > 0 
            ? Array.from(this._selectedObjects)[0] 
            : null;
    }

    /**
     * 获取所有选中对象（多选模式）
     */
    public getSelectedObjects(): Object2DBase[] {
        return Array.from(this._selectedObjects);
    }

    /**
     * 设置选中对象
     */
    public setSelection(object: Object2DBase | null): void {
        // 清除之前的选中
        this.clearSelection();
        
        if (object) {
            this._selectedObjects.add(object);
            object.setSelected(true);
            // 发送选中事件
        }
    }

    /**
     * 添加到选中（多选）
     */
    public addToSelection(object: Object2DBase): void {
        if (!this._selectedObjects.has(object)) {
            this._selectedObjects.add(object);
            object.setSelected(true);

        }
    }

    /**
     * 设置悬浮对象
     */
    public setHovered(object: Object2DBase | null): void {
        if (this._hoveredObject === object) return;

        if (this._hoveredObject) {
            this._hoveredObject.setHovered(false);
        }

        this._hoveredObject = object;
        if (object) {
            object.setHovered(true);
        }
    }

    /**
     * 清除选中
     */
    public clearSelection(): void {
        const deselected = Array.from(this._selectedObjects);
        this._selectedObjects.forEach(obj => obj.setSelected(false));
        this._selectedObjects.clear();
    }

    /**
     * 销毁
     */
    public dispose(): void {
        this.clearSelection();
        this._hoveredObject = null;
    }
} 