const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./hotel.Dos6w4x9.js","./index.DMfFgggD.js"])))=>i.map(i=>d[i]);
import{_ as ie}from"./index.DMfFgggD.js";var Q={exports:{}},B={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var le=window.React,se=Symbol.for("react.element"),ce=Symbol.for("react.fragment"),ue=Object.prototype.hasOwnProperty,fe=le.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,he={key:!0,ref:!0,__self:!0,__source:!0};function X(e,t,n){var a,r={},o=null,i=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(i=t.ref);for(a in t)ue.call(t,a)&&!he.hasOwnProperty(a)&&(r[a]=t[a]);if(e&&e.defaultProps)for(a in t=e.defaultProps,t)r[a]===void 0&&(r[a]=t[a]);return{$$typeof:se,type:e,key:o,ref:i,props:r,_owner:fe.current}}B.Fragment=ce;B.jsx=X;B.jsxs=X;Q.exports=B;var y=Q.exports;function $(){return $=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},$.apply(null,arguments)}var x;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(x||(x={}));var A=function(e){return e},J="beforeunload",pe="popstate";function de(e){e===void 0&&(e={});var t=e,n=t.window,a=n===void 0?document.defaultView:n,r=a.history;function o(){var s=a.location,c=s.pathname,m=s.search,R=s.hash,v=r.state||{};return[v.idx,A({pathname:c,search:m,hash:R,state:v.usr||null,key:v.key||"default"})]}var i=null;function l(){if(i)w.call(i),i=null;else{var s=x.Pop,c=o(),m=c[0],R=c[1];if(w.length){if(m!=null){var v=f-m;v&&(i={action:s,location:R,retry:function(){j(v*-1)}},j(v))}}else L(s)}}a.addEventListener(pe,l);var u=x.Pop,h=o(),f=h[0],d=h[1],p=G(),w=G();f==null&&(f=0,r.replaceState($({},r.state,{idx:f}),""));function E(s){return typeof s=="string"?s:ve(s)}function b(s,c){return c===void 0&&(c=null),A($({pathname:d.pathname,hash:"",search:""},typeof s=="string"?_(s):s,{state:c,key:me()}))}function H(s,c){return[{usr:s.state,key:s.key,idx:c},E(s)]}function D(s,c,m){return!w.length||(w.call({action:s,location:c,retry:m}),!1)}function L(s){u=s;var c=o();f=c[0],d=c[1],p.call({action:u,location:d})}function U(s,c){var m=x.Push,R=b(s,c);function v(){U(s,c)}if(D(m,R,v)){var S=H(R,f+1),T=S[0],k=S[1];try{r.pushState(T,"",k)}catch{a.location.assign(k)}L(m)}}function z(s,c){var m=x.Replace,R=b(s,c);function v(){z(s,c)}if(D(m,R,v)){var S=H(R,f),T=S[0],k=S[1];r.replaceState(T,"",k),L(m)}}function j(s){r.go(s)}var oe={get action(){return u},get location(){return d},createHref:E,push:U,replace:z,go:j,back:function(){j(-1)},forward:function(){j(1)},listen:function(c){return p.push(c)},block:function(c){var m=w.push(c);return w.length===1&&a.addEventListener(J,q),function(){m(),w.length||a.removeEventListener(J,q)}}};return oe}function q(e){e.preventDefault(),e.returnValue=""}function G(){var e=[];return{get length(){return e.length},push:function(n){return e.push(n),function(){e=e.filter(function(a){return a!==n})}},call:function(n){e.forEach(function(a){return a&&a(n)})}}}function me(){return Math.random().toString(36).substr(2,8)}function ve(e){var t=e.pathname,n=t===void 0?"/":t,a=e.search,r=a===void 0?"":a,o=e.hash,i=o===void 0?"":o;return r&&r!=="?"&&(n+=r.charAt(0)==="?"?r:"?"+r),i&&i!=="#"&&(n+=i.charAt(0)==="#"?i:"#"+i),n}function _(e){var t={};if(e){var n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));var a=e.indexOf("?");a>=0&&(t.search=e.substr(a),e=e.substr(0,a)),e&&(t.pathname=e)}return t}/**
 * React Router v6.3.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const V=window.React.createContext,C=window.React.useContext,K=window.React.useMemo,we=window.React.useRef,Z=window.React.useEffect,ge=window.React.useCallback,N=window.React.createElement;window.React.useState;window.React.useLayoutEffect;const Re=window.React.Children,ye=window.React.isValidElement,xe=window.React.Fragment,ee=V(null),M=V(null),F=V({outlet:null,matches:[]});function g(e,t){throw new Error(t)}function Pe(e,t,n){n===void 0&&(n="/");let a=typeof t=="string"?_(t):t,r=ne(a.pathname||"/",n);if(r==null)return null;let o=te(e);Se(o);let i=null;for(let l=0;i==null&&l<o.length;++l)i=Be(o[l],r);return i}function te(e,t,n,a){return t===void 0&&(t=[]),n===void 0&&(n=[]),a===void 0&&(a=""),e.forEach((r,o)=>{let i={relativePath:r.path||"",caseSensitive:r.caseSensitive===!0,childrenIndex:o,route:r};i.relativePath.startsWith("/")&&(i.relativePath.startsWith(a)||g(),i.relativePath=i.relativePath.slice(a.length));let l=P([a,i.relativePath]),u=n.concat(i);r.children&&r.children.length>0&&(r.index===!0&&g(),te(r.children,t,u,l)),!(r.path==null&&!r.index)&&t.push({path:l,score:ke(l,r.index),routesMeta:u})}),t}function Se(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:$e(t.routesMeta.map(a=>a.childrenIndex),n.routesMeta.map(a=>a.childrenIndex)))}const _e=/^:\w+$/,Ee=3,be=2,je=1,Ce=10,Oe=-2,Y=e=>e==="*";function ke(e,t){let n=e.split("/"),a=n.length;return n.some(Y)&&(a+=Oe),t&&(a+=be),n.filter(r=>!Y(r)).reduce((r,o)=>r+(_e.test(o)?Ee:o===""?je:Ce),a)}function $e(e,t){return e.length===t.length&&e.slice(0,-1).every((a,r)=>a===t[r])?e[e.length-1]-t[t.length-1]:0}function Be(e,t){let{routesMeta:n}=e,a={},r="/",o=[];for(let i=0;i<n.length;++i){let l=n[i],u=i===n.length-1,h=r==="/"?t:t.slice(r.length)||"/",f=Le({path:l.relativePath,caseSensitive:l.caseSensitive,end:u},h);if(!f)return null;Object.assign(a,f.params);let d=l.route;o.push({params:a,pathname:P([r,f.pathname]),pathnameBase:ae(P([r,f.pathnameBase])),route:d}),f.pathnameBase!=="/"&&(r=P([r,f.pathnameBase]))}return o}function Le(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,a]=Te(e.path,e.caseSensitive,e.end),r=t.match(n);if(!r)return null;let o=r[0],i=o.replace(/(.)\/+$/,"$1"),l=r.slice(1);return{params:a.reduce((h,f,d)=>{if(f==="*"){let p=l[d]||"";i=o.slice(0,o.length-p.length).replace(/(.)\/+$/,"$1")}return h[f]=Ne(l[d]||""),h},{}),pathname:o,pathnameBase:i,pattern:e}}function Te(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0);let a=[],r="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^$?{}|()[\]]/g,"\\$&").replace(/:(\w+)/g,(i,l)=>(a.push(l),"([^\\/]+)"));return e.endsWith("*")?(a.push("*"),r+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r+=n?"\\/*$":"(?:(?=[.~-]|%[0-9A-F]{2})|\\b|\\/|$)",[new RegExp(r,t?void 0:"i"),a]}function Ne(e,t){try{return decodeURIComponent(e)}catch{return e}}function We(e,t){t===void 0&&(t="/");let{pathname:n,search:a="",hash:r=""}=typeof e=="string"?_(e):e;return{pathname:n?n.startsWith("/")?n:Ie(n,t):t,search:Me(a),hash:Fe(r)}}function Ie(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(r=>{r===".."?n.length>1&&n.pop():r!=="."&&n.push(r)}),n.length>1?n.join("/"):"/"}function Ve(e,t,n){let a=typeof e=="string"?_(e):e,r=e===""||a.pathname===""?"/":a.pathname,o;if(r==null)o=n;else{let l=t.length-1;if(r.startsWith("..")){let u=r.split("/");for(;u[0]==="..";)u.shift(),l-=1;a.pathname=u.join("/")}o=l>=0?t[l]:"/"}let i=We(a,o);return r&&r!=="/"&&r.endsWith("/")&&!i.pathname.endsWith("/")&&(i.pathname+="/"),i}function ne(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=e.charAt(t.length);return n&&n!=="/"?null:e.slice(t.length)||"/"}const P=e=>e.join("/").replace(/\/\/+/g,"/"),ae=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Me=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Fe=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function O(){return C(M)!=null}function re(){return O()||g(),C(M).location}function He(){O()||g();let{basename:e,navigator:t}=C(ee),{matches:n}=C(F),{pathname:a}=re(),r=JSON.stringify(n.map(l=>l.pathnameBase)),o=we(!1);return Z(()=>{o.current=!0}),ge(function(l,u){if(u===void 0&&(u={}),!o.current)return;if(typeof l=="number"){t.go(l);return}let h=Ve(l,JSON.parse(r),a);e!=="/"&&(h.pathname=P([e,h.pathname])),(u.replace?t.replace:t.push)(h,u.state)},[e,t,r,a])}function De(e,t){O()||g();let{matches:n}=C(F),a=n[n.length-1],r=a?a.params:{};a&&a.pathname;let o=a?a.pathnameBase:"/";a&&a.route;let i=re(),l;if(t){var u;let p=typeof t=="string"?_(t):t;o==="/"||(u=p.pathname)!=null&&u.startsWith(o)||g(),l=p}else l=i;let h=l.pathname||"/",f=o==="/"?h:h.slice(o.length)||"/",d=Pe(e,{pathname:f});return Ue(d&&d.map(p=>Object.assign({},p,{params:Object.assign({},r,p.params),pathname:P([o,p.pathname]),pathnameBase:p.pathnameBase==="/"?o:P([o,p.pathnameBase])})),n)}function Ue(e,t){return t===void 0&&(t=[]),e==null?null:e.reduceRight((n,a,r)=>N(F.Provider,{children:a.route.element!==void 0?a.route.element:n,value:{outlet:n,matches:t.concat(e.slice(0,r+1))}}),null)}function ze(e){let{to:t,replace:n,state:a}=e;O()||g();let r=He();return Z(()=>{r(t,{replace:n,state:a})}),null}function W(e){g()}function Ae(e){let{basename:t="/",children:n=null,location:a,navigationType:r=x.Pop,navigator:o,static:i=!1}=e;O()&&g();let l=ae(t),u=K(()=>({basename:l,navigator:o,static:i}),[l,o,i]);typeof a=="string"&&(a=_(a));let{pathname:h="/",search:f="",hash:d="",state:p=null,key:w="default"}=a,E=K(()=>{let b=ne(h,l);return b==null?null:{pathname:b,search:f,hash:d,state:p,key:w}},[l,h,f,d,p,w]);return E==null?null:N(ee.Provider,{value:u},N(M.Provider,{children:n,value:{location:E,navigationType:r}}))}function Je(e){let{children:t,location:n}=e;return De(I(t),n)}function I(e){let t=[];return Re.forEach(e,n=>{if(!ye(n))return;if(n.type===xe){t.push.apply(t,I(n.props.children));return}n.type!==W&&g();let a={caseSensitive:n.props.caseSensitive,element:n.props.element,index:n.props.index,path:n.props.path};n.props.children&&(a.children=I(n.props.children)),t.push(a)}),t}/**
 * React Router DOM v6.3.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const qe=window.React.useRef,Ge=window.React.useState,Ke=window.React.useLayoutEffect,Ye=window.React.createElement;window.React.forwardRef;window.React.useCallback;window.React.useMemo;function Qe(e){let{basename:t,children:n,window:a}=e,r=qe();r.current==null&&(r.current=de({window:a}));let o=r.current,[i,l]=Ge({action:o.action,location:o.location});return Ke(()=>o.listen(l),[o]),Ye(Ae,{basename:t,children:n,location:i.location,navigationType:i.action,navigator:o})}const Xe=window.React,Ze=window.React.Suspense,et=()=>{var t;const e=window.location.pathname||"";return((t=window.location.hostname)==null?void 0:t.indexOf("miniapp"))>-1?e.split("/")[1]:e.substring(1,e.lastIndexOf("/"))},tt=Xe.lazy(()=>ie(()=>import("./hotel.Dos6w4x9.js"),__vite__mapDeps([0,1]),import.meta.url));function nt(){const e=et();return y.jsx("div",{className:"App",children:y.jsx(Qe,{basename:e,children:y.jsx(Ze,{children:y.jsxs(Je,{children:[y.jsx(W,{path:"*",element:y.jsx(ze,{to:"/Hotel"})}),y.jsx(W,{path:"/Hotel",element:y.jsx(tt,{})})]})})})})}const at=window.ReactDomClient.createRoot,rt=at(document.getElementById("app"));rt.render(y.jsx(nt,{}));const lt=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"}));export{$ as _,lt as b,y as j};
