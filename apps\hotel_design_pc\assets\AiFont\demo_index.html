<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4755574" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe601;</span>
                <div class="name">Frame</div>
                <div class="code-name">&amp;#xe601;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe664;</span>
                <div class="name">相机设置_Nor</div>
                <div class="code-name">&amp;#xe664;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe799;</span>
                <div class="name">1_1</div>
                <div class="code-name">&amp;#xe799;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79a;</span>
                <div class="name">3_2</div>
                <div class="code-name">&amp;#xe79a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79b;</span>
                <div class="name">2_3</div>
                <div class="code-name">&amp;#xe79b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe790;</span>
                <div class="name">4_3</div>
                <div class="code-name">&amp;#xe790;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79c;</span>
                <div class="name">16_9</div>
                <div class="code-name">&amp;#xe79c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe78f;</span>
                <div class="name">9_16</div>
                <div class="code-name">&amp;#xe78f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe78e;</span>
                <div class="name">3_4</div>
                <div class="code-name">&amp;#xe78e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe78d;</span>
                <div class="name">1_2</div>
                <div class="code-name">&amp;#xe78d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe82c;</span>
                <div class="name">吊顶</div>
                <div class="code-name">&amp;#xe82c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe82d;</span>
                <div class="name">分区</div>
                <div class="code-name">&amp;#xe82d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe82e;</span>
                <div class="name">属性</div>
                <div class="code-name">&amp;#xe82e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe82a;</span>
                <div class="name">铺砖</div>
                <div class="code-name">&amp;#xe82a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe827;</span>
                <div class="name">放大</div>
                <div class="code-name">&amp;#xe827;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe828;</span>
                <div class="name">墙</div>
                <div class="code-name">&amp;#xe828;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe829;</span>
                <div class="name">布局</div>
                <div class="code-name">&amp;#xe829;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe826;</span>
                <div class="name">加素材</div>
                <div class="code-name">&amp;#xe826;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe825;</span>
                <div class="name">风格</div>
                <div class="code-name">&amp;#xe825;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe824;</span>
                <div class="name">信息</div>
                <div class="code-name">&amp;#xe824;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe821;</span>
                <div class="name">智能搭柜</div>
                <div class="code-name">&amp;#xe821;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe822;</span>
                <div class="name">筛选</div>
                <div class="code-name">&amp;#xe822;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe823;</span>
                <div class="name">开门</div>
                <div class="code-name">&amp;#xe823;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8e9;</span>
                <div class="name">info</div>
                <div class="code-name">&amp;#xe8e9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8e8;</span>
                <div class="name">info_hover</div>
                <div class="code-name">&amp;#xe8e8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe81e;</span>
                <div class="name">勾</div>
                <div class="code-name">&amp;#xe81e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe81f;</span>
                <div class="name">收纳</div>
                <div class="code-name">&amp;#xe81f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe820;</span>
                <div class="name">空间利用率</div>
                <div class="code-name">&amp;#xe820;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe81a;</span>
                <div class="name">家居布局</div>
                <div class="code-name">&amp;#xe81a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe81b;</span>
                <div class="name">星星</div>
                <div class="code-name">&amp;#xe81b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe81c;</span>
                <div class="name">动线</div>
                <div class="code-name">&amp;#xe81c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe81d;</span>
                <div class="name">半星</div>
                <div class="code-name">&amp;#xe81d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe785;</span>
                <div class="name">heart</div>
                <div class="code-name">&amp;#xe785;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe786;</span>
                <div class="name">heartall</div>
                <div class="code-name">&amp;#xe786;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe707;</span>
                <div class="name">渲染Render</div>
                <div class="code-name">&amp;#xe707;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe818;</span>
                <div class="name">save</div>
                <div class="code-name">&amp;#xe818;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe816;</span>
                <div class="name">已选</div>
                <div class="code-name">&amp;#xe816;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe817;</span>
                <div class="name">未选</div>
                <div class="code-name">&amp;#xe817;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe815;</span>
                <div class="name">分享</div>
                <div class="code-name">&amp;#xe815;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe643;</span>
                <div class="name">勾</div>
                <div class="code-name">&amp;#xe643;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe814;</span>
                <div class="name">导示图</div>
                <div class="code-name">&amp;#xe814;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8ae;</span>
                <div class="name">预报价</div>
                <div class="code-name">&amp;#xe8ae;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe798;</span>
                <div class="name">图库</div>
                <div class="code-name">&amp;#xe798;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe813;</span>
                <div class="name">历史版本</div>
                <div class="code-name">&amp;#xe813;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe812;</span>
                <div class="name">添加素材</div>
                <div class="code-name">&amp;#xe812;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe811;</span>
                <div class="name">AI出图</div>
                <div class="code-name">&amp;#xe811;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe794;</span>
                <div class="name">下载</div>
                <div class="code-name">&amp;#xe794;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe795;</span>
                <div class="name">加入资产</div>
                <div class="code-name">&amp;#xe795;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe796;</span>
                <div class="name">户型_L</div>
                <div class="code-name">&amp;#xe796;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe797;</span>
                <div class="name">户型_s</div>
                <div class="code-name">&amp;#xe797;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe793;</span>
                <div class="name">check</div>
                <div class="code-name">&amp;#xe793;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe792;</span>
                <div class="name">DeleteFilled</div>
                <div class="code-name">&amp;#xe792;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe791;</span>
                <div class="name">重新编辑</div>
                <div class="code-name">&amp;#xe791;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d0;</span>
                <div class="name">caretup</div>
                <div class="code-name">&amp;#xe7d0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d2;</span>
                <div class="name">closecirle_fill</div>
                <div class="code-name">&amp;#xe7d2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d7;</span>
                <div class="name">caretleft</div>
                <div class="code-name">&amp;#xe7d7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe80b;</span>
                <div class="name">fight</div>
                <div class="code-name">&amp;#xe80b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe80c;</span>
                <div class="name">exclamationcircle_line</div>
                <div class="code-name">&amp;#xe80c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe80d;</span>
                <div class="name">hide</div>
                <div class="code-name">&amp;#xe80d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe80e;</span>
                <div class="name">left</div>
                <div class="code-name">&amp;#xe80e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe80f;</span>
                <div class="name">search</div>
                <div class="code-name">&amp;#xe80f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe810;</span>
                <div class="name">smarttemplate</div>
                <div class="code-name">&amp;#xe810;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe819;</span>
                <div class="name">questioncicle_line</div>
                <div class="code-name">&amp;#xe819;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe70a;</span>
                <div class="name">close</div>
                <div class="code-name">&amp;#xe70a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe717;</span>
                <div class="name">chec</div>
                <div class="code-name">&amp;#xe717;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe756;</span>
                <div class="name">reset</div>
                <div class="code-name">&amp;#xe756;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e8;</span>
                <div class="name">Share</div>
                <div class="code-name">&amp;#xe6e8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ee;</span>
                <div class="name">Close_Large</div>
                <div class="code-name">&amp;#xe6ee;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe706;</span>
                <div class="name">筛选Fliter</div>
                <div class="code-name">&amp;#xe706;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ab;</span>
                <div class="name">手动框选Frame Selection</div>
                <div class="code-name">&amp;#xe7ab;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe644;</span>
                <div class="name">house</div>
                <div class="code-name">&amp;#xe644;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe82b;</span>
                <div class="name">显示Show</div>
                <div class="code-name">&amp;#xe82b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe87e;</span>
                <div class="name">720</div>
                <div class="code-name">&amp;#xe87e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe885;</span>
                <div class="name">arrow_fill_down</div>
                <div class="code-name">&amp;#xe885;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe884;</span>
                <div class="name">arrow_fill_up</div>
                <div class="code-name">&amp;#xe884;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe880;</span>
                <div class="name">arrow_line_down</div>
                <div class="code-name">&amp;#xe880;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe881;</span>
                <div class="name">arrow_line_left</div>
                <div class="code-name">&amp;#xe881;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c4;</span>
                <div class="name">info_fill</div>
                <div class="code-name">&amp;#xe7c4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7cd;</span>
                <div class="name">caretdown</div>
                <div class="code-name">&amp;#xe7cd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7cf;</span>
                <div class="name">caretright</div>
                <div class="code-name">&amp;#xe7cf;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe80a;</span>
                <div class="name">安装Install</div>
                <div class="code-name">&amp;#xe80a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe809;</span>
                <div class="name">change_logo</div>
                <div class="code-name">&amp;#xe809;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe802;</span>
                <div class="name">rotate</div>
                <div class="code-name">&amp;#xe802;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe808;</span>
                <div class="name">delete</div>
                <div class="code-name">&amp;#xe808;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe806;</span>
                <div class="name">感叹号</div>
                <div class="code-name">&amp;#xe806;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe807;</span>
                <div class="name">关闭</div>
                <div class="code-name">&amp;#xe807;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe803;</span>
                <div class="name">恢复</div>
                <div class="code-name">&amp;#xe803;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe804;</span>
                <div class="name">撤销</div>
                <div class="code-name">&amp;#xe804;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe805;</span>
                <div class="name">主页</div>
                <div class="code-name">&amp;#xe805;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8cb;</span>
                <div class="name">聚焦</div>
                <div class="code-name">&amp;#xe8cb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7fc;</span>
                <div class="name">旋转45度</div>
                <div class="code-name">&amp;#xe7fc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7fd;</span>
                <div class="name">专注空间</div>
                <div class="code-name">&amp;#xe7fd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7fe;</span>
                <div class="name">解组</div>
                <div class="code-name">&amp;#xe7fe;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ff;</span>
                <div class="name">离地</div>
                <div class="code-name">&amp;#xe7ff;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe800;</span>
                <div class="name">打组</div>
                <div class="code-name">&amp;#xe800;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe801;</span>
                <div class="name">粘贴</div>
                <div class="code-name">&amp;#xe801;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7fa;</span>
                <div class="name">尺寸</div>
                <div class="code-name">&amp;#xe7fa;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b2;</span>
                <div class="name">horizontalflip_line</div>
                <div class="code-name">&amp;#xe6b2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b3;</span>
                <div class="name">verflip_line</div>
                <div class="code-name">&amp;#xe6b3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe88e;</span>
                <div class="name">锁定</div>
                <div class="code-name">&amp;#xe88e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe88f;</span>
                <div class="name">解锁</div>
                <div class="code-name">&amp;#xe88f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f7;</span>
                <div class="name">素材</div>
                <div class="code-name">&amp;#xe7f7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f8;</span>
                <div class="name">锁定布局</div>
                <div class="code-name">&amp;#xe7f8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f9;</span>
                <div class="name">解锁布局</div>
                <div class="code-name">&amp;#xe7f9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f3;</span>
                <div class="name">删除布局</div>
                <div class="code-name">&amp;#xe7f3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f4;</span>
                <div class="name">锁定风格</div>
                <div class="code-name">&amp;#xe7f4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f5;</span>
                <div class="name">解锁风格</div>
                <div class="code-name">&amp;#xe7f5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f6;</span>
                <div class="name">清除风格</div>
                <div class="code-name">&amp;#xe7f6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ef;</span>
                <div class="name">素材</div>
                <div class="code-name">&amp;#xe7ef;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f0;</span>
                <div class="name">属性</div>
                <div class="code-name">&amp;#xe7f0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f1;</span>
                <div class="name">布局</div>
                <div class="code-name">&amp;#xe7f1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f2;</span>
                <div class="name">户型</div>
                <div class="code-name">&amp;#xe7f2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ed;</span>
                <div class="name">工具</div>
                <div class="code-name">&amp;#xe7ed;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ee;</span>
                <div class="name">风格</div>
                <div class="code-name">&amp;#xe7ee;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ec;</span>
                <div class="name">左箭头</div>
                <div class="code-name">&amp;#xe7ec;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7eb;</span>
                <div class="name">Search</div>
                <div class="code-name">&amp;#xe7eb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e8;</span>
                <div class="name">用户管理</div>
                <div class="code-name">&amp;#xe7e8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e9;</span>
                <div class="name">账号分享</div>
                <div class="code-name">&amp;#xe7e9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ea;</span>
                <div class="name">链接分享</div>
                <div class="code-name">&amp;#xe7ea;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e7;</span>
                <div class="name">更多_bold</div>
                <div class="code-name">&amp;#xe7e7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e5;</span>
                <div class="name">列表模式</div>
                <div class="code-name">&amp;#xe7e5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e6;</span>
                <div class="name">卡片模式</div>
                <div class="code-name">&amp;#xe7e6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7fb;</span>
                <div class="name">more</div>
                <div class="code-name">&amp;#xe7fb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8b1;</span>
                <div class="name">上</div>
                <div class="code-name">&amp;#xe8b1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8b2;</span>
                <div class="name">下</div>
                <div class="code-name">&amp;#xe8b2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8b3;</span>
                <div class="name">左</div>
                <div class="code-name">&amp;#xe8b3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8b4;</span>
                <div class="name">右</div>
                <div class="code-name">&amp;#xe8b4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8d2;</span>
                <div class="name">close-blod</div>
                <div class="code-name">&amp;#xe8d2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe630;</span>
                <div class="name">none</div>
                <div class="code-name">&amp;#xe630;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e2;</span>
                <div class="name">我的方案</div>
                <div class="code-name">&amp;#xe7e2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e3;</span>
                <div class="name">我的户型</div>
                <div class="code-name">&amp;#xe7e3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e4;</span>
                <div class="name">回收站</div>
                <div class="code-name">&amp;#xe7e4;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.eot?t=1749890595433'); /* IE9 */
  src: url('iconfont.eot?t=1749890595433#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('data:application/x-font-woff2;charset=utf-8;base64,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') format('woff2'),
       url('iconfont.woff?t=1749890595433') format('woff'),
       url('iconfont.ttf?t=1749890595433') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-Frame"></span>
            <div class="name">
              Frame
            </div>
            <div class="code-name">.icon-Frame
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiangjishezhi"></span>
            <div class="name">
              相机设置_Nor
            </div>
            <div class="code-name">.icon-xiangjishezhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-1_1"></span>
            <div class="name">
              1_1
            </div>
            <div class="code-name">.icon-a-1_1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-3_2"></span>
            <div class="name">
              3_2
            </div>
            <div class="code-name">.icon-a-3_2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-2_3"></span>
            <div class="name">
              2_3
            </div>
            <div class="code-name">.icon-a-2_3
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-4_3"></span>
            <div class="name">
              4_3
            </div>
            <div class="code-name">.icon-a-4_3
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-16_9"></span>
            <div class="name">
              16_9
            </div>
            <div class="code-name">.icon-a-16_9
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-9_16"></span>
            <div class="name">
              9_16
            </div>
            <div class="code-name">.icon-a-9_16
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-3_4"></span>
            <div class="name">
              3_4
            </div>
            <div class="code-name">.icon-a-3_4
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-1_2"></span>
            <div class="name">
              1_2
            </div>
            <div class="code-name">.icon-a-1_2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-diaoding"></span>
            <div class="name">
              吊顶
            </div>
            <div class="code-name">.icon-diaoding
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fenqu"></span>
            <div class="name">
              分区
            </div>
            <div class="code-name">.icon-fenqu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuxing"></span>
            <div class="name">
              属性
            </div>
            <div class="code-name">.icon-shuxing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-puzhuan"></span>
            <div class="name">
              铺砖
            </div>
            <div class="code-name">.icon-puzhuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fangda"></span>
            <div class="name">
              放大
            </div>
            <div class="code-name">.icon-fangda
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qiang"></span>
            <div class="name">
              墙
            </div>
            <div class="code-name">.icon-qiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-buju"></span>
            <div class="name">
              布局
            </div>
            <div class="code-name">.icon-buju
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chanpinzhiru"></span>
            <div class="name">
              加素材
            </div>
            <div class="code-name">.icon-chanpinzhiru
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fengge"></span>
            <div class="name">
              风格
            </div>
            <div class="code-name">.icon-fengge
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xinxi"></span>
            <div class="name">
              信息
            </div>
            <div class="code-name">.icon-xinxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhinengdagui"></span>
            <div class="name">
              智能搭柜
            </div>
            <div class="code-name">.icon-zhinengdagui
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shaixuan"></span>
            <div class="name">
              筛选
            </div>
            <div class="code-name">.icon-shaixuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-kaimen"></span>
            <div class="name">
              开门
            </div>
            <div class="code-name">.icon-kaimen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-info"></span>
            <div class="name">
              info
            </div>
            <div class="code-name">.icon-info
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-info_hover"></span>
            <div class="name">
              info_hover
            </div>
            <div class="code-name">.icon-info_hover
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gou1"></span>
            <div class="name">
              勾
            </div>
            <div class="code-name">.icon-gou1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shouna"></span>
            <div class="name">
              收纳
            </div>
            <div class="code-name">.icon-shouna
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-kongjianliyongshuai"></span>
            <div class="name">
              空间利用率
            </div>
            <div class="code-name">.icon-kongjianliyongshuai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiajubuju"></span>
            <div class="name">
              家居布局
            </div>
            <div class="code-name">.icon-jiajubuju
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingxing"></span>
            <div class="name">
              星星
            </div>
            <div class="code-name">.icon-xingxing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dongxian"></span>
            <div class="name">
              动线
            </div>
            <div class="code-name">.icon-dongxian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-banxing"></span>
            <div class="name">
              半星
            </div>
            <div class="code-name">.icon-banxing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-heart"></span>
            <div class="name">
              heart
            </div>
            <div class="code-name">.icon-heart
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-heart-o"></span>
            <div class="name">
              heartall
            </div>
            <div class="code-name">.icon-heart-o
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xuanranRender"></span>
            <div class="name">
              渲染Render
            </div>
            <div class="code-name">.icon-xuanranRender
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-save"></span>
            <div class="name">
              save
            </div>
            <div class="code-name">.icon-save
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yixuan"></span>
            <div class="name">
              已选
            </div>
            <div class="code-name">.icon-yixuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-weixuan"></span>
            <div class="name">
              未选
            </div>
            <div class="code-name">.icon-weixuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fenxiang1"></span>
            <div class="name">
              分享
            </div>
            <div class="code-name">.icon-fenxiang1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gou"></span>
            <div class="name">
              勾
            </div>
            <div class="code-name">.icon-gou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-daoshitu"></span>
            <div class="name">
              导示图
            </div>
            <div class="code-name">.icon-daoshitu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-baojia"></span>
            <div class="name">
              预报价
            </div>
            <div class="code-name">.icon-baojia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tuku"></span>
            <div class="name">
              图库
            </div>
            <div class="code-name">.icon-tuku
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-lishibanben"></span>
            <div class="name">
              历史版本
            </div>
            <div class="code-name">.icon-lishibanben
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tianjiasucai"></span>
            <div class="name">
              添加素材
            </div>
            <div class="code-name">.icon-tianjiasucai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-AIchutu"></span>
            <div class="name">
              AI出图
            </div>
            <div class="code-name">.icon-AIchutu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiazai"></span>
            <div class="name">
              下载
            </div>
            <div class="code-name">.icon-xiazai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiaruzichan"></span>
            <div class="name">
              加入资产
            </div>
            <div class="code-name">.icon-jiaruzichan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-huxing_L"></span>
            <div class="name">
              户型_L
            </div>
            <div class="code-name">.icon-huxing_L
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-huxing_s"></span>
            <div class="name">
              户型_s
            </div>
            <div class="code-name">.icon-huxing_s
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-check"></span>
            <div class="name">
              check
            </div>
            <div class="code-name">.icon-check
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-DeleteFilled1"></span>
            <div class="name">
              DeleteFilled
            </div>
            <div class="code-name">.icon-DeleteFilled1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhongxinbianji"></span>
            <div class="name">
              重新编辑
            </div>
            <div class="code-name">.icon-zhongxinbianji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-caretup"></span>
            <div class="name">
              caretup
            </div>
            <div class="code-name">.icon-caretup
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-closecirle_fill"></span>
            <div class="name">
              closecirle_fill
            </div>
            <div class="code-name">.icon-closecirle_fill
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-caretleft"></span>
            <div class="name">
              caretleft
            </div>
            <div class="code-name">.icon-caretleft
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fight"></span>
            <div class="name">
              fight
            </div>
            <div class="code-name">.icon-fight
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-exclamationcircle_line"></span>
            <div class="name">
              exclamationcircle_line
            </div>
            <div class="code-name">.icon-exclamationcircle_line
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-hide"></span>
            <div class="name">
              hide
            </div>
            <div class="code-name">.icon-hide
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-left"></span>
            <div class="name">
              left
            </div>
            <div class="code-name">.icon-left
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-search"></span>
            <div class="name">
              search
            </div>
            <div class="code-name">.icon-search
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-smarttemplate"></span>
            <div class="name">
              smarttemplate
            </div>
            <div class="code-name">.icon-smarttemplate
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-questioncicle_line"></span>
            <div class="name">
              questioncicle_line
            </div>
            <div class="code-name">.icon-questioncicle_line
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-close1"></span>
            <div class="name">
              close
            </div>
            <div class="code-name">.icon-close1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chec"></span>
            <div class="name">
              chec
            </div>
            <div class="code-name">.icon-chec
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-reset"></span>
            <div class="name">
              reset
            </div>
            <div class="code-name">.icon-reset
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fenxiang"></span>
            <div class="name">
              Share
            </div>
            <div class="code-name">.icon-fenxiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Close_Large"></span>
            <div class="name">
              Close_Large
            </div>
            <div class="code-name">.icon-Close_Large
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Fliter"></span>
            <div class="name">
              筛选Fliter
            </div>
            <div class="code-name">.icon-Fliter
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-FrameSelection"></span>
            <div class="name">
              手动框选Frame Selection
            </div>
            <div class="code-name">.icon-FrameSelection
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-house"></span>
            <div class="name">
              house
            </div>
            <div class="code-name">.icon-house
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Show"></span>
            <div class="name">
              显示Show
            </div>
            <div class="code-name">.icon-Show
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-720"></span>
            <div class="name">
              720
            </div>
            <div class="code-name">.icon-a-720
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fill_down"></span>
            <div class="name">
              arrow_fill_down
            </div>
            <div class="code-name">.icon-fill_down
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fill_up"></span>
            <div class="name">
              arrow_fill_up
            </div>
            <div class="code-name">.icon-fill_up
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-line_down"></span>
            <div class="name">
              arrow_line_down
            </div>
            <div class="code-name">.icon-line_down
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-line_left"></span>
            <div class="name">
              arrow_line_left
            </div>
            <div class="code-name">.icon-line_left
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-info_fill"></span>
            <div class="name">
              info_fill
            </div>
            <div class="code-name">.icon-info_fill
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-caretdown"></span>
            <div class="name">
              caretdown
            </div>
            <div class="code-name">.icon-caretdown
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-caretright"></span>
            <div class="name">
              caretright
            </div>
            <div class="code-name">.icon-caretright
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-anzhuangInstall"></span>
            <div class="name">
              安装Install
            </div>
            <div class="code-name">.icon-anzhuangInstall
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-change_logo"></span>
            <div class="name">
              change_logo
            </div>
            <div class="code-name">.icon-change_logo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-rotate"></span>
            <div class="name">
              rotate
            </div>
            <div class="code-name">.icon-rotate
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-delete"></span>
            <div class="name">
              delete
            </div>
            <div class="code-name">.icon-delete
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-tianchongFace"></span>
            <div class="name">
              感叹号
            </div>
            <div class="code-name">.icon-a-tianchongFace
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-tianchongFace-1"></span>
            <div class="name">
              关闭
            </div>
            <div class="code-name">.icon-a-tianchongFace-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-huifu"></span>
            <div class="name">
              恢复
            </div>
            <div class="code-name">.icon-huifu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chexiao"></span>
            <div class="name">
              撤销
            </div>
            <div class="code-name">.icon-chexiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhuye"></span>
            <div class="name">
              主页
            </div>
            <div class="code-name">.icon-zhuye
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jujiao"></span>
            <div class="name">
              聚焦
            </div>
            <div class="code-name">.icon-jujiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-Property2xuanzhuan45du"></span>
            <div class="name">
              旋转45度
            </div>
            <div class="code-name">.icon-a-Property2xuanzhuan45du
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhuanzhukongjian"></span>
            <div class="name">
              专注空间
            </div>
            <div class="code-name">.icon-zhuanzhukongjian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiezu-2"></span>
            <div class="name">
              解组
            </div>
            <div class="code-name">.icon-jiezu-2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-lidi"></span>
            <div class="name">
              离地
            </div>
            <div class="code-name">.icon-lidi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zuhe-2"></span>
            <div class="name">
              打组
            </div>
            <div class="code-name">.icon-zuhe-2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-niantie"></span>
            <div class="name">
              粘贴
            </div>
            <div class="code-name">.icon-niantie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chicun"></span>
            <div class="name">
              尺寸
            </div>
            <div class="code-name">.icon-chicun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-horizontalflip_line"></span>
            <div class="name">
              horizontalflip_line
            </div>
            <div class="code-name">.icon-horizontalflip_line
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-verflip_line"></span>
            <div class="name">
              verflip_line
            </div>
            <div class="code-name">.icon-verflip_line
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-suoding1"></span>
            <div class="name">
              锁定
            </div>
            <div class="code-name">.icon-suoding1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiesuo1"></span>
            <div class="name">
              解锁
            </div>
            <div class="code-name">.icon-jiesuo1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-sucai"></span>
            <div class="name">
              素材
            </div>
            <div class="code-name">.icon-sucai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-suodingbuju"></span>
            <div class="name">
              锁定布局
            </div>
            <div class="code-name">.icon-suodingbuju
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiesuobuju"></span>
            <div class="name">
              解锁布局
            </div>
            <div class="code-name">.icon-jiesuobuju
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shanchubuju"></span>
            <div class="name">
              删除布局
            </div>
            <div class="code-name">.icon-shanchubuju
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-suodingfengge"></span>
            <div class="name">
              锁定风格
            </div>
            <div class="code-name">.icon-suodingfengge
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiesuofengge"></span>
            <div class="name">
              解锁风格
            </div>
            <div class="code-name">.icon-jiesuofengge
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qingchufengge"></span>
            <div class="name">
              清除风格
            </div>
            <div class="code-name">.icon-qingchufengge
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-TypesucaiStateDefault"></span>
            <div class="name">
              素材
            </div>
            <div class="code-name">.icon-a-TypesucaiStateDefault
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-TypeshuxingStateDefault"></span>
            <div class="name">
              属性
            </div>
            <div class="code-name">.icon-a-TypeshuxingStateDefault
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-TypebujuStateDefault"></span>
            <div class="name">
              布局
            </div>
            <div class="code-name">.icon-a-TypebujuStateDefault
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-TypehuxingStateDefault"></span>
            <div class="name">
              户型
            </div>
            <div class="code-name">.icon-a-TypehuxingStateDefault
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-TypegongjuStateDefault"></span>
            <div class="name">
              工具
            </div>
            <div class="code-name">.icon-a-TypegongjuStateDefault
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-TypefenggeStateDefault"></span>
            <div class="name">
              风格
            </div>
            <div class="code-name">.icon-a-TypefenggeStateDefault
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiantou"></span>
            <div class="name">
              左箭头
            </div>
            <div class="code-name">.icon-jiantou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Search"></span>
            <div class="name">
              Search
            </div>
            <div class="code-name">.icon-Search
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yonghuguanli"></span>
            <div class="name">
              用户管理
            </div>
            <div class="code-name">.icon-yonghuguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhanghaofenxiang"></span>
            <div class="name">
              账号分享
            </div>
            <div class="code-name">.icon-zhanghaofenxiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-lianjiefenxiang"></span>
            <div class="name">
              链接分享
            </div>
            <div class="code-name">.icon-lianjiefenxiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gengduo_bold"></span>
            <div class="name">
              更多_bold
            </div>
            <div class="code-name">.icon-gengduo_bold
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-UnorderedListOutlined"></span>
            <div class="name">
              列表模式
            </div>
            <div class="code-name">.icon-UnorderedListOutlined
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-kapianmoshi"></span>
            <div class="name">
              卡片模式
            </div>
            <div class="code-name">.icon-kapianmoshi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-more"></span>
            <div class="name">
              more
            </div>
            <div class="code-name">.icon-more
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-fangxiangshang"></span>
            <div class="name">
              上
            </div>
            <div class="code-name">.icon-a-fangxiangshang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-fangxiangxia"></span>
            <div class="name">
              下
            </div>
            <div class="code-name">.icon-a-fangxiangxia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-fangxiangzuo"></span>
            <div class="name">
              左
            </div>
            <div class="code-name">.icon-a-fangxiangzuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-fangxiangyou"></span>
            <div class="name">
              右
            </div>
            <div class="code-name">.icon-a-fangxiangyou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon"></span>
            <div class="name">
              close-blod
            </div>
            <div class="code-name">.icon-icon
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-none"></span>
            <div class="name">
              none
            </div>
            <div class="code-name">.icon-none
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Bookmark"></span>
            <div class="name">
              我的方案
            </div>
            <div class="code-name">.icon-Bookmark
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe"></span>
            <div class="name">
              我的户型
            </div>
            <div class="code-name">.icon-xingzhuangjiehe
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-DeleteFilled"></span>
            <div class="name">
              回收站
            </div>
            <div class="code-name">.icon-DeleteFilled
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Frame"></use>
                </svg>
                <div class="name">Frame</div>
                <div class="code-name">#icon-Frame</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiangjishezhi"></use>
                </svg>
                <div class="name">相机设置_Nor</div>
                <div class="code-name">#icon-xiangjishezhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-1_1"></use>
                </svg>
                <div class="name">1_1</div>
                <div class="code-name">#icon-a-1_1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-3_2"></use>
                </svg>
                <div class="name">3_2</div>
                <div class="code-name">#icon-a-3_2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-2_3"></use>
                </svg>
                <div class="name">2_3</div>
                <div class="code-name">#icon-a-2_3</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-4_3"></use>
                </svg>
                <div class="name">4_3</div>
                <div class="code-name">#icon-a-4_3</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-16_9"></use>
                </svg>
                <div class="name">16_9</div>
                <div class="code-name">#icon-a-16_9</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-9_16"></use>
                </svg>
                <div class="name">9_16</div>
                <div class="code-name">#icon-a-9_16</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-3_4"></use>
                </svg>
                <div class="name">3_4</div>
                <div class="code-name">#icon-a-3_4</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-1_2"></use>
                </svg>
                <div class="name">1_2</div>
                <div class="code-name">#icon-a-1_2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-diaoding"></use>
                </svg>
                <div class="name">吊顶</div>
                <div class="code-name">#icon-diaoding</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fenqu"></use>
                </svg>
                <div class="name">分区</div>
                <div class="code-name">#icon-fenqu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuxing"></use>
                </svg>
                <div class="name">属性</div>
                <div class="code-name">#icon-shuxing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-puzhuan"></use>
                </svg>
                <div class="name">铺砖</div>
                <div class="code-name">#icon-puzhuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fangda"></use>
                </svg>
                <div class="name">放大</div>
                <div class="code-name">#icon-fangda</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qiang"></use>
                </svg>
                <div class="name">墙</div>
                <div class="code-name">#icon-qiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-buju"></use>
                </svg>
                <div class="name">布局</div>
                <div class="code-name">#icon-buju</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chanpinzhiru"></use>
                </svg>
                <div class="name">加素材</div>
                <div class="code-name">#icon-chanpinzhiru</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fengge"></use>
                </svg>
                <div class="name">风格</div>
                <div class="code-name">#icon-fengge</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xinxi"></use>
                </svg>
                <div class="name">信息</div>
                <div class="code-name">#icon-xinxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhinengdagui"></use>
                </svg>
                <div class="name">智能搭柜</div>
                <div class="code-name">#icon-zhinengdagui</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shaixuan"></use>
                </svg>
                <div class="name">筛选</div>
                <div class="code-name">#icon-shaixuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kaimen"></use>
                </svg>
                <div class="name">开门</div>
                <div class="code-name">#icon-kaimen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-info"></use>
                </svg>
                <div class="name">info</div>
                <div class="code-name">#icon-info</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-info_hover"></use>
                </svg>
                <div class="name">info_hover</div>
                <div class="code-name">#icon-info_hover</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gou1"></use>
                </svg>
                <div class="name">勾</div>
                <div class="code-name">#icon-gou1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shouna"></use>
                </svg>
                <div class="name">收纳</div>
                <div class="code-name">#icon-shouna</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kongjianliyongshuai"></use>
                </svg>
                <div class="name">空间利用率</div>
                <div class="code-name">#icon-kongjianliyongshuai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiajubuju"></use>
                </svg>
                <div class="name">家居布局</div>
                <div class="code-name">#icon-jiajubuju</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingxing"></use>
                </svg>
                <div class="name">星星</div>
                <div class="code-name">#icon-xingxing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dongxian"></use>
                </svg>
                <div class="name">动线</div>
                <div class="code-name">#icon-dongxian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-banxing"></use>
                </svg>
                <div class="name">半星</div>
                <div class="code-name">#icon-banxing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-heart"></use>
                </svg>
                <div class="name">heart</div>
                <div class="code-name">#icon-heart</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-heart-o"></use>
                </svg>
                <div class="name">heartall</div>
                <div class="code-name">#icon-heart-o</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xuanranRender"></use>
                </svg>
                <div class="name">渲染Render</div>
                <div class="code-name">#icon-xuanranRender</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-save"></use>
                </svg>
                <div class="name">save</div>
                <div class="code-name">#icon-save</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yixuan"></use>
                </svg>
                <div class="name">已选</div>
                <div class="code-name">#icon-yixuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-weixuan"></use>
                </svg>
                <div class="name">未选</div>
                <div class="code-name">#icon-weixuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fenxiang1"></use>
                </svg>
                <div class="name">分享</div>
                <div class="code-name">#icon-fenxiang1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gou"></use>
                </svg>
                <div class="name">勾</div>
                <div class="code-name">#icon-gou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-daoshitu"></use>
                </svg>
                <div class="name">导示图</div>
                <div class="code-name">#icon-daoshitu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-baojia"></use>
                </svg>
                <div class="name">预报价</div>
                <div class="code-name">#icon-baojia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tuku"></use>
                </svg>
                <div class="name">图库</div>
                <div class="code-name">#icon-tuku</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lishibanben"></use>
                </svg>
                <div class="name">历史版本</div>
                <div class="code-name">#icon-lishibanben</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tianjiasucai"></use>
                </svg>
                <div class="name">添加素材</div>
                <div class="code-name">#icon-tianjiasucai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-AIchutu"></use>
                </svg>
                <div class="name">AI出图</div>
                <div class="code-name">#icon-AIchutu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiazai"></use>
                </svg>
                <div class="name">下载</div>
                <div class="code-name">#icon-xiazai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiaruzichan"></use>
                </svg>
                <div class="name">加入资产</div>
                <div class="code-name">#icon-jiaruzichan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-huxing_L"></use>
                </svg>
                <div class="name">户型_L</div>
                <div class="code-name">#icon-huxing_L</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-huxing_s"></use>
                </svg>
                <div class="name">户型_s</div>
                <div class="code-name">#icon-huxing_s</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-check"></use>
                </svg>
                <div class="name">check</div>
                <div class="code-name">#icon-check</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-DeleteFilled1"></use>
                </svg>
                <div class="name">DeleteFilled</div>
                <div class="code-name">#icon-DeleteFilled1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhongxinbianji"></use>
                </svg>
                <div class="name">重新编辑</div>
                <div class="code-name">#icon-zhongxinbianji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-caretup"></use>
                </svg>
                <div class="name">caretup</div>
                <div class="code-name">#icon-caretup</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-closecirle_fill"></use>
                </svg>
                <div class="name">closecirle_fill</div>
                <div class="code-name">#icon-closecirle_fill</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-caretleft"></use>
                </svg>
                <div class="name">caretleft</div>
                <div class="code-name">#icon-caretleft</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fight"></use>
                </svg>
                <div class="name">fight</div>
                <div class="code-name">#icon-fight</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-exclamationcircle_line"></use>
                </svg>
                <div class="name">exclamationcircle_line</div>
                <div class="code-name">#icon-exclamationcircle_line</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-hide"></use>
                </svg>
                <div class="name">hide</div>
                <div class="code-name">#icon-hide</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-left"></use>
                </svg>
                <div class="name">left</div>
                <div class="code-name">#icon-left</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-search"></use>
                </svg>
                <div class="name">search</div>
                <div class="code-name">#icon-search</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-smarttemplate"></use>
                </svg>
                <div class="name">smarttemplate</div>
                <div class="code-name">#icon-smarttemplate</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-questioncicle_line"></use>
                </svg>
                <div class="name">questioncicle_line</div>
                <div class="code-name">#icon-questioncicle_line</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-close1"></use>
                </svg>
                <div class="name">close</div>
                <div class="code-name">#icon-close1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chec"></use>
                </svg>
                <div class="name">chec</div>
                <div class="code-name">#icon-chec</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-reset"></use>
                </svg>
                <div class="name">reset</div>
                <div class="code-name">#icon-reset</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fenxiang"></use>
                </svg>
                <div class="name">Share</div>
                <div class="code-name">#icon-fenxiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Close_Large"></use>
                </svg>
                <div class="name">Close_Large</div>
                <div class="code-name">#icon-Close_Large</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Fliter"></use>
                </svg>
                <div class="name">筛选Fliter</div>
                <div class="code-name">#icon-Fliter</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-FrameSelection"></use>
                </svg>
                <div class="name">手动框选Frame Selection</div>
                <div class="code-name">#icon-FrameSelection</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-house"></use>
                </svg>
                <div class="name">house</div>
                <div class="code-name">#icon-house</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Show"></use>
                </svg>
                <div class="name">显示Show</div>
                <div class="code-name">#icon-Show</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-720"></use>
                </svg>
                <div class="name">720</div>
                <div class="code-name">#icon-a-720</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fill_down"></use>
                </svg>
                <div class="name">arrow_fill_down</div>
                <div class="code-name">#icon-fill_down</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fill_up"></use>
                </svg>
                <div class="name">arrow_fill_up</div>
                <div class="code-name">#icon-fill_up</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-line_down"></use>
                </svg>
                <div class="name">arrow_line_down</div>
                <div class="code-name">#icon-line_down</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-line_left"></use>
                </svg>
                <div class="name">arrow_line_left</div>
                <div class="code-name">#icon-line_left</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-info_fill"></use>
                </svg>
                <div class="name">info_fill</div>
                <div class="code-name">#icon-info_fill</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-caretdown"></use>
                </svg>
                <div class="name">caretdown</div>
                <div class="code-name">#icon-caretdown</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-caretright"></use>
                </svg>
                <div class="name">caretright</div>
                <div class="code-name">#icon-caretright</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-anzhuangInstall"></use>
                </svg>
                <div class="name">安装Install</div>
                <div class="code-name">#icon-anzhuangInstall</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-change_logo"></use>
                </svg>
                <div class="name">change_logo</div>
                <div class="code-name">#icon-change_logo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-rotate"></use>
                </svg>
                <div class="name">rotate</div>
                <div class="code-name">#icon-rotate</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-delete"></use>
                </svg>
                <div class="name">delete</div>
                <div class="code-name">#icon-delete</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-tianchongFace"></use>
                </svg>
                <div class="name">感叹号</div>
                <div class="code-name">#icon-a-tianchongFace</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-tianchongFace-1"></use>
                </svg>
                <div class="name">关闭</div>
                <div class="code-name">#icon-a-tianchongFace-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-huifu"></use>
                </svg>
                <div class="name">恢复</div>
                <div class="code-name">#icon-huifu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chexiao"></use>
                </svg>
                <div class="name">撤销</div>
                <div class="code-name">#icon-chexiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhuye"></use>
                </svg>
                <div class="name">主页</div>
                <div class="code-name">#icon-zhuye</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jujiao"></use>
                </svg>
                <div class="name">聚焦</div>
                <div class="code-name">#icon-jujiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-Property2xuanzhuan45du"></use>
                </svg>
                <div class="name">旋转45度</div>
                <div class="code-name">#icon-a-Property2xuanzhuan45du</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhuanzhukongjian"></use>
                </svg>
                <div class="name">专注空间</div>
                <div class="code-name">#icon-zhuanzhukongjian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiezu-2"></use>
                </svg>
                <div class="name">解组</div>
                <div class="code-name">#icon-jiezu-2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lidi"></use>
                </svg>
                <div class="name">离地</div>
                <div class="code-name">#icon-lidi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zuhe-2"></use>
                </svg>
                <div class="name">打组</div>
                <div class="code-name">#icon-zuhe-2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-niantie"></use>
                </svg>
                <div class="name">粘贴</div>
                <div class="code-name">#icon-niantie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chicun"></use>
                </svg>
                <div class="name">尺寸</div>
                <div class="code-name">#icon-chicun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-horizontalflip_line"></use>
                </svg>
                <div class="name">horizontalflip_line</div>
                <div class="code-name">#icon-horizontalflip_line</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-verflip_line"></use>
                </svg>
                <div class="name">verflip_line</div>
                <div class="code-name">#icon-verflip_line</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-suoding1"></use>
                </svg>
                <div class="name">锁定</div>
                <div class="code-name">#icon-suoding1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiesuo1"></use>
                </svg>
                <div class="name">解锁</div>
                <div class="code-name">#icon-jiesuo1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-sucai"></use>
                </svg>
                <div class="name">素材</div>
                <div class="code-name">#icon-sucai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-suodingbuju"></use>
                </svg>
                <div class="name">锁定布局</div>
                <div class="code-name">#icon-suodingbuju</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiesuobuju"></use>
                </svg>
                <div class="name">解锁布局</div>
                <div class="code-name">#icon-jiesuobuju</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shanchubuju"></use>
                </svg>
                <div class="name">删除布局</div>
                <div class="code-name">#icon-shanchubuju</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-suodingfengge"></use>
                </svg>
                <div class="name">锁定风格</div>
                <div class="code-name">#icon-suodingfengge</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiesuofengge"></use>
                </svg>
                <div class="name">解锁风格</div>
                <div class="code-name">#icon-jiesuofengge</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qingchufengge"></use>
                </svg>
                <div class="name">清除风格</div>
                <div class="code-name">#icon-qingchufengge</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-TypesucaiStateDefault"></use>
                </svg>
                <div class="name">素材</div>
                <div class="code-name">#icon-a-TypesucaiStateDefault</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-TypeshuxingStateDefault"></use>
                </svg>
                <div class="name">属性</div>
                <div class="code-name">#icon-a-TypeshuxingStateDefault</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-TypebujuStateDefault"></use>
                </svg>
                <div class="name">布局</div>
                <div class="code-name">#icon-a-TypebujuStateDefault</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-TypehuxingStateDefault"></use>
                </svg>
                <div class="name">户型</div>
                <div class="code-name">#icon-a-TypehuxingStateDefault</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-TypegongjuStateDefault"></use>
                </svg>
                <div class="name">工具</div>
                <div class="code-name">#icon-a-TypegongjuStateDefault</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-TypefenggeStateDefault"></use>
                </svg>
                <div class="name">风格</div>
                <div class="code-name">#icon-a-TypefenggeStateDefault</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiantou"></use>
                </svg>
                <div class="name">左箭头</div>
                <div class="code-name">#icon-jiantou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Search"></use>
                </svg>
                <div class="name">Search</div>
                <div class="code-name">#icon-Search</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yonghuguanli"></use>
                </svg>
                <div class="name">用户管理</div>
                <div class="code-name">#icon-yonghuguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhanghaofenxiang"></use>
                </svg>
                <div class="name">账号分享</div>
                <div class="code-name">#icon-zhanghaofenxiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lianjiefenxiang"></use>
                </svg>
                <div class="name">链接分享</div>
                <div class="code-name">#icon-lianjiefenxiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gengduo_bold"></use>
                </svg>
                <div class="name">更多_bold</div>
                <div class="code-name">#icon-gengduo_bold</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-UnorderedListOutlined"></use>
                </svg>
                <div class="name">列表模式</div>
                <div class="code-name">#icon-UnorderedListOutlined</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kapianmoshi"></use>
                </svg>
                <div class="name">卡片模式</div>
                <div class="code-name">#icon-kapianmoshi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-more"></use>
                </svg>
                <div class="name">more</div>
                <div class="code-name">#icon-more</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-fangxiangshang"></use>
                </svg>
                <div class="name">上</div>
                <div class="code-name">#icon-a-fangxiangshang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-fangxiangxia"></use>
                </svg>
                <div class="name">下</div>
                <div class="code-name">#icon-a-fangxiangxia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-fangxiangzuo"></use>
                </svg>
                <div class="name">左</div>
                <div class="code-name">#icon-a-fangxiangzuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-fangxiangyou"></use>
                </svg>
                <div class="name">右</div>
                <div class="code-name">#icon-a-fangxiangyou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon"></use>
                </svg>
                <div class="name">close-blod</div>
                <div class="code-name">#icon-icon</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-none"></use>
                </svg>
                <div class="name">none</div>
                <div class="code-name">#icon-none</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Bookmark"></use>
                </svg>
                <div class="name">我的方案</div>
                <div class="code-name">#icon-Bookmark</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe"></use>
                </svg>
                <div class="name">我的户型</div>
                <div class="code-name">#icon-xingzhuangjiehe</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-DeleteFilled"></use>
                </svg>
                <div class="name">回收站</div>
                <div class="code-name">#icon-DeleteFilled</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
