import { XmlEntityBase } from "./XmlEntityBase";
import { XmlSpace } from "./XmlEntities/XmlSpace";
import { XmlStdWardrobeEntity } from "./XmlEntities/XmlStdWardrobeEntity";
import { FormulaParser, NumberVariable } from "..";


/**
 *  自由柜驱动的参数化系统
 */
export class XmlParamsSystem
{
    protected _xmlSpaces : Map<string,XmlSpace>;
    protected _stdEntities : Map<string,XmlEntityBase>

    protected _rootEntity : XmlEntityBase = null;
    protected _formulas : Map<string, {Variable:NumberVariable, formula?:FormulaParser}>;
    static RootKey = "Root";

    constructor()
    {
        this._xmlSpaces = new Map();
        this._stdEntities = new Map();
        this._formulas = new Map();
    }
    clear()
    {
        this._xmlSpaces.clear();
        this._stdEntities.clear();
        this._formulas.clear();
        this._rootEntity = null;
    }

    bindWardrobeEntity(EntityBase:XmlEntityBase,isRoot:boolean = true)
    {
        let entity = EntityBase as XmlStdWardrobeEntity;
        if(entity?.Children?.CWhSpaceEntity)
        {
            this.bindSpace(entity.Children.CWhSpaceEntity[0]);
        }
        if(isRoot)
        {
            this._rootEntity = EntityBase;
        }
        entity.updateValueObject();
        this.registerEntity(EntityBase,isRoot);
        if(entity.Children)
        {
            for(let key in entity.Children)
            {
                if(entity.Children[key] === entity.Children.CWhSpaceEntity)
                {
                    continue;
                }
                if(Array.isArray(entity.Children[key]))
                {
                    entity.Children[key].forEach((child)=>{
                        this.bindWardrobeEntity(child,false);
                    })
                }
            }
        }

    }   

    // 中-左-右 的遍历顺序
    bindSpace(space : XmlSpace)
    {
        if(!space) return;
        this.registerSpace(space);
        if(space.childSpaces)
        {
            space.childSpaces.forEach((space)=>this.bindSpace(space));
        }

    }

    registerSpace(space:XmlSpace)
    {
        this._xmlSpaces.set(''+space.uidN,space);
        let entity = space;
        if(entity?.VariableComponet?.NumberVariable)
        {
            if(Array.isArray(entity.VariableComponet.NumberVariable))
            {
                entity.VariableComponet.NumberVariable.forEach((numVar,index)=>{
                    let key = ''+space.uidN+"."+(numVar.nameS||"---");
                    let valueExpressionS = numVar.valueExpressionS;
                    if(valueExpressionS && !FormulaParser.isNumeric(valueExpressionS))
                    {
                        this._formulas.set(key,{
                            Variable : numVar,
                            formula : new FormulaParser(numVar.valueExpressionS) 
                        });
                    }
                    else{
                        // this._formulas.set(key,{
                        //     Variable : numVar
                        // });
                    }

                })
            }
        }
    }
    registerEntity(entity:XmlEntityBase,isRoot:boolean = false)
    {
        let uidS = this.getUidOfEntity(entity);
        this._stdEntities.set(uidS,entity);
        if(entity?.VariableComponet?.NumberVariable)
        {
            if(Array.isArray(entity.VariableComponet.NumberVariable))
            {
                entity.VariableComponet.NumberVariable.forEach((numVar,index)=>{
                    let key = uidS+"."+(numVar.nameS||"---");
                    let valueExpressionS = numVar.valueExpressionS;
                    if(valueExpressionS && !FormulaParser.isNumeric(valueExpressionS))
                    {
                        this._formulas.set(key,{
                            Variable : numVar,
                            formula : new FormulaParser(numVar.valueExpressionS) 
                        });
                    }
                    else{
                        // this._formulas.set(key,{
                        //     Variable : numVar
                        // });
                    }

                })
            }
        }
    }
    getUidOfEntity(entity : XmlEntityBase)
    {
        let uidS= entity.uidN?(''+entity.uidN):(entity.uidS||'');
        if(uidS.length == 0)
        {
            if(entity === this._rootEntity)
            {
                uidS = XmlParamsSystem.RootKey;
            }
        }
        return uidS;
    }
    getEntityByUid(uid:string)
    {
        return this._stdEntities.get(uid) || null;
    }

    getSpaceByUid(uid:string)
    {
        return this._xmlSpaces.get(uid) || null;
    }
    computeVisibleExpS(visibleExpS:string,  context:{values:{[key:string]:number|string}}={values:{}})
    {
        let t : boolean = true;
        try {
            t = new Function(...Object.keys(context.values),`return ${visibleExpS}`)(...Object.values(context.values));

        } catch (error) {
            console.log(t, {...context.values},visibleExpS);
        }
        return t;
    }
    checkVisibleB(entity:XmlEntityBase = null, context:{values:{[key:string]:number|string}}={values:{}})
    {
        if(!entity)
        {
            entity = this._rootEntity;
        }
        if(!entity) return;
        let visibleResult = true;
        let srcValueObj = {...context.values};
        if(entity.visibleExpS)
        {
            let visibleExpS = entity.visibleExpS;
            let tt = this.computeVisibleExpS(visibleExpS,context);
            if(tt !== entity.visibleB)
            {
                console.log(tt, {...context.values},visibleExpS);
            }
        }

        context.values = {...context.values,...entity.valuesObj}
        if(entity.Children)
        {
            let t_entity = entity as XmlStdWardrobeEntity;
            if(t_entity.Children.CWhSpaceEntity)
            {
                t_entity.Children.CWhSpaceEntity.forEach((child)=>{
                    this.checkVisibleB(child,context);
                })
            }
            for(let key in entity.Children)
            {
                if(entity.Children[key] === t_entity.Children.CWhSpaceEntity)
                {
                    continue;
                }
                entity.Children[key].forEach((child)=>{
                    this.checkVisibleB(child,context);
                })
            }
        }
        context.values = {...srcValueObj}; // 递归复原处理
    }

    

}