{"base-global": {"colorWhite": {"value": "#ffffff", "type": "color", "description": "不随主题变化的白色"}, "colorBlack": {"value": "#000000", "type": "color", "description": "不随主题变化的黑色"}, "color": {"green": {"1": {"value": "#f6ffed", "type": "color"}, "2": {"value": "#d9f7be", "type": "color"}, "3": {"value": "#b7eb8f", "type": "color"}, "4": {"value": "#95de64", "type": "color"}, "5": {"value": "#73d13d", "type": "color"}, "6": {"value": "#52c41a", "type": "color"}, "7": {"value": "#389e0d", "type": "color"}, "8": {"value": "#237804", "type": "color"}, "9": {"value": "#135200", "type": "color"}, "10": {"value": "#092b00", "type": "color"}}, "gold": {"1": {"value": "#fffbe6", "type": "color"}, "2": {"value": "#fff1b8", "type": "color"}, "3": {"value": "#ffe58f", "type": "color"}, "4": {"value": "#ffd666", "type": "color"}, "5": {"value": "#ffc53d", "type": "color"}, "6": {"value": "#faad14", "type": "color"}, "7": {"value": "#d48806", "type": "color"}, "8": {"value": "#ad6800", "type": "color"}, "9": {"value": "#874d00", "type": "color"}, "10": {"value": "#613400", "type": "color"}}, "red": {"1": {"value": "#fff2f0", "type": "color"}, "2": {"value": "#ffccc7", "type": "color"}, "3": {"value": "#ffa39e", "type": "color"}, "4": {"value": "#ff7875", "type": "color"}, "5": {"value": "#ff4d4f", "type": "color"}, "6": {"value": "#f5222d", "type": "color"}, "7": {"value": "#cf1322", "type": "color"}, "8": {"value": "#a8071a", "type": "color"}, "9": {"value": "#820014", "type": "color"}, "10": {"value": "#5c0011", "type": "color"}}, "geekblue": {"1": {"value": "#f0f5ff", "type": "color"}, "2": {"value": "#d6e4ff", "type": "color"}, "3": {"value": "#adc6ff", "type": "color"}, "4": {"value": "#85a5ff", "type": "color"}, "5": {"value": "#597ef7", "type": "color"}, "6": {"value": "#2f54eb", "type": "color"}, "7": {"value": "#1d39c4", "type": "color"}, "8": {"value": "#10239e", "type": "color"}, "9": {"value": "#061178", "type": "color"}, "10": {"value": "#030852", "type": "color"}}, "blue": {"1": {"value": "#E6F6FF", "type": "color"}, "2": {"value": "#b8e2ff", "type": "color"}, "3": {"value": "#8fceff", "type": "color"}, "4": {"value": "#66b8ff", "type": "color"}, "5": {"value": "#3d9eff", "type": "color"}, "6": {"value": "#147ffa", "type": "color"}, "7": {"value": "#065fd4", "type": "color"}, "8": {"value": "#0045ad", "type": "color"}, "9": {"value": "#003287", "type": "color"}, "10": {"value": "#002061", "type": "color"}}, "volcano": {"1": {"value": "#fff2e8", "type": "color"}, "2": {"value": "#ffd8bf", "type": "color"}, "3": {"value": "#ffbb96", "type": "color"}, "4": {"value": "#ff9c6e", "type": "color"}, "5": {"value": "#ff7a45", "type": "color"}, "6": {"value": "#fa541c", "type": "color"}, "7": {"value": "#d4380d", "type": "color"}, "8": {"value": "#ad2102", "type": "color"}, "9": {"value": "#871400", "type": "color"}, "10": {"value": "#610b00", "type": "color"}}, "orange": {"1": {"value": "#fff7e6", "type": "color"}, "2": {"value": "#ffe7ba", "type": "color"}, "3": {"value": "#ffd591", "type": "color"}, "4": {"value": "#ffc069", "type": "color"}, "5": {"value": "#ffa940", "type": "color"}, "6": {"value": "#fa8c16", "type": "color"}, "7": {"value": "#d46b08", "type": "color"}, "8": {"value": "#ad4e00", "type": "color"}, "9": {"value": "#873800", "type": "color"}, "10": {"value": "#612500", "type": "color"}}, "yellow": {"1": {"value": "#feffe6", "type": "color"}, "2": {"value": "#ffffb8", "type": "color"}, "3": {"value": "#fffb8f", "type": "color"}, "4": {"value": "#fff566", "type": "color"}, "5": {"value": "#ffec3d", "type": "color"}, "6": {"value": "#fadb14", "type": "color"}, "7": {"value": "#d4b106", "type": "color"}, "8": {"value": "#ad8b00", "type": "color"}, "9": {"value": "#876800", "type": "color"}, "10": {"value": "#614700", "type": "color"}}, "lime": {"1": {"value": "#fcffe6", "type": "color"}, "2": {"value": "#f4ffb8", "type": "color"}, "3": {"value": "#eaff8f", "type": "color"}, "4": {"value": "#d3f261", "type": "color"}, "5": {"value": "#bae637", "type": "color"}, "6": {"value": "#a0d911", "type": "color"}, "7": {"value": "#7cb305", "type": "color"}, "8": {"value": "#5b8c00", "type": "color"}, "9": {"value": "#3f6600", "type": "color"}, "10": {"value": "#254000", "type": "color"}}, "cyan": {"1": {"value": "#e6fffb", "type": "color"}, "2": {"value": "#b5f5ec", "type": "color"}, "3": {"value": "#87e8de", "type": "color"}, "4": {"value": "#5cdbd3", "type": "color"}, "5": {"value": "#36cfc9", "type": "color"}, "6": {"value": "#13c2c2", "type": "color"}, "7": {"value": "#08979c", "type": "color"}, "8": {"value": "#006d75", "type": "color"}, "9": {"value": "#00474f", "type": "color"}, "10": {"value": "#002329", "type": "color"}}, "purple": {"1": {"value": "#f9f0ff", "type": "color"}, "2": {"value": "#efdbff", "type": "color"}, "3": {"value": "#d3adf7", "type": "color"}, "4": {"value": "#b37feb", "type": "color"}, "5": {"value": "#9254de", "type": "color"}, "6": {"value": "#722ed1", "type": "color"}, "7": {"value": "#531dab", "type": "color"}, "8": {"value": "#391085", "type": "color"}, "9": {"value": "#22075e", "type": "color"}, "10": {"value": "#120338", "type": "color"}}, "magenta": {"1": {"value": "#fff0f6", "type": "color"}, "2": {"value": "#ffd6e7", "type": "color"}, "3": {"value": "#ffadd2", "type": "color"}, "4": {"value": "#ff85c0", "type": "color"}, "5": {"value": "#f759ab", "type": "color"}, "6": {"value": "#eb2f96", "type": "color"}, "7": {"value": "#c41d7f", "type": "color"}, "8": {"value": "#9e1068", "type": "color"}, "9": {"value": "#780650", "type": "color"}, "10": {"value": "#520339", "type": "color"}}, "gry": {"1": {"value": "#FAFAFA", "type": "color"}, "2": {"value": "#F5F5F5", "type": "color"}, "3": {"value": "#F2F3F5", "type": "color"}, "4": {"value": "#EEEFF2", "type": "color"}, "5": {"value": "#DDDFE4", "type": "color"}, "6": {"value": "#BCBEC2", "type": "color"}, "7": {"value": "#93989F", "type": "color"}, "8": {"value": "#6C7175", "type": "color"}, "9": {"value": "#565B60", "type": "color"}, "10": {"value": "#42464B", "type": "color"}, "11": {"value": "#25282D", "type": "color"}, "12": {"value": "#1C1E21", "type": "color"}, "13": {"value": "#131415", "type": "color"}}}}, "base-light": {"Neutral": {"colorWhite": {"value": "#fff", "type": "color", "description": "不随主题变化的纯白色"}, "colorText": {"value": "$color.gry.11", "type": "color", "description": "一级文本色  默认的文字色，也是最深文本色"}, "colorTextSecondary": {"value": "$color.gry.9", "type": "color", "description": "二级文本色  作为第二梯度的文本颜色，一般用于不那么需要强化文本的颜色场景"}, "colorTextTertiary": {"value": "$color.gry.7", "type": "color", "description": "三级文本色  一般用户描述性文本，例如表单中的补充说明性文字"}, "colorTextQuaternary": {"value": "$color.gry.6", "type": "color", "description": "四级文本色  一般用于禁用和占位符文本"}, "colorBgContainer": {"value": "$colorWhite", "type": "color", "description": "组件容器背景色  在亮色模式下，该颜色为默认的组件容器颜色"}, "colorBgElevated": {"value": "$colorWhite", "type": "color", "description": "浮层容器背景色  悬浮类型组件的背景色"}, "colorBgLayout": {"value": "{color.gry.1}", "type": "color", "description": "布局背景色  页面整体布局中最底层的颜色"}, "colorBgSpotlight": {"value": "rgba(0,0,0,0.85)", "type": "color", "description": "引起注意的背景色  该色用于需要引起用户强烈关注的内容背景色，目前只用在tooltip的背景色上"}, "colorBgMask": {"value": "rgba(0,0,0,0.45)", "type": "color", "description": "浮层背景蒙层颜色  用作于模态弹窗下的背景蒙层"}, "colorBorder": {"value": "$color.gry.5", "type": "color", "description": "一级边框色  默认使用的边框颜色，用于组件中的描边颜色，例如描边按钮、输入框等"}, "colorBorderSecondary": {"value": "$color.gry.4", "type": "color", "description": "二级边框色  常用作于容器的边框色和容器内部组与组之间的分割线颜色"}, "colorFill": {"value": "$color.gry.5", "type": "color", "description": "一级填充色  最深的填充色，用于拉开与二、三级填充色的区分度，目前在Slider 的 hover 效果用用到"}, "colorFillSecondary": {"value": "$color.gry.4", "type": "color", "description": "二级填充色  用于较为明显地勾勒出元素形体，如 Rate、Skeleton 等。也可以作为三级填充色的 Hover 状态，如 Table 等"}, "colorFillTertiary": {"value": "$color.gry.3", "type": "color", "description": "三级填充色  用于勾勒出元素形体的场景，如 Slider、Segmented 等。如无强调需求的情况下，建议使用三级填色作为默认填色"}, "colorFillQuaternary": {"value": "{color.gry.1}", "type": "color", "description": "四级填充色  最弱一级的填充色，适用于不易引起注意的色块，例如斑马纹、区分边界的色块等"}}, "Primary": {"colorPrimaryBg": {"value": "{color.blue.1}", "type": "color", "description": "主色浅色背景色\n主色浅色背景色，一般用于视角层级较弱的选中状态"}, "colorPrimaryBgHover": {"value": "{color.blue.2}", "type": "color", "description": "主色浅色背景悬浮态\n与主色浅色背景颜色相对的悬浮态颜色"}, "colorPrimaryBorder": {"value": "{color.blue.3}", "type": "color", "description": "主色描边色\n主色梯度下的描边用色，用在 Slider 组件的描边上"}, "colorPrimaryBorderHover": {"value": "$color.blue.4", "type": "color", "description": "主色描边悬浮态\n主色梯度下的描边用色的悬浮态，Slider 、Button 等组件的描边 Hover 时会使用"}, "colorPrimaryHover": {"value": "$color.blue.5", "type": "color", "description": "主色悬浮态\n主色梯度下的悬浮态，高使用频率"}, "colorPrimary": {"value": "{color.blue.6}", "type": "color", "description": "品牌主色\n品牌色是体现产品特性和传播理念最直观的视觉元素之一。在你完成品牌主色的选取之后，我们会自动帮你生成一套完整的色板，并赋予它们有效的设计语义"}, "colorPrimaryActive": {"value": "$color.blue.7", "type": "color", "description": "主色激活态\n主色梯度下深色激活态"}, "colorPrimaryTextHover": {"value": "$color.blue.5", "type": "color", "description": "主色文本悬浮态\n主色梯度下的文本悬浮态"}, "colorPrimaryText": {"value": "$color.blue.6", "type": "color", "description": "主色文本\n主色梯度下的文本颜色"}, "colorPrimaryTextActive": {"value": "{color.blue.7}", "type": "color", "description": "主色文本激活态\n主色梯度下的文本激活状态"}}, "Success": {"colorSuccessBg": {"value": "$color.green.1", "type": "color", "description": "成功色的浅色背景颜色  用于 Tag 和 Alert 的成功态背景色"}, "colorSuccessBgHover": {"value": "$color.green.2", "type": "color", "description": "成功色浅色背景颜色hover状态色  一般用于视觉层级较弱的选中状态"}, "colorSuccessBorder": {"value": "$color.green.3", "type": "color", "description": "成功色的描边色      用于 Tag 和 Alert 的成功态描边色"}, "colorSuccessBorderHover": {"value": "$color.green.4", "type": "color", "description": "成功色的描边色悬浮态"}, "colorSuccessHover": {"value": "$color.green.4", "type": "color", "description": "成功色的悬浮态"}, "colorSuccess": {"value": "$color.green.6", "type": "color", "description": "成功色  用于表示操作成功的Token序列，如 Result、Progress 等组件会使用该组梯度变量"}, "colorSuccessActive": {"value": "$color.green.7", "type": "color", "description": "成功色的激活态"}, "colorSuccessTextHover": {"value": "$color.green.5", "type": "color", "description": "成功色的文本悬浮态"}, "colorSuccessText": {"value": "$color.green.6", "type": "color", "description": "成功色的文本默认态"}, "colorSuccessTextActive": {"value": "$color.green.7", "type": "color", "description": "成功色的文本激活态"}}, "Warning": {"colorWarningBg": {"value": "$color.gold.1", "type": "color", "description": "警戒色的浅色背景颜色"}, "colorWarningBgHover": {"value": "$color.gold.2", "type": "color", "description": "警戒色的浅色背景色悬浮态"}, "colorWarningBorder": {"value": "$color.gold.3", "type": "color", "description": "警戒色的描边色"}, "colorWarningBorderHover": {"value": "$color.gold.4", "type": "color", "description": "警戒色的描边色悬浮态"}, "colorWarningHover": {"value": "$color.gold.4", "type": "color", "description": "警戒色的悬浮态"}, "colorWarning": {"value": "$color.gold.6", "type": "color", "description": "警示色  用于表示操作警告的Token序列，如 Notification、 Alert等警告类组件或 Input 输入类等组件会使用该组梯度变量"}, "colorWarningActive": {"value": "$color.gold.7", "type": "color", "description": "警戒色的激活态"}, "colorWarningTextHover": {"value": "$color.gold.5", "type": "color", "description": "警戒色的文本悬浮态"}, "colorWarningText": {"value": "$color.gold.6", "type": "color", "description": "警戒色的文本默认态"}, "colorWarningTextActive": {"value": "$color.gold.7", "type": "color", "description": "警戒色的文本激活态"}}, "Error": {"coloErrorBg": {"value": "$color.red.1", "type": "color", "description": "错误色的浅色背景颜色"}, "colorErrorBgHover": {"value": "$color.red.2", "type": "color", "description": "错误色的浅色背景色悬浮态"}, "colorErrorBorder": {"value": "$color.red.3", "type": "color", "description": "错误色的描边色"}, "colorWarningErrorHover": {"value": "$color.red.4", "type": "color", "description": "错误色的描边色悬浮态"}, "colorErrorHover": {"value": "$color.red.4", "type": "color", "description": "错误色的悬浮态"}, "colorError": {"value": "$color.red.6", "type": "color", "description": "错误色  用于表示操作失败的Token序列，如失败按钮、错误状态提示（Result）组件等"}, "colorErrorActive": {"value": "$color.red.7", "type": "color", "description": "错误色的激活态"}, "colorErrorTextHover": {"value": "$color.red.5", "type": "color", "description": "错误色的文本悬浮态"}, "colorErrorText": {"value": "$color.red.6", "type": "color", "description": "错误色的文本默认态"}, "colorErrorTextActive": {"value": "$color.red.7", "type": "color", "description": "错误色的文本激活态"}}, "Info": {"colorInfoBg": {"value": "$Primary.colorPrimaryBg", "type": "color", "description": "信息色的浅色背景颜色"}, "colorInfoBgHover": {"value": "$Primary.colorPrimaryBgHover", "type": "color", "description": "信息色的浅色背景色悬浮态"}, "colorInfoBorder": {"value": "$Primary.colorPrimaryBorder", "type": "color", "description": "信息色的描边色"}, "colorWarningInfoHover": {"value": "$Primary.colorPrimaryHover", "type": "color", "description": "信息色的描边色悬浮态"}, "colorInfoHover": {"value": "$Primary.colorPrimaryHover", "type": "color", "description": "信息色的悬浮态"}, "colorInfo": {"value": "$Primary.colorPrimary", "type": "color", "description": "信息色  信息色目前跟随主色，各产品可以根据变量重新定义一个信息色，用于表示操作信息的Token序列，如 Alert 、Tag、 Progress 等组件都有用到该组梯度变量"}, "colorInfoActive": {"value": "$Primary.colorPrimaryActive", "type": "color", "description": "信息色的激活态"}, "colorInfoTextHover": {"value": "$Primary.colorPrimaryTextHover", "type": "color", "description": "信息色的文本悬浮态"}, "colorInfoText": {"value": "$Primary.colorPrimaryText", "type": "color", "description": "信息色的文本默认态"}, "colorInfoTextActive": {"value": "$Primary.colorPrimaryTextActive", "type": "color", "description": "信息色的文本激活态"}}, "fontSize": {"value": "14px", "type": "fontSizes", "description": "默认字号\n设计系统中使用最广发的字体大小"}, "fontSizeSM": {"value": "12px", "type": "fontSizes"}, "fontSizeLG": {"value": "16px", "type": "fontSizes"}, "fontSizeXL": {"value": "20px", "type": "fontSizes"}, "fontSizeHeading1": {"value": "38px", "type": "fontSizes"}, "fontSizeHeading2": {"value": "30px", "type": "fontSizes"}, "fontSizeHeading3": {"value": "24px", "type": "fontSizes"}, "fontSizeHeading4": {"value": "20px", "type": "fontSizes"}, "fontSizeHeading5": {"value": "16px", "type": "fontSizes"}, "lineHeight": {"value": "157%", "type": "lineHeights"}, "lineHeightSM": {"value": "167%", "type": "lineHeights"}, "lineHeightLG": {"value": "150%", "type": "lineHeights"}, "lineHeightHeading1": {"value": "121%", "type": "lineHeights"}, "lineHeightHeading2": {"value": "127%", "type": "lineHeights"}, "lineHeightHeading3": {"value": "133%", "type": "lineHeights"}, "lineHeightHeading4": {"value": "140%", "type": "lineHeights"}, "lineHeightHeading5": {"value": "150%", "type": "lineHeights"}, "borderRadius": {"value": "6px", "type": "borderRadius", "description": "基础圆角\n基础组件的圆角大小，例如卡片、按钮、输入框等"}, "borderRadiusSM": {"value": "4px", "type": "borderRadius", "description": "SM号圆角\nSM号圆角，用于小尺寸组件的圆角，例如按钮、输入框、selec等输入类型控件在small size下的圆角"}, "borderRadiusLG": {"value": "8px", "type": "borderRadius", "description": "LG号圆角\n用于组件中的一些大圆角，如card、modal等一些组件样式"}, "borderRadiusXS": {"value": "2px", "type": "borderRadius", "description": "XS号圆角\n用于组件中的一些小圆角，如segmented、arrow等一些内部圆角的组件样式中"}, "boxShadow": {"value": [{"x": "0", "y": "6", "blur": "16", "spread": "-8", "color": "rgba(0,0,0,0.08)", "type": "dropShadow"}, {"x": "0", "y": "9", "blur": "28", "spread": "0", "color": "rgba(0,0,0,0.05)", "type": "dropShadow"}, {"x": "0", "y": "12", "blur": "48", "spread": "16", "color": "rgba(0,0,0,0.03)", "type": "dropShadow"}], "type": "boxShadow", "description": "一级阴影"}, "boxShadowSecondary": {"value": [{"x": "0", "y": "6", "blur": "16", "spread": "0", "color": "rgba(0,0,0,0.08)", "type": "dropShadow"}, {"x": "0", "y": "3", "blur": "6", "spread": "-4", "color": "rgba(0,0,0,0.12)", "type": "dropShadow"}, {"x": "0", "y": "9", "blur": "28", "spread": "8", "color": "rgba(0,0,0,0.05)", "type": "dropShadow"}], "type": "boxShadow"}, "regular": {"value": "400", "type": "fontWeights", "description": "常规粗细"}, "medium": {"value": "500", "type": "fontWeights", "description": "中粗  极少使用需要字体支持"}, "bold": {"value": "600", "type": "fontWeights", "description": "加粗"}, "light": {"value": "300", "type": "fontWeights", "description": "细体"}, "fontFamily": {"value": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "type": "fontFamilies", "description": "字体家族中优先使用系统默认的界面字体，同时提供了一套利于屏显的备用字体库，来维护在不同平台以及浏览器的显示下，字体始终保持良好的易读性和可读性，体现了友好、稳定和专业的特性"}, "boxShadowTertiary": {"value": [{"x": "0", "y": "1px", "blur": "2px", "spread": "0", "color": "rgba(0, 0, 0, 0.03)", "type": "dropShadow"}, {"x": "0", "y": "1px", "blur": "6px", "spread": "-1px", "color": "rgba(0, 0, 0, 0.02)", "type": "dropShadow"}, {"x": "0", "y": "2px", "blur": "4px", "spread": "0", "color": "rgba(0, 0, 0, 0.02)", "type": "dropShadow"}], "type": "boxShadow", "description": "三级阴影"}}, "base-dark": {"Neutral": {"colorWhite": {"value": "#fff", "type": "color", "description": "不随主题变化的白色"}, "colorText": {"value": "$color.gry.4", "type": "color", "description": "一级文本色  默认的文字色"}, "colorTextSecondary": {"value": "$color.gry.7", "type": "color", "description": "二级文本色  作为第二梯度的文本颜色，一般用于不那么需要强化文本的颜色场景"}, "colorTextTertiary": {"value": "$color.gry.8", "type": "color", "description": "三级文本色  一般用户描述性文本，例如表单中的补充说明性文字"}, "colorTextQuaternary": {"value": "$color.gry.10", "type": "color", "description": "四级文本色  一般用于禁用和占位符文本"}, "colorBgContainer": {"value": "$color.gry.13", "type": "color", "description": "组件容器背景色  在亮色模式下，该颜色为默认的组件容器颜色"}, "colorBgElevated": {"value": "$color.gry.12", "type": "color", "description": "浮层容器背景色  悬浮类型组件的背景色"}, "colorBgLayout": {"value": "$color.gry.13", "type": "color", "description": "布局背景色  页面整体布局中最底层的颜色"}, "colorBgSpotlight": {"value": "$color.gry.10", "type": "color", "description": "引起注意的背景色  该色用于需要引起用户强烈关注的内容背景色，目前只用在tooltip的背景色上"}, "colorBgMask": {"value": "rgba(0,0,0,0.45)", "type": "color", "description": "浮层背景蒙层颜色  用作于模态弹窗下的背景蒙层"}, "colorBorder": {"value": "$color.gry.10", "type": "color", "description": "一级边框色  默认使用的边框颜色，用于组件中的描边颜色，例如描边按钮、输入框等"}, "colorBorderSecondary": {"value": "$color.gry.11", "type": "color", "description": "二级边框色  常用作于容器的边框色和容器内部组与组之间的分割线颜色"}, "colorFill": {"value": "rgba(191,216,255,0.16)", "type": "color", "description": "一级填充色  最深的填充色，用于拉开与二、三级填充色的区分度，目前在Slider 的 hover 效果用用到"}, "colorFillSecondary": {"value": "rgba(191,216,255,0.12)", "type": "color", "description": "二级填充色  用于较为明显地勾勒出元素形体，如 Rate、Skeleton 等。也可以作为三级填充色的 Hover 状态，如 Table 等"}, "colorFillTertiary": {"value": "rgba(191,216,255,0.08)", "type": "color", "description": "三级填充色  用于勾勒出元素形体的场景，如 Slider、Segmented 等。如无强调需求的情况下，建议使用三级填色作为默认填色"}, "colorFillQuaternary": {"value": "rgba(191,216,255,0.04)", "type": "color", "description": "四级填充色  最弱一级的填充色，适用于不易引起注意的色块，例如斑马纹、区分边界的色块等"}}, "Primary": {"colorPrimaryBg": {"value": "{color.blue.1}", "type": "color", "description": "主色浅色背景色\n主色浅色背景色，一般用于视角层级较弱的选中状态"}, "colorPrimaryBgHover": {"value": "{color.blue.2}", "type": "color", "description": "主色浅色背景悬浮态\n与主色浅色背景颜色相对的悬浮态颜色"}, "colorPrimaryBorder": {"value": "{color.blue.3}", "type": "color", "description": "主色描边色\n主色梯度下的描边用色，用在 Slider 组件的描边上"}, "colorPrimaryBorderHover": {"value": "$color.blue.4", "type": "color", "description": "主色描边悬浮态\n主色梯度下的描边用色的悬浮态，Slider 、Button 等组件的描边 Hover 时会使用"}, "colorPrimaryHover": {"value": "$color.blue.5", "type": "color", "description": "主色悬浮态\n主色梯度下的悬浮态，高使用频率"}, "colorPrimary": {"value": "{color.blue.6}", "type": "color", "description": "品牌主色\n品牌色是体现产品特性和传播理念最直观的视觉元素之一。在你完成品牌主色的选取之后，我们会自动帮你生成一套完整的色板，并赋予它们有效的设计语义"}, "colorPrimaryActive": {"value": "$color.blue.7", "type": "color", "description": "主色激活态\n主色梯度下深色激活态"}, "colorPrimaryTextHover": {"value": "$color.blue.5", "type": "color", "description": "主色文本悬浮态\n主色梯度下的文本悬浮态"}, "colorPrimaryText": {"value": "$color.blue.6", "type": "color", "description": "主色文本\n主色梯度下的文本颜色"}, "colorPrimaryTextActive": {"value": "{color.blue.7}", "type": "color", "description": "主色文本激活态\n主色梯度下的文本激活状态"}}, "Success": {"colorSuccessBg": {"value": "$color.green.1", "type": "color", "description": "成功色的浅色背景颜色  用于 Tag 和 Alert 的成功态背景色"}, "colorSuccessBgHover": {"value": "$color.green.2", "type": "color", "description": "成功色浅色背景颜色hover状态色  一般用于视觉层级较弱的选中状态"}, "colorSuccessBorder": {"value": "$color.green.3", "type": "color", "description": "成功色的描边色      用于 Tag 和 Alert 的成功态描边色"}, "colorSuccessBorderHover": {"value": "$color.green.4", "type": "color", "description": "成功色的描边色悬浮态"}, "colorSuccessHover": {"value": "$color.green.4", "type": "color", "description": "成功色的悬浮态"}, "colorSuccess": {"value": "$color.green.6", "type": "color", "description": "成功色  用于表示操作成功的Token序列，如 Result、Progress 等组件会使用该组梯度变量"}, "colorSuccessActive": {"value": "$color.green.7", "type": "color", "description": "成功色的激活态"}, "colorSuccessTextHover": {"value": "$color.green.5", "type": "color", "description": "成功色的文本悬浮态"}, "colorSuccessText": {"value": "$color.green.6", "type": "color", "description": "成功色的文本默认态"}, "colorSuccessTextActive": {"value": "$color.green.7", "type": "color", "description": "成功色的文本激活态"}}, "Warning": {"colorWarningBg": {"value": "$color.gold.1", "type": "color", "description": "警戒色的浅色背景颜色"}, "colorWarningBgHover": {"value": "$color.gold.2", "type": "color", "description": "警戒色的浅色背景色悬浮态"}, "colorWarningBorder": {"value": "$color.gold.3", "type": "color", "description": "警戒色的描边色"}, "colorWarningBorderHover": {"value": "$color.gold.4", "type": "color", "description": "警戒色的描边色悬浮态"}, "colorWarningHover": {"value": "$color.gold.4", "type": "color", "description": "警戒色的悬浮态"}, "colorWarning": {"value": "$color.gold.6", "type": "color", "description": "警示色  用于表示操作警告的Token序列，如 Notification、 Alert等警告类组件或 Input 输入类等组件会使用该组梯度变量"}, "colorWarningActive": {"value": "$color.gold.7", "type": "color", "description": "警戒色的激活态"}, "colorWarningTextHover": {"value": "$color.gold.5", "type": "color", "description": "警戒色的文本悬浮态"}, "colorWarningText": {"value": "$color.gold.6", "type": "color", "description": "警戒色的文本默认态"}, "colorWarningTextActive": {"value": "$color.gold.7", "type": "color", "description": "警戒色的文本激活态"}}, "Error": {"coloErrorBg": {"value": "$color.red.1", "type": "color", "description": "错误色的浅色背景颜色"}, "colorErrorBgHover": {"value": "$color.red.2", "type": "color", "description": "错误色的浅色背景色悬浮态"}, "colorErrorBorder": {"value": "$color.red.3", "type": "color", "description": "错误色的描边色"}, "colorWarningErrorHover": {"value": "$color.red.4", "type": "color", "description": "错误色的描边色悬浮态"}, "colorErrorHover": {"value": "$color.red.4", "type": "color", "description": "错误色的悬浮态"}, "colorError": {"value": "$color.red.6", "type": "color", "description": "错误色  用于表示操作失败的Token序列，如失败按钮、错误状态提示（Result）组件等"}, "colorErrorActive": {"value": "$color.red.7", "type": "color", "description": "错误色的激活态"}, "colorErrorTextHover": {"value": "$color.red.5", "type": "color", "description": "错误色的文本悬浮态"}, "colorErrorText": {"value": "$color.red.6", "type": "color", "description": "错误色的文本默认态"}, "colorErrorTextActive": {"value": "$color.red.7", "type": "color", "description": "错误色的文本激活态"}}, "Info": {"colorInfoBg": {"value": "$Primary.colorPrimaryBg", "type": "color", "description": "信息色的浅色背景颜色"}, "colorInfoBgHover": {"value": "$Primary.colorPrimaryBgHover", "type": "color", "description": "信息色的浅色背景色悬浮态"}, "colorInfoBorder": {"value": "$Primary.colorPrimaryBorder", "type": "color", "description": "信息色的描边色"}, "colorWarningInfoHover": {"value": "$Primary.colorPrimaryHover", "type": "color", "description": "信息色的描边色悬浮态"}, "colorInfoHover": {"value": "$Primary.colorPrimaryHover", "type": "color", "description": "信息色的悬浮态"}, "colorInfo": {"value": "$Primary.colorPrimary", "type": "color", "description": "信息色  信息色目前跟随主色，各产品可以根据变量重新定义一个信息色，用于表示操作信息的Token序列，如 Alert 、Tag、 Progress 等组件都有用到该组梯度变量"}, "colorInfoActive": {"value": "$Primary.colorPrimaryActive", "type": "color", "description": "信息色的激活态"}, "colorInfoTextHover": {"value": "$Primary.colorPrimaryTextHover", "type": "color", "description": "信息色的文本悬浮态"}, "colorInfoText": {"value": "$Primary.colorPrimaryText", "type": "color", "description": "信息色的文本默认态"}, "colorInfoTextActive": {"value": "$Primary.colorPrimaryTextActive", "type": "color", "description": "信息色的文本激活态"}}, "fontSize": {"value": "14px", "type": "fontSizes", "description": "默认字号\n设计系统中使用最广发的字体大小"}, "fontSizeSM": {"value": "12px", "type": "fontSizes"}, "fontSizeLG": {"value": "16px", "type": "fontSizes"}, "fontSizeXL": {"value": "20px", "type": "fontSizes"}, "fontSizeHeading1": {"value": "38px", "type": "fontSizes"}, "fontSizeHeading2": {"value": "30px", "type": "fontSizes"}, "fontSizeHeading3": {"value": "24px", "type": "fontSizes"}, "fontSizeHeading4": {"value": "20px", "type": "fontSizes"}, "fontSizeHeading5": {"value": "16px", "type": "fontSizes"}, "lineHeight": {"value": "157%", "type": "lineHeights"}, "lineHeightSM": {"value": "167%", "type": "lineHeights"}, "lineHeightLG": {"value": "150%", "type": "lineHeights"}, "lineHeightHeading1": {"value": "121%", "type": "lineHeights"}, "lineHeightHeading2": {"value": "127%", "type": "lineHeights"}, "lineHeightHeading3": {"value": "133%", "type": "lineHeights"}, "lineHeightHeading4": {"value": "140%", "type": "lineHeights"}, "lineHeightHeading5": {"value": "150%", "type": "lineHeights"}, "borderRadius": {"value": "6px", "type": "borderRadius", "description": "基础圆角\n基础组件的圆角大小，例如卡片、按钮、输入框等"}, "borderRadiusSM": {"value": "4px", "type": "borderRadius", "description": "SM号圆角\nSM号圆角，用于小尺寸组件的圆角，例如按钮、输入框、selec等输入类型控件在small size下的圆角"}, "borderRadiusLG": {"value": "8px", "type": "borderRadius", "description": "LG号圆角\n用于组件中的一些大圆角，如card、modal等一些组件样式"}, "borderRadiusXS": {"value": "2px", "type": "borderRadius", "description": "XS号圆角\n用于组件中的一些小圆角，如segmented、arrow等一些内部圆角的组件样式中"}, "boxShadow": {"value": [{"x": "0", "y": "6", "blur": "16", "spread": "-8", "color": "rgba(0,0,0,0.08)", "type": "dropShadow"}, {"x": "0", "y": "9", "blur": "28", "spread": "0", "color": "rgba(0,0,0,0.05)", "type": "dropShadow"}, {"x": "0", "y": "12", "blur": "48", "spread": "16", "color": "rgba(0,0,0,0.03)", "type": "dropShadow"}], "type": "boxShadow", "description": "一级阴影"}, "boxShadowSecondary": {"value": [{"x": "0", "y": "6", "blur": "16", "spread": "0", "color": "rgba(0,0,0,0.08)", "type": "dropShadow"}, {"x": "0", "y": "3", "blur": "6", "spread": "-4", "color": "rgba(0,0,0,0.12)", "type": "dropShadow"}, {"x": "0", "y": "9", "blur": "28", "spread": "8", "color": "rgba(0,0,0,0.05)", "type": "dropShadow"}], "type": "boxShadow"}, "boxShadowTertiary": {"value": [{"x": "0", "y": "1px", "blur": "2px", "spread": "0", "color": "rgba(0, 0, 0, 0.03)", "type": "dropShadow"}, {"x": "0", "y": "1px", "blur": "6px", "spread": "-1px", "color": "rgba(0, 0, 0, 0.02)", "type": "dropShadow"}, {"x": "0", "y": "2px", "blur": "4px", "spread": "0", "color": "rgba(0, 0, 0, 0.02)", "type": "dropShadow"}], "type": "boxShadow", "description": "三级阴影"}, "regular": {"value": "400", "type": "fontWeights", "description": "常规粗细"}, "medium": {"value": "500", "type": "fontWeights", "description": "中粗  极少使用需要字体支持"}, "bold": {"value": "600", "type": "fontWeights", "description": "加粗"}, "light": {"value": "300", "type": "fontWeights", "description": "细体"}, "fontFamily": {"value": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "type": "fontFamilies", "description": "字体家族中优先使用系统默认的界面字体，同时提供了一套利于屏显的备用字体库，来维护在不同平台以及浏览器的显示下，字体始终保持良好的易读性和可读性，体现了友好、稳定和专业的特性"}}, "$themes": [], "$metadata": {"tokenSetOrder": ["base-global", "base-light", "base-dark"]}}