import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, compareN<PERSON>s } from "@layoutai/z_polygon";
import { Vector3 } from "three";

import { <PERSON><PERSON>Width, <PERSON><PERSON><PERSON>wayWidth, TRoomEdgeProp } from "@layoutai/basic_data";
import { I_ElementPoseData, I_FigureSolverParam, TCollisionJudge, TFigureElement, TRoom } from "@layoutai/layout_scheme";

import { TLayoutRelation } from "../TLayoutRelation";


export interface I_AlignInRoomInfo
{
    accepted_min_level ?: number;
    accepted_max_level ?: number;
    prefer_to_near_rooms ?: string[];
    
}

/**
 *  图元要在房间内的移动策略
 */
export class TAlignInRoomRelation extends TLayoutRelation
{

    target_element : TFigureElement;

    align_room_info : I_AlignInRoomInfo;


    _candidate_edges : ZEdge[];
    _prefer_pos_list : Vector3[];


    _candidate_ele_pose_list : I_ElementPoseData[];
    _candidate_results : {nor:Vector3, back_center : Vector3, score:number}[];
    _rect_center_before_applied : Vector3;
    _candidate_data_list : {candidate_edge : ZEdge, score : number, offset : Vector3}[];

    _root_type : number;
    constructor()
    {
        super();
        this.align_room_info = {};
        this._candidate_data_list = [];
        this._candidate_ele_pose_list = [];
        this._order = 1;
        this._root_type = 0;
    }

    ready_compute(): void {

        if(!this.target_element) return;
        let feature_shape = this._layout_f_shape;
        if(!feature_shape) return;

        let candidate_edges : ZEdge[] = [];
        let min_level = this.align_room_info.accepted_min_level || 2; // 默认是2
        let max_level = this.align_room_info.accepted_max_level || 10;
        for(let edge of feature_shape._align_candidate_edges)
        {
            if(edge.getProperty(TRoomEdgeProp.CandidateLevel)>=min_level && edge.getProperty(TRoomEdgeProp.CandidateLevel)<=max_level)
            {
                candidate_edges.push(edge);
            }
        }
        let prefer_to_pos_list : Vector3[] = [];

        if(this.align_room_info.prefer_to_near_rooms)
        {
            if(feature_shape._room)
            {
                for(let win of (feature_shape._room as TRoom).windows)
                {
                    if(win.room_names && win.room_names.length > 1)
                    {
                        if(compareNames(win.room_names,this.align_room_info.prefer_to_near_rooms))
                        {
                            if(win.rect)
                            {
                                prefer_to_pos_list.push(win.center.clone());

                            }
                        }
                    }
                }
            }
        }

        this._candidate_edges = candidate_edges;
        this._prefer_pos_list = prefer_to_pos_list;
        if(this._root_type==1)
        {
            this._candidate_ele_pose_list = [];
            this.readyComputeOfOtherRootElement();
        }
    }
    precompute(): void {

        this._has_computed = 2;


        if(this._root_type >= 2)
        {
            this.sortCandidatesOfRootElement();
            this._attempt_num = this._candidate_data_list.length; // 尝试所有的备选边
        }
        else if(this._root_type == 1)
        {
            this._attempt_num = 1;
            this.sortCandidatesOfOtherRootElement();
            this._rect_center_before_applied = this.target_element.rect.rect_center;
            // this._attempt_num = this._candidate_ele_pose_list.length;
        }
        else{
            this.sortCandidatesOfChildElement();
            this._rect_center_before_applied = this.target_element.rect.rect_center;
            this._attempt_num = 1; // 尝试多个位置
        }
    }

    sortCandidatesOfRootElement()
    {
        this._candidate_data_list = [];

        let candidate_edges = this._candidate_edges;
        let prefer_to_pos_list = this._prefer_pos_list;
        let target_len = this.target_element.rect._w * 1.25;
        let len_score = (len:number)=>{
            return len / target_len;
        }
        let pos_score = (edge : ZEdge)=> {
            let points = [edge.v0.pos,edge.v1.pos,edge.center];

            let min_dist: number = 9999999;
            for(let p of points)
            {
                for(let kp of prefer_to_pos_list)
                {
                    let dist = p.distanceTo(kp);
                    if(dist < min_dist) min_dist = dist;
                }
            }
            return min_dist;
        }
        let level_score = (edge : ZEdge)=>{
            return edge.getProperty(TRoomEdgeProp.CandidateLevel);
        }
        candidate_edges.sort((a,b)=>{
            let len_b = len_score(b.length); 
            let len_a = len_score(a.length);

            if(len_b > 1. && len_a > 1.)
            {
                let level_a = level_score(a);
                let level_b = level_score(b);

                if(Math.abs(level_b - level_a) < 0.1)
                {
                    let pscore_b = pos_score(b);
                    let pscore_a = pos_score(a);
                    // console.log(len_b,len_a,pscore_a,pscore_b);

                    return pscore_a - pscore_b;
                }
                else{
                    return level_b - level_a;
                }
       
            }
            return len_b-len_a;
        });

        this._candidate_data_list = [];

        for(let candidate_edge of this._candidate_edges)
        {
            
            let up_pos_list : Vector3[] = [
                    new Vector3(candidate_edge.length/2,0,0),
                    new Vector3(candidate_edge.length - this.target_element.rect._w/2. - 100),
                    new Vector3(this.target_element.rect._w/2+100,0,0)];

                if(candidate_edge.length < this.target_element.rect._w * 2.)
                {
                    up_pos_list = [                    
                        new Vector3(candidate_edge.length/2,0,0),
                        ];
                }
            for(let p of up_pos_list)
            {
                this._candidate_data_list.push({
                    candidate_edge : candidate_edge,
                    score : 0,
                    offset : p
                });
            }        

        }
    }
    

    sortCandidatesOfChildElement()
    {
        this._candidate_data_list = [];
        let candidate_edges = this._candidate_edges;
        let ans_candidate : ZEdge = null;
        let max_score = -1000;
        let solver_params = this.target_element._solver_params;
        let init_pos = new Vector3().copy(this.target_element._solver_params._init_position);
        let rect = this.target_element.rect;
        let init_back_pos = init_pos.clone().sub(rect._nor.clone().multiplyScalar(-rect._h/2));

        let __compute_offset = function(candidate_edge: ZEdge, solver_params:I_FigureSolverParam){
            let nor_offset_dist = candidate_edge.center.clone().sub(init_back_pos).dot(rect._nor);

            let points = [candidate_edge.unprojectEdge2d({x:rect._w/2,y:0}),
             candidate_edge.center.clone(), candidate_edge.unprojectEdge2d({x:candidate_edge.length-rect._w/2,y:0})];

             let pp = candidate_edge.projectEdge2d(init_back_pos);

             if(pp.x >= 0 && pp.x < candidate_edge.length)
             {
                points.push(candidate_edge.unprojectEdge2d({x:pp.x,y:0}));
             }
             
            let closest_pos :Vector3 = null;
            for(let p of points)
            {
                if(!closest_pos || p.distanceTo(init_back_pos) < closest_pos.distanceTo(init_back_pos))
                {
                    closest_pos = p;
                }
            }
            let dir_val = closest_pos.clone().sub(init_back_pos).dot(rect.dv);

        

            return {x:dir_val,y:nor_offset_dist,z:0};
        }

        let __check_score = function(candidate_edge:ZEdge, solver_params:I_FigureSolverParam,nor_offset_dist:number,dir_val:number)
        {

            let score = candidate_edge.length / rect._w;
            if(score < 0.5) return -1000;
            score = score * score * 10;
            if(score > 10) score = 10;
            if(solver_params._nor_offset_range.max < nor_offset_dist )
            {
                let dm = Math.abs(nor_offset_dist);
                score -= dm * (solver_params._nor_offset_range.weight||1) / rect._w;
            }

            else if(solver_params._nor_offset_range.min > nor_offset_dist)
            {
                let dm = Math.abs( nor_offset_dist);
                score -= dm * (solver_params._nor_offset_range.weight || 1) / rect._w;
            }
            else{
                score += 1. + 0.2 * candidate_edge.getProperty(TRoomEdgeProp.CandidateLevel);
            }

            if(solver_params._dir_offset_range.max < dir_val)
            {
                let dm = Math.abs(dir_val);
                score -= dm * (solver_params._dir_offset_range.weight || 1) / rect._w;
            }
            else if(solver_params._dir_offset_range.min > dir_val)
            {
                let dm = Math.abs( dir_val);
                score -= dm * (solver_params._dir_offset_range.weight || 1) / rect._w;
            }
            else{
                score += 1. + 0.2 * candidate_edge.getProperty(TRoomEdgeProp.CandidateLevel);
            }
            // score -= Math.abs(nor_offset_dist) / rect._w;
            return score;

        }

        this._candidate_data_list = [];
        for(let candidate_edge of candidate_edges)
        {
            if((candidate_edge.nor.dot(rect._nor)) > -0.5) continue; // 同侧朝向


            let offset = __compute_offset(candidate_edge,solver_params);

            let score = __check_score(candidate_edge,solver_params,offset.y,offset.x);
            this._candidate_data_list.push({
                candidate_edge : candidate_edge,
                score : score,
                offset : new Vector3().copy(offset)
            });

            this._candidate_data_list.sort((a,b)=>b.score - a.score);
        }
    }

    readyComputeOfOtherRootElement()
    {
        if(!this.target_element) return null;
        let feature_shape = this._layout_f_shape;
        if(!feature_shape) return null;

        let pos_nor_len : number = MinHallwayWidth;
        let test_nor_num = 2;
        let test_dir_num = 2;
        let rect = this.target_element.rect;
        this._candidate_ele_pose_list = [];

        let candidate_ele_pose_list : I_ElementPoseData[] = [];
        for(let edge of this._candidate_edges)
        {       
            let pos = edge.center;
            let nor = edge.nor.clone().negate();     
            let dv = edge.dv.clone();
            
            for(let i=0; i < test_nor_num; i++)
            {
                for(let j=-test_dir_num; j < test_dir_num; j++)
                {
                    let target_pos = pos.clone().add(nor.clone().multiplyScalar(i*pos_nor_len)).add((dv.clone().multiplyScalar(j * pos_nor_len)));
                    let target_nor = nor.clone();

                    for(let flag=0; flag < 2; flag++)
                    {
                        let res_pos = target_pos.clone();
                        if(flag == 1)
                        {
                            target_nor = target_nor.clone().cross(new Vector3(0,0,1));
                            res_pos.add(nor.clone().multiplyScalar(rect._w/2));
                        }
                        else{
                            res_pos.add(nor.clone().multiplyScalar(rect._h/2));
                        }
                        candidate_ele_pose_list.push({
                            nor : target_nor,
                            rect_center : res_pos,
                            _score_0 : -99999,
                            score : -99999
                        });
                    }

                }

            }
            
        }
        this._rect_center_before_applied = rect.rect_center.clone();

        let scope = this;

        let add_candidate_pose = (pose : I_ElementPoseData) =>{
            for(let t_pose of scope._candidate_ele_pose_list)
            {
                if(t_pose.nor.dot(pose.nor) >= 1.)
                {
                    if(t_pose.rect_center.distanceTo(pose.rect_center) < MinHallwayWidth/4)
                    {
                        return false;
                    }

                }
            }
            scope._candidate_ele_pose_list.push(pose);
            return true;
        }
        this._candidate_ele_pose_list = [];
        let bbox = this._layout_f_shape._contours.computeBBox();

        for(let candidate_pose of candidate_ele_pose_list)
        {
            rect._nor.copy(candidate_pose.nor);
            rect.rect_center = candidate_pose.rect_center;

            if(!bbox.containsPoint(candidate_pose.rect_center))
            {
                continue;
            }
            let res_list = TCollisionJudge.checkCollision_rect_edges(rect, this._layout_f_shape._forbidden_area_edges);
            if(res_list.length == 0)
            {
                candidate_pose.score = 0.;
            }
            else{
                let max_dval = 0;
                for(let rdata of res_list)
                {
                    if(Math.abs(rdata.dval) > max_dval) max_dval = Math.abs(rdata.dval);
                }
                candidate_pose.score = -max_dval;                
            }
            if(candidate_pose.score >= -10)
            {
                add_candidate_pose(candidate_pose);
            }
        }

        this._candidate_ele_pose_list.sort((a,b)=>b.score-a.score);

        for(let pose of this._candidate_ele_pose_list)
        {
            pose._score_0 = pose.score;
        }
        

        return true;
    }
    sortCandidatesOfOtherRootElement()
    {
        let forbidden_edges : ZEdge[] = [];

        for(let ele of this._graph._curr_figure_list.figure_elements)
        {
            // console.log(ele.category,ele.priority,this.target_element.priority);

            if(ele.priority < this.target_element.priority && ele !== this.target_element)
            {
                let rect = new ZRect(ele.rect._w, ele.rect._h + HallwayWidth * 2.);
                rect._back_center = ele.rect._back_center.clone();
                rect._nor.copy(ele.rect._nor);
                rect.updateRect();
                rect.computeZNor();
                for(let edge of rect.edges)
                {
                    forbidden_edges.push(edge);
                }

                for(let edge of ele.rect.edges)
                {
                    forbidden_edges.push(edge);
                }
            }
        }
        for(let edge of this._layout_f_shape._contours.edges)
        {
            forbidden_edges.push(edge);
        }

        for(let pose of this._candidate_ele_pose_list)
        {
            pose.score = pose._score_0 || 0;
        }

        let candidate_ele_pose_list = this._candidate_ele_pose_list;
        let rect = this.target_element.rect;

  
        for(let candidate_pose of candidate_ele_pose_list)
        {
  
            rect._nor.copy(candidate_pose.nor);
            rect.rect_center = candidate_pose.rect_center;
            
            
            let res_list = TCollisionJudge.checkCollision_rect_edges(rect, forbidden_edges);
            if(res_list.length > 0)
             {
                let max_dval = 0;
                for(let rdata of res_list)
                {
                    if(Math.abs(rdata.dval) > max_dval) max_dval = Math.abs(rdata.dval);
                }
                candidate_pose.score = Math.min(-max_dval,candidate_pose.score);                
            }
        }

        let prefer_to_pos_list = this._prefer_pos_list || [];
        let pos_score = (pos : Vector3)=> {
            let points = [pos];

            let min_dist: number = 9999999;
            for(let p of points)
            {
                for(let kp of prefer_to_pos_list)
                {
                    let dist = p.distanceTo(kp);
                    if(dist < min_dist) min_dist = dist;
                }
            }
            return min_dist;
        }
        this._candidate_ele_pose_list.sort((a,b)=>{
            if(a.score > -10 && b.score > -10)
            {
                return pos_score(a.rect_center) - pos_score(b.rect_center);
            }
            else{
                return b.score - a.score;
            }

        });

        let count = 0;
        for(let pose of this._candidate_ele_pose_list)
        {
            if(pose.score >= -0.1)
            {
                count++;
            }
        }

    
        this._attempt_num = Math.max(count,1);
             
    }

    applyRootElement()  // 根节点, 一般是电视柜、隔断柜等
    {
        let candidate_data = this._candidate_data_list[this._attempt_id];

        
        let candidate_edge = candidate_data.candidate_edge;
        let rect = this.target_element.rect;
        rect._back_center = candidate_edge.unprojectEdge2d(candidate_data.offset);
        rect._nor.copy( candidate_edge.nor.clone().negate());
        rect.updateRect();

    }


    applyChildElement() // 一般是沙发，由电视柜驱动
    {
        let candidate_data = this._candidate_data_list[this._attempt_id];
        if(this._rect_center_before_applied)
        {
            this.target_element.rect.rect_center = this._rect_center_before_applied;
        }
        if(candidate_data && candidate_data.score > -100)
        {
            let offset = candidate_data.offset;
            this.target_element.rect._back_center.add(this.target_element.rect.unproject({x:offset.x,y:offset.y+this.target_element.rect._h/2}).sub(this.target_element.rect._back_center));
            this.target_element.rect.updateRect();


            
        }
        

    }

    applyOtherRootElement() // 一般是餐桌
    {
        let candidate_pose = this._candidate_ele_pose_list[this._attempt_id];

        if(!candidate_pose) return;
        
        let rect = this.target_element.rect;
        rect._nor.copy( candidate_pose.nor.clone().negate());
        rect.rect_center = candidate_pose.rect_center;
        rect.updateRect();
    }
    checkScore(): void {

        let res = TCollisionJudge.checkCollision_rect_edges(this.target_element.rect,this._layout_f_shape._forbidden_area_edges);
        if(res.length > 0)
        {
            console.log(this.target_element,res);

        }

        for(let ele of this._graph._curr_figure_list.figure_elements)
        {
            if(ele.priority < this.target_element.priority)
            {
                let res =  TCollisionJudge.checkCollision_rect_edges(this.target_element.rect,ele.rect.edges);
                if(res.length > 0)
                {
                    console.log(this.target_element,ele);
                }
            }
        }
    }
    get is_root()
    {
        return (this._root_type >= 2);
    }


    apply(): void {
        if(!this._has_computed)
        {
            this.precompute();
        }
        if(this._root_type == 2)
        {
            this.applyRootElement();
        }
        else if(this._root_type == 1){

            this.applyOtherRootElement();
        }
        else {
            this.applyChildElement();
        }

    }

    
}