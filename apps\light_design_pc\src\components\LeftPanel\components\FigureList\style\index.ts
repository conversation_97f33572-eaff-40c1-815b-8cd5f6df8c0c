
import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  return {
    figure: css`
      display: flex;
      // justify-content: space-evenly;
      flex: 1;
      flex-wrap: wrap;
      max-height: calc(95vh - 200px);
      /* padding-top: 16px; */
      overflow-y: scroll;
      padding-left: 0px;
      transition: all .3s;

      &::-webkit-scrollbar {
        width: 0px;
      }

      .item {
        flex-grow: 1;
        min-width: 30%;
        max-width: 33%;
        margin: 4px;
        cursor: pointer;
        transition: box-shadow 0.3s ease;
        user-select:none;
        .image {
          height: 60px;
          /* background: rgba(242,242,242,1); */
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;
          overflow:hidden;
          transition: all .3s;
          .ant-image-img {
            width: 100%;
            height: 60px;
            vertical-align: middle;
            user-select:none;
            pointer-events: none; 
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .group_image.ant-image-img {
            width:60px;
            height: 60px;
            vertical-align: middle;
            user-select:none;
            pointer-events: none; 
            display: flex;
            margin-left:0%;

          }

        }

        .title {
          color: #000000;
          font-size: 12px;
          padding: 10px 0;
          height: 40px;
          line-height: 20px;
          text-align: center;
        }
        &:hover {
          /* box-shadow: 3px 3px 8px 0px rgba(0, 0, 0, 0.12); */
          .image {
            /* background-color: #DDDFE4; */
            
          }
        }
      }
    `
  };
});
