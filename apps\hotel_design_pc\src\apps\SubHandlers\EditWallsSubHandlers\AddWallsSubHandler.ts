import { AI2BaseModeHandler } from "@/apps/AI2BaseModeHandler";
import { EventName, LayoutAI_App, LayoutAI_Commands, LayoutAI_CursorState, TBaseEntity, TWall, T_AddWallOperationInfo } from "@layoutai/layout_scheme";
import { Vector3, Vector3Like } from "three";
import { I_MouseEvent, ZEdge, ZRect } from "@layoutai/z_polygon";
import { ZInputDimension } from "@layoutai/layout_scheme";
import { HouseDesignSubHandler } from "./HouseDesignBaseHandler";

// 绘制墙体逻辑：
// 1. 点击左键，开始绘制墙体记录一个起点
// 2. 再次点击左键，点击的点为终点，生成墙体，同时记录下一个墙体的起点
// 3. 点击右键，结束结束当前绘制的墙体，需要重新点击记录起点
// 4. 双击右键，退出绘制墙体模式
// 5.长按左键、中键，移动画布
export class AddWallsSubHandler extends HouseDesignSubHandler {

    _wall_end_points: Vector3[];

    /**
     *  前次绘制的矩形
     */
    _previous_rect: ZRect;
    drawing_wall_rects: ZRect[];
    _candidate_point: Vector3 = null;
    _candidate_point_type: string = ""; // 顶点、垂点、端点
    closeNum: number;
    align_pos0: Vector3;
    align_pos1: Vector3;

    drawEdage: ZEdge;
    drawPoint: Vector3;
    centerpoint: Vector3;
    private isMouseDown: boolean = false;
    private mouseDownTime: number = 0;
    private mouseButton: number = -1;  // 记录按下的是哪个按键
    private _MIN_TIME_TO_MOVE_CANVAS: number = 300;
    
    // 添加输入框相关变量
    private _inputDim: ZInputDimension | null = null;
    private _currentLength: number = 0;
    private _isDirectionX: boolean = true; // 是否是水平方向绘制墙体
    private _dim_offset: number = 300;

    constructor(cad_mode_handler: AI2BaseModeHandler) {
        super(cad_mode_handler);
        this._wall_end_points = [];
        this.drawing_wall_rects = [];

        this._candidate_point = new Vector3(0, 0, 0);
        this.closeNum = 0;
        this.align_pos0 = null;
        this.align_pos1 = null;
        this.drawEdage = null;
        this.drawPoint = null;
    }

    get wall_rects(): ZRect[] {
        return this.container.getCandidateRects(["Wall"]);
    }
    enter(state?: number): void {
        super.enter(state);

        LayoutAI_App.emit(EventName.ShowWallTopMenu, true);
        LayoutAI_App.emit_M(EventName.ShowFigureMenuList, false);
        LayoutAI_App.emit_M(EventName.SelectingTarget, null);
        LayoutAI_App.emit(EventName.ShowSubHandlerBtn, true);
        this.cleanSelection();
        this.manager.layout_container.cleanDimension();
        this.manager.setCanvasCursorState(LayoutAI_CursorState.Drawing);

        this.update();
    }
    leave(state?: number): void {
        
        // 清理输入框
        if (this._inputDim) {
            this._inputDim.dispose();
            this._inputDim = null;
        }

        super.leave(state);
        this.updateCandidateRects();
        LayoutAI_App.emit(EventName.ShowWallTopMenu, false);
        LayoutAI_App.emit_M(EventName.ShowFigureMenuList, false);
        LayoutAI_App.emit(EventName.ShowSubHandlerBtn, false);

        this.manager.setCanvasCursorState(LayoutAI_CursorState.Default);
        this.drawing_wall_rects = [];
        this._wall_end_points = [];
        this.closeNum = 0;
    }


    // 更新候选点的方向
    private updateCandidatePointDirection() {
        if (this._wall_end_points.length === 0 || !this._candidate_point) return;
        
        const lastPoint = this._wall_end_points[this._wall_end_points.length - 1];
        if (!lastPoint) return; // 额外的安全检查
        
        const t_v = this._candidate_point.clone().sub(lastPoint);
        
        // 根据当前方向调整候选点
        if (this._isDirectionX) {
            // 水平方向 - 只保留 x 变化
            t_v.y = 0;
        } else {
            // 垂直方向 - 只保留 y 变化
            t_v.x = 0;
        }
        
        // 保持当前长度
        const length = this._currentLength > 0 ? this._currentLength : t_v.length();
        t_v.normalize().multiplyScalar(length);
        
        this._candidate_point.copy(lastPoint.clone().add(t_v));
    }

    // 设置输入尺寸控件
    private _setupInputDimension() {
        if (this._wall_end_points.length === 0 || !this._candidate_point) return;
        
        const lastPoint = this._wall_end_points[this._wall_end_points.length - 1];
        
        if (!this._inputDim) {
            // 统一使用固定偏移距离
            const offset = this._dim_offset;
            
            // 获取墙体的方向向量
            let wallVec = this._candidate_point.clone().sub(lastPoint).normalize();
            
            // 计算左侧偏移向量（逆时针旋转90度）
            // 二维平面上，向量(x,y)逆时针旋转90度变为(-y,x)
            let leftOffset = new Vector3(-wallVec.y, wallVec.x, 0).multiplyScalar(offset);
            
            // 创建偏移后的起点和终点
            const startPoint = lastPoint.clone().add(leftOffset);
            const endPoint = this._candidate_point.clone().add(leftOffset);
            
            this._inputDim = new ZInputDimension(startPoint, endPoint);
            
            // 设置回调
            this._inputDim.setOnEnterPressed((value: number) => this.onEnterPressed(value));
        }
    }
    
    // 输入框回车事件处理
    private onEnterPressed = (value: number) => {
        if (!this._wall_end_points || this._wall_end_points.length === 0 || !this._candidate_point) return;
        
        // 输入值需要大于一个最小值
        if (value < 60 || value > 100000) {
            // message.error(String(LayoutAI_App.t('墙体长度不能小于60')));
            return;
        }
        
        // 更新当前长度
        this._currentLength = value;
        
        // 保存当前的候选点，以防后续处理中丢失
        const candidatePointClone = this._candidate_point.clone();
        
        // 更新候选点位置
        this.updateCandidatePointDirection();
        
        // 确保候选点仍然有效
        if (!this._candidate_point) {
            this._candidate_point = candidatePointClone;
        }

        // 创建墙体
        this.updateDrawingRects();
        
        // 只有当绘制矩形不为空时才添加墙体
        if (this.drawing_wall_rects && this.drawing_wall_rects.length > 0) {
            // 保存候选点为下一个墙体的起点
            
            // 添加墙体
            this._addCurrentWall();
            
            // 重置绘制状态并设置下一个墙体的起点
            this._wall_end_points = [];
            this.drawing_wall_rects = [];
            
            // 清理输入框
            if (this._inputDim) {
                this._inputDim.hideInput();
                this._inputDim.dispose();
                this._inputDim = null;
            }
        }
        
        // 创建墙体
        this.updateDrawingRects();
        this._cad_mode_handler._is_moving_element = false;
        this.update();
    }
    
    // 更新输入尺寸的位置
    private updateDimensionPosition() {
        if (!this._inputDim || this._wall_end_points.length === 0 || !this._candidate_point) return;
        
        const lastPoint = this._wall_end_points[this._wall_end_points.length - 1];
        
        // 统一使用固定偏移距离
        const offset = this._dim_offset;
        
        // 获取墙体的方向向量
        let wallVec = this._candidate_point.clone().sub(lastPoint).normalize();
        
        // 计算左侧偏移向量（逆时针旋转90度）
        // 二维平面上，向量(x,y)逆时针旋转90度变为(-y,x)
        let leftOffset = new Vector3(-wallVec.y, wallVec.x, 0).multiplyScalar(offset);
        
        // 更新偏移后的起点和终点
        const startPoint = lastPoint.clone().add(leftOffset);
        const endPoint = this._candidate_point.clone().add(leftOffset);
        
        this._inputDim.setPoints(startPoint, endPoint);
    }

    adsorbent(pos: Vector3Like) {
        let t_pos: Vector3 = new Vector3().copy(pos);
        this.drawEdage = null;
        this.drawPoint = null;
        this.centerpoint = null;
        let dist = 50;
        for (let rect of this.wall_rects) {
            for (let edge of rect.edges) {
                // 鼠标起始点移动时候的吸附
                let pp = edge.projectEdge2d(t_pos);
                //   if(pp.x < 0 || pp.x > edge.length) continue;
                if (Math.abs(pp.y) < Math.abs(dist)) {
                    dist = (pp.y);
                    this.drawEdage = edge;
                    let pos = edge.unprojectEdge2d({ x: pp.x, y: 0 });
                    this.drawPoint = pos;
                }
                //   if(edge.length < rect._h + 1.) continue;
                if (Math.abs(t_pos.distanceTo(edge.center)) < 60) {
                    this.drawPoint = edge.center.clone();
                    this.centerpoint = edge.center.clone();
                }
            }
        }
    }

    alignment(pos: Vector3Like) {
        if (this.drawing_wall_rects.length == 0) return;
        let align_pos = null;
        let dv = null
        this.align_pos0 = null;
        this.align_pos1 = null;
        dv = this.drawing_wall_rects[this.drawing_wall_rects.length - 1].dv;
        let dist = 50;
        for (let rect of this.wall_rects) {
            // 墙的宽度大于某个值才去对齐
            if (rect._w < 100) continue;
            for (let edge of rect.edges) {
                if (Math.abs(dv.dot(edge.nor)) < 0.1) continue;
                let pp = edge.projectEdge2d(this._candidate_point);
                if (Math.abs(pp.y) < Math.abs(dist)) {
                    dist = (pp.y);
                    align_pos = edge.unprojectEdge2d({ x: pp.x, y: 0 });
                    this._candidate_point.copy(align_pos);
                    this.align_pos0 = align_pos.clone();
                    this.align_pos1 = edge.start_pos.clone();
                }
            }
        }
    }

    udpateCandidatePoint(pos: Vector3Like): void {
        let t_pos: Vector3 = new Vector3().copy(pos);
        if (this._wall_end_points.length > 0) {
            let t_v = t_pos.clone().sub(this._wall_end_points[this._wall_end_points.length - 1]);
            // 支持斜墙用这个
            // if(Math.abs(t_v.x) < 150)
            // {
            //     t_v.x = 0;
            // }
            // if(Math.abs(t_v.y) < 150)
            // {
            //     t_v.y = 0;
            // }
            if (Math.abs(t_v.x) > Math.abs(t_v.y)) {
                t_v.y = 0;
                this._isDirectionX = true;
            }
            else {
                t_v.x = 0;
                this._isDirectionX = false;
            }
            t_pos = this._wall_end_points[this._wall_end_points.length - 1].clone().add(t_v);
            
            // 更新当前长度
            this._currentLength = t_v.length();
        }

        if (this.drawPoint) {
            this._candidate_point.copy(this.drawPoint);
        } else {
            this._candidate_point.copy(t_pos);
        }
    }

    onmousedown(ev: I_MouseEvent): void {
        this.isMouseDown = true;
        this.mouseButton = ev.button;  // 记录按下的按键

        if (ev.button == 0 || ev.button == 1) {
            this.closeNum = 0;
            this.drawPoint = null;
            this.mouseDownTime = Date.now();
        }
        this.drawPoint = null;
    }

    onmousemove(ev: I_MouseEvent): void {
        let pos = { x: ev.posX, y: ev.posY, z: 0 };

        // 使用记录的按键值来判断
        if (this.isMouseDown && (this.mouseButton == 0 || this.mouseButton == 1)) {
            this._cad_mode_handler._is_moving_element = false;
            this.update();
            return;
        }

        this._cad_mode_handler._is_moving_element = true;
        this.udpateCandidatePoint(pos);

        // 添加墙的时候的对齐功能
        if (this._candidate_point && this._wall_end_points.length > 0) {
            this.alignment(pos);
        } else {
            // 初始点吸附功能
            this.adsorbent(pos);
        }

        this.updateDrawingRects();

        this.update();
    }

    onmouseup(ev: I_MouseEvent): void {
        if (!this.isMouseDown) return;

        // 检查是否是长按移动画布的情况
        let isLongPress = Date.now() - this.mouseDownTime > this._MIN_TIME_TO_MOVE_CANVAS;

        this.isMouseDown = false;
        this.mouseDownTime = 0;
        this.mouseButton = -1;  // 重置按键记录

        this._cad_mode_handler._is_moving_element = false;
        this.align_pos0 = null;
        this.align_pos1 = null;
        // 右键
        if (ev.button == 2) {
            this.closeNum++;
            this._wall_end_points = [];
            this.drawing_wall_rects = [];
            if (this._inputDim) {
                this._inputDim.hideInput();
            }
            if (this.closeNum >= 2) {
                this.manager._runCommand(LayoutAI_Commands.LeaveSubHandler);
            }

            this.update();
            return;
        }

        // 只在非长按的情况下记录点位
        if (ev.button == 0 && this._candidate_point && !isLongPress) {
            if (this._wall_end_points.length == 0) {
                // 如果是第一个点，添加为起点
                this._wall_end_points.push(this._candidate_point.clone());
            } else {
                // 更新绘制矩形
                this.updateDrawingRects();
                
                // 添加墙体
                if (this.drawing_wall_rects.length > 0) {
                    // 保存候选点为下一个墙体的起点
                    const nextStartPoint = this._candidate_point.clone();
                    
                    // 添加墙体
                    this._addCurrentWall();
                    
                    // 重置绘制状态
                    this._wall_end_points = [];
                    this.drawing_wall_rects = [];
                    
                    // 将结束点作为下一个墙体的起点
                    this._wall_end_points.push(nextStartPoint);
                    
                    // 清理输入框
                    if (this._inputDim) {
                        this._inputDim.hideInput();
                        this._inputDim.dispose();
                        this._inputDim = null;
                    }
                }
            }
        }
        
        this.update();
    }

    // 拆分出墙体的添加逻辑，供输入框回车和点击处理复用
    private _addCurrentWall() {
        // 如果墙太小就不添加了
        let add_rects = this.drawing_wall_rects.filter((rect) => rect && rect._w > 60);
        if (add_rects.length == 0) return;
        
        let adding_figure_rect = add_rects[add_rects.length - 1];
        if (!adding_figure_rect) return; // 额外的安全检查
        
        let alternative_rect = add_rects[add_rects.length - 2];   //判断是否有交叉
        let vertical = false;   //是否垂直
        if (alternative_rect) {
            Math.abs(adding_figure_rect.nor.dot(alternative_rect.nor)) < 0.1 ? vertical = true : vertical = false;
        }

        let add_entity = TWall.getOrMakeEntityOfCadRect(adding_figure_rect) as TWall;
        if (!add_entity) return; // 额外的安全检查
        
        add_entity.initNeighborsWalls(this.container._wall_entities);
        if (add_entity._vertical_neighbor_walls.length > 0) {
            for (let neighbor_entity of add_entity._vertical_neighbor_walls) {
                if (!neighbor_entity) continue; // 安全检查
                
                let record_v_rect = neighbor_entity.getRecordRect();
                if (!record_v_rect) continue; // 安全检查

                let v_rect = add_entity.getRecordRect();
                if (!v_rect) continue; // 安全检查
                
                let pp = record_v_rect.project(adding_figure_rect.rect_center);
                let pos = record_v_rect.unproject({ x: pp.x + (pp.x > 0 ? 1 : -1) * (v_rect.h / 2), y: 0 });
                let fix_pos = record_v_rect.unproject({ x: (pp.x > 0 ? -1 : 1) * (record_v_rect.length / 2), y: 0 });
                let new_v_rect = ZRect.SideExtendToPoint(record_v_rect, pos, fix_pos);
                
                if (new_v_rect && neighbor_entity.rect) {
                    neighbor_entity.rect.copy(new_v_rect);
                    neighbor_entity.update();
                    neighbor_entity.updateInWallWindows();
                }
            }
        }

        let info = new T_AddWallOperationInfo(this.manager);
        let entity = TWall.getOrMakeEntityOfCadRect(adding_figure_rect);
        info._entity = entity as TWall;
        info.redo(this.manager);
        this.manager.appendOperationInfo(info);
        
        // 确保墙体创建后立即刷新
        this.update();
        
        // 清空当前绘制的矩形
        this.drawing_wall_rects = [];
    }

    updateDrawingRects() {
        this.drawing_wall_rects = [];

        if (!this._candidate_point || !this._wall_end_points || this._wall_end_points.length === 0) return;
        
        const t_points: Vector3[] = [...this._wall_end_points, this._candidate_point];
        if (t_points.length < 2) return;
        
        for (let i = 0; i < t_points.length - 1; i++) {
            const start_pos0 = t_points[i];
            const start_pos1 = t_points[i + 1];
            
            if (!start_pos0 || !start_pos1) continue;

            let center = null;
            const nor = (start_pos1.clone().sub(start_pos0)).normalize().cross({ x: 0, y: 0, z: 1 });
            
            if (this._candidate_point_type === '中心线') {
                center = start_pos0.clone().add(start_pos1).multiplyScalar(0.5);
            } else if (this._candidate_point_type === '外线') {
                center = start_pos0.clone().add(start_pos1).multiplyScalar(0.5).addScaledVector(nor, this._thickness / 2);
            } else {
                center = start_pos0.clone().add(start_pos1).multiplyScalar(0.5).addScaledVector(nor, -this._thickness / 2);
            }
            
            const ww = (start_pos1.clone().sub(start_pos0)).length();
            const hh = this._thickness;

            const rect = new ZRect(ww, hh);
            rect.nor = nor;
            rect.rect_center = center;
            TBaseEntity.set_polygon_type(rect, 'Wall');
            this.drawing_wall_rects.push(rect);
        }
    }

    drawCanvas(): void {
        // 检查绘制墙体相关逻辑
        if (this.drawing_wall_rects && this.drawing_wall_rects.length > 0) {
            const add_rects = this.drawing_wall_rects.filter(rect => rect && rect._w > 0);
            if (add_rects.length === 0) return;
            
            const draw_wall = add_rects[add_rects.length - 1];
            if (!draw_wall) return;
            
            this.painter.fillStyle = "#3D9EFF";
            this.painter.strokeStyle = "#2b2b2b";

            this.painter.fillPolygons([draw_wall]);
            this.painter.strokePolygons([draw_wall]);
            
            // 处理输入框相关
            if (this._wall_end_points && 
                this._wall_end_points.length > 0 && 
                this._candidate_point) {
                
                // 设置输入框
                this._setupInputDimension();
                
                if (this._inputDim) {
                    this.updateDimensionPosition();
                    
                    // 绘制输入维度
                    this._inputDim.drawDimension(this.painter);
                    
                    // 显示输入框
                    const currentLength = Math.round(this._currentLength);
                    this._inputDim.showInput(this.painter, currentLength, true);
                }
            } else if (this._inputDim) {
                this._inputDim.hideInput();
            }
        } else if (this._inputDim) {
            this._inputDim.hideInput();
        }
        
        // 绘制对齐线
        if (this.align_pos0 && this.align_pos1) {
            this.painter._context.lineWidth = 1;
            this.painter.drawLineSegment(this.align_pos0, this.align_pos1, '#f23dd1');
        }

        // 绘制吸附点和线
        if (this.drawEdage && this.drawPoint) {
            this.painter._context.lineWidth = 1;
            this.painter.drawLineSegment(this.drawEdage.center, this.drawPoint, '#f23dd1');
            const radius = 6 / this.painter._p_sc;
            this.painter.strokeStyle = "#f23dd1";
            this.painter.drawPointCircle(this.drawPoint, radius, 4);
        }

        // 绘制候选点和墙体端点
        if (this._candidate_point && 
            this._wall_end_points && 
            this._wall_end_points.length > 0 && 
            this._wall_end_points[this._wall_end_points.length - 1]) {
            
            this.painter._context.lineWidth = 1;
            const radius = 3 / this.painter._p_sc;
            this.painter.strokeStyle = "#f23dd1";
            this.painter.drawPointCircle(this._candidate_point, radius, 3);
            this.painter.drawPointCircle(this._wall_end_points[this._wall_end_points.length - 1], radius, 3);
        }

        // 绘制中点标记
        if (this.centerpoint) {
            this.painter.drawRectText(
                new Vector3(
                    this.centerpoint.x + (20 / this._p_sc), 
                    this.centerpoint.y - (20 / this._p_sc), 
                    0
                ), 
                30, 20, 1, "中点"
            );
        }
    }
} 