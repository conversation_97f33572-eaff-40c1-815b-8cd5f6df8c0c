import type { IState } from '../../interfaces/IState';
import { Design2DStates } from '../../const/FSMConst';
import { DrawRectController } from "../../../ctrls/2d/DrawRectController";
import { PreviewRect2D } from "../../../object/2d/PreviewRect2D";
import { appContext } from '../../../AppContext';
import { Idle2DController } from '../../../ctrls/2d/Idle2DController';
import { DesignControllerType } from '../../../ctrls/DesignControllerType';
import { DesignEntityType } from '../../../object/DesignEntityType';

/**
 * 2D初始化状态
 */
export class Init2DState implements IState {
  name: string;
  parent?: any;

  constructor(name: string = Design2DStates.INIT2D) {
    this.name = name;
  }

  onEnter(data?: any): void {
    const design2DContext = appContext.design2DContext;
    if (design2DContext) {
      design2DContext.controllerManager.createAndRegisterCtrl(DesignControllerType.IDLE2D_CTRL, Idle2DController);
      design2DContext.controllerManager.createAndRegisterCtrl(DesignControllerType.DRAW_RECT_CTRL, DrawRectController);
      design2DContext.object2DManager.registerEntityObject2D(DesignEntityType.previewRect2D, PreviewRect2D);
    }
    
    // 自动转换到下一个状态
    appContext.mainFSM.transitionTo(Design2DStates.IDLE2D);
  }

  onExit(data?: any): void {
    // 状态退出逻辑
  }
} 