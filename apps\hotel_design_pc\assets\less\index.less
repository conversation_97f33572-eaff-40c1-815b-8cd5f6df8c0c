body {
  font-family: PingFangSC-Regular, sans-serif;
  font-size: 12px;
  overflow-y: hidden;
  user-select: none;  // 禁止选中
  background-color: #eaeaeb;
}

&::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background: transparent;
}

&::-webkit-scrollbar-thumb {
  background: transparent;
}
&::-webkit-scrollbar-thumb:hover {
  border-radius: 4px;
  background: hsla(0, 0%, 53%, 0.5);
}

&::-webkit-scrollbar-track-piece {
  background: transparent;
}

&:hover::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background: hsla(0, 0%, 53%, 0.4);
}

&:hover::-webkit-scrollbar-track {
  background: hsla(0, 0%, 53%, 0);
}
.ant-popover .ant-popover-inner{
  padding: 0 !important;
  border-radius: 8px;
  background-color: #25282D !important;
}
.ant-popconfirm-inner-content {
  padding: 12px !important;
  border-radius: 8px;
  background-color: #FFFFFF !important;
}
.ant-color-picker .ant-popover-inner{
  padding: 10px !important;
  border-radius: 8px;
  background-color: #fff !important;
}
.ant-message-loading{
  width: 196px;
  height: 64px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #25282D;
  font-size: 16px;
}
.ant-modal-mask{
  background-color: rgba(0, 0, 0, 0.1) !important;
}
.edit_input
{
  width: auto;
  height: 18px;
  position: absolute;
  border: 1px solid #d2d3d4;
  border-radius: 2px;
  outline: none;
  // padding: 10px;
  background: #f8f8f8;
  font-size: 12px;
}
.expand_input
{
  width: auto;
  height: 26px;
  position: absolute;
  border: 1px solid #d2d3d4;
  border-radius: 4px;
  outline: none;
  // padding: 10px;
  background: #f8f8f8;
  font-size: 14px;
}

[class^="edit_input"],
[class^="dimension_input"]
{
  width: auto;
  height: 18px;
  position: absolute;
  border: 1px solid #d2d3d4;
  border-radius: 2px;
  outline: none;
  // padding: 10px;
  background: #f8f8f8;
  font-size: 12px;
}
.swj-baseComponent-Containersbox-title-text
{
  font-weight: 600;
}
.swj-property-info-item-img-mask
{
  display: none !important;
}
.swj-top-menu-button-item
{
  width: auto !important;
}
.svg-nomyu7
{
  min-width: 102px;
  max-width: 150px;
  width: 100% !important;
}

// .ant-form-item-explain
// {
//   display: none !important;   //国际化先隐藏form表单的错误提示
// }

.ant-message {
  z-index: 999999 !important;
}


.shareModal
{
  .ant-modal-content
  {
      padding: 8px 16px 16px 16px !important;
  }
}
.fadeEnter
{
  opacity: 0;
}
.fadeEnterActive
{
  opacity: 1;
  transition: opacity 300ms ease-in-out;
}
.fadeExit
{
  opacity: 1;
}
.fadeExitActive
{
  opacity: 0;
  transition: opacity 300ms ease-in-out;
}

.custom-class
{
  .ant-message-notice-content
  {
    background: rgba(0, 0, 0, 0.40)!important;
    -webkit-backdrop-filter: blur(50px)!important;
    backdrop-filter: blur(50px)!important;
    color: #fff!important;
  }
}