import { createRoot } from 'react-dom/client';
import ReactDOM from 'react-dom';
import { injectI18n } from './i18n'; // 在这里导入 i18n.js
// import { StyleProvider } from '@ant-design/cssinjs'; // 兼容低版本浏览器
import './style';
injectI18n();

import App from './App';

const root = createRoot(document.getElementById('app') as any);
if (process.env.NODE_ENV === 'development') {
  ;(ReactDOM as any).injectDevTools(); // 用于解决esternals后热更新失效的问题
}

// root.render(
//   // `hashPriority` 默认为 `low`，配置为 `high` 后，
//   // 会移除 `:where` 选择器封装 https://lib-doc.3weijia.com/svg-antd/2.0.9/react/compatible-style
//   <StyleProvider hashPriority="high">
//     <App />
//   </StyleProvider>
// );
root.render(<App />);
