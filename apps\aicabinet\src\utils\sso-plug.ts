import { LogOutParams, SsoPlus, SSOUrlParams, svgSsoPlus } from '@svg/sso-plus';
import { ENV } from '@/config/env';
export class SSOPlug {
  private static readonly APPID = 'ai-cabinet';

  private static ssoPlug: SsoPlus;

  public static init(): void {
    this.ssoPlug = svgSsoPlus({
      appId: SSOPlug.APPID,
      env: ENV,
    });
  }

  public static getSsoUrl(p: SSOUrlParams = { type: 'pc_login' }): Promise<string> {
    return this.ssoPlug.getSsoUrl(p);
  }

  public static toLogOut(l: LogOutParams = { appId: SSOPlug.APPID }): Promise<any> {
    return this.ssoPlug.toLogOut(l);
  }
}