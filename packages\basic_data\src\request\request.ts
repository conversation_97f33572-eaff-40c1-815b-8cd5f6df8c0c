import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
// 这里是用于设定请求后端时，所用的 Token KEY
// 可以根据自己的需要修改，常见的如 Access-Token，Authorization
// 需要注意的是，请尽量保证使用中横线`-` 来作为分隔符，
// 避免被 nginx 等负载均衡器丢弃了自定义的请求头..

export const REQUEST_TOKEN_KEY = 'Access-Token';

export interface ResponseBody<T = any> {
  message: string;
  code: number;
  data?: T | T[];
}

/** 统一返回结构体 */

export interface PageResult<T = any> {
  data: T[];
  current?: number;
  pageSize?: number;
  total?: number;
  success: boolean;
}

export interface RequestResult<T = any> {
  data: T;
  success: boolean;
  errorMessage: string;
}

export interface ICodeMessage {
  [key: number]: any;
}

const codeMessage: ICodeMessage = {
  200: '服务器成功返回请求的数据。',
  201: '新建或修改数据成功。',
  202: '一个请求已经进入后台排队（异步任务）。',
  204: '删除数据成功。',
  400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
  401: '用户没有权限（令牌、用户名、密码错误）。',
  403: '用户得到授权，但是访问是被禁止的。',
  404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
  405: '请求方法不被允许。',
  406: '请求的格式不可得。',
  410: '请求的资源被永久删除，且不会再得到的。',
  422: '当创建一个对象时，发生一个验证错误。',
  500: '服务器发生错误，请检查服务器。',
  502: '网关错误。',
  503: '服务不可用，服务器暂时过载或维护。',
  504: '网关超时。',
};

/**
 * @description 函数
 */
class proRequest {
  axiosInstance: any
  constructor() {
    this.axiosInstance = {};
  }

  toInit() {
    this.axiosInstance = axios.create({
      // API 请求的默认前缀
      baseURL:"",
      timeout: 10000, // 请求超时时间
    });
    this.toSetInterceptors();
  }

  toSetInterceptors() {
    this.axiosInstance.interceptors.request.use(this.requestHandler, this.errorHandler);
    this.axiosInstance.interceptors.response.use(this.responseHandler, this.errorHandler);
  }

  errorHandler(error: AxiosError) {
    if (error.response) {
      const { data = {}, status, statusText, config }: any = error.response;
      // 403 无权限
      if (data.error_description) {
        console.warn({ error_description: data.error_description })
      } else {
        const errorText = codeMessage[status] || statusText;
        const { url } = config || {};
        console.warn({ status, url, errorText })
      }
    }
    return Promise.reject(error);
  }

  requestHandler(
    config: AxiosRequestConfig,
  ) {
    let headersConfig = {};
    const { headers: initHeaders } = config;
    config.headers = { ...headersConfig, ...initHeaders };
    return config;
  }

  responseHandler(response: AxiosResponse) {
    //  console.log(response)
    // 转换数据，处理那些200请求，但是异常的response
    const { data } = response;
    const { success, code, msg, errorCode } = data;
    let trueResponse = data;
    // 处理请求错误
    if (success !== undefined && !success) {
      trueResponse = {
        code,
        errorMessage: msg,
        data: data?.data,
        success: false,
      };
    }

    return trueResponse;
  }
}

/**
 * @description 函数
 */

export const createProRequest: any = () => {
  const $request = new proRequest();
  $request.toInit();
  return $request.axiosInstance;
};

export { AxiosResponse };

export default createProRequest;
