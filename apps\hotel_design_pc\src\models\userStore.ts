import { makeAutoObservable } from 'mobx';

interface IUserData {
  userId?: string;
  nickname?: string;
  mobile?: string;
  username?: string;
  tenantId?: string;
  regSource?: string;
  isFactory?: number;
  operations?: any[]
}

/**
 * @description 用户信息数据
 */
class UserStore {
  userInfo: IUserData = {};
  aihouse: boolean = false;
  hasAuth?: boolean = true;
  checkUse?: number = 0;
  totalUse?: number = 0;
  beta?: boolean = false;
  overLimit?: boolean = false;
  constructor() {
    makeAutoObservable(this, {}, { autoBind: true }); // mobx6.0之后必须要加上这一句
  }

  setUserInfo = (data: IUserData) => {
    this.userInfo = data;
  };

  setAihouse = (aihouse: boolean) => {
    this.aihouse = aihouse;
  }
  setHasAuth = (hasAuth: boolean) => {
    this.hasAuth = hasAuth;
  }
  setCheckUse = (checkUse: number) => {
    this.checkUse = checkUse;
  }
  setTotalUse = (totalUse: number) => {
    this.totalUse = totalUse;
  }

  setBeta = (beta: boolean) => {
    this.beta = beta;
  }
  setOverlimit = (overLimit: boolean) => {
    this.overLimit = overLimit;
  }

  getCheckCurrent = async () => {
    // const res = await getUseDetail({
    //   module: 'enter3d'
    // })
    let res = null;
    if (res?.success && res?.result) {
      this.setTotalUse(res.result?.total);
      this.setCheckUse(res.result?.used);
      this.setOverlimit(res.result?.overLimit)
    }
  }

  getIsBusiness = () => {
    return this.userInfo.isFactory === 1;
  }
}

export default UserStore;
