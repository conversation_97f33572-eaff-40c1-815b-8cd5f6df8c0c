import React, { useEffect, useState } from 'react';
import { PropertyPanel } from '@svg/antd-cloud-design';
import useStyles from './style';
import { LayoutAI_App, LayoutAI_Events,EventName,AI2DesignBasicModes} from '@layoutai/layout_scheme';
import RowButton from './widget/RowButton/rowButton';
import RotateWidget from './widget/RotateWidget';
import ColorWidget from './widget/ColorWidget';
import { observer } from "mobx-react-lite";
import { useStore } from '@/models';
import SubAreaWidget from "./widget/SubAreaWidget/subAreaWidget";
import { IPropertySchema } from '@svg/antd-cloud-design/lib/PropertyPanel';
import RightPropertyPanel from '../RightPropertyPanel';

const MyComponent: React.FC = () => {
  const store = useStore();
  const t = LayoutAI_App.t;
  const { styles } = useStyles();
  const [update,setUpdate]  = useState(0);
  const [key,setKey] = useState(0);
  const [locked, setLocked] = useState<number>(-1);
  const [title, setTile] = useState<string>('');
  const [schema, setSchema] = useState<IPropertySchema>({});
  const [panelType, setPanelType] = useState<number>(0);
  const [visible, setVisible] = useState<boolean>(false);

  let update_timestamp = 0;
  // 处理属性面板
  const attribuiteHandle = (params: IPropertySchema) => {
    const panel_type = params.panel_type||0;
    const mode = params.mode;
    if (params.properties?.locked != null) {
      setLocked(params.properties.locked.defaultValue ? 1 : 0);
    } else {
      setLocked(-1);
    }

    if (mode === 'init') {
      let newSchema = {
        type: 'object',
        ...params
      };
      let visible = false;
      params.title === '空间信息' ? visible = false : visible = true;  //先暂时这样处理空间面板  [i18n:ignore]
      
      if (params?.properties?.goods?.value?.type === 'Furniture') {
        const {angle, pitch, fov,option_length, goods, height,length,line, material_name, pos_z, label, ui_realType, ui_type, width,corner_width,corner_depth, area} = params.properties;
        let properties = {
          ...(goods && {goods}),
          ...(ui_type && {ui_type}),
          ...(ui_realType && {ui_realType}),
          ...(label && {label}),
          ...(material_name && {material_name}),
          ...(option_length && { option_length }),
          line: {...line, props: {fullScreen: true, space: 0}},
          adjustment: {
            name: t('调整'),
            defaultValue: true,
            widget: 'CollapseItem',
            properties: {
              size: {
                name: t('尺寸'),
                defaultValue: true,
                expanded: true,
                widget: 'CollapseItem',
                properties: {
                  length,
                  width,
                  height,
                }
              },
              ...(pos_z && {
                position: {
                  name: t('位置'),
                  defaultValue: true,
                  expanded: true,
                  widget: 'CollapseItem',
                  properties: {
                    pos_z
                  }
                }
              }),
              angle: {
                name: t('旋转'),
                type: 'string',
                props: {
                  angle: {...angle}
                },
                widget: 'RotateWidget'
              },
            },
          },
          ...(area && {area}),
        };

        if (params?.properties?.material_name?.defaultValue.indexOf("L型") > -1 && corner_width && corner_depth)  //[i18n:ignore]
        {
          (properties.adjustment.properties.size.properties as any)["corner_width"] = corner_width;
          (properties.adjustment.properties.size.properties as any)["corner_depth"] = corner_depth;
        }

        if (params?.properties?.material_name?.defaultValue === '相机' || params?.properties?.material_name?.defaultValue === '人物')  //[i18n:ignore]
        {
          (properties.adjustment.properties as any)["pos_z"] = pos_z;
          (properties.adjustment.properties as any)["pitch"] = pitch;
          (properties.adjustment.properties as any)["fov"] = fov;

          delete properties.adjustment.properties["size"];
          delete properties.adjustment.properties["position"];
          delete properties["ui_realType"];
          delete properties["ui_type"];
          delete properties["material_name"];
          properties.goods.value.size = null;
        }

        if(goods)
        {
          goods.value.size = `${Math.round(length.defaultValue)} * ${Math.round(width.defaultValue)} * ${Math.round(height.defaultValue)}`;
        }

        if(store.userStore.userInfo?.regSource === 'aihouse')
        {
          delete (properties as any)["public_category"];
          
        }
        let newParams = JSON.parse(JSON.stringify(params));
        setVisible(true);
        const newSchema = {...newParams, properties, type: 'object'};
        setSchema(newSchema);
        setPanelType(0);
        // console.log("attribute.set",newSchema.properties.adjustment.properties.size.properties.height.defaultValue);
      } else if(params?.properties?.goods?.value?.type === 'BaseGroup')
      {
        const {angle, pitch, fov,option_length, goods, height,length,line, material_name, pos_z, label, ui_realType, ui_type, width,corner_width,corner_depth, area,exportTemplate} = params.properties;
        let properties = {
          ...(goods && {goods}),
          ...(length && {length}),
          ...(width && {width}),
          ...(height && {height}),
          ...(pos_z && {pos_z}),
          ...(exportTemplate && {exportTemplate})
        }
        if(goods)
        {
          goods.value.size = `${Math.round(length.defaultValue)} * ${Math.round(width.defaultValue)} * ${Math.round(height.defaultValue)}`;
        }
        let newParams = JSON.parse(JSON.stringify(params));
        setVisible(true);
        const newSchema = {...newParams, properties, type: 'object'};
        setSchema(newSchema);
      }
      else {
        setVisible(true);
        setSchema(newSchema);
        setPanelType(0);
      };
      if(store.homeStore.designMode === AI2DesignBasicModes.HouseDesignMode && params.title === '空间信息')  //[i18n:ignore]
      {
        let newSchemaParams = params;
        setVisible(true);
        setSchema(newSchemaParams);
        setPanelType(0);
      }
      setUpdateData();
    } 
    else if (params.mode === 'hide') 
    {
      setVisible(false);
      setSchema({});
      setPanelType(panel_type);
    } 
    else if (params.mode === "edit") 
    {
      setSchema(params);
    }
    else if(params.mode === "force")
    {
      store.homeStore.setKey(new Date().getTime())
    }
    
  };

  const setUpdateData = ()=>{
    setUpdate(update_timestamp++);
  }

  const onClickLock = () => {
    if (locked == 1) {
      setLocked(0);
      LayoutAI_App.DispatchEvent(LayoutAI_Events.OnExtraAttributeChange, "unlock");
    } else {
      setLocked(1);
      LayoutAI_App.DispatchEvent(LayoutAI_Events.OnExtraAttributeChange, "lock");
    }
  }

  useEffect(() => {
    if(store.homeStore.designMode === AI2DesignBasicModes.HouseDesignMode 
      && schema && schema.title === '空间信息')  //[i18n:ignore]
    {
      let newSchemaParams = schema;
      delete newSchemaParams?.properties["clearLayout"];
      delete newSchemaParams?.properties["ai_layout"];
      setSchema(newSchemaParams);
      store.homeStore.setKey(new Date().getTime())
    }
  },[store.homeStore.designMode])

  useEffect(()=>{
    if(LayoutAI_App.instance)
    {
        LayoutAI_App.on(EventName.AttributeUpdate,setUpdateData);
        LayoutAI_App.on(EventName.AttributeHandle, (params) => {
          attribuiteHandle(params);
        });
    }
    update_timestamp = 0;
  },[]);


  const ShowPanel = ()=>{
    if(visible)
    {
      return <PropertyPanel 
        key={store.homeStore.key}
        widgets={{ RowButton, RotateWidget,ColorWidget,SubAreaWidget}} 
        title={schema.title} 
        // draggable={true} 
        schema={schema}
        top={48}
        right={0}
        />
    }
    else{
      if(panelType == 1)
      {
        return <></>
        // return <EzdxfRightPanel></EzdxfRightPanel>
      }
      else{
        // 非AI拆改模式下，显示右侧属性面板
        if(store.homeStore.designMode === AI2DesignBasicModes.RemodelingMode || store.homeStore.designMode === AI2DesignBasicModes.HouseCorrectionMode)
        {
          return <></>
          // return (
          //   <OriginalHousePlan/>
          // );
        }
        else{
          return <RightPropertyPanel right={true} left={false} selectedFigureElement={store.homeStore?.selectEntity?.figureElement}></RightPropertyPanel>
        }
      }
    }
  
  }
  return (
      <div className={styles.root} >
        {ShowPanel()}
      </div>
  );
};


export default observer(MyComponent);
