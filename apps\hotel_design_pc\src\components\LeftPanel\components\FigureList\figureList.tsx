import React, { useEffect, useState } from 'react';
import { Image } from '@svg/antd';
import useStyles from './style';
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_Events } from "@layoutai/layout_scheme";
import { DragEventListener } from '@svg/antd-cloud-design'
import { message, Space } from '@svg/antd'
import { useStore } from '@/models';
import { observer } from "mobx-react-lite";
import { useTranslation } from 'react-i18next'
interface Module {
  image: string;
  png: string;
  title: string;
  label: string;
  group_code?:string;
  icon: string;
}

interface ImageGalleryProps {
  data: Module[];
}
const ImageGallery: React.FC<ImageGalleryProps> = ({ data }) => {
  const { t } = useTranslation()
  const { styles } = useStyles();
  const store = useStore();
  const [label, setLabel] = useState<string>('');
  const scrollContainerRef = React.useRef<HTMLDivElement>(null);


  useEffect(() => {
    const dragEventListener = new DragEventListener({
      // 展示缩略图
      isShowThumbnail: true,
      container: document.getElementById('side_pannel') as HTMLElement,
      // 打印
      log: false,
    })
    dragEventListener.bindDrag()
    return () => {
      dragEventListener.unbindDrag()
    }
  }, [])

  // useEffect(() => {
  //   if(label)
  //   {
  //     if (store.homeStore.ismoveCanvas) {
  //       // 离开canvas
  //       LayoutAI_App.DispatchEvent(LayoutAI_Events.MoveFurniture, '');
  //     } else {
  //       // 进入canvas
  //       LayoutAI_App.DispatchEvent(LayoutAI_Events.SelectedFurniture, label);
  //     }

  //   }
  // }, [store.homeStore.ismoveCanvas]);

  useEffect(() => {
    // 鼠标弹起的时候设置label为空
    const handleMouseUp = () => {
      if(label)
      {
        LayoutAI_App.RunCommand(LayoutAI_Commands.LeaveSubHandler);
        setLabel('');
      }

    };
  
    window.addEventListener('mouseup', handleMouseUp);
  
    // 在组件卸载时移除事件监听器
    return () => {
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [label]);


  useEffect(() => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTop = 0;  // 将滚动位置设置为 0
    }
  }, [data]);

  return (
    <div className={styles.figure} ref={scrollContainerRef}>
      {data.map((item, index) => (
          <div key={index} className="item" 
            onPointerDown={(ev)=>{
              let label = item.title;
              if(item.group_code)
              {
                label = "GroupTemplate:"+item.group_code;
                // setLabel("GroupTemplate:"+item.group_code)
              }

              if(item.title.includes('单开门') || item.title.includes('推拉门') || item.title.includes('一字窗') ||  item.title.includes('飘窗')) /*[i18n:ignore]*/
              {
                LayoutAI_App.DispatchEvent(LayoutAI_Events.SelectedFurniture, label);
                return;
              } 
              else 
              {
                LayoutAI_App.DispatchEvent(LayoutAI_Events.SelectedFurniture, label);
              }
            }}

          >
            <div className="image">
              {/* <Image src={(getPrefix() + `static/figures_imgs/${item.png}`)} preview={false} alt={item.title}/> */}
                <svg className="icon" aria-hidden="true" style={{width: 60}}>
                  <use xlinkHref={`#${item.icon}`}></use>
              </svg>
            </div>
            <div className="title">{t(item.title)}</div>
          </div>
        ))}  
    </div>
  );
};

export default observer(ImageGallery);
