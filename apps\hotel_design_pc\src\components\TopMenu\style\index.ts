import { createStyles } from '@svg/antd/es/theme/utils';
export default createStyles(({ css }) => {
  // color: ${token.colorPrimary};
  return {
    topmenuInfo: css`
        #swj-top-menu-id-shareBtn
        {
            display: flex;
            flex-direction: row !important;
            // background: #147FFA;
            border-radius: 2px;
            width: 55px;
            padding-right: 5px;
           .swj-top-menu-button-item
           {
            // background-color: #147FFA;
           }
        }

    `,
    container: css`
        background-color: #fff; 
        width: 200px;
        height: 140px; 
        padding: 10px; 
        text-align: center; 
    `,
    top: css`
        display: flex;
        align-items: center;
        img{
            margin-right: 10px;
        }
    `,
    line:css`
        height: 1px;
        width: 100%;
        background-color: #e6e6e6;
        margin: 10px 0;
    `,
    item: css`
        position: relative;
        height: 32px;
        padding: 0 12px;
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-align-items: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        cursor: pointer;
        margin: 2px 0;  
        :hover{
            background-color: #f2f3f5;
        }
    `,
    shareContainer: css`
        position: fixed;
        top: 200px;
        left: 500px;
        z-index: 99999999;
        width: 500px;
    `
    
  }
});
