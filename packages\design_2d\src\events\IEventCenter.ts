/**
 * 事件中心接口
 * 定义事件通信的抽象契约
 */
export interface IEventCenter {
    /**
     * 发射事件
     * @param eventName 事件名称
     * @param data 事件数据
     */
    emit(eventName: string, data?: any): void;

    /**
     * 监听事件
     * @param eventName 事件名称
     * @param handler 事件处理函数
     */
    on(eventName: string, handler: (data?: any) => void): void;

    /**
     * 移除事件监听
     * @param eventName 事件名称
     * @param handler 事件处理函数（可选）
     */
    off(eventName: string, handler?: (data?: any) => void): void;

    /**
     * 一次性监听事件
     * @param eventName 事件名称
     * @param handler 事件处理函数
     */
    once(eventName: string, handler: (data?: any) => void): void;

    /**
     * 移除所有事件监听
     */
    removeAllListeners(): void;

    /**
     * 监听Canvas事件
     * @param eventType 事件类型
     * @param handler 事件处理函数
     */
    onCanvas<T extends Event>(eventType: string, handler: (event: T) => void): void;

    /**
     * 移除Canvas事件监听
     * @param eventType 事件类型
     * @param handler 事件处理函数
     */
    offCanvas<T extends Event>(eventType: string, handler: (event: T) => void): void;

    /**
     * 销毁事件中心
     */
    dispose(): void;
} 