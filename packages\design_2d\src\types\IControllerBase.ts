import { Design2DContext } from "../Design2DContext";

/**
 * 控制器基础接口
 * 所有控制器都必须实现此接口
 */
export interface IControllerBase {
    /** 控制器唯一标识 */
    readonly type: string;
    
    /** 控制器是否激活 */
    readonly isActive: boolean;

    /** 关联的上下文 */
    readonly context: Design2DContext;

    /**
     * 激活控制器
     */
    activate(): void;

    /**
     * 反激活控制器
     */
    deactivate(): void;

    /**
     * 鼠标按下事件
     * @param event 鼠标事件
     */
    onMouseDown(event: MouseEvent): void;

    /**
     * 鼠标移动事件
     * @param event 鼠标事件
     */
    onMouseMove(event: MouseEvent): void;

    /**
     * 鼠标抬起事件
     * @param event 鼠标事件
     */
    onMouseUp(event: MouseEvent): void;

    /**
     * 鼠标滚轮事件
     * @param event 滚轮事件
     */
    onWheel(event: WheelEvent): void;

    /**
     * 更新控制器
     */
    update(): void;

    /**
     * 渲染控制器
     */
    render(): void;

    /**
     * 销毁控制器
     */
    dispose(): void;
} 