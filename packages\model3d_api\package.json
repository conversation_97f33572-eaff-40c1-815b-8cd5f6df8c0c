{"name": "@layoutai/model3d_api", "version": "1.0.7", "type": "module", "description": "", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "scripts": {"build": "tsc", "watch": "tsc -watch", "buildProd": "node build_prod.cjs", "viteBuild": "vite build", "buildIndex": "cti entrypoint ./src -b -o index.ts", "update": "pnpm run buildIndex && pnpm run build", "release:oss": "pkg-release oss", "search_layoutai": "node search_main.cjs", "buildPublish": "pnpm publish --no-git-checks"}, "maintainers": [{"name": "wang<PERSON><PERSON>", "email": "<EMAIL>"}], "publishConfig": {"registry": "http://registry.cnpm.3weijia.com/"}, "devDependencies": {"@svg/pkg-release-cli": "^1.0.3", "@types/three": "^0.171.0", "create-ts-index": "^1.14.0", "rollup": "^4.41.1", "three": "^0.171.0", "tsx": "^4.19.2", "typescript": "^5.6.3", "vite": "^5.2.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-dts": "^4.5.4", "vite-plugin-externals": "^0.6.2", "vite-plugin-html": "^3.2.2", "vite-tsconfig-paths": "^5.1.4", "@layoutai/z_polygon": "workspace:^", "@layoutai/effects3d": "workspace:^"}, "dependencies": {"@svg/request": "^0.4.0", "axios": "0.27.2", "axios-cookiejar-support": "^5.0.5", "fflate": "^0.8.2", "buffer": "^6.0.3", "@layoutai/basic_data": "workspace:^"}, "keywords": [], "author": "sunvega", "license": "ISC"}