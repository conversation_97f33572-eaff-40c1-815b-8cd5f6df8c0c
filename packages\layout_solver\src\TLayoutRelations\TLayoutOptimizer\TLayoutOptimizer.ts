import { FigureZValRangeType, IRoomEntityType, TsAI_app } from "@layoutai/basic_data";
import { FigLayoutState, I_LayoutOptRule, TFeatureShape, TGroupTemplate, TRoom, WPolygon } from "@layoutai/layout_scheme";
import { ZEdge, ZRect, compareNames, range_substract } from "@layoutai/z_polygon";
import { Vector3 } from "three";
import { TLayoutOptmizerOnWallRules } from "./TLayoutOptimizerOnWallRules";
import { TLayoutOptimizerWithRules } from "./TLayoutOptmizerWithOptRules";
import { TPostProcessLayout } from "./TPostProcessLayout";

export interface IRuleReferTargets
{
    back_w_edge?:ZEdge;
    side_w_edge?:ZEdge;
    front_w_edge ?: ZEdge;
    back_group_rect ?: ZRect;
    main_rect ?: ZRect;
}

/**
 *  同条边上的长度优化时会使用的数据接口
 */
export interface I_OnLineFigureInterval{
    /**
     *  备选的长度值
     */
    length_values : number[], 

    margin_value ?: number;
    /**
     *  初始 或 期望的长度值
     */
    target_length : number;

    /**
     *  结果长度值
     */
    result_length : number;

    /**
     *  中点位置
     */
    center_x : number;

    /**
     *  目标重点位置值
     */
    target_center_x ?: number;

    /**
     *  误差权重
     */
    length_differ_weight : number;

    /**
     *  unvalid_intervals
     */
    interval_unvalid_ranges?:number[][];
    

    /**
     *  零值误差权重
     */
    zero_differ_weight ?: number;

    /**
     *  中心距离值误差权重
     */
    center_diff_weight ?: number;

    /**
     *  目标值id
     */
    t_id ?: number;

    /**
     *  当前值id
     */
    c_id ?: number;

    /**
     *  名称
     */
    name ?: string;

    /**
     *  放置时的u_dv朝向
     */
    u_dv_flag ?: number;


    is_at_start ?: boolean;

    is_at_end ?: boolean;
    
}
/**
 *  布局优化器
 *      --- 已经给出了布局后, 对布局进行优化
 *      --- 
 */
export class TLayoutOptimizer
{


    constructor()
    {
        
    }

    static get_group_templates_with_differnt_zvals(group_templates:TGroupTemplate[],room_name:string)
    {
        let high_group_templates : TGroupTemplate[] = [];
        let floor_group_templates : TGroupTemplate[] = [];
        let onwall_group_templates : TGroupTemplate[] = [];

        let post_process_group_templates : TGroupTemplate[] = [];

        
        for(let group_template of group_templates)
        {
            let config = group_template.getFigureGroupConfigs(room_name);
            if(!config) continue;

            if(config.group_length_levels)
            {
               if(group_template._target_rect.w < 1.)
               {
                   group_template._target_rect._w =  config.group_length_levels.min || config.group_length_levels.values[0] || 100;
                   group_template._target_rect.updateRect();
               } 
            }
            if(config.zval_range_type === undefined){
                config.zval_range_type = FigureZValRangeType.All;
            }

            if(config.layout_state === FigLayoutState.PostProcess)
            {
                post_process_group_templates.push(group_template);
                continue;
            }

            if(config.zval_range_type == FigureZValRangeType.All)
            {
                high_group_templates.push(group_template);
            }
            else if(config.zval_range_type === FigureZValRangeType.OnFloor)
            {
                floor_group_templates.push(group_template);
            }
            else if(config.zval_range_type === FigureZValRangeType.OnHalfWall)
            {
                onwall_group_templates.push(group_template);
            }
        }
        return {floor_group_templates:floor_group_templates, high_group_templates:high_group_templates,onwall_group_templates:onwall_group_templates,post_process_group_templates:post_process_group_templates};

    }
    static optimize_groups_in_shape(feature_shape:TFeatureShape,group_templates:TGroupTemplate[],room_name:string, methods:number= 3,consider_placeholder:boolean= true)
    {
        if(compareNames([room_name],["厨房"]))
        {
            return this.optimize_groups_in_kitchen(feature_shape,group_templates);
        }
        let main_group_templates:TGroupTemplate[] = [];

        let pre_process_group_templates : TGroupTemplate[] = [];
        let post_process_group_templates : TGroupTemplate[] = [];

        for(let group_template of group_templates)
        {
            let config = group_template.getFigureGroupConfigs(room_name);
            if(!config) continue;

            if(config.zval_range_type === undefined){
                config.zval_range_type = FigureZValRangeType.All;
            }

            if(config.layout_state === FigLayoutState.PostProcess)
            {
                post_process_group_templates.push(group_template);
                continue;
            }
            main_group_templates.push(group_template);

        }

        if(methods & 1)
        {
            TLayoutOptimizerWithRules.optimize_groups_with_opt_rules(feature_shape,
                main_group_templates,room_name,consider_placeholder);

        }
        if(methods & 2)
        {
            TLayoutOptmizerOnWallRules.optimize_groups_with_onwall_rules(feature_shape,main_group_templates,room_name);

        }
        if(methods & 2)
        {
            TPostProcessLayout.post_process_group_by_wall_occulsion(feature_shape,[...main_group_templates,...post_process_group_templates],room_name);

        }

        TPostProcessLayout.post_process_group_by_opt_rule(feature_shape,post_process_group_templates,main_group_templates,room_name);


    }

    static optimize_groups_in_kitchen(feature_shape:TFeatureShape,group_templates:TGroupTemplate[])
    {
        let room_name = feature_shape._room.roomname;
        let high_group_templates : TGroupTemplate[] = [];
        let floor_group_templates : TGroupTemplate[] = [];
        let onwall_group_templates : TGroupTemplate[] = [];

        let pre_process_group_templates : TGroupTemplate[] = [];
        let post_process_group_templates : TGroupTemplate[] = [];

        for(let group_template of group_templates)
        {
            let config = group_template.getFigureGroupConfigs(room_name);
            if(!config) continue;

            if(config.zval_range_type === undefined){
                config.zval_range_type = FigureZValRangeType.All;
            }

            if(config.layout_state === FigLayoutState.PostProcess)
            {
                post_process_group_templates.push(group_template);
                continue;
            }

            if(config.zval_range_type == FigureZValRangeType.All)
            {
                high_group_templates.push(group_template);
            }
            else if(config.zval_range_type === FigureZValRangeType.OnFloor)
            {
                floor_group_templates.push(group_template);
            }
            else if(config.zval_range_type === FigureZValRangeType.OnHalfWall)
            {
                onwall_group_templates.push(group_template);
            }
        }


            TLayoutOptimizerWithRules.optimize_groups_with_opt_rules(feature_shape,
                [...high_group_templates,...floor_group_templates],room_name);
            if(onwall_group_templates.length > 0)
            {
                TLayoutOptimizerWithRules.optimize_groups_with_opt_rules(feature_shape,[...high_group_templates,...onwall_group_templates],room_name);
            }
        

            TLayoutOptmizerOnWallRules.optimize_groups_with_onwall_rules(feature_shape,[...high_group_templates,...floor_group_templates],room_name);

            for(let group_template of high_group_templates)
            {
                group_template._target_rect.ex_prop['opt_place_order'] = '4';
            }
            if(onwall_group_templates.length > 0)
            {
                TLayoutOptmizerOnWallRules.optimize_groups_with_onwall_rules(feature_shape,[...high_group_templates,...onwall_group_templates],room_name,true);
            }
            for(let group_template of high_group_templates)
            {
                delete group_template._target_rect.ex_prop['opt_place_order'];
            }
        
        TPostProcessLayout.post_process_group_by_wall_occulsion(feature_shape,[...high_group_templates,...post_process_group_templates],room_name);

        TPostProcessLayout.post_process_group_by_opt_rule(feature_shape,post_process_group_templates,[...high_group_templates,...floor_group_templates],room_name);

        TPostProcessLayout.post_process_kitchen_orientaion(feature_shape._room as TRoom,group_templates);

        // 重新定向一下
    }

    /**
     * 预处理房间内墙边缘,从墙边缘中减去门窗等开口位置
     * @param feature_shape 特征形状,包含房间多边形等信息
     * @param wall_edges 需要处理的墙边缘数组
     * @param room_name 房间名称,用于判断不同房间类型的处理逻辑
     * @param win_types 需要处理的窗户类型数组,默认为["Door","Hallway"]
     * @param ignore_neigbor_room_names 忽略的相邻房间名称数组,默认为["阳台"]
     * @returns 处理后的墙边缘数组
     * 
     * 主要功能:
     * 1. 遍历房间多边形的每条边
     * 2. 对于门、过道等开口位置,在内侧创建一个矩形区域
     * 3. 根据开口类型和房间类型设置不同的延伸距离
     * 4. 使用矩形区域从wall_edges中减去相应的边缘
     * 5. 返回处理后的wall_edges
     */
    static pre_sub_room_edges_inwall(feature_shape:TFeatureShape, wall_edges:ZEdge[], room_name:string,
         win_types:IRoomEntityType[]= ["Door","Hallway"], ignore_neigbor_room_names:string[] = ["阳台"])
    {
        // 获取房间的墙体多边形
        let w_poly = feature_shape._w_poly;

        // if(compareNames([room_name],["阳台"],false)==1) return wall_edges;

        // 遍历房间多边形的每条边
        for(let edge of w_poly.edges)
        {
            // 获取边上的窗户/门等开口
            let win = WPolygon.getWindowOnEdge(edge);
            if(!win) continue;

            // 判断是否是需要处理的开口类型(门或过道)
            if(win_types.indexOf(win.type)>=0) 
            {
                // 设置开口向内延伸的距离,默认800
                let front_extend = 800;

                // 如果是窗户、推拉门、大开口(>1.5m)或厨房的开口,延伸距离设为30
                if(win.type === "Window" || win.realType==="SlidingDoor" || (win.length||0)>1500 || (compareNames(win.room_names||[],["厨房"])))
                {
                    front_extend = 30;
                }
                
                // 创建一个矩形区域,宽度为开口宽度,深度为延伸距离
                let rect = new ZRect(edge.length, Math.min(edge.length,front_extend));

                // 设置矩形的法向量为边的法向量的反方向(向内)
                rect.nor = edge.nor.clone().negate(); 

                // 设置矩形的后中心点为边的中心点
                rect._back_center = edge.center;

                // 更新矩形的各项参数
                rect.updateRect();

                // 设置侧边距离(留给柜子的最小深度),默认300
                let side_dist = 300;  
                
                // 如果是客餐厅或厨房,侧边距离设为5
                if(compareNames([room_name],["客餐厅","厨房"])==1)
                {
                    side_dist = 5;
                }

                // 如果相邻房间是阳台且开口宽度大于1.6m,侧边距离设为5
                if(win.room_names && compareNames(win.room_names,ignore_neigbor_room_names)==1 && win.length > 1600) {
                    side_dist = 5;
                }
    
                // 使用矩形区域从wall_edges中减去相应的边缘
                TLayoutOptimizer.sub_edges_by_rect(rect, wall_edges, side_dist);
            }
        }

        return wall_edges;

    }

    static fill_refer_targets_of_group(wall_edges:ZEdge[],group_template:TGroupTemplate,room_name:string,placeholder_rects:ZRect[])
    {
        let rect = group_template._target_rect;
        let config =  group_template.getFigureGroupConfigs(room_name);
        if(!config) { 
            TsAI_app.log("Not Found Config", group_template);
            return null;
        }
        let rules = config.opt_rules || null;
        if(!rules) return null;


        let backwall_edge : ZEdge = null;
        let min_dist : number = config.backwall_max_dist || 1000;
        for(let edge of wall_edges)
        {
            let pp = edge.projectEdge2d(rect.back_center);

            if(Math.abs(edge.nor.dot(rect.nor)) < 0.9) continue;  // 墙的外方向 跟  矩形的方向要 一致;
            if(pp.x < 0 || pp.x > edge.length) continue; // 中点要落在边内

            
            if(Math.abs(pp.y) < Math.abs(min_dist))
            {
                backwall_edge = edge;
                min_dist = pp.y;
            }                
        }

        
        if(!backwall_edge)
        {
            for(let edge of wall_edges)
            {
                let pp = edge.projectEdge2d(rect.back_center);

                if(edge.islayOn(rect.backEdge,min_dist,0.2))
                {
                    if(Math.abs(pp.y) < Math.abs(min_dist))
                    {
                        if(!backwall_edge || edge.length > backwall_edge.length)
                        {
                            backwall_edge = edge;
                            min_dist = pp.y;
                        }
                
                    }   
                }                                 
            }
        }

        // TsAI_app.log(group_template.category, backwall_edge,min_dist,rect.w,rect.h,rect.nor,rect._back_center,wall_edges);
        // if(!backwall_edge) continue; // 如果没有找到背靠边, 则跳过
        // console.log(group_template.group_space_category);

        let side_wall_edge : ZEdge = null;
        let side_dist = 100000;
        for(let edge of wall_edges)  
        {
            if(Math.abs(edge.nor.dot(rect.nor)) > 0.1) continue;  // 侧靠墙要求法向垂直

            let pp = edge.projectEdge2d(rect.rect_center);
            if(pp.x < 0 || pp.x > edge.length) continue; // 中点要落在边内

            let t_dist = Math.abs(Math.abs(pp.y) - rect.w/2);
            if(!side_wall_edge || t_dist < side_dist)
            {
                side_wall_edge = edge;
                side_dist = t_dist;
            }
        }
        let front_wall_edge : ZEdge = null;
        let front_dist = 10000;

        let f_w_edges = [...wall_edges];
        for(let t_rect of placeholder_rects)
        {
            f_w_edges.push(t_rect._outter_edges()[0]);
        }
        let r_outter_edge = rect._outter_edges()[0];
        for(let edge of f_w_edges)  
        {
            if(Math.abs(edge.nor.dot(rect.nor)) < 0.9) continue;  // 正对的边
            if(!r_outter_edge.islayOn(edge,10000, 0.3)) continue;

            
            let pp = rect.project(edge.center);

            if(pp.y < 0.) continue;
            let t_dist = Math.abs(pp.y);
            if(!front_wall_edge || t_dist < front_dist)
            {
                front_wall_edge = edge;
                front_dist = t_dist;
            }
        }

        rect.ex_prop['group_name'] = group_template.group_space_category;



        let refer_targets : IRuleReferTargets = {
            back_w_edge : backwall_edge,
            side_w_edge : side_wall_edge,
            front_w_edge : front_wall_edge
        }


        return refer_targets;

    }

 

    static getTargetRect(rule:I_LayoutOptRule,feature_shape:TFeatureShape, group_templates:TGroupTemplate[], c_pos : Vector3)
    {
        if(!rule.params?.target_group_category) return null;

        if(compareNames(["平开窗"],[rule.params.target_group_category])==1)
        {
            let windows = feature_shape?._room?.windows;
            if(!windows) return null;

            let target_rect : ZRect = null;
            let min_dist = 99999999;
            for(let win of windows)
            {

                if(win.type !== "Window") {
                    let balcony_door = false;
                    if(win.type === "Door" && (win.realType ==="SlidingDoor" || win.length > 1800))
                    {
                        if(compareNames(win.room_names,["阳台"]))
                        {
                            balcony_door = true;
                        }
                    }
                    if(!balcony_door) continue;

                }
                let rect = win.rect;

                let dist = rect.rect_center.distanceTo(c_pos);
                if(!target_rect || dist < min_dist)
                {
                    target_rect = rect;
                    min_dist = dist;
                }
            }
            return target_rect;
        }
        else{
            let target_rect : ZRect = null;
            let min_dist = 99999999;
            for(let group_template of group_templates)
            {
                if(compareNames([group_template.group_space_category],[rule.params.target_group_category])==1)
                {



                    if(rule.params.target_figure_category)
                    {

                        let target_figure_category = rule.params.target_figure_category.split(",");
                        let figure_group = group_template.current_s_group;
                    
                        if(group_template._target_rect.w <= 0.1) continue;

                        if(!figure_group) continue;
                        let elements = figure_group.figure_elements;

                        for(let ele of elements)
                        {
                            if(compareNames([ele.sub_category],target_figure_category,false)==1)
                            {
                                let rect = ele.rect;
                                let dist = rect.back_center.distanceTo(c_pos);
                                if(!target_rect || dist < min_dist)
                                {
                                    target_rect = rect.clone();
                                    min_dist = dist;
                                }
                            }
                        }
                    }
                    else
                    {

                        let dist = group_template._target_rect.back_center.distanceTo(c_pos);
                        if(!target_rect || dist < min_dist)
                        {
                            target_rect = group_template._target_rect;
                            min_dist = dist;
                        }
                    }

                }
            }
            return target_rect;
        }

    }
   


    /**
     * 三: 干涉优化:  这里主要对干涉进行优化
     *      --- 与 可解释微调 不同，这里采用切蛋糕逻辑
     *      --- 先把其它不能自适应调整的位置 对空间进行裁剪， 剩一个（或多个)多边形
     *      --- 自适应调整元就会 自适应调整（根据尺寸链）调整它的长宽
     */
    static optimize_groups_at_collisions(feature_shape: TFeatureShape, group_templates:TGroupTemplate[], room_name:string)
    {
        let adative_space_elements : TGroupTemplate[] = [];

        let poly = feature_shape._poly; // 默认形状
        let sub_rects : ZRect[] = []; // 要删减的形状
        for(let group_template of group_templates)
        {
            let config = group_template.getFigureGroupConfigs(room_name);
            if(!config) continue;
            if(config.is_adaptive_space) 
            {
                adative_space_elements.push(group_template);
            }
        }
    }





    static sub_edges_by_rect(rect:ZRect, wall_edges:ZEdge[], side_dist:number=100)
    {
        let removed_w_edges : ZEdge[] = [];
        let added_w_edges : ZEdge[] = [];
        for(let w_edge of wall_edges)
        {
            let win = WPolygon.getWindowOnEdge(w_edge);

            let target_r_edge : ZEdge = null;

            for(let t_edge of rect.edges)
            {
                if(!w_edge.islayOn(t_edge,side_dist,0.05)) continue;
                target_r_edge = t_edge;
                break;
            }


            if(!target_r_edge) continue;
            if(side_dist > 250)
            {
                // console.log(target_r_edge, rect.ex_prop);
            }
            let px0 = w_edge.projectEdge2d(target_r_edge.v0.pos).x;
            let px1 = w_edge.projectEdge2d(target_r_edge.v1.pos).x;

            if(px0 > px1) {
                let tmp = px1; px1 = px0; px0 = tmp;
            }

            let unvalid_pairs = [[px0,px1]];

            let valid_pairs = range_substract(w_edge.length,unvalid_pairs);
            removed_w_edges.push(w_edge);

            for(let pair of valid_pairs)
            {
                let p0 = w_edge.unprojectEdge2d({x:pair[0],y:0});
                let p1 = w_edge.unprojectEdge2d({x:pair[1],y:0});
                let t_edge = new ZEdge({pos:p0},{pos:p1});

                t_edge._nor.copy(w_edge.nor);
                t_edge._attached_elements = w_edge._attached_elements;
                for(let key in w_edge._ex_props)
                {
                    t_edge._ex_props[key] = w_edge._ex_props[key];
                }

                if(t_edge.length > 50){
                    added_w_edges.push(t_edge);
                }
            }
        }

        
        for(let edge of removed_w_edges)
        {
            let id = wall_edges.indexOf(edge);
            if(id >= 0)
            {
                wall_edges.splice(id,1);
            }
        }
        if(added_w_edges.length>0)
        {
            wall_edges.push(...added_w_edges);
        }
       let length_list = [];
       for(let edge of added_w_edges)
       {
         length_list.push(edge.length);
       }
    }

 




}