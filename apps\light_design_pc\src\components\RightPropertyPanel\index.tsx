import React, { useEffect, useState, useRef, useContext } from "react";
import useStyles from "./style";
import { useTranslation } from "react-i18next";
import { observer } from "mobx-react-lite";
import { useStore } from "../../store/RootStore";
import { PanelContainer } from "@svg/antd-cloud-design";
import { FloorProperties } from "../../components";
import type { IEntityBase } from "@layoutai/design_domain";
import { AI_PolyTargetType } from "@layoutai/basic_data";

/**
 * @description 右侧属性面板内容
 */

interface RoomSeriesPlanProps {
    right: boolean;
    left: boolean;
    selectedEntity: IEntityBase | undefined;
}

const RightPropertyPanel: React.FC<RoomSeriesPlanProps> = ({ right, left, selectedEntity }) => {
    const { t } = useTranslation();
    const { styles } = useStyles();
    const store = useStore();

    let divRef = useRef(null);
    // 当前属性面板的实体类型
    const [curType, setCurType] = useState<string>("default");
    // 是否显示布局列表
    const [showLayoutList, setShowLayoutList] = useState<boolean>(true); // 区分选布局还是选搭配
    // 属性面板的类型名称 类型与面板标题 对应关系
    // 默认是楼层信息
    const typeNameMap: Map<string, string> = new Map([
        [AI_PolyTargetType.RoomSubArea, t("分区信息")],
        [AI_PolyTargetType.Wall, t("墙体信息")],
        [AI_PolyTargetType.Door, t("门信息")],
        [AI_PolyTargetType.Window, t("窗信息")],
        [AI_PolyTargetType.StructureEntity, t("结构体信息")],
        [AI_PolyTargetType.RoomArea, t("空间信息")],
        [AI_PolyTargetType.Furniture, t("模型位信息")],
        ["default", t("楼层信息")],
    ]);

    useEffect(() => {}, []);

    const calcHeight = () => {
        return document.documentElement.clientHeight - 48;
    };
    return (
        <PanelContainer
            right={right ? 0 : "null"}
            left={left ? 0 : "null"}
            height={calcHeight()}
            width={250}
            draggable={true}
            title="基本"
            showHeader={false}
        >
            <div ref={divRef}>
                <div className={styles.rootTitle}>
                    <span>{typeNameMap.get(curType)}</span>
                </div>
                {curType === "default" && <FloorProperties showLayoutList={showLayoutList} roomInfos={store.homeStore.roomInfos} />}
            </div>
        </PanelContainer>
    );
};

export default observer(RightPropertyPanel);
