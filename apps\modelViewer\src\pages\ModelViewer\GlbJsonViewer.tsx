import "./index.less";

import { useEffect, useRef, useState } from "react";
import { Group, Object3D } from "three";
import { generateUUID } from "three/src/math/MathUtils.js";

import { I_DesignMaterialInfo, StyleBrush } from "@layoutai/basic_data";
import {  EdgesBuilder, I_RawGltfExtras, Model3dViewer, RawGltfNode, SvgGltfCabinetNode, SvgGltfNode } from "@layoutai/model3d_api";
import { LayoutAI_MsgConfig } from "@layoutai/basic_data";
import { createMagiccubeRequest, getImgDomain } from "@svg/request";


export const GltfNodeTreeItem :React.FC<{object:Object3D,prefix:string,level:number}>= (props:{object:Object3D,prefix:string,level:number})=>{
    let object = props.object;
    let level = props.level;
    let uuid = object.uuid || generateUUID();

    let div_id = "TreeItem"+uuid;
    if(object.userData.isExpand === undefined)
    {
        object.userData.isExpand = (level < 2)?true:false; 
    }
    let isExpand = object.userData.isExpand || false;
    let svgGltfNode = object as SvgGltfNode;

    let extension_name = object.userData?.extras?.name || "";
    if(svgGltfNode?.category && svgGltfNode.category.includes("门"))
    {
        extension_name += (svgGltfNode.orientationType? `(${svgGltfNode.orientationType})`:"");
    }
    let extras = object.userData.extras as I_RawGltfExtras;
    if(extras && extras.length)
    {
        extension_name += ` ${Math.round(extras.length)}X${Math.round(extras.width)}X${Math.round(extras.height)}`;
    }

    if(svgGltfNode.materialId)
    {
        extension_name+=`[${svgGltfNode.materialId}]`;
    }

    extension_name = ((object as RawGltfNode).boardCategory || "")+"-" + extension_name;
    let isSolid = (object as RawGltfNode).isSolid;
    let expand_tag = isExpand ?"▼":"▷";
    return (
        <div id={div_id} className="tree-item" key={uuid}>
            <div className="content" onClick={()=>{
                if(Model3dViewer?.instance?.updateSelectionTarget)
                {

      
                    Model3dViewer.instance.updateSelectionTarget(object);
                }
            }}><span className="prefix" onClick={()=>{

                let isExpand = object.userData.isExpand || false;
                isExpand = !isExpand;
                object.userData.isExpand = isExpand;
                let expand_tag = isExpand ?"▼":"▷";

                let c_div = document.getElementById(div_id);

                if(c_div)
                {
                    let children_div = c_div.getElementsByClassName("tree-children")[0];
                    if(children_div)
                    {
                        children_div.className = 'tree-children'+(isExpand?" isExpand":"");
                    }
                    let prefixSpan = c_div.getElementsByClassName("prefix")[0];
                    if(prefixSpan)
                    {
                        prefixSpan.innerHTML = props.prefix+expand_tag;
                    }
                }

            }}>{props.prefix+expand_tag}</span><span className={isSolid?"IsSolidNode":""}>{object.name+' '+extension_name+`(${object.children?.length||"-"})`}</span></div>
            <div className={'tree-children'+(isExpand?" isExpand":"")}>
            { object.children && object.children.length > 0 && object.children.map((child,index)=>{
                return <GltfNodeTreeItem object={child}  key={child.uuid+"-"+index} prefix={props.prefix+"  "} level={props.level+1}></GltfNodeTreeItem>
            }) }
            </div>

        </div>
    )

}
async function saveStyleBrush(styleBrushId: string, styleBrush:StyleBrush): Promise<boolean> {
    try {
        let postReqBody = {
            styleBrushId: styleBrushId,
            styleBrush: styleBrush
        };

        const magicCubeRequest = createMagiccubeRequest({
            sysCode : LayoutAI_MsgConfig.APP_ID,
            env : "prod",
        });
        magicCubeRequest.defaults.withCredentials = true;
        magicCubeRequest.defaults.headers = {
          'Content-Type': 'application/json',
          'sysCode': LayoutAI_MsgConfig.APP_ID,
          'Magiccube-App-Id': LayoutAI_MsgConfig.APP_ID,
          'Magiccube-Token': LayoutAI_MsgConfig.MagiccubeToken,
          'Joint-Token': null,
        };
        
        

        const res = await magicCubeRequest({
            method: 'post',
            url: `/dp-ai-web/saveStyleBrush`,
            data: {
                ...postReqBody,
            },
            timeout: 60000,
        });

        if (res.data == true) {
            return true;
        } else {
            return false;
        }
    } catch (e) {
        return false;
    }
}
const  GlbJsonViewer : React.FC = ()=>{
    const mainDivRef = useRef(null);
    const [isShowTree,setShowTree] = useState<boolean>(true);
    const [RootNode,setRootNode] = useState<Group>(null);
    const [cabinetStyleList,setCabinetStyleList] = useState<{boardCategory:string,info:I_DesignMaterialInfo}[]>([]);
    const [isMergedNodes,setIsMergeNodes] = useState<boolean>(true);
    const [currentJsonUrl,setCurrentJsonUrl] = useState<string>("");
    const loadJson = async (glbJsonUrl:string)=>{
        let data = await fetch(glbJsonUrl).then(val=>val.json());
        setCurrentJsonUrl(glbJsonUrl);
        let svgGltfCabinetNode = new SvgGltfCabinetNode(data);
        if(isMergedNodes)
        {
            svgGltfCabinetNode.mergeBatchedNodes();
        }


        // setRootNode(svgGltfCabinetNode);
        await svgGltfCabinetNode.updateSolidModels();
        setRootNode(svgGltfCabinetNode);



        console.log(svgGltfCabinetNode);

    }
    useEffect(()=>{
        if(mainDivRef.current)
        {
            Model3dViewer.instance.bindParentDiv(mainDivRef.current as HTMLDivElement);
            Model3dViewer.instance.startRender();
        }

        //  loadJson("glbjson.json");
        loadJson("https://3vj-content.3vjia.com/ai/layout-design/cabinet-glb/257606233/257606233_3300X370X2400_1152079.json");

    },[]);

    useEffect(()=>{
   
        if(RootNode)
        {
            // RootNode.rotateZ(Math.PI);
        }
        Model3dViewer.instance.setMainGroup(RootNode as Group);
    },[RootNode]);

    useEffect(()=>{
        loadJson(currentJsonUrl);
    },[isMergedNodes]);
    return <>

        <div id="Model3DViewer" className='main_3d_div' ref={mainDivRef}></div>
        <div className='modelTreeContainer'>
         <div className='tabTile' onClick={(ev)=>{setShowTree(!isShowTree);}}>
              {isShowTree ? "显示树状":"显示列表"}
          </div>
          <div className='tabTile' onClick={(ev)=>{setIsMergeNodes(!isMergedNodes);}}>
              {isMergedNodes ? "合批":"不合批"}
          </div>
            {isShowTree && RootNode && <GltfNodeTreeItem object={RootNode} prefix="" level={0}></GltfNodeTreeItem>}
            {!isShowTree && <div>
                {cabinetStyleList.map((item,index)=>{
                    return <div className="imgItem" key={"item"+index}>
                        <img src={getImgDomain()+item.info.ImagePath}></img>
                        <div className="title">{item.boardCategory}-{item.info.MaterialId}</div>
                    </div>
                })}    
            </div>}
        </div>

    </>
}

export default GlbJsonViewer;