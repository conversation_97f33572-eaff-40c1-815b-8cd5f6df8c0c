import { TRoomEntity,TRoom,TSeriesSample,LayoutAI_App,TFigureElement, EventName, g_FigureImagePaths, FigureCategoryManager, LayoutAI_Events, TAppManagerBase} from '@layoutai/layout_scheme';
import { AI_PolyTargetType, I_MaterialMatchingItem } from '@layoutai/basic_data';
import { useStore } from '@/models';
import { getPrefix } from '@/utils/common';
import { observer } from "mobx-react-lite";
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import MenuList from '../menuList';
import MaterialList from '../materialList';
import CustomStyleList from '../customStyleList';
import useStyles from './style';

interface RoomSeriesPlanProps {
  roomInfo: TRoom;
  currenScheme: any;
  roomInfos: TRoom[];
  showLayoutList: boolean;
  selectedFigureElement: TFigureElement;
}
interface FigureProps { image_path: string, title: string, centerTitle: string, bottomTitle: string, checked?: boolean, figure_element: TFigureElement, room?: TRoom }
const RoomProperties: React.FC<RoomSeriesPlanProps> = ({ roomInfo, currenScheme, roomInfos, showLayoutList, selectedFigureElement }) => {
  const { t } = useTranslation();
  const { styles }: any = useStyles();
  const [menuList, setMenuList] = useState<any>([]);
  const [materialList, setMaterialList] = useState<any>([]);
  const [styleList, setStyleList] = useState<any>([]);
  const [area, setArea] = useState(0);
  const [updated, setUpdate] = useState(0);
  let figure_list: FigureProps[] = [];
  let good_list: FigureProps[] = [];
  let hard_decoration_list: FigureProps[] = [];
  let custom_cabinet_list: FigureProps[] = [];
  const [locked, setLocked] = useState<number>(0);

  const store = useStore();
  const UpdateFigureList = () => {

    let styleListTemp: any = [];
    for (let room of (LayoutAI_App.instance as TAppManagerBase).layout_container._rooms) {
      if (room.uuid === roomInfo.uuid) {
        styleListTemp.push({
          title: room._scope_series_map?.soft?.ruleName || null,
          title2: room._scope_series_map?.cabinet?.ruleName || null,
          title3: room._scope_series_map?.hard?.ruleName || null,
          img: room._scope_series_map?.soft?.thumbnail || null,
          img1: room._scope_series_map?.cabinet?.thumbnail || null,
          img2: room._scope_series_map?.hard?.thumbnail || null,
          bottomTitle: room.roomname,
          area: room.area?.toFixed(2),
          room: room
        })
      }
    }
    setStyleList(styleListTemp);
    if (showLayoutList) {
      setMenuList([
        {
          label: t('图例'),
          figureList: figure_list,
        },
      ])
    } else {
      let materialList = [
        {
          label: t('软装'),
          figureList: good_list
        },
        {
          label: t('定制'),
          figureList: custom_cabinet_list
        },
        {
          label: t('硬装'),
          figureList: hard_decoration_list,
        }
      ];
      // if (figure_list.length > 0) {
      //   menuList.push({
      //     label: t('图例'),
      //     figureList: figure_list,
      //   });
      // }
      setMaterialList(materialList);
    }
  }

  const onClickLock = () => {
    if (locked == 1) {
      setLocked(0);
      roomInfo.locked = false;
    } else {
      setLocked(1);
      roomInfo.locked = true;
    }
  }

  useEffect(() => {
    if(!selectedFigureElement) return;
    for (let fig_data of figure_list) {
      if (fig_data.figure_element === selectedFigureElement) {
        fig_data.checked = true;
      }
      else {
        fig_data.checked = false;
      }
    }
    UpdateFigureList();
  }, [selectedFigureElement]);
  useEffect(() => {
    if (!roomInfo) return;
    setLocked(roomInfo.locked ? 1 : 0);
    figure_list = [];
    good_list = [];
    if (showLayoutList) {
      let figure_ele_list = [...roomInfo._furniture_list];
      figure_ele_list.sort((a, b) => {
        return b.default_drawing_order - a.default_drawing_order;
      })
      figure_ele_list.forEach((item: TFigureElement) => {

        if (!LayoutAI_App.IsDebug && (item._is_decoration || item._is_sub_board)) return;
        figure_list.push({
          image_path: g_FigureImagePaths[item.sub_category]?.png || getPrefix() + "static/figures_imgs/square_pillar.svg",
          title: t(item.modelLoc) + " | " + t(item.sub_category),
          centerTitle: `${Math.round(item.length)}*${Math.round(item.depth)}*${Math.round(item.height)}`,
          bottomTitle: LayoutAI_App.IsDebug ? t('离地高')+' ' + Math.round(item.min_z) + "mm" : '',
          figure_element: item,
          room: roomInfo
        })
      });
    }
    else {
      // 选择了套系后才展示商品列表
      // let hasSeries = store.homeStore.room2SeriesSampleArray.some((item: any) => item[0].uuid === roomInfo.uuid);
      if (true) {
        let figure_ele_list = [...roomInfo._furniture_list];
        roomInfo._furniture_list.forEach(fe => {
          if (!fe.haveMatchedMaterial()) {
            figure_ele_list.push(...fe.getAlternativeFigureElements());
          } else {
            figure_ele_list.push(...fe.getMembersFigureElements());
          }
        });
        if(roomInfo.decoration_elements)
        {
            figure_ele_list.push(...roomInfo.decoration_elements);
        }
        figure_ele_list.sort((a, b) => {
          return b.default_drawing_order - a.default_drawing_order;
        })
        figure_ele_list.forEach((item) => {

          if (!LayoutAI_App.IsDebug && (item._is_decoration || item._is_sub_board)) return;
          let figure_img_path = g_FigureImagePaths[item.sub_category]?.png || getPrefix() + "static/figures_imgs/square_pillar.svg";
          let is_have_material = item.haveMatchedMaterial2() || item.haveDeletedMaterial();
          if (!is_have_material) {
            figure_list.push({
              image_path: figure_img_path,
              title: t(item.modelLoc) + " | " + t(item.sub_category),
              centerTitle: `${Math.round(item.length)}*${Math.round(item.depth)}*${Math.round(item.height)}`,
              bottomTitle: LayoutAI_App.IsDebug ?  t('离地高')+' ' + Math.round(item.min_z) + "mm" : '',
              figure_element: item,
              room: roomInfo
            })
          }
          if (item._decoration_type === "Electricity") return;
          let material_img_path = item._matched_material?.imageUrl || getPrefix() + "static/figures_imgs/square_pillar.svg";
          let modelId = item._matched_material?.modelId || t("无"); //[i18n:ignore]
          const good: FigureProps = {
            image_path: is_have_material ? material_img_path : figure_img_path,
            title: t(item.sub_category) + " " + t("素材") + 'ID:' + modelId,
            centerTitle: `${t('目标尺寸')} ${Math.round(item.rect.length)}*${Math.round(item.rect.depth)}`,
            bottomTitle: item?._matched_material && item?._matched_material?.modelId ? (`${t('素材尺寸')} ${Math.round(item?._matched_material?.length)}*${Math.round(item?._matched_material?.width)}*${Math.round(item?._matched_material?.height)}`
              + ` Z${Math.round(item?._matched_material?.targetPosition?.z || 0)}`) : '',
            figure_element: item,
            room: roomInfo
          };
          if (item.haveMatchedCustomCabinet()) {
            if (is_have_material) {
              custom_cabinet_list.push(good);
            } else {
              custom_cabinet_list = [good, ...custom_cabinet_list];
            }
          } else {
            if (is_have_material) {
              good_list.push(good);
            } else {
              good_list = [good, ...good_list];
            }
          }
        });

        // 更新硬装的内容
        let hard_figures = roomInfo.getHardDecorationList();
        hard_figures.forEach(item => {
          let figure_img_path = g_FigureImagePaths[item.sub_category]?.png || getPrefix() + "static/figures_imgs/square_pillar.svg";
          let is_have_material = item.haveMatchedMaterial() || item.haveDeletedMaterial();
          let material_img_path = item._matched_material?.imageUrl || getPrefix() + "static/figures_imgs/square_pillar.svg";
          let modelId = item._matched_material?.modelId || t("无"); //[i18n:ignore]
          let hardFigureProps: FigureProps = {
            image_path: is_have_material ? material_img_path : figure_img_path,
            title: t(item.sub_category) + " " + t("素材ID:") + modelId,
            centerTitle: t("名称")+":" + (t(item?._matched_material?.name) || "") + " " + ((item.category === "吊顶" && item._matched_material?.topOffset) ? (t("下吊") + item._matched_material.topOffset) + "mm" : ""),
            bottomTitle: t("适用")+":" + ((t(item?._matched_material?.applySpace) || false) ? item._matched_material.applySpace.map(space => t(space)).join(",") : t("全屋")),
            figure_element: item
          };
          if (is_have_material) {
            hard_decoration_list.push(hardFigureProps);
          } else {
            hard_decoration_list = [hardFigureProps, ...hard_decoration_list];
          }
        })
      }
    }

    // console.log('roomInfo', roomInfo);
    UpdateFigureList();
    let area = 0;
    roomInfos.forEach((room: TRoom) => {
      area += room?.room_shape?._area || 0;
    });
    setArea(parseFloat(area.toFixed(2)));

    if (LayoutAI_App.instance) {
      LayoutAI_App.on(EventName.RoomMaterialsUpdated, () => {
        setUpdate(updated + 1);
      });
    }

  }, [roomInfo, currenScheme, updated, showLayoutList, store.homeStore.room2SeriesSampleArray]);


  return (
    <div className={styles.root} >
      <div className={styles.rootInfo}>
        <div>
          <div className={styles.rootItem}>
            <div>{t('名称')}</div>
            <div>{t(roomInfo.name)}</div>
          </div>
          <div className={styles.rootItem}>
            <div>{t('房屋所使用的面积')}</div>
            <div>{area}m²</div>
          </div>
        </div>
        <div className={styles.line}>
        </div>
        {showLayoutList && <MenuList menuList={menuList} ></MenuList>}
        {!showLayoutList && <>
        <CustomStyleList customStyleList={[
        {
          label: t('风格'),
          figureList: styleList,
        }]} multiExpand={true} ></CustomStyleList>
        <div className={styles.line}></div>
        <MaterialList materialList={materialList} ></MaterialList>
        </>}
      </div>
      {showLayoutList ?
        // <div className={styles.clearBtn}>
        //   <button onClick={() => {
        //     let roomToDelete: TRoom = null;
        //     roomToDelete = roomInfo;
        //     LayoutAI_App.DispatchEvent(LayoutAI_Events.ClearLayout, null);
        //     setMenuList([
        //       {
        //         label: t('图例'),
        //         figureList: [],
        //       },
        //     ])
        //   }}>清除布局</button>
        // </div>
        <></>
        :
        <div className={styles.clearBtn}>
          <button onClick={() => {
            LayoutAI_App.DispatchEvent(LayoutAI_Events.ClearSeries, roomInfo);
            setMenuList([
              {
                label: t('风格'),
                figureList: [],
              },
              {
                label: t('家具'),
                figureList: []
              }
            ])
          }}>{t('清除套系')}</button>
        </div>
      }
    </div>
  );
};


export default observer(RoomProperties);
