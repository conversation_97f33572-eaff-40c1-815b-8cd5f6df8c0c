import { I_XmlCDoorBoardEntityBase } from "../IWardrobeEntityInterface";
import { XmlCFaceBoardEntity } from "./XmlCFaceBoardEntity";
import { XmlEntityBase } from "../XmlEntityBase";


export class XmlCDoorBoardEntityBase extends XmlEntityBase implements I_XmlCDoorBoardEntityBase {
    isFunctorSyncB?: boolean;
    placeRuleS?: string;
    isQuoteB?: boolean;
    isBoardPointEditUnlimitedB?: boolean;
    isQuoteSplitN?: number;
    isOutsourcingN?: number;
    ocTypeS?: string;
    standardCategoryS?: string;
    isSplitN?: number;
    isSetThI_XmlCkN?: number;
    canReplaceMaterialB?: boolean;
    canSetHandleSizeB?: boolean;
    reverseSizeB?: boolean;
    textureDirectionN?: number;
    doorBoardMaterialChangeS?: string;
    canEditDoorBoardThI_XmlCkB?: boolean;
    Visible?: {
        MiddleVariable?: {
            nameS?: string;
            valueS?: string;
            valueExpressionS?: string;
        };
    };
    CWhMaterialComponent?: {
        materialMapVoIdS?: string;
        isRotateN?: number;
        oriMaterialMapVoIdS?: string;
    };

    declare Children?: {
        CDoorBoardEntityBase?: XmlCDoorBoardEntityBase[];
        CFaceBoardEntity?: XmlCFaceBoardEntity[];
        [key:string]:XmlEntityBase[];
    };
    EdgeComponent?: {
        fatEdgeStartN?: number;
        fatEdgeEndN?: number;
        edgeBandModeN?: number;
        edgeBandPlanInfoS?: string;
        edgeBandArrS?: string;
        edgeModifyByUserN?: number;
    };

    constructor(data?: Partial<I_XmlCDoorBoardEntityBase>) {
        super(data);
        this.EdgeComponent = data.EdgeComponent || {};
        this.CWhMaterialComponent = data.CWhMaterialComponent || {};
        this.Visible = data.Visible || {};
        this.ocTypeS = data.ocTypeS || "";
        this.standardCategoryS = data.standardCategoryS || "";
        this.isFunctorSyncB = data.isFunctorSyncB ?? false;
        this.canReplaceMaterialB = data.canReplaceMaterialB ?? false;
        this.canSetHandleSizeB = data.canSetHandleSizeB ?? false;     
        this.isQuoteB = data.isQuoteB ?? false;
        this.textureDirectionN = data.textureDirectionN ?? 0;
        this.reverseSizeB = data.reverseSizeB ?? false;
        this.placeRuleS = data.placeRuleS ?? "";
        
    }
}
XmlEntityBase.Generators["CDoorBoardEntityBase"] = (data:I_XmlCDoorBoardEntityBase)=>new XmlCDoorBoardEntityBase(data);