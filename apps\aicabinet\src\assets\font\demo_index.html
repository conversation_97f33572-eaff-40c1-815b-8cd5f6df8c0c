<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4755574" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe82c;</span>
                <div class="name">吊顶</div>
                <div class="code-name">&amp;#xe82c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe82d;</span>
                <div class="name">分区</div>
                <div class="code-name">&amp;#xe82d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe82e;</span>
                <div class="name">属性</div>
                <div class="code-name">&amp;#xe82e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe82a;</span>
                <div class="name">铺砖</div>
                <div class="code-name">&amp;#xe82a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe827;</span>
                <div class="name">放大</div>
                <div class="code-name">&amp;#xe827;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe828;</span>
                <div class="name">墙</div>
                <div class="code-name">&amp;#xe828;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe829;</span>
                <div class="name">布局</div>
                <div class="code-name">&amp;#xe829;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe826;</span>
                <div class="name">加素材</div>
                <div class="code-name">&amp;#xe826;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe825;</span>
                <div class="name">风格</div>
                <div class="code-name">&amp;#xe825;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe824;</span>
                <div class="name">信息</div>
                <div class="code-name">&amp;#xe824;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe821;</span>
                <div class="name">智能搭柜</div>
                <div class="code-name">&amp;#xe821;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe822;</span>
                <div class="name">筛选</div>
                <div class="code-name">&amp;#xe822;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe823;</span>
                <div class="name">开门</div>
                <div class="code-name">&amp;#xe823;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8e9;</span>
                <div class="name">info</div>
                <div class="code-name">&amp;#xe8e9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8e8;</span>
                <div class="name">info_hover</div>
                <div class="code-name">&amp;#xe8e8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe81e;</span>
                <div class="name">勾</div>
                <div class="code-name">&amp;#xe81e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe81f;</span>
                <div class="name">收纳</div>
                <div class="code-name">&amp;#xe81f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe820;</span>
                <div class="name">空间利用率</div>
                <div class="code-name">&amp;#xe820;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe81a;</span>
                <div class="name">家居布局</div>
                <div class="code-name">&amp;#xe81a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe81b;</span>
                <div class="name">星星</div>
                <div class="code-name">&amp;#xe81b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe81c;</span>
                <div class="name">动线</div>
                <div class="code-name">&amp;#xe81c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe81d;</span>
                <div class="name">半星</div>
                <div class="code-name">&amp;#xe81d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe785;</span>
                <div class="name">heart</div>
                <div class="code-name">&amp;#xe785;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe786;</span>
                <div class="name">heartall</div>
                <div class="code-name">&amp;#xe786;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe707;</span>
                <div class="name">渲染Render</div>
                <div class="code-name">&amp;#xe707;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe818;</span>
                <div class="name">save</div>
                <div class="code-name">&amp;#xe818;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe816;</span>
                <div class="name">已选</div>
                <div class="code-name">&amp;#xe816;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe817;</span>
                <div class="name">未选</div>
                <div class="code-name">&amp;#xe817;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe815;</span>
                <div class="name">分享</div>
                <div class="code-name">&amp;#xe815;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe643;</span>
                <div class="name">勾</div>
                <div class="code-name">&amp;#xe643;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe814;</span>
                <div class="name">导示图</div>
                <div class="code-name">&amp;#xe814;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8ae;</span>
                <div class="name">预报价</div>
                <div class="code-name">&amp;#xe8ae;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe798;</span>
                <div class="name">图库</div>
                <div class="code-name">&amp;#xe798;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe813;</span>
                <div class="name">历史版本</div>
                <div class="code-name">&amp;#xe813;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe812;</span>
                <div class="name">添加素材</div>
                <div class="code-name">&amp;#xe812;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe811;</span>
                <div class="name">AI出图</div>
                <div class="code-name">&amp;#xe811;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe794;</span>
                <div class="name">下载</div>
                <div class="code-name">&amp;#xe794;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe795;</span>
                <div class="name">加入资产</div>
                <div class="code-name">&amp;#xe795;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe796;</span>
                <div class="name">户型_L</div>
                <div class="code-name">&amp;#xe796;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe797;</span>
                <div class="name">户型_s</div>
                <div class="code-name">&amp;#xe797;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe793;</span>
                <div class="name">check</div>
                <div class="code-name">&amp;#xe793;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe792;</span>
                <div class="name">DeleteFilled</div>
                <div class="code-name">&amp;#xe792;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe791;</span>
                <div class="name">重新编辑</div>
                <div class="code-name">&amp;#xe791;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d0;</span>
                <div class="name">caretup</div>
                <div class="code-name">&amp;#xe7d0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d2;</span>
                <div class="name">closecirle_fill</div>
                <div class="code-name">&amp;#xe7d2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d7;</span>
                <div class="name">caretleft</div>
                <div class="code-name">&amp;#xe7d7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe80b;</span>
                <div class="name">fight</div>
                <div class="code-name">&amp;#xe80b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe80c;</span>
                <div class="name">exclamationcircle_line</div>
                <div class="code-name">&amp;#xe80c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe80d;</span>
                <div class="name">hide</div>
                <div class="code-name">&amp;#xe80d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe80e;</span>
                <div class="name">left</div>
                <div class="code-name">&amp;#xe80e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe80f;</span>
                <div class="name">search</div>
                <div class="code-name">&amp;#xe80f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe810;</span>
                <div class="name">smarttemplate</div>
                <div class="code-name">&amp;#xe810;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe819;</span>
                <div class="name">questioncicle_line</div>
                <div class="code-name">&amp;#xe819;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe70a;</span>
                <div class="name">close</div>
                <div class="code-name">&amp;#xe70a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe717;</span>
                <div class="name">chec</div>
                <div class="code-name">&amp;#xe717;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe756;</span>
                <div class="name">reset</div>
                <div class="code-name">&amp;#xe756;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e8;</span>
                <div class="name">Share</div>
                <div class="code-name">&amp;#xe6e8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ee;</span>
                <div class="name">Close_Large</div>
                <div class="code-name">&amp;#xe6ee;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe706;</span>
                <div class="name">筛选Fliter</div>
                <div class="code-name">&amp;#xe706;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ab;</span>
                <div class="name">手动框选Frame Selection</div>
                <div class="code-name">&amp;#xe7ab;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe644;</span>
                <div class="name">house</div>
                <div class="code-name">&amp;#xe644;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe82b;</span>
                <div class="name">显示Show</div>
                <div class="code-name">&amp;#xe82b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe87e;</span>
                <div class="name">720</div>
                <div class="code-name">&amp;#xe87e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe885;</span>
                <div class="name">arrow_fill_down</div>
                <div class="code-name">&amp;#xe885;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe884;</span>
                <div class="name">arrow_fill_up</div>
                <div class="code-name">&amp;#xe884;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe880;</span>
                <div class="name">arrow_line_down</div>
                <div class="code-name">&amp;#xe880;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe881;</span>
                <div class="name">arrow_line_left</div>
                <div class="code-name">&amp;#xe881;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c4;</span>
                <div class="name">info_fill</div>
                <div class="code-name">&amp;#xe7c4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7cd;</span>
                <div class="name">caretdown</div>
                <div class="code-name">&amp;#xe7cd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7cf;</span>
                <div class="name">caretright</div>
                <div class="code-name">&amp;#xe7cf;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe80a;</span>
                <div class="name">安装Install</div>
                <div class="code-name">&amp;#xe80a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe809;</span>
                <div class="name">change_logo</div>
                <div class="code-name">&amp;#xe809;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe802;</span>
                <div class="name">rotate</div>
                <div class="code-name">&amp;#xe802;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe808;</span>
                <div class="name">delete</div>
                <div class="code-name">&amp;#xe808;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe806;</span>
                <div class="name">感叹号</div>
                <div class="code-name">&amp;#xe806;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe807;</span>
                <div class="name">关闭</div>
                <div class="code-name">&amp;#xe807;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe803;</span>
                <div class="name">恢复</div>
                <div class="code-name">&amp;#xe803;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe804;</span>
                <div class="name">撤销</div>
                <div class="code-name">&amp;#xe804;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe805;</span>
                <div class="name">主页</div>
                <div class="code-name">&amp;#xe805;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8cb;</span>
                <div class="name">聚焦</div>
                <div class="code-name">&amp;#xe8cb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7fc;</span>
                <div class="name">旋转45度</div>
                <div class="code-name">&amp;#xe7fc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7fd;</span>
                <div class="name">专注空间</div>
                <div class="code-name">&amp;#xe7fd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7fe;</span>
                <div class="name">解组</div>
                <div class="code-name">&amp;#xe7fe;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ff;</span>
                <div class="name">离地</div>
                <div class="code-name">&amp;#xe7ff;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe800;</span>
                <div class="name">打组</div>
                <div class="code-name">&amp;#xe800;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe801;</span>
                <div class="name">粘贴</div>
                <div class="code-name">&amp;#xe801;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7fa;</span>
                <div class="name">尺寸</div>
                <div class="code-name">&amp;#xe7fa;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b2;</span>
                <div class="name">horizontalflip_line</div>
                <div class="code-name">&amp;#xe6b2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b3;</span>
                <div class="name">verflip_line</div>
                <div class="code-name">&amp;#xe6b3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe88e;</span>
                <div class="name">锁定</div>
                <div class="code-name">&amp;#xe88e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe88f;</span>
                <div class="name">解锁</div>
                <div class="code-name">&amp;#xe88f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f7;</span>
                <div class="name">素材</div>
                <div class="code-name">&amp;#xe7f7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f8;</span>
                <div class="name">锁定布局</div>
                <div class="code-name">&amp;#xe7f8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f9;</span>
                <div class="name">解锁布局</div>
                <div class="code-name">&amp;#xe7f9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f3;</span>
                <div class="name">删除布局</div>
                <div class="code-name">&amp;#xe7f3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f4;</span>
                <div class="name">锁定风格</div>
                <div class="code-name">&amp;#xe7f4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f5;</span>
                <div class="name">解锁风格</div>
                <div class="code-name">&amp;#xe7f5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f6;</span>
                <div class="name">清除风格</div>
                <div class="code-name">&amp;#xe7f6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ef;</span>
                <div class="name">素材</div>
                <div class="code-name">&amp;#xe7ef;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f0;</span>
                <div class="name">属性</div>
                <div class="code-name">&amp;#xe7f0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f1;</span>
                <div class="name">布局</div>
                <div class="code-name">&amp;#xe7f1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f2;</span>
                <div class="name">户型</div>
                <div class="code-name">&amp;#xe7f2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ed;</span>
                <div class="name">工具</div>
                <div class="code-name">&amp;#xe7ed;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ee;</span>
                <div class="name">风格</div>
                <div class="code-name">&amp;#xe7ee;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ec;</span>
                <div class="name">左箭头</div>
                <div class="code-name">&amp;#xe7ec;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7eb;</span>
                <div class="name">Search</div>
                <div class="code-name">&amp;#xe7eb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e8;</span>
                <div class="name">用户管理</div>
                <div class="code-name">&amp;#xe7e8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e9;</span>
                <div class="name">账号分享</div>
                <div class="code-name">&amp;#xe7e9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ea;</span>
                <div class="name">链接分享</div>
                <div class="code-name">&amp;#xe7ea;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e7;</span>
                <div class="name">更多_bold</div>
                <div class="code-name">&amp;#xe7e7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e5;</span>
                <div class="name">列表模式</div>
                <div class="code-name">&amp;#xe7e5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e6;</span>
                <div class="name">卡片模式</div>
                <div class="code-name">&amp;#xe7e6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7fb;</span>
                <div class="name">more</div>
                <div class="code-name">&amp;#xe7fb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8b1;</span>
                <div class="name">上</div>
                <div class="code-name">&amp;#xe8b1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8b2;</span>
                <div class="name">下</div>
                <div class="code-name">&amp;#xe8b2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8b3;</span>
                <div class="name">左</div>
                <div class="code-name">&amp;#xe8b3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8b4;</span>
                <div class="name">右</div>
                <div class="code-name">&amp;#xe8b4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8d2;</span>
                <div class="name">close-blod</div>
                <div class="code-name">&amp;#xe8d2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe630;</span>
                <div class="name">none</div>
                <div class="code-name">&amp;#xe630;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e2;</span>
                <div class="name">我的方案</div>
                <div class="code-name">&amp;#xe7e2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e3;</span>
                <div class="name">我的户型</div>
                <div class="code-name">&amp;#xe7e3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e4;</span>
                <div class="name">回收站</div>
                <div class="code-name">&amp;#xe7e4;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.eot?t=1746758065083'); /* IE9 */
  src: url('iconfont.eot?t=1746758065083#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('data:application/x-font-woff2;charset=utf-8;base64,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') format('woff2'),
       url('iconfont.woff?t=1746758065083') format('woff'),
       url('iconfont.ttf?t=1746758065083') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-diaoding"></span>
            <div class="name">
              吊顶
            </div>
            <div class="code-name">.icon-diaoding
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fenqu"></span>
            <div class="name">
              分区
            </div>
            <div class="code-name">.icon-fenqu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuxing"></span>
            <div class="name">
              属性
            </div>
            <div class="code-name">.icon-shuxing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-puzhuan"></span>
            <div class="name">
              铺砖
            </div>
            <div class="code-name">.icon-puzhuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fangda"></span>
            <div class="name">
              放大
            </div>
            <div class="code-name">.icon-fangda
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qiang"></span>
            <div class="name">
              墙
            </div>
            <div class="code-name">.icon-qiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-buju"></span>
            <div class="name">
              布局
            </div>
            <div class="code-name">.icon-buju
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chanpinzhiru"></span>
            <div class="name">
              加素材
            </div>
            <div class="code-name">.icon-chanpinzhiru
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fengge"></span>
            <div class="name">
              风格
            </div>
            <div class="code-name">.icon-fengge
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xinxi"></span>
            <div class="name">
              信息
            </div>
            <div class="code-name">.icon-xinxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhinengdagui"></span>
            <div class="name">
              智能搭柜
            </div>
            <div class="code-name">.icon-zhinengdagui
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shaixuan"></span>
            <div class="name">
              筛选
            </div>
            <div class="code-name">.icon-shaixuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-kaimen"></span>
            <div class="name">
              开门
            </div>
            <div class="code-name">.icon-kaimen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-info"></span>
            <div class="name">
              info
            </div>
            <div class="code-name">.icon-info
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-info_hover"></span>
            <div class="name">
              info_hover
            </div>
            <div class="code-name">.icon-info_hover
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gou1"></span>
            <div class="name">
              勾
            </div>
            <div class="code-name">.icon-gou1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shouna"></span>
            <div class="name">
              收纳
            </div>
            <div class="code-name">.icon-shouna
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-kongjianliyongshuai"></span>
            <div class="name">
              空间利用率
            </div>
            <div class="code-name">.icon-kongjianliyongshuai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiajubuju"></span>
            <div class="name">
              家居布局
            </div>
            <div class="code-name">.icon-jiajubuju
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingxing"></span>
            <div class="name">
              星星
            </div>
            <div class="code-name">.icon-xingxing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dongxian"></span>
            <div class="name">
              动线
            </div>
            <div class="code-name">.icon-dongxian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-banxing"></span>
            <div class="name">
              半星
            </div>
            <div class="code-name">.icon-banxing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-heart"></span>
            <div class="name">
              heart
            </div>
            <div class="code-name">.icon-heart
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-heart-o"></span>
            <div class="name">
              heartall
            </div>
            <div class="code-name">.icon-heart-o
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xuanranRender"></span>
            <div class="name">
              渲染Render
            </div>
            <div class="code-name">.icon-xuanranRender
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-save"></span>
            <div class="name">
              save
            </div>
            <div class="code-name">.icon-save
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yixuan"></span>
            <div class="name">
              已选
            </div>
            <div class="code-name">.icon-yixuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-weixuan"></span>
            <div class="name">
              未选
            </div>
            <div class="code-name">.icon-weixuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fenxiang1"></span>
            <div class="name">
              分享
            </div>
            <div class="code-name">.icon-fenxiang1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gou"></span>
            <div class="name">
              勾
            </div>
            <div class="code-name">.icon-gou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-daoshitu"></span>
            <div class="name">
              导示图
            </div>
            <div class="code-name">.icon-daoshitu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-baojia"></span>
            <div class="name">
              预报价
            </div>
            <div class="code-name">.icon-baojia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tuku"></span>
            <div class="name">
              图库
            </div>
            <div class="code-name">.icon-tuku
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-lishibanben"></span>
            <div class="name">
              历史版本
            </div>
            <div class="code-name">.icon-lishibanben
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tianjiasucai"></span>
            <div class="name">
              添加素材
            </div>
            <div class="code-name">.icon-tianjiasucai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-AIchutu"></span>
            <div class="name">
              AI出图
            </div>
            <div class="code-name">.icon-AIchutu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiazai"></span>
            <div class="name">
              下载
            </div>
            <div class="code-name">.icon-xiazai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiaruzichan"></span>
            <div class="name">
              加入资产
            </div>
            <div class="code-name">.icon-jiaruzichan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-huxing_L"></span>
            <div class="name">
              户型_L
            </div>
            <div class="code-name">.icon-huxing_L
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-huxing_s"></span>
            <div class="name">
              户型_s
            </div>
            <div class="code-name">.icon-huxing_s
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-check"></span>
            <div class="name">
              check
            </div>
            <div class="code-name">.icon-check
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-DeleteFilled1"></span>
            <div class="name">
              DeleteFilled
            </div>
            <div class="code-name">.icon-DeleteFilled1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhongxinbianji"></span>
            <div class="name">
              重新编辑
            </div>
            <div class="code-name">.icon-zhongxinbianji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-caretup"></span>
            <div class="name">
              caretup
            </div>
            <div class="code-name">.icon-caretup
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-closecirle_fill"></span>
            <div class="name">
              closecirle_fill
            </div>
            <div class="code-name">.icon-closecirle_fill
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-caretleft"></span>
            <div class="name">
              caretleft
            </div>
            <div class="code-name">.icon-caretleft
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fight"></span>
            <div class="name">
              fight
            </div>
            <div class="code-name">.icon-fight
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-exclamationcircle_line"></span>
            <div class="name">
              exclamationcircle_line
            </div>
            <div class="code-name">.icon-exclamationcircle_line
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-hide"></span>
            <div class="name">
              hide
            </div>
            <div class="code-name">.icon-hide
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-left"></span>
            <div class="name">
              left
            </div>
            <div class="code-name">.icon-left
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-search"></span>
            <div class="name">
              search
            </div>
            <div class="code-name">.icon-search
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-smarttemplate"></span>
            <div class="name">
              smarttemplate
            </div>
            <div class="code-name">.icon-smarttemplate
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-questioncicle_line"></span>
            <div class="name">
              questioncicle_line
            </div>
            <div class="code-name">.icon-questioncicle_line
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-close1"></span>
            <div class="name">
              close
            </div>
            <div class="code-name">.icon-close1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chec"></span>
            <div class="name">
              chec
            </div>
            <div class="code-name">.icon-chec
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-reset"></span>
            <div class="name">
              reset
            </div>
            <div class="code-name">.icon-reset
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fenxiang"></span>
            <div class="name">
              Share
            </div>
            <div class="code-name">.icon-fenxiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Close_Large"></span>
            <div class="name">
              Close_Large
            </div>
            <div class="code-name">.icon-Close_Large
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Fliter"></span>
            <div class="name">
              筛选Fliter
            </div>
            <div class="code-name">.icon-Fliter
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-FrameSelection"></span>
            <div class="name">
              手动框选Frame Selection
            </div>
            <div class="code-name">.icon-FrameSelection
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-house"></span>
            <div class="name">
              house
            </div>
            <div class="code-name">.icon-house
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Show"></span>
            <div class="name">
              显示Show
            </div>
            <div class="code-name">.icon-Show
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-720"></span>
            <div class="name">
              720
            </div>
            <div class="code-name">.icon-a-720
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fill_down"></span>
            <div class="name">
              arrow_fill_down
            </div>
            <div class="code-name">.icon-fill_down
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fill_up"></span>
            <div class="name">
              arrow_fill_up
            </div>
            <div class="code-name">.icon-fill_up
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-line_down"></span>
            <div class="name">
              arrow_line_down
            </div>
            <div class="code-name">.icon-line_down
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-line_left"></span>
            <div class="name">
              arrow_line_left
            </div>
            <div class="code-name">.icon-line_left
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-info_fill"></span>
            <div class="name">
              info_fill
            </div>
            <div class="code-name">.icon-info_fill
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-caretdown"></span>
            <div class="name">
              caretdown
            </div>
            <div class="code-name">.icon-caretdown
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-caretright"></span>
            <div class="name">
              caretright
            </div>
            <div class="code-name">.icon-caretright
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-anzhuangInstall"></span>
            <div class="name">
              安装Install
            </div>
            <div class="code-name">.icon-anzhuangInstall
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-change_logo"></span>
            <div class="name">
              change_logo
            </div>
            <div class="code-name">.icon-change_logo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-rotate"></span>
            <div class="name">
              rotate
            </div>
            <div class="code-name">.icon-rotate
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-delete"></span>
            <div class="name">
              delete
            </div>
            <div class="code-name">.icon-delete
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-tianchongFace"></span>
            <div class="name">
              感叹号
            </div>
            <div class="code-name">.icon-a-tianchongFace
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-tianchongFace-1"></span>
            <div class="name">
              关闭
            </div>
            <div class="code-name">.icon-a-tianchongFace-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-huifu"></span>
            <div class="name">
              恢复
            </div>
            <div class="code-name">.icon-huifu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chexiao"></span>
            <div class="name">
              撤销
            </div>
            <div class="code-name">.icon-chexiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhuye"></span>
            <div class="name">
              主页
            </div>
            <div class="code-name">.icon-zhuye
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jujiao"></span>
            <div class="name">
              聚焦
            </div>
            <div class="code-name">.icon-jujiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-Property2xuanzhuan45du"></span>
            <div class="name">
              旋转45度
            </div>
            <div class="code-name">.icon-a-Property2xuanzhuan45du
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhuanzhukongjian"></span>
            <div class="name">
              专注空间
            </div>
            <div class="code-name">.icon-zhuanzhukongjian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiezu-2"></span>
            <div class="name">
              解组
            </div>
            <div class="code-name">.icon-jiezu-2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-lidi"></span>
            <div class="name">
              离地
            </div>
            <div class="code-name">.icon-lidi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zuhe-2"></span>
            <div class="name">
              打组
            </div>
            <div class="code-name">.icon-zuhe-2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-niantie"></span>
            <div class="name">
              粘贴
            </div>
            <div class="code-name">.icon-niantie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chicun"></span>
            <div class="name">
              尺寸
            </div>
            <div class="code-name">.icon-chicun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-horizontalflip_line"></span>
            <div class="name">
              horizontalflip_line
            </div>
            <div class="code-name">.icon-horizontalflip_line
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-verflip_line"></span>
            <div class="name">
              verflip_line
            </div>
            <div class="code-name">.icon-verflip_line
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-suoding1"></span>
            <div class="name">
              锁定
            </div>
            <div class="code-name">.icon-suoding1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiesuo1"></span>
            <div class="name">
              解锁
            </div>
            <div class="code-name">.icon-jiesuo1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-sucai"></span>
            <div class="name">
              素材
            </div>
            <div class="code-name">.icon-sucai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-suodingbuju"></span>
            <div class="name">
              锁定布局
            </div>
            <div class="code-name">.icon-suodingbuju
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiesuobuju"></span>
            <div class="name">
              解锁布局
            </div>
            <div class="code-name">.icon-jiesuobuju
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shanchubuju"></span>
            <div class="name">
              删除布局
            </div>
            <div class="code-name">.icon-shanchubuju
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-suodingfengge"></span>
            <div class="name">
              锁定风格
            </div>
            <div class="code-name">.icon-suodingfengge
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiesuofengge"></span>
            <div class="name">
              解锁风格
            </div>
            <div class="code-name">.icon-jiesuofengge
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qingchufengge"></span>
            <div class="name">
              清除风格
            </div>
            <div class="code-name">.icon-qingchufengge
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-TypesucaiStateDefault"></span>
            <div class="name">
              素材
            </div>
            <div class="code-name">.icon-a-TypesucaiStateDefault
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-TypeshuxingStateDefault"></span>
            <div class="name">
              属性
            </div>
            <div class="code-name">.icon-a-TypeshuxingStateDefault
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-TypebujuStateDefault"></span>
            <div class="name">
              布局
            </div>
            <div class="code-name">.icon-a-TypebujuStateDefault
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-TypehuxingStateDefault"></span>
            <div class="name">
              户型
            </div>
            <div class="code-name">.icon-a-TypehuxingStateDefault
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-TypegongjuStateDefault"></span>
            <div class="name">
              工具
            </div>
            <div class="code-name">.icon-a-TypegongjuStateDefault
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-TypefenggeStateDefault"></span>
            <div class="name">
              风格
            </div>
            <div class="code-name">.icon-a-TypefenggeStateDefault
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiantou"></span>
            <div class="name">
              左箭头
            </div>
            <div class="code-name">.icon-jiantou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Search"></span>
            <div class="name">
              Search
            </div>
            <div class="code-name">.icon-Search
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yonghuguanli"></span>
            <div class="name">
              用户管理
            </div>
            <div class="code-name">.icon-yonghuguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhanghaofenxiang"></span>
            <div class="name">
              账号分享
            </div>
            <div class="code-name">.icon-zhanghaofenxiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-lianjiefenxiang"></span>
            <div class="name">
              链接分享
            </div>
            <div class="code-name">.icon-lianjiefenxiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gengduo_bold"></span>
            <div class="name">
              更多_bold
            </div>
            <div class="code-name">.icon-gengduo_bold
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-UnorderedListOutlined"></span>
            <div class="name">
              列表模式
            </div>
            <div class="code-name">.icon-UnorderedListOutlined
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-kapianmoshi"></span>
            <div class="name">
              卡片模式
            </div>
            <div class="code-name">.icon-kapianmoshi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-more"></span>
            <div class="name">
              more
            </div>
            <div class="code-name">.icon-more
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-fangxiangshang"></span>
            <div class="name">
              上
            </div>
            <div class="code-name">.icon-a-fangxiangshang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-fangxiangxia"></span>
            <div class="name">
              下
            </div>
            <div class="code-name">.icon-a-fangxiangxia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-fangxiangzuo"></span>
            <div class="name">
              左
            </div>
            <div class="code-name">.icon-a-fangxiangzuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-fangxiangyou"></span>
            <div class="name">
              右
            </div>
            <div class="code-name">.icon-a-fangxiangyou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon"></span>
            <div class="name">
              close-blod
            </div>
            <div class="code-name">.icon-icon
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-none"></span>
            <div class="name">
              none
            </div>
            <div class="code-name">.icon-none
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Bookmark"></span>
            <div class="name">
              我的方案
            </div>
            <div class="code-name">.icon-Bookmark
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe"></span>
            <div class="name">
              我的户型
            </div>
            <div class="code-name">.icon-xingzhuangjiehe
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-DeleteFilled"></span>
            <div class="name">
              回收站
            </div>
            <div class="code-name">.icon-DeleteFilled
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-diaoding"></use>
                </svg>
                <div class="name">吊顶</div>
                <div class="code-name">#icon-diaoding</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fenqu"></use>
                </svg>
                <div class="name">分区</div>
                <div class="code-name">#icon-fenqu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuxing"></use>
                </svg>
                <div class="name">属性</div>
                <div class="code-name">#icon-shuxing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-puzhuan"></use>
                </svg>
                <div class="name">铺砖</div>
                <div class="code-name">#icon-puzhuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fangda"></use>
                </svg>
                <div class="name">放大</div>
                <div class="code-name">#icon-fangda</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qiang"></use>
                </svg>
                <div class="name">墙</div>
                <div class="code-name">#icon-qiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-buju"></use>
                </svg>
                <div class="name">布局</div>
                <div class="code-name">#icon-buju</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chanpinzhiru"></use>
                </svg>
                <div class="name">加素材</div>
                <div class="code-name">#icon-chanpinzhiru</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fengge"></use>
                </svg>
                <div class="name">风格</div>
                <div class="code-name">#icon-fengge</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xinxi"></use>
                </svg>
                <div class="name">信息</div>
                <div class="code-name">#icon-xinxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhinengdagui"></use>
                </svg>
                <div class="name">智能搭柜</div>
                <div class="code-name">#icon-zhinengdagui</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shaixuan"></use>
                </svg>
                <div class="name">筛选</div>
                <div class="code-name">#icon-shaixuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kaimen"></use>
                </svg>
                <div class="name">开门</div>
                <div class="code-name">#icon-kaimen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-info"></use>
                </svg>
                <div class="name">info</div>
                <div class="code-name">#icon-info</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-info_hover"></use>
                </svg>
                <div class="name">info_hover</div>
                <div class="code-name">#icon-info_hover</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gou1"></use>
                </svg>
                <div class="name">勾</div>
                <div class="code-name">#icon-gou1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shouna"></use>
                </svg>
                <div class="name">收纳</div>
                <div class="code-name">#icon-shouna</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kongjianliyongshuai"></use>
                </svg>
                <div class="name">空间利用率</div>
                <div class="code-name">#icon-kongjianliyongshuai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiajubuju"></use>
                </svg>
                <div class="name">家居布局</div>
                <div class="code-name">#icon-jiajubuju</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingxing"></use>
                </svg>
                <div class="name">星星</div>
                <div class="code-name">#icon-xingxing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dongxian"></use>
                </svg>
                <div class="name">动线</div>
                <div class="code-name">#icon-dongxian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-banxing"></use>
                </svg>
                <div class="name">半星</div>
                <div class="code-name">#icon-banxing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-heart"></use>
                </svg>
                <div class="name">heart</div>
                <div class="code-name">#icon-heart</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-heart-o"></use>
                </svg>
                <div class="name">heartall</div>
                <div class="code-name">#icon-heart-o</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xuanranRender"></use>
                </svg>
                <div class="name">渲染Render</div>
                <div class="code-name">#icon-xuanranRender</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-save"></use>
                </svg>
                <div class="name">save</div>
                <div class="code-name">#icon-save</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yixuan"></use>
                </svg>
                <div class="name">已选</div>
                <div class="code-name">#icon-yixuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-weixuan"></use>
                </svg>
                <div class="name">未选</div>
                <div class="code-name">#icon-weixuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fenxiang1"></use>
                </svg>
                <div class="name">分享</div>
                <div class="code-name">#icon-fenxiang1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gou"></use>
                </svg>
                <div class="name">勾</div>
                <div class="code-name">#icon-gou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-daoshitu"></use>
                </svg>
                <div class="name">导示图</div>
                <div class="code-name">#icon-daoshitu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-baojia"></use>
                </svg>
                <div class="name">预报价</div>
                <div class="code-name">#icon-baojia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tuku"></use>
                </svg>
                <div class="name">图库</div>
                <div class="code-name">#icon-tuku</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lishibanben"></use>
                </svg>
                <div class="name">历史版本</div>
                <div class="code-name">#icon-lishibanben</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tianjiasucai"></use>
                </svg>
                <div class="name">添加素材</div>
                <div class="code-name">#icon-tianjiasucai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-AIchutu"></use>
                </svg>
                <div class="name">AI出图</div>
                <div class="code-name">#icon-AIchutu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiazai"></use>
                </svg>
                <div class="name">下载</div>
                <div class="code-name">#icon-xiazai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiaruzichan"></use>
                </svg>
                <div class="name">加入资产</div>
                <div class="code-name">#icon-jiaruzichan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-huxing_L"></use>
                </svg>
                <div class="name">户型_L</div>
                <div class="code-name">#icon-huxing_L</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-huxing_s"></use>
                </svg>
                <div class="name">户型_s</div>
                <div class="code-name">#icon-huxing_s</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-check"></use>
                </svg>
                <div class="name">check</div>
                <div class="code-name">#icon-check</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-DeleteFilled1"></use>
                </svg>
                <div class="name">DeleteFilled</div>
                <div class="code-name">#icon-DeleteFilled1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhongxinbianji"></use>
                </svg>
                <div class="name">重新编辑</div>
                <div class="code-name">#icon-zhongxinbianji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-caretup"></use>
                </svg>
                <div class="name">caretup</div>
                <div class="code-name">#icon-caretup</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-closecirle_fill"></use>
                </svg>
                <div class="name">closecirle_fill</div>
                <div class="code-name">#icon-closecirle_fill</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-caretleft"></use>
                </svg>
                <div class="name">caretleft</div>
                <div class="code-name">#icon-caretleft</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fight"></use>
                </svg>
                <div class="name">fight</div>
                <div class="code-name">#icon-fight</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-exclamationcircle_line"></use>
                </svg>
                <div class="name">exclamationcircle_line</div>
                <div class="code-name">#icon-exclamationcircle_line</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-hide"></use>
                </svg>
                <div class="name">hide</div>
                <div class="code-name">#icon-hide</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-left"></use>
                </svg>
                <div class="name">left</div>
                <div class="code-name">#icon-left</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-search"></use>
                </svg>
                <div class="name">search</div>
                <div class="code-name">#icon-search</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-smarttemplate"></use>
                </svg>
                <div class="name">smarttemplate</div>
                <div class="code-name">#icon-smarttemplate</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-questioncicle_line"></use>
                </svg>
                <div class="name">questioncicle_line</div>
                <div class="code-name">#icon-questioncicle_line</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-close1"></use>
                </svg>
                <div class="name">close</div>
                <div class="code-name">#icon-close1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chec"></use>
                </svg>
                <div class="name">chec</div>
                <div class="code-name">#icon-chec</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-reset"></use>
                </svg>
                <div class="name">reset</div>
                <div class="code-name">#icon-reset</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fenxiang"></use>
                </svg>
                <div class="name">Share</div>
                <div class="code-name">#icon-fenxiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Close_Large"></use>
                </svg>
                <div class="name">Close_Large</div>
                <div class="code-name">#icon-Close_Large</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Fliter"></use>
                </svg>
                <div class="name">筛选Fliter</div>
                <div class="code-name">#icon-Fliter</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-FrameSelection"></use>
                </svg>
                <div class="name">手动框选Frame Selection</div>
                <div class="code-name">#icon-FrameSelection</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-house"></use>
                </svg>
                <div class="name">house</div>
                <div class="code-name">#icon-house</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Show"></use>
                </svg>
                <div class="name">显示Show</div>
                <div class="code-name">#icon-Show</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-720"></use>
                </svg>
                <div class="name">720</div>
                <div class="code-name">#icon-a-720</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fill_down"></use>
                </svg>
                <div class="name">arrow_fill_down</div>
                <div class="code-name">#icon-fill_down</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fill_up"></use>
                </svg>
                <div class="name">arrow_fill_up</div>
                <div class="code-name">#icon-fill_up</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-line_down"></use>
                </svg>
                <div class="name">arrow_line_down</div>
                <div class="code-name">#icon-line_down</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-line_left"></use>
                </svg>
                <div class="name">arrow_line_left</div>
                <div class="code-name">#icon-line_left</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-info_fill"></use>
                </svg>
                <div class="name">info_fill</div>
                <div class="code-name">#icon-info_fill</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-caretdown"></use>
                </svg>
                <div class="name">caretdown</div>
                <div class="code-name">#icon-caretdown</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-caretright"></use>
                </svg>
                <div class="name">caretright</div>
                <div class="code-name">#icon-caretright</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-anzhuangInstall"></use>
                </svg>
                <div class="name">安装Install</div>
                <div class="code-name">#icon-anzhuangInstall</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-change_logo"></use>
                </svg>
                <div class="name">change_logo</div>
                <div class="code-name">#icon-change_logo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-rotate"></use>
                </svg>
                <div class="name">rotate</div>
                <div class="code-name">#icon-rotate</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-delete"></use>
                </svg>
                <div class="name">delete</div>
                <div class="code-name">#icon-delete</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-tianchongFace"></use>
                </svg>
                <div class="name">感叹号</div>
                <div class="code-name">#icon-a-tianchongFace</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-tianchongFace-1"></use>
                </svg>
                <div class="name">关闭</div>
                <div class="code-name">#icon-a-tianchongFace-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-huifu"></use>
                </svg>
                <div class="name">恢复</div>
                <div class="code-name">#icon-huifu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chexiao"></use>
                </svg>
                <div class="name">撤销</div>
                <div class="code-name">#icon-chexiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhuye"></use>
                </svg>
                <div class="name">主页</div>
                <div class="code-name">#icon-zhuye</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jujiao"></use>
                </svg>
                <div class="name">聚焦</div>
                <div class="code-name">#icon-jujiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-Property2xuanzhuan45du"></use>
                </svg>
                <div class="name">旋转45度</div>
                <div class="code-name">#icon-a-Property2xuanzhuan45du</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhuanzhukongjian"></use>
                </svg>
                <div class="name">专注空间</div>
                <div class="code-name">#icon-zhuanzhukongjian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiezu-2"></use>
                </svg>
                <div class="name">解组</div>
                <div class="code-name">#icon-jiezu-2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lidi"></use>
                </svg>
                <div class="name">离地</div>
                <div class="code-name">#icon-lidi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zuhe-2"></use>
                </svg>
                <div class="name">打组</div>
                <div class="code-name">#icon-zuhe-2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-niantie"></use>
                </svg>
                <div class="name">粘贴</div>
                <div class="code-name">#icon-niantie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chicun"></use>
                </svg>
                <div class="name">尺寸</div>
                <div class="code-name">#icon-chicun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-horizontalflip_line"></use>
                </svg>
                <div class="name">horizontalflip_line</div>
                <div class="code-name">#icon-horizontalflip_line</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-verflip_line"></use>
                </svg>
                <div class="name">verflip_line</div>
                <div class="code-name">#icon-verflip_line</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-suoding1"></use>
                </svg>
                <div class="name">锁定</div>
                <div class="code-name">#icon-suoding1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiesuo1"></use>
                </svg>
                <div class="name">解锁</div>
                <div class="code-name">#icon-jiesuo1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-sucai"></use>
                </svg>
                <div class="name">素材</div>
                <div class="code-name">#icon-sucai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-suodingbuju"></use>
                </svg>
                <div class="name">锁定布局</div>
                <div class="code-name">#icon-suodingbuju</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiesuobuju"></use>
                </svg>
                <div class="name">解锁布局</div>
                <div class="code-name">#icon-jiesuobuju</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shanchubuju"></use>
                </svg>
                <div class="name">删除布局</div>
                <div class="code-name">#icon-shanchubuju</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-suodingfengge"></use>
                </svg>
                <div class="name">锁定风格</div>
                <div class="code-name">#icon-suodingfengge</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiesuofengge"></use>
                </svg>
                <div class="name">解锁风格</div>
                <div class="code-name">#icon-jiesuofengge</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qingchufengge"></use>
                </svg>
                <div class="name">清除风格</div>
                <div class="code-name">#icon-qingchufengge</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-TypesucaiStateDefault"></use>
                </svg>
                <div class="name">素材</div>
                <div class="code-name">#icon-a-TypesucaiStateDefault</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-TypeshuxingStateDefault"></use>
                </svg>
                <div class="name">属性</div>
                <div class="code-name">#icon-a-TypeshuxingStateDefault</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-TypebujuStateDefault"></use>
                </svg>
                <div class="name">布局</div>
                <div class="code-name">#icon-a-TypebujuStateDefault</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-TypehuxingStateDefault"></use>
                </svg>
                <div class="name">户型</div>
                <div class="code-name">#icon-a-TypehuxingStateDefault</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-TypegongjuStateDefault"></use>
                </svg>
                <div class="name">工具</div>
                <div class="code-name">#icon-a-TypegongjuStateDefault</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-TypefenggeStateDefault"></use>
                </svg>
                <div class="name">风格</div>
                <div class="code-name">#icon-a-TypefenggeStateDefault</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiantou"></use>
                </svg>
                <div class="name">左箭头</div>
                <div class="code-name">#icon-jiantou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Search"></use>
                </svg>
                <div class="name">Search</div>
                <div class="code-name">#icon-Search</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yonghuguanli"></use>
                </svg>
                <div class="name">用户管理</div>
                <div class="code-name">#icon-yonghuguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhanghaofenxiang"></use>
                </svg>
                <div class="name">账号分享</div>
                <div class="code-name">#icon-zhanghaofenxiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lianjiefenxiang"></use>
                </svg>
                <div class="name">链接分享</div>
                <div class="code-name">#icon-lianjiefenxiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gengduo_bold"></use>
                </svg>
                <div class="name">更多_bold</div>
                <div class="code-name">#icon-gengduo_bold</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-UnorderedListOutlined"></use>
                </svg>
                <div class="name">列表模式</div>
                <div class="code-name">#icon-UnorderedListOutlined</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kapianmoshi"></use>
                </svg>
                <div class="name">卡片模式</div>
                <div class="code-name">#icon-kapianmoshi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-more"></use>
                </svg>
                <div class="name">more</div>
                <div class="code-name">#icon-more</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-fangxiangshang"></use>
                </svg>
                <div class="name">上</div>
                <div class="code-name">#icon-a-fangxiangshang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-fangxiangxia"></use>
                </svg>
                <div class="name">下</div>
                <div class="code-name">#icon-a-fangxiangxia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-fangxiangzuo"></use>
                </svg>
                <div class="name">左</div>
                <div class="code-name">#icon-a-fangxiangzuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-fangxiangyou"></use>
                </svg>
                <div class="name">右</div>
                <div class="code-name">#icon-a-fangxiangyou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon"></use>
                </svg>
                <div class="name">close-blod</div>
                <div class="code-name">#icon-icon</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-none"></use>
                </svg>
                <div class="name">none</div>
                <div class="code-name">#icon-none</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Bookmark"></use>
                </svg>
                <div class="name">我的方案</div>
                <div class="code-name">#icon-Bookmark</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe"></use>
                </svg>
                <div class="name">我的户型</div>
                <div class="code-name">#icon-xingzhuangjiehe</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-DeleteFilled"></use>
                </svg>
                <div class="name">回收站</div>
                <div class="code-name">#icon-DeleteFilled</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
