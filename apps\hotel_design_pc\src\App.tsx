import React, { Suspense, lazy, useState } from 'react';
import { Route, Routes, Navigate, BrowserRouter } from 'react-router-dom';
import '@svg/antd/es/style/reset.css';
import { ConfigProvider, getSvgTokenConfig } from '@svg/antd';
import type { ConfigProviderProps } from '@svg/antd';

/**
 * @description 获取基础路由
 */
const getBasename = () => {
  if (process.env.NODE_ENV === 'development') {
    return '';
  }
  const pathname = window.location.pathname || '';
  if (window.location.hostname?.indexOf('miniapp') > -1) {
    const pathnameArr = pathname.split('/');
    return pathnameArr[1];
  }
  return pathname.substring(1, pathname.lastIndexOf('/'));
}
const Hotel = React.lazy(()=>import('@/pages/Hotel/hotel'));

function App() {
  const basename = getBasename();
  const [token, setToken] = useState<ConfigProviderProps>(getSvgTokenConfig('light'));

  return (
    <div className="App">
     <ConfigProvider {...token}>
        <BrowserRouter basename={basename}>
          <Suspense>
            <Routes>
              <Route path="*" element={<Navigate to="/Hotel" />} />
              <Route path="/Hotel" element={<Hotel />}/>
            </Routes>
          </Suspense>
        </BrowserRouter>
      </ConfigProvider>
    </div>
  )
}
 
export default App;

