{"name": "layout_package_app", "private": true, "scripts": {"build": "pnpm --filter model3d_api run build", "buildIndex": "pnpm --filter z_polygon run buildIndex", "build-model3d": "pnpm --filter msg_center run build && pnpm --filter model3d_api run build && pnpm --filter modelviewer run copy_model3d  && pnpm --filter modelviewer run dev", "update-pc": "pnpm --filter basic_data run update && pnpm --filter z_polygon run update && pnpm --filter basic_request run update && pnpm --filter series_matcher run update && pnpm --filter design_domain run update && pnpm --filter design_3d run update && pnpm --filter design_2d run update"}, "devDependencies": {"@types/node": "^22.15.21", "vite-plugin-dts": "^4.5.4"}}