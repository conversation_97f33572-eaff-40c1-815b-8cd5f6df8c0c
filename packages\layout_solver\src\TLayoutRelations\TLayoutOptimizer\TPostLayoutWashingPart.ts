import { TGroupTemplate, TRoom } from "@layoutai/layout_scheme";
import { ZEdge, ZRect, compareNames } from "@layoutai/z_polygon";


export class TPostLayoutWashingPart 
{
    private static _instance : TPostLayoutWashingPart = null;

    constructor()
    {

    }
    static get instance()
    {
        if(!TPostLayoutWashingPart._instance)
        {
            TPostLayoutWashingPart._instance = new TPostLayoutWashingPart();
        }
        return TPostLayoutWashingPart._instance;
    }

    postInfillLayout(room:TRoom,group_templates:TGroupTemplate[]=null)
    {
        if(!compareNames([room.roomname],["卫生间"])) return false;
        if(!group_templates)
        {
            group_templates = TGroupTemplate.extractGroupTemplates(room._furniture_list,"卫生间");
        }

        // 先清空先有的淋浴房
        let shower_areas = group_templates.filter(((val)=>compareNames([val.group_space_category],["淋浴房"])));
        shower_areas.forEach((gt)=>{
            let id = group_templates.indexOf(gt);
            if(id>=0) group_templates.splice(id,1);
        });
        shower_areas = [];

        let shower_head = group_templates.find((val)=>compareNames([val.group_space_category],["花洒"]));

        if(!shower_head) return false;

        let side_wall_edge : ZEdge = null;
        let side_wall_dist : number = 600;
        let shower_head_rect = shower_head._target_rect;


        let front_wall_edge : ZEdge = null;
        let front_wall_dist : number = 10000;
        let back_wall_edge : ZEdge = null;
        let back_wall_dist : number = 10000;


        /**
         *  先计算前、后、侧都有哪些边
         */
        let update_side_edges = ()=>{
            
            front_wall_edge  = null;
            front_wall_dist  = 10000;
            back_wall_edge  = null;
            back_wall_dist  = 10000;
            room.room_shape._feature_shape._w_poly.edges.forEach(edge=>{
                
                
                if(edge.islayOn(shower_head_rect.leftEdge,600,0.5)|| edge.islayOn(shower_head_rect.rightEdge,600,0.3))
                {
                    let dist = shower_head_rect.project(edge.center).x;
                    if(!side_wall_edge || Math.abs(dist)<Math.abs(side_wall_dist))
                    {
                        side_wall_edge = edge;
                        side_wall_dist = dist;
                    }
                }
    
                if(shower_head_rect.checkSameNormal(edge.nor,false,0.5) && edge.islayOn( shower_head_rect.frontEdge, front_wall_dist+1,0.3))
                {
                    let dist = shower_head_rect.project(edge.center,true).y; // 用背靠中心距离
                    if(!front_wall_edge || (dist>0 &&dist < front_wall_dist))
                    {
                        front_wall_edge = edge;
                        front_wall_dist = dist;
                    }
                }
    
                if(shower_head_rect.checkSameNormal(edge.nor.clone().negate(),false,0.5) && edge.islayOn( shower_head_rect.backEdge, back_wall_dist+1,0.3))
                {
                    let dist = shower_head_rect.project(edge.center,true).y; // 用背靠中心距离
                    if(!back_wall_edge ||  Math.abs(dist) < Math.abs(back_wall_dist))
                    {
                        back_wall_edge = edge;
                        back_wall_dist = dist;
                    }
                }
            });
    
        }

        update_side_edges();
        if(!side_wall_edge) return false; // 只有侧靠墙的时候，才会有需要添加淋浴房


        if(room.windows)
        {
            let check_layon_window = ()=>{
                return room.windows.find((win)=>{
                    let rect = win.rect;
                    if(!rect) return false;
                    if(win.type!=="Window") return false;
                    if(shower_head_rect.backEdge.islayOn(rect.frontEdge,rect.h+100,0.01))
                    {
                        return true;
                    }

                    return false;
                })
            }
            let layon_win = check_layon_window();

            if(layon_win && back_wall_edge)
            {
                let pos = shower_head_rect.unproject({x:0,y:-shower_head_rect.h/2+450});

                let pp = side_wall_edge.projectEdge2d(pos);

                pos = side_wall_edge.unprojectEdge2d({x:pp.x,y:0});

                shower_head_rect.nor = side_wall_edge.nor.clone().negate();
                // shower_head_rect.u_dv = back_wall_edge.nor.clone().negate();
                shower_head_rect.back_center = pos;

                shower_head_rect.updateRect();

                shower_head.updateByTargetRect();
                update_side_edges();

                if(back_wall_edge)
                {
                    let pp = back_wall_edge.projectEdge2d(shower_head_rect.back_center);

                    if(pp.x < 450){
                        pp.x = 450;
                    }
                    if(pp.x > back_wall_edge.length - 450)
                    {
                        pp.x = back_wall_edge.length- 450;
                    }

                    shower_head_rect.back_center = back_wall_edge.unprojectEdge2d({x:pp.x,y:0});
                    shower_head_rect.updateRect();
                }
                update_side_edges();


            }
        }

        let front_area_dist : number = front_wall_dist;


        let door_entrance_side : number = 1;
        let shower_area_rect = shower_head_rect.clone();
        shower_area_rect._w = 900;
        shower_area_rect._h = 900;
        shower_area_rect.updateRect();
        if(room.windows)
        {
            // 如果背靠窗, 需要调整花洒的位置
            room.windows.forEach(win=>{
                let rect = win.rect;
                if(!rect) return;
                if(win.type !== "Door") return;

                rect = rect.clone();
                let r_center = rect.rect_center;
                rect._h += 600 * 2;  // 两侧各600
                rect.rect_center = r_center;

                rect.edges.forEach(win_edge=>{
                    if(win_edge.islayOn(shower_area_rect.frontEdge,3000,0.01))
                    {
                        let dist = shower_area_rect.project(win_edge.center,true).y;
                        if(dist > 10 && dist < front_area_dist) front_area_dist = dist;

                    }

                })

            })

        }
    
         group_templates.forEach((group_template)=>{
            if(compareNames([group_template.group_space_category],["浴室柜","马桶"]))
            {
                if(group_template._target_rect.frontEdge.islayOn(shower_area_rect.frontEdge,3000,0.01))
                {
                    let dist = shower_area_rect.project(group_template._target_rect.frontEdge.center,true).y;
                    if(dist > 0 && dist < front_area_dist) front_area_dist = dist;
                    return true;
                }
            }
            return false;
        });

        room.windows && room.windows.forEach((door)=>{
            if(door.type !== "Door") return;
            let pp = shower_head_rect.project( door.rect.rect_center);

            door_entrance_side = pp.x > 0?1:-1;
        });



        let other_side_flag = side_wall_dist < 0?1:-1;

        if(front_area_dist < front_wall_dist - 1) // 用钻石型淋浴房为主
        {
            if(front_area_dist > 1000 && side_wall_edge.length > 600)
            {
                let t_rect = new ZRect(1000,1000);
                t_rect.back_center = shower_head_rect.unproject({x:side_wall_dist+t_rect.w/2*other_side_flag,y:-shower_head_rect.h/2});
                t_rect.nor = shower_head_rect.nor;
                t_rect.u_dv = side_wall_edge.nor.clone();
    
                let t_template = TGroupTemplate.makeGroupTemplateByGroupSpaceCategory("钻石形淋浴房区","卫生间",t_rect);
    
                if(t_template)
                {
                    group_templates.push(t_template);
                }
            }

        }
        else{
            if(front_wall_dist > 1100)
            {

                let t_rect = new ZRect(front_wall_dist,70);


                t_rect.back_center = shower_head_rect.unproject({x:Math.max(450,shower_head_rect.w/2)*other_side_flag,y:-shower_head_rect.h/2+front_wall_dist/2})
    
                t_rect.nor = shower_head_rect.dv.clone().multiplyScalar(door_entrance_side);

                t_rect.u_dv = shower_area_rect.nor;

                let inside_toilet = group_templates.find((template)=>{

                    if(!compareNames([template.group_space_category],["浴室柜","马桶"])) return false;
                    let rect = template._target_rect;

                    let pp = t_rect.project(rect.rect_center);

                    if(pp.x > 0) 
                    {
                        return true;
                    }
                    return false;
                });

                if(!inside_toilet)
                {
                    let t_template = TGroupTemplate.makeGroupTemplateByGroupSpaceCategory("一字形淋浴房区","卫生间",t_rect);


                    if(t_template)
                    {
                        group_templates.push(t_template);
                    }
                } 


            }

        }

        return group_templates;




    }
}