import { LayoutAI_App, LayoutAI_Events,EventName} from '@layoutai/layout_scheme';
import { Input, Select } from '@svg/antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import useStyles from './style/index';

/**
 * @description 墙体的顶部菜单
 */
const TopMenuBar: React.FC = () => {
  const { t } = useTranslation()
  const { styles } = useStyles();
  const { Option } = Select
  const [inputValue, setInputValue] = useState("240");
  const [lineValue, setLineValue] = useState(t("中心线"));
  const selectChange = (item: string) => {
    setInputValue(item);
  }

  const inputChange = (e: any) => {
    setInputValue(e.target.value);
  }

  useEffect(() => {
    LayoutAI_App.DispatchEvent(LayoutAI_Events.setTopWallMenuProps, { width: inputValue, lineValue: lineValue });
  }, [inputValue, lineValue]);

  const selectAfter = (
    <Select popupClassName={styles.popupClass} placement={'bottomRight'} defaultValue="" value='' onChange={selectChange}>
      <Option value="60">60</Option>
      <Option value="100">100</Option>
      <Option value="120">120</Option>
      <Option value="240">240</Option>
      <Option value="300">300</Option>
      <Option value="400">400</Option>
    </Select>
  )
  useEffect(() => {

  }, []);
  return (
    <div className={styles.container}>
      <span className={styles.label}>{t("定位线")}</span>
      <Select style={{ width: 80 }} size={'small'} value={lineValue} onChange={(value: any) => {
        setLineValue(value);
      }}
        options={[
          { value: t('外线'), label: t('外线') },
          { value: t('中心线'), label: t('中心线') },
          { value: t('内线'), label: t('内线') },
        ]}
      >

      </Select>

      <span className={styles.label}>{t('墙厚')}</span>
      <Input
        value={inputValue}
        size={'small'}
        style={{ width: 90 }}
        addonAfter={selectAfter}
        className={styles.wallInput}
        onChange={inputChange}
        defaultValue="" />
    </div>
  );
};

export default TopMenuBar;
