/**
 * @description 获取浏览器默认语言
 * @return string
 */
export const getBrowserLang = () => {
  const navigator :any = window.navigator
  const browserLang = navigator.language ? navigator.language : navigator.browserLanguage
  if (
    browserLang.toLowerCase() === 'cn' ||
    browserLang.toLowerCase() === 'zh' ||
    browserLang.toLowerCase() === 'zh-cn'
  ) {
    return 'zh_CN'
  } else {
    return 'en_US'
  }
}
