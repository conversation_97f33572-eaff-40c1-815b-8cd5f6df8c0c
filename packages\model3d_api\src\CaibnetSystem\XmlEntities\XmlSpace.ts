import { I_XmlSpace } from "../IWardrobeEntityInterface";
import { NumberVariable } from "../NumberVariable";
import { XmlEntityBase } from "../XmlEntityBase";

export class XmlSpace extends XmlEntityBase implements I_XmlSpace {
    oldSpaceUidS?: string;
    splitDirN?: number;
    splitRatioS?: string;
    expandsU?: string;
    splitUnitS?: string;
    splitValueS?: string;
    splitUListS?: string;
    splitVListS?: string;
    splitUExpressS?: string;
    splitVExpressS?: string;
    CWhSpaceSplitComponent?: {
        SubNodeCWhSpaceSplit?: {
            indexN?: number;
            boardUisS?: string;
        };
    };
    /**
     *  当splitDirN 为 4 的时候,
     *    --- 分裂出的子空间考虑arroundIndexS
     *    --- arroundIndexS
     *      --- 2: 左侧---W -= thickness, PX += thickness
     *      --- 1: 右侧---W -= thickness
     *      --- 5: 顶部---H -= thickness
     *      --- 4: 深度横截 产生两个子空间:
     *        ---  深度depth_offset: thickness +  缝隙
     *        ---  子空间一:  D -= depth_offset, PY - depth_offset
     *        ---  子空间二:  D -= thickness + 1
     *      --- 6: 底部---H-thickness, PZ + thickness
     * 
     */
    CWhSpaceArroundComponent?: {
        arroundIndexS?: string;
    };

    CWhTagComponent?: {
        CWhTagFrameSpace?: Record<string, unknown>;
        CWhTagBackBoard?: {
            bbEnumS?: string;
            backDataUidS?: string;
        };
    };
    childSpaces?: XmlSpace[];
    constructor(data?: Partial<I_XmlSpace>) {
        super(data);
        this.CWhSpaceArroundComponent = data.CWhSpaceArroundComponent || {};
        this.CWhTagComponent = data.CWhTagComponent || null;
        this.CWhSpaceSplitComponent = data.CWhSpaceSplitComponent || {};
        this.oldSpaceUidS = data.oldSpaceUidS || "";
        this.splitDirN = data.splitDirN || 0;
        this.splitRatioS = data.splitRatioS || "";
        this.expandsU = data.expandsU || "";
        this.splitVExpressS = data.splitVExpressS || "";
        this.splitUExpressS = data.splitUExpressS || "";
        this.splitUListS = data.splitUListS || "";
        this.splitVListS = data.splitVListS || "";
        this.splitValueS = data.splitValueS || "";
        this.splitUnitS = data.splitUnitS || "";
        if (data) {
            if (data.childSpaces) {
                this.childSpaces = data.childSpaces.map(s => new XmlSpace(s));
                this.childSpaces.forEach((space)=>space.parent = this);
            }
        }
        this.updateValueObject();
    }

}
