import { I_XmlCFaceBoardEntity } from "../IWardrobeEntityInterface";
import { XmlEntityBase } from "../XmlEntityBase";


export class XmlCFaceBoardEntity extends XmlEntityBase implements I_XmlCFaceBoardEntity {
    MaterialComponent?: {
        materialMapVoIdS?: string;
        isRotateN?: number;
        oriMaterialMapVoIdS?: string;
    };
    PointDef?: {
        posXS?: string;
        posYS?: string;
        typeS?: string;
        centerXS?: string;
        centerYS?: string;
        angleS?: string;
        sectionS?: string;
    }[];

    constructor(data?: Partial<I_XmlCFaceBoardEntity>) {
        super(data);
        this.MaterialComponent = data.MaterialComponent || {};
        this.PointDef = [];
        if(data.PointDef)
        {
            if(Array.isArray(data.PointDef))
            {
                this.PointDef = data.PointDef.map((data)=>{return {...data}});
            }
            else{
                console.log(data);
            }
        }
    }
}
XmlEntityBase.Generators["CFaceBoardEntity"] = (data:I_XmlCFaceBoardEntity)=>new XmlCFaceBoardEntity(data);