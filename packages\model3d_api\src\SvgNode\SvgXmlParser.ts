import { ICabinetPart, IPartValues, ISubPart, IValueExpressions, IVariable, IVariableOption, TPartType } from "./SvgXmlInterface";

/**
 * 完整的XML到ICabinetPart转换器（使用DOMParser）
 * @param xmlStr XML字符串
 */
export function parseCabinetPart(xmlStr: string): ICabinetPart {
    const parser = new DOMParser();
    const doc = parser.parseFromString(xmlStr, "text/xml");
    
    // 基础错误检查
    const errors = doc.getElementsByTagName("parsererror");
    if (errors.length > 0) {
      throw new Error("XML解析错误：" + errors[0].textContent);
    }
  
    const partElement = doc.documentElement;
    if (!partElement || partElement.tagName !== "Part") {
      throw new Error("无效的XML结构：缺少Part根节点");
    }
  
    // 主转换函数
    return {
      // 基础属性
      areaExpression: getStringAttr(partElement, "areaExpression"),
      lengthExpression: getStringAttr(partElement, "lengthExpression"),
      name: getStringAttr(partElement, "name") || "",
      nameExpression: getStringAttr(partElement, "nameExpression"),
      type: getPartType(partElement),
      // 业务属性
      canAddAccessory: getBooleanAttr(partElement, "canAddAccessory"),
      limitName: getStringAttr(partElement, "limitName"),
      partNumber: getStringAttr(partElement, "partNumber"),
      uid: getStringAttr(partElement, "uid"),
      standardCategory: getStringAttr(partElement, "standardCategory"),
      placeRule: getStringAttr(partElement, "placeRule"),
      isQuote: getBooleanAttr(partElement, "isQuote"),
      isMovable: getBooleanAttr(partElement, "isMovable"),
      isDeleteAble: getBooleanAttr(partElement, "isDeleteAble"),
      isSplit: getBooleanAttr(partElement, "isSplit"),
      isQuoteSplit: getBooleanAttr(partElement, "isQuoteSplit"),
      isOutsourcing: getBooleanAttr(partElement, "isOutsourcing"),
      solidWood: getSolidWoodType(partElement),
      specialVariableGroups: getStringAttr(partElement, "specialVariableGroups"),
      version: getStringAttr(partElement, "version"),
  
      // 复杂结构
      Variables: parseVariables(partElement),
      RelateInfo: parseRelateInfo(partElement),
      EdgeInfo: parseEdgeInfo(partElement),
      Part: parseSubParts(partElement),
      Logs: parseLogs(partElement)
    };
  }
  
  // ==================== 辅助工具函数 ====================
  
  
  /** 获取可选字符串属性 */
  function getStringAttr(element: Element, attr: string): string | undefined {
    return element.getAttribute(attr) ?? undefined;
  }
  
  /** 获取布尔属性 */
  function getBooleanAttr(element: Element, attr: string): boolean | undefined {
    const value = element.getAttribute(attr);
    return value ? value === "true" : undefined;
  }
  
  /** 解析部件类型 */
  function getPartType(element: Element): TPartType {
    const type = element.getAttribute("type");
    return type || "";
  }
  
  /** 解析solidWood类型 */
  function getSolidWoodType(element: Element): 0 | 1 | undefined {
    const value = element.getAttribute("solidWood");
    if (value === "0") return 0;
    if (value === "1") return 1;
    return undefined;
  }
  
  // ==================== 复杂结构解析 ====================
  
  /** 解析变量集合 */
  function parseVariables(element: Element): { Variable: IVariable[] } | undefined {
    const varsElement = element.getElementsByTagName("Variables")[0];
    if (!varsElement) return undefined;
  
    const variables = Array.from(varsElement.getElementsByTagName("Variable")).map(varElement => ({
      name: getStringAttr(varElement, "name") || "",
      value: getNumberAttr(varElement, "value"),
      valueExpression: getStringAttr(varElement, "valueExpression"),
      unitType: getNumberAttr(varElement, "unitType") as 0 | 1 | 2 | 3,
      minValue: getNumberAttr(varElement, "minValue"),
      maxValue: getNumberAttr(varElement, "maxValue"),
      minValueExpress: getStringOrNumberAttr(varElement, "minValueExpress"),
      maxValueExpress: getStringOrNumberAttr(varElement, "maxValueExpress"),
      isShowSize: getNumberAttr(varElement, "isShowSize") as 0 | 1 | undefined,
      alias: getStringAttr(varElement, "alias"),
      isGlobal: getNumberAttr(varElement, "isGlobal") as 0 | 1 | undefined,
      ValueArray: parseValueArray(varElement)
    }));
  
    return { Variable: variables };
  }
  
  /** 解析值选项数组 */
  function parseValueArray(element: Element): { ExpOption: IVariableOption[] } | undefined {
    const arrayElement = element.getElementsByTagName("ValueArray")[0];
    if (!arrayElement) return undefined;
  
    const options = Array.from(arrayElement.getElementsByTagName("ExpOption")).map(optElement => ({
      value: getStringAttr(optElement, "value") || "",
      alias: getStringAttr(optElement, "alias")
    }));
  
    return { ExpOption: options };
  }
  
  /** 解析关联信息 */
  function parseRelateInfo(element: Element): Record<string, unknown> | undefined {
    // 根据实际业务需求实现
    return undefined;
  }
  
  /** 解析边缘信息 */
  function parseEdgeInfo(element: Element): { fatEdgeStart: -1 | number; fatEdgeEnd: -1 | number } | undefined {
    const edgeElement = element.getElementsByTagName("EdgeInfo")[0];
    if (!edgeElement) return undefined;
  
    return {
      fatEdgeStart: getEdgeValue(edgeElement, "fatEdgeStart"),
      fatEdgeEnd: getEdgeValue(edgeElement, "fatEdgeEnd")
    };
  }
  
  /** 解析子部件集合 */
  function parseSubParts(element: Element): ISubPart[] | undefined {
    const parts = Array.from(element.getElementsByTagName("Part"));
    if (parts.length === 0) return undefined;
  
    return parts.map(partElement => ({
      materialId: getNumberAttr(partElement, "materialId"),
      isQuote: getBooleanAttr(partElement, "isQuote"),
      isMovable: getBooleanAttr(partElement, "isMovable"),
      isDeleteAble: getBooleanAttr(partElement, "isDeleteAble"),
      limitName: getStringAttr(partElement, "limitName"),
      materialMapVoId: getNumberAttr(partElement, "materialMapVoId"),
      standardCategory: getStringAttr(partElement, "standardCategory"),
      isSplit: getSplitType(partElement),
      isQuoteSplit: getSplitType(partElement, "isQuoteSplit"),
      isOutsourcing: getSplitType(partElement, "isOutsourcing"),
      isNotPutOutCabinetNum: getBooleanAttr(partElement, "isNotPutOutCabinetNum"),
      canAddAccessory: getBooleanAttr(partElement, "canAddAccessory"),
      uid: getStringAttr(partElement, "uid"),
      isReversePlane: getNumberAttr(partElement, "isReversePlane") as 0 | 1 | undefined,
      ValueExpressions: parseValueExpressions(partElement),
      Values: parseValues(partElement),
      EdgeInfo: parsePartEdgeInfo(partElement)
    }));
  }
  
  /** 解析日志信息 */
  function parseLogs(element: Element): { userID: string; version: string } | undefined {
    const logsElement = element.getElementsByTagName("Logs")[0];
    if (!logsElement) return undefined;
  
    return {
      userID: getStringAttr(logsElement, "userID") || "",
      version: getStringAttr(logsElement, "version") || ""
    };
  }
  
  // ==================== 次级解析工具 ====================
  
  /** 解析边缘值（支持-1） */
  function getEdgeValue(element: Element, attr: string): -1 | number {
    const value = element.getAttribute(attr);
    return value === "-1" ? -1 : Number(value) || 0;
  }
  
  /** 解析分裂类型（支持布尔和-1/0/1） */
  function getSplitType(element: Element, attr = "isSplit"): boolean | -1 | 0 | 1 | undefined {
    const value = element.getAttribute(attr);
    if (value === "true") return true;
    if (value === "false") return false;
    if (value === "-1") return -1;
    if (value === "1") return 1;
    if (value === "0") return 0;
    return undefined;
  }
  
  /** 解析数值属性 */
  function getNumberAttr(element: Element, attr: string): number | undefined {
    const value = element.getAttribute(attr);
    return value ? Number(value) : undefined;
  }
  
  /** 解析字符串或数值属性 */
  function getStringOrNumberAttr(element: Element, attr: string): string | number | undefined {
    const value = element.getAttribute(attr);
    if (!value) return undefined;
    return isNaN(Number(value)) ? value : Number(value);
  }
  
  /** 解析值表达式 */
  function parseValueExpressions(element: Element): IValueExpressions | undefined {
    const exprElement = element.getElementsByTagName("ValueExpressions")[0];
    if (!exprElement) return undefined;
  
    let attributeNames = exprElement.getAttributeNames();
    let result : IValueExpressions = {};
    attributeNames.forEach((name)=>{
        result[name] = getStringAttr(exprElement,name);
    })
    return result;
  }
  
  /** 解析实际尺寸值 */
  function parseValues(element: Element): IPartValues | undefined {
    const valuesElement = element.getElementsByTagName("Values")[0];
    if (!valuesElement) return undefined;
    let result : IPartValues = {};
    let attributeNames = valuesElement.getAttributeNames();
    attributeNames.forEach((name)=>{
        result[name] =getStringOrNumberAttr(valuesElement,name);
    })
    return result;
  }
  
  /** 解析子部件边缘信息 */
  function parsePartEdgeInfo(element: Element): { edgeModifyByUser: 0 | 1 } | undefined {
    const edgeElement = element.getElementsByTagName("EdgeInfo")[0];
    if (!edgeElement) return undefined;
  
    const value = getNumberAttr(edgeElement, "edgeModifyByUser");
    return value === 0 || value === 1 ? { edgeModifyByUser: value } : undefined;
  }