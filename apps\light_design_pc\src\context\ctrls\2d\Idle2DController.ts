import { ControllerBase, type Design2DContext } from "@layoutai/design_2d";
import { DesignControllerType } from "../DesignControllerType";
import { Vector2 } from "three";

/**
 * 空闲控制器
 * 专门负责矩形的绘制操作
 */
export class Idle2DController extends ControllerBase {
  private _mouseDownPos: Vector2 | null = null;
  private _isDragging = false;
  private readonly DRAG_THRESHOLD = 3;

  constructor(context: Design2DContext) {
    super(DesignControllerType.IDLE2D_CTRL, context);
  }

  /**
   * 初始化控制器
   */
  public initialize(): void {
    this._reset();
  }

  /**
   * 控制器激活时的回调
   */
  public activate(): void {
    super.activate();
    this._reset();
  }

  /**
   * 控制器停用时的回调
   */
  public deactivate(): void {
    super.deactivate();
    this._reset();
  }



  /**
   * 处理鼠标按下事件
   */
  public onMouseDown (event: MouseEvent): void 
  {
    if(event.button !== 0) {
      return;
    }
    
    this._mouseDownPos = new Vector2(event.clientX, event.clientY);
    this._isDragging = false;
    
    let pos = this.screenToCanvas(new Vector2(event.clientX, event.clientY));
    const hitObjects = this.context.selectionManager.hitTest(pos);
    console.log(hitObjects);
  }

  /**
   * 处理鼠标移动事件
   */
  public onMouseMove(event: MouseEvent): void {
    if (!this._mouseDownPos) {
      return;
    }
    const currentPos = new Vector2(event.clientX, event.clientY);
    const distance = currentPos.distanceTo(this._mouseDownPos);

    if (distance > this.DRAG_THRESHOLD && !this._isDragging) {
      this._isDragging = true;
    }
  }

  /**
   * 处理鼠标抬起事件
   */
  public onMouseUp (event: MouseEvent): void 
  {
    if (!this._mouseDownPos) {
      return;
    }

    if (this._isDragging) {
    }

    this._reset();
  }


  private _reset(): void {
    this._mouseDownPos = null;
    this._isDragging = false;
  }

  /**
   * 销毁控制器
   */
  public dispose(): void {
    this._reset();
    super.dispose();
  }
}