<!doctype html>
<html lang="en">
  <head>
    <meta http-equiv="Content-Type" content="text/html" charset="utf-8" />
    <meta name="renderer" content="webkit" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="full-screen" content="true" />
    <meta name="x5-fullscreen" content="true" />
    <meta name="360-fullscreen" content="true" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta http-equiv="expires" content="0" />
    <title>AI轻设计-酒店排房设计工具</title>
    <link rel="icon" href="https://3vj-fe.3vjia.com/fe-common/image/logo/favicon.ico" type="image/x-icon" />
    
  </head>
  <body style="margin: 0;">
    <div id="app" class="app">
      <style>
        html,body{margin:0;padding:0;overflow:hidden}
        .ant-skeleton{display:table;width:100%;padding:0 20px}
        .ant-skeleton .ant-skeleton-content{display:table-cell;width:100%;vertical-align:top}
        .ant-skeleton .ant-skeleton-content .ant-skeleton-title{width:38%;height:16px;background:linear-gradient(90deg, #EEEFF2 25%, #DDDFE4 37%, #EEEFF2 63%) #EEEFF2;background-size: 400% 100%;border-radius:4px;margin:16px 0;animation:css-plugin-ant-skeleton-loading ease-out 1.4s infinite}
        .ant-skeleton .ant-skeleton-content .ant-skeleton-paragraph{padding:0}
        .ant-skeleton .ant-skeleton-content .ant-skeleton-title +.ant-skeleton-paragraph{margin-block-start:16px}
        .ant-skeleton .ant-skeleton-content .ant-skeleton-paragraph li{width:100%;height:16px;list-style:none;background:linear-gradient(90deg, #EEEFF2 25%, #DDDFE4 37%, #EEEFF2 63%) #EEEFF2;background-size: 400% 100%;border-radius:4px;animation:css-plugin-ant-skeleton-loading ease-out 1.4s infinite}
        .ant-skeleton .ant-skeleton-content .ant-skeleton-paragraph li+li{margin-block-start:16px}
        .ant-skeleton .ant-skeleton-content .ant-skeleton-paragraph >li:first-child{display: none;}
        .ant-skeleton .ant-skeleton-content .ant-skeleton-paragraph li:last-child{width:61%}
        @media screen and (min-width: 1024px) {
          .ant-skeleton .ant-skeleton-content .ant-skeleton-title{width:100%;}
          .ant-skeleton .ant-skeleton-content .ant-skeleton-paragraph >li{width:24%;}
          .ant-skeleton .ant-skeleton-content .ant-skeleton-paragraph >li:first-child{display:block;width:100%}
          .ant-skeleton .ant-skeleton-content .ant-skeleton-paragraph >li:first-child + li{width:12%}
          .ant-skeleton .ant-skeleton-content .ant-skeleton-paragraph >li:last-child{width:16%}
        }
        @keyframes css-plugin-ant-skeleton-loading{0%{background-position:100% 50%;}100%{background-position:0 50%;}}
      </style>
      <div class="ant-skeleton">
        <div class="ant-skeleton-content">
          <h3 class="ant-skeleton-title"></h3>
          <ul class="ant-skeleton-paragraph"><li></li><li></li><li></li><li></li><li></li><li></li></ul>
        </div>
      </div>
    </div>
    <script type="text/javascript" src="https://3vj-fe.3vjia.com/fe-common/js/react@18.2.0/react.development.js"></script>
    <script type="text/javascript" src="https://3vj-fe.3vjia.com/fe-common/js/react-dom@18.2.0/react-dom.development.js"></script>
    <script type="text/javascript" src="https://3vj-fe.3vjia.com/fe-common/js/react-dom@18.2.0/react-dom-client.js"></script>
    <script type="text/javascript" src="https://3vj-fe.3vjia.com/fe-common/js/mobx@6.6.1/mobx.development.js"></script>
    <script type="text/javascript" src="https://3vj-fe.3vjia.com/fe-common/js/mobx-react-lite@3.4.0/mobxreactlite.development.js"></script>
    <script type="text/javascript" src="https://3vj-fe.3vjia.com/fe-common/js/mobx-react@7.5.2/mobxreact.development.js"></script>

    <script type="module" crossorigin src="./js/index.DMfFgggD.js"></script>


  </body>
</html>
