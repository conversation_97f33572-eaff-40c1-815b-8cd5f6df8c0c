import { TGroupTemplate, TRoom, WPolygon } from "@layoutai/layout_scheme";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, compareNames } from "@layoutai/z_polygon";



export class TPostLayoutDiningPart 
{
    private static _instance : TPostLayoutDiningPart = null;

    constructor()
    {
        TPostLayoutDiningPart._instance = this;
    }

    static get instance()
    {
        if(!TPostLayoutDiningPart._instance)
        {
            TPostLayoutDiningPart._instance = new TPostLayoutDiningPart();
        }
        return TPostLayoutDiningPart._instance;
    }

    // 添加餐厅区
    postAddDiningPart(room:TRoom, group_templates:TGroupTemplate[] = null)
    {
        if(compareNames([room.roomname],["客餐厅"])==0){
            return null;
        }
        if(!group_templates)
        {
            group_templates = TGroupTemplate.extractGroupTemplates(room._furniture_list,room.roomname);
        }

        let dinning_cabinet_template = group_templates.find((val)=>compareNames([val.group_space_category],["餐边柜"]) && val._target_rect.w > 1);
        let dinning_table_template = group_templates.find((val)=>compareNames([val.group_space_category],["餐桌"]));

        if(dinning_table_template) return null;


        let sofa_area_template = group_templates.find((val)=>compareNames([val.group_space_category],["沙发组合区"]));
        let other_cabinet_templates =  group_templates.filter((val)=>compareNames([val.group_space_category],["玄关柜","电视柜","餐边柜"]));

        let cutted_rects : ZRect[] = [];

        if(other_cabinet_templates)
        {
           for(let cabinet of other_cabinet_templates)
           {
               let c_rect = cabinet._target_rect.clone();
               c_rect._h = Math.max(550, c_rect._h);
               c_rect.updateRect();
               cutted_rects.push(c_rect);
           }


        }
        let candidate_table_rects : {back_edge:ZEdge,rect:ZRect}[] = [];
        let default_table_length = 1800;
        let default_table_depth = 1800;
        let default_hallwall_length = 1200; // 留出600的过道区域 
        let default_offset = 300;
        let default_cabinet_depth = 600;
        
        // 如果有餐边柜, 则在餐边柜前面添加一个区域

        let valid_shape = room.valid_shape_list[0];
        let hallway_rects : ZRect[] = [];
        if(valid_shape?._feature_shape?._w_poly)
        {
            let w_poly = valid_shape._feature_shape._w_poly;
            for(let edge of w_poly.edges)
            {
                let win = WPolygon.getWindowOnEdge(edge);

                // 由门、过道产生的矩形
                if(win && (win.type==="Door" || win.type==="Hallway") && (!compareNames(win.room_names||[],["厨房","阳台"])))
                {
                    let t_rect = new ZRect(edge.length, default_hallwall_length);
                    t_rect.nor = edge.nor.clone().negate();
                    t_rect.back_center = edge.center;
                    t_rect.updateRect();
                    hallway_rects.push(t_rect);
                    
                }
            }
        }


        if(dinning_cabinet_template)
        {

            // 当餐边柜的宽度 比较长的时候, 说明时候横放
            if(dinning_cabinet_template._target_rect.w > default_table_length + default_cabinet_depth)
            {
                let rect = new ZRect(dinning_cabinet_template._target_rect.w, default_table_depth + default_hallwall_length);
                rect.nor = dinning_cabinet_template._target_rect.nor;

                // 横放的时候，
                rect.back_center = dinning_cabinet_template._target_rect.frontEdge.center.clone().add(rect.nor.clone().multiplyScalar(500));
                rect.updateRect();
                candidate_table_rects.push({rect:rect,back_edge:dinning_cabinet_template._target_rect.backEdge});
            }
            // 当餐边柜的宽度 相对较窄的时候，那就尝试纵放，形成丁字摆放
            {
                let rect = new ZRect(default_table_length + default_hallwall_length*2,default_table_length + default_hallwall_length*2);
                rect._h = rect._w;
                rect.nor = dinning_cabinet_template._target_rect.dv;
                rect.rect_center = dinning_cabinet_template._target_rect.frontEdge.center.clone()
                    .add(dinning_cabinet_template._target_rect.nor.clone().multiplyScalar(rect.w/2 + 100));
                rect.updateRect();
                candidate_table_rects.push({rect:rect,back_edge:dinning_cabinet_template._target_rect.backEdge});
            }



        }
        else{
            let valid_shape_list = room.valid_shape_list;
            if(valid_shape_list[1])
            {
                let rect = valid_shape_list[1].getRect();
                if(rect)
                {
                    candidate_table_rects.push({rect:rect,back_edge:rect.backEdge});
                }
            }
        }
        let ans_group_templates : TGroupTemplate[] = [];


        for(let data of candidate_table_rects)
        {
            let rect = data.rect;
             let valid_shape = room.valid_shape_list.find((val)=>val._poly.containsPoint(rect.rect_center));

             if(!valid_shape) continue;
             let valid_rect = valid_shape.getRect();

             if(!valid_rect) continue;

             valid_rect = valid_rect.clone();
             let r_center = valid_rect.rect_center;
             // 有效区域长宽各自收缩一点
             let short_hall_way = 300;
             valid_rect._w -= short_hall_way ; 
             valid_rect._h -= short_hall_way; 

             valid_rect.rect_center = r_center;
             valid_rect.reOrderByOrientation();
             let t_rect = rect.clone().intersect_rect(valid_rect);
             if(!t_rect) continue;

             t_rect = ZRect.fromPoints(t_rect.positions,rect.nor,rect.u_dv_flag);

             
             if(!t_rect) continue;


             for(let c_rect of cutted_rects)
             {
                let res_rect = t_rect.clip_side_rect(c_rect);
                if(res_rect)
                {
                    t_rect.copy(res_rect);
                }
             }
             t_rect = ZRect.fromPoints(t_rect.positions,rect.nor,rect.u_dv_flag);

             for(let c_rect of hallway_rects)
             {

                let res_rect = t_rect.clip_side_rect(c_rect);

                if(res_rect) t_rect.copy(res_rect);
                

             }
            //  t_rect = ZRect.fromPoints(t_rect.positions,rect.nor,rect.u_dv_flag);

             // 再尝试裁剪沙发区




             
             if(sofa_area_template)
             {
                let sofa_rect = sofa_area_template._target_rect.clone();

                let cutted_rect = t_rect.clip_side_rect(sofa_rect,sofa_rect.nor);

                if(cutted_rect)
                {
                    t_rect.copy(cutted_rect);
                }


                if(sofa_rect.containsPoint(t_rect.rect_center))
                {
                    continue;
                }
             }

             t_rect = ZRect.fromPoints(t_rect.positions,rect.nor,rect.u_dv_flag);
             if(t_rect.w < t_rect.h && t_rect.max_hh < 1200)
             {
                t_rect.swapWidthAndHeight();
             }
             let tt_group_template = new TGroupTemplate();
             tt_group_template.group_space_category ="餐桌区";
             tt_group_template.group_code = "餐桌-FC4_餐椅";
             tt_group_template._target_rect = t_rect.clone();

             if(dinning_cabinet_template)
             {
                // 设置target_point, 是尽可能地贴到餐边柜区
                // if(dinning_cabinet_template._target_rect.checkSameNormal(t_rect.nor))
                {
                    tt_group_template._target_point = dinning_cabinet_template._target_rect.rect_center;
                }
             }
             tt_group_template._normal_free = true;
             if(tt_group_template.updateByTargetRect({query_same_space_category:false,consider_depth:true}))
             {
                 ans_group_templates.push(tt_group_template);
                 break;
             }
            
        }

        return ans_group_templates;
    }
}