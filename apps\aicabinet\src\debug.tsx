import '@svg/antd/es/style/reset.css';
import { createRoot } from 'react-dom/client';
import ReactDOM from 'react-dom';
import { Button } from '@svg/antd';
import SunvegaAPI from '@api/clouddesign';

const { Core, Platform } = SunvegaAPI;

const manifest = await import('../app.debug.json');
const mainEntry = manifest?.main?.entry;
const appInfo: any = manifest?.appInfo;

Core.APIManager.init('debug-plugin-demo')

document.head.appendChild;
document.body.style.cssText = 'overflow: hidden;';

const scriptTag = document.createElement('script')
scriptTag.src = 'https://3vj-fe.3vjia.com/project/3d-design/website/debug_platform.umd.js';
document.head.appendChild(scriptTag);


const App = () => {
  const openApp = () => {
    Platform.Application.openApp({ appInfo });
  }
  const closeApp = () => {
    Platform.Application.closeApp({ appId: appInfo?.appId });
  }
  return (
    <>
      <iframe src={mainEntry} style={{ border: 'none', width: '100%', height: '100%' }}></iframe>
      <div style={{ position: 'fixed', left: '300px', top: '48px', zIndex: 100000 }}>
        <Button onClick={openApp}>打开本地插件应用</Button>  
        <Button onClick={closeApp}>关闭本地插件应用</Button>  
      </div>
    </>
  )
}

const root = createRoot(document.body as any);
if (process.env.NODE_ENV === 'development') {
  ;(ReactDOM as any).injectDevTools(); // 用于解决esternals后热更新失效的问题
}

root.render(<App />);
