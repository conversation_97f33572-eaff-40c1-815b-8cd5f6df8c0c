import { TFigureElement, TGroupTemplate, WPolygon } from "@layoutai/layout_scheme";
import { TSimpleRoomPartRelation } from "./TSimpleRoomPartRelation";


export class THuaweiKitchenRelation extends TSimpleRoomPartRelation
{
    constructor()
    {
        super();
    }

    precompute(): void {
        this._candidate_figure_list = [];

        
        if (!this._room) return;

        // if (!compareNames([this._room.roomname], ["厨房"])) return;
        if (!this._room.room_shape._feature_shape) {
            this._room.updateFeatures();
        }
        /**
         *  w_poly 是自动合并门窗后并分段后的多边形
         */
        let w_poly = this._room.room_shape._feature_shape._w_poly;

        if(w_poly) 
        {
            // 没有窗，就说明只有墙
            let wall_edges = w_poly.edges.filter((edge)=>!WPolygon.getWindowOnEdge(edge));
            if(wall_edges.length == 0)
            {
                wall_edges = [...w_poly.edges];
            }
            if(wall_edges.length ==0) return;
            wall_edges.sort((a,b)=>b.length - a.length); // 从长到短排序
            let w_edge = wall_edges[0];

            let figure_element = TFigureElement.createSimple("水吧台");
            figure_element.length = 800;
            figure_element.depth = 600;
            // 方向要向内
            figure_element.nor = w_edge.nor.clone().negate(); 

            // 背靠边的中点
            figure_element.rect.back_center = w_edge.unprojectEdge2d({x:w_edge.length/2,y:0});

            figure_element.rect.updateRect();


            let t_group_template = new TGroupTemplate().makeBySeedFigureGroup({main_figure:figure_element.exportJson(),sub_figures:[]})
            t_group_template._target_rect.copy(figure_element.rect);            

            this._candidate_figure_list.push({group_templates:[t_group_template],debug_data:{scheme_name:"华为厨房"}});
        }

        this._attempt_num = this._candidate_figure_list.length;
    }


}