import { I_SwjBaseGroup } from "@layoutai/basic_data";
import { TBaseGroupEntity, TBaseRoomToolUtil } from "@layoutai/layout_scheme";
import { ZRect } from "@layoutai/z_polygon";
import { Vector3 } from "three";
import { TLayoutQueryTemplateToolUtil } from "./TLayoutQueryTemplateToolUtil";

export class TLayoutGroupTemlateToolUtil
{
    private static _instance: TLayoutGroupTemlateToolUtil;

    private templateMetaDatas: Map<string, TBaseGroupEntity[][]>;
    
    private constructor()
    {
        this.templateMetaDatas = new Map<string, TBaseGroupEntity[][]>();
    }

    public static get instance(): TLayoutGroupTemlateToolUtil
    {
        if (!TLayoutGroupTemlateToolUtil._instance)
        {
            TLayoutGroupTemlateToolUtil._instance = new TLayoutGroupTemlateToolUtil();
        }
        return TLayoutGroupTemlateToolUtil._instance;
    }

    public async queryTemplateData()
    {
        // 1. 先获取组合数据, 并将其进行组装
        let params: any = {
            "pageIndex":1,
            "pageSize":100,
            "cateCode": "combination" 
        }
        const result : any = await TLayoutQueryTemplateToolUtil.instance.queryMetaCategory(params);
        if(!result.success)
        {
            return;
        }
        for(let categoryRes of result?.result)
        {
            if(!categoryRes?.subCategoryList)
            {
                continue;
            }
            for(let subCategory of categoryRes?.subCategoryList)
            {
                let subCategoryGroups: TBaseGroupEntity[][] = [];
                for(let subMeta of subCategory.metaImageList)
                {
                    let metaSizeParams: any = {
                        "pageIndex": 1,
                        "pageSize": 100,
                        "metaImageId": subMeta.id
                    }
                    const metaResult : any = await TLayoutQueryTemplateToolUtil.instance.queryMetaItemSize(metaSizeParams);
                    if(!metaResult.success)
                    {
                        continue;
                    }
                    let groupTemplates: any[] = paresMetaSizeResult(metaResult.result);
                    groupTemplates.sort((a, b) => {
                        return a.rect.area - b.rect.area;
                    })
                    subCategoryGroups.push(groupTemplates);
                }
                this.templateMetaDatas.set(subCategory.subCateName, subCategoryGroups);
            }
        }
    }

    // 获取用户创建的组合数据内最优的图元出来
    public getSubFineTemplateData(targetRect: ZRect, subCateName: string): {groupTemplate: TBaseGroupEntity, subArea: number}[]
    {
        let allSubGroupTemplates: TBaseGroupEntity[][] = this.templateMetaDatas.get(subCateName);
        let targetRectRange: any  = TBaseRoomToolUtil.instance.getRange2dByPolygon(targetRect);
        // 这里目前是选一个最优的, 首先要排除
        let containSubGroupTemplates:{groupTemplate: TBaseGroupEntity, subArea: number}[] = [];
        for(let subGroupTemplates of allSubGroupTemplates)
        {
            let minSubArea: number = null;
            let minSubGroupTemplate: TBaseGroupEntity = null;
            for(let groupTemplate of subGroupTemplates)
            {
                // 1. 优先进行朝向矫正,暂时不清楚这里的groupTemplate如何接入
                let adjustGroupTemplate: ZRect = adjustRectLikeBaseRect(targetRect, groupTemplate.rect);
                let adjustGroupTemplateRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(adjustGroupTemplate);
                // 只找在目标区域内的图元，剩下的不管
                if(!TBaseRoomToolUtil.instance.isContainByRange(targetRectRange, adjustGroupTemplateRange))
                {
                    continue;
                }
                let subArea: number = targetRect.area - adjustGroupTemplate.area;
                if(minSubArea == null || subArea < minSubArea)
                {
                    minSubArea = subArea;
                    minSubGroupTemplate = groupTemplate;
                }
            }
            if(minSubGroupTemplate)
            {
                containSubGroupTemplates.push({groupTemplate: minSubGroupTemplate, subArea: minSubArea});
            }
        }
        return containSubGroupTemplates;
    }

    public getFineTemplateData(targetRect: ZRect, subCateName: string): TBaseGroupEntity
    {
        let subGroupTemplates: {groupTemplate: TBaseGroupEntity, subArea: number}[] = this.getSubFineTemplateData(targetRect, subCateName);
        let minSubArea: number = null;
        let minSubGroupTemplate: TBaseGroupEntity = null;
        for(let subGroupTemplate of subGroupTemplates)
        {
            if(minSubArea == null || subGroupTemplate.subArea < minSubArea)
            {
                minSubArea = subGroupTemplate.subArea;
                minSubGroupTemplate = subGroupTemplate.groupTemplate;
            }
        }
        return minSubGroupTemplate;
    }
}

function paresMetaSizeResult(metaResult: any): any[]
{
    let groupTemplates: any[] = [];
    for(let metaSize of metaResult.result)
    {
        let groupTemplate: any = TBaseGroupEntity.importData(JSON.parse(metaSize.metaData) as I_SwjBaseGroup);
        groupTemplates.push(groupTemplate);
    }
    if(groupTemplates.length == 0)
    {
        return null;
    }
    return groupTemplates;
}

function adjustRectLikeBaseRect(baseRect: ZRect, groupTemplateRect: ZRect): ZRect
{
    let baseRectNor: Vector3 = baseRect.nor.clone();
    let baseRectBackCenter: Vector3 = baseRect.back_center.clone();
    let copyGroupTemplateRect: ZRect = groupTemplateRect.clone();
    if(Math.abs(baseRectNor.dot(copyGroupTemplateRect.nor)) > 0.9)
    {
        copyGroupTemplateRect.nor = baseRectNor;
    }
    else
    {
        let oldGroupTemplateLen: number = copyGroupTemplateRect.length;
        let oldGroupTemplateWidth: number = copyGroupTemplateRect.depth;
        copyGroupTemplateRect.length = oldGroupTemplateWidth;
        copyGroupTemplateRect.depth = oldGroupTemplateLen;
        copyGroupTemplateRect.nor = baseRectNor;
    }
    copyGroupTemplateRect.back_center = baseRectBackCenter;
    copyGroupTemplateRect.updateRect();
    return copyGroupTemplateRect;
}