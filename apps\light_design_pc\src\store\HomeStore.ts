import { makeAutoObservable } from "mobx";

import { HomeState } from "./HomeState";
import { Object2DBase } from "@layoutai/design_2d";

/**
 * @description 主页store
 * <AUTHOR>
 * @date 2025-06-19
 * @lastEditTime 2025-06-19 11:14:12
 * @lastEditors xuld
 */

export default class HomeStore {
    currentState: HomeState = HomeState.State2D;

    currenScheme = {} as any;
    roomInfos = [] as any;
    selectObject = {} as Object2DBase;

    designMode = "AiCadMode" as string;

    constructor() {
        makeAutoObservable(this, {}, { autoBind: true });
    }

    setState(data: HomeState) {
        this.currentState = data;
    }
    setCurrenScheme(data: any) {
        this.currenScheme = data;
    }
    setRoomInfos(data: any) {
        this.roomInfos = data;
    }
    setSelectObject(data: Object2DBase) {
        this.selectObject = data;
    }
    setDesignMode(data: string) {
        this.designMode = data;
    }
}
