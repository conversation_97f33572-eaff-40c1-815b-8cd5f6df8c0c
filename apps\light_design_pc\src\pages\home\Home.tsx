import React, { useEffect, useState, useMemo } from "react";
import { useStore } from "../../store/RootStore";
import Design3D from "./components/Design3D";
import Design2D from "./components/Design2D";
import { HomeState } from "../../store/HomeState";
// import styles from './Home.module.css';
import useStyles from "./style";
import { appContext } from "../../context/AppContext";
import { Design2DStates } from "../../context/fsm/const/FSMConst";
import { TopMenu, LeftPanel, AttributeEdit } from "../../components";
import { Button } from "@svg/antd";
import { LeftMenuBar } from "@svg/antd-cloud-design";
import type { ILeftMenuItem } from "@svg/antd-cloud-design/lib/LeftMenuBar";
import { useTranslation } from "react-i18next";
import { DomainApiHub } from "@layoutai/design_domain";
import { EntityType } from "@layoutai/design_domain";

const Home: React.FC = () => {
    const { t } = useTranslation();
    const { styles } = useStyles();
    const store = useStore();
    const [currentState, setCurrentState] = useState<HomeState>(store.homeStore.currentState);
    const [currentFSMState, setCurrentFSMState] = useState<string>(appContext.mainFSM.getCurrentLeafState().name);
    const [layoutSchemeName, setLayoutSchemeName] = useState<string | null>(null); // 布局方案名称

    const items: ILeftMenuItem[] = [
        {
            key: "",
            label: ``,
            icon: [""],
            children: <LeftPanel />,
        },
    ];
    let DesignCanvas = currentState === HomeState.State2D ? <Design2D /> : <Design3D />;

    useEffect(() => {
        store.homeStore.setCurrenScheme(DomainApiHub.instance.getScheme());
        store.homeStore.setRoomInfos(DomainApiHub.instance.getEntitiesByType(EntityType.room));
    }, []);

    useEffect(() => {
        setLayoutSchemeName(store.homeStore.currenScheme?.name || "");
    }, []);

    useEffect(() => {
        // 定期检查FSM状态变化
        const checkFSMState = () => {
            const newFSMState = appContext.mainFSM.getCurrentLeafState().name;
            if (newFSMState !== currentFSMState) {
                setCurrentFSMState(newFSMState);
            }
        };

        // 使用定时器定期检查状态
        const interval = setInterval(checkFSMState, 100);

        return () => clearInterval(interval);
    }, [currentFSMState]);

    const handleClick = () => {
        const newState = currentState === HomeState.State2D ? HomeState.State3D : HomeState.State2D;
        store.homeStore.setState(newState);
        setCurrentState(newState);
    };

    // const handleDrawRect = () => {
    //     console.log("进入绘制矩形模式");
    //     // 这里可以添加绘制矩形的逻辑
    //     appContext.mainFSM.transitionTo(Design2DStates.DRAW_RECT);
    // };

    const handleDrawFinishClick = () => {
        appContext.mainFSM.transitionTo(Design2DStates.IDLE2D);
    };

    return (
        <div className={styles.root}>
            {/* 顶部菜单 */}
            <TopMenu
                handler={null}
                title={
                    <>
                        <span style={{ color: "#FFFFFF0F" }}>|</span> {!layoutSchemeName ? "" : " 【" + layoutSchemeName + "】"}
                    </>
                }
                create3DLayout={null}
            />
            <div className={styles.content}>
                {/* 左侧面板 */}
                {currentState === HomeState.State2D && (
                    <div className={styles.side_pannel}>
                        <LeftMenuBar items={items} contentClassName={styles.left_content} />
                    </div>
                )}
                {/* 画布区域 */}
                <div className={styles.canvas_pannel}>
                    {DesignCanvas}
                    <div className="canvas_btns">
                        {currentFSMState !== Design2DStates.DRAW_RECT && (
                            <Button className="btn" type="primary" onClick={handleClick}>
                                {currentState === HomeState.State2D ? t("切换3D") : t("切换2D")}
                            </Button>
                        )}
                        {currentFSMState == Design2DStates.DRAW_RECT && (
                            <Button className="btn" type="primary" onClick={handleDrawFinishClick}>
                                {t("完成")}
                            </Button>
                        )}
                    </div>
                </div>
                {/* 属性面板 */}
                {currentState === HomeState.State2D && <AttributeEdit />}
            </div>

            {/* <div className={styles.homeContainer}>
                <div className={styles.header}>
                    <button onClick={handleClick}>{currentState === HomeState.State2D ? "2D" : "3D"}</button>
                    <button onClick={handleDrawRect} style={{ marginLeft: '10px' }}>绘制矩形</button>
                </div>
                <div className={styles.content}>
                    {comp}
                </div>
            </div> */}
        </div>
    );
};

export default Home;
