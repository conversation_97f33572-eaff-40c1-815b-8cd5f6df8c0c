{"name": "ai-cabinet", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "tsc --noEmit && vite", "build": "tsc && vite build", "build:hws": "tsc && vite build", "build:pre": "tsc && vite build", "build:prod": "tsc && vite build", "build:production": "tsc && vite build", "dev:debug": "concurrently \"cross-env DEBUG=1 npm run dev\" \"npm run dev\"", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ant-design/cssinjs": "^1.20.0", "@ant-design/icons": "^5.3.7", "@api/clouddesign": "^1.14.6", "@svg/antd": "^2.5.3", "@svg/antd-cloud-design": "^5.4.5", "@svg/oss-upload": "^1.1.2", "@svg/request": "^0.2.0", "@svg/sso-plus": "^1.1.0", "axios": "1.5.0", "i18next": "^21.9.1", "mobx": "6.6.1", "mobx-react": "7.5.2", "react": "18.2.0", "react-dom": "18.2.0", "react-i18next": "11.18.4", "react-if": "^4.1.5", "react-router-dom": "6.3.0", "weixin-js-sdk": "^1.6.5"}, "devDependencies": {"@babel/preset-env": "^7.14.8", "@babel/preset-react": "^7.14.5", "@types/node": "^20.14.0", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "concurrently": "^8.2.0", "copy-vite-plugin": "^1.0.1", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "less": "^4.2.0", "less-loader": "^12.2.0", "typescript": "^5.2.2", "vite": "^5.2.0", "vite-plugin-externals": "^0.6.2", "vite-plugin-html": "^3.2.2"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all", "last 1 version", "> 1%", "IE 9"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version", "last 1 version", "> 1%", "IE 9"]}, "engines": {"node": ">=18.0.0"}}