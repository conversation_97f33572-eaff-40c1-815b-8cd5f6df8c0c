import { useState, useEffect } from 'react';
import { fsmObservable } from '../context/fsm/FSMObservable';
import type { StateChangeEvent } from '../context/fsm/FSM';

/**
 * 使用RxJS Observable监听FSM状态变化的Hook
 * 提供更强大的响应式状态管理能力
 */
export function useFSMObservable() {
  const [currentState, setCurrentState] = useState<string>('');

  useEffect(() => {
    const subscription = fsmObservable.getCurrentState().subscribe(state => {
      setCurrentState(state);
    });

    return () => subscription.unsubscribe();
  }, []);

  return {
    currentState,
    observable: fsmObservable
  };
}

/**
 * 监听特定状态进入的Hook
 */
export function useStateEnter(stateName: string, callback: (event: StateChangeEvent) => void) {
  useEffect(() => {
    const subscription = fsmObservable.onStateEnter(stateName).subscribe(callback);
    return () => subscription.unsubscribe();
  }, [stateName, callback]);
}

/**
 * 监听特定状态退出的Hook
 */
export function useStateExit(stateName: string, callback: (event: StateChangeEvent) => void) {
  useEffect(() => {
    const subscription = fsmObservable.onStateExit(stateName).subscribe(callback);
    return () => subscription.unsubscribe();
  }, [stateName, callback]);
}

/**
 * 监听状态模式匹配的Hook
 */
export function useStatePattern(pattern: RegExp, callback: (event: StateChangeEvent) => void) {
  useEffect(() => {
    const subscription = fsmObservable.onStatePattern(pattern).subscribe(callback);
    return () => subscription.unsubscribe();
  }, [pattern, callback]);
}

/**
 * 监听所有状态变化的Hook
 */
export function useAllStateChanges(callback: (event: StateChangeEvent) => void) {
  useEffect(() => {
    const subscription = fsmObservable.getStateChanges().subscribe(callback);
    return () => subscription.unsubscribe();
  }, [callback]);
}
