import { generateUUID } from "three/src/math/MathUtils";
import { compareNames ,ZPolygon} from "@layoutai/z_polygon";
import { DomainApiHub, IEntityBase } from "@layoutai/design_domain";
import { TGraphBasicConfigs } from "../config/TGraphBasicConfigs";
import { TPainter } from "../draw/TPainter";
import { DrawParam } from "./DrawParam";
import { DrawingManager } from "../draw/DrawingManager";
import { Vector2, Vector3 } from "three";

/**
 * 2D 对象基类
 */
export class Object2DBase {
    private _entityUuid: string;
    private _uuid: string = generateUUID();
    // 绘制参数
    protected _drawParam: DrawParam = new DrawParam();
    // 交互状态
    protected _isSelected: boolean = false;
    protected _isHovered: boolean = false;
    protected _isInteractive: boolean = true;

    constructor(uuid: string) {
        this._entityUuid = uuid;
    }

    public get uuid(): string {
        return this._uuid;
    }
    // 是否可交互
    public get isInteractive(): boolean {
        return this._isInteractive;
    }

    // 是否可交互
    public set isInteractive(value: boolean) {
        this._isInteractive = value;
    }
    /**
     * 是否命中测试
     * @param point  世界坐标 若需要用画布坐标，请使用 CoordinateTransformer.canvasToGlobal(point, this._context) 转换
     * @returns boolean
     */
    public hitTest(point: Vector3): boolean {
        return false;
    }

    public get entity(): IEntityBase | undefined {
        let hub: DomainApiHub = DomainApiHub.instance;
        if (!hub) {
            console.error("DomainApiHub 未初始化");
            return;
        }
        return hub.getEntity(this._entityUuid);
    }

    public render(painter: TPainter): void {
    }

    public update(): any | undefined {
    }
    
    public registerRenderCommands(drawingManager: DrawingManager): void {
        if (!this.entity) return;
        drawingManager.addRenderCommand(this.uuid, this.entity.entityType, this.render.bind(this));
    }

    public updateDrawParam(ctx: CanvasRenderingContext2D, drawParam: DrawParam): void {
        // 设置绘制参数
        ctx.fillStyle = drawParam.fillStyle;
        ctx.strokeStyle = drawParam.strokeStyle;
        ctx.lineWidth = drawParam.lineWidth;
        ctx.font = `${drawParam.fontSize}px`;
        ctx.globalAlpha = drawParam.alpha;

        this._drawParam = drawParam;
    }

    /**
     * 设置选中状态
     */
    public setSelected(selected: boolean): void {
        if (this._isSelected === selected) return;
        this._isSelected = selected;
        this._updateVisualState();
    }

    /**
     * 设置悬浮状态
     */
    public setHovered(hovered: boolean): void {
        if (this._isHovered === hovered) return;
        this._isHovered = hovered;
        this._updateVisualState();
    }

    /**
     * 更新视觉状态
     */
    protected _updateVisualState(): void {
        // 根据状态更新绘制参数
        if (this._isSelected) {
            this._drawParam.strokeStyle = '#ff0000';
            this._drawParam.lineWidth = 2;
        } else if (this._isHovered) {
            this._drawParam.strokeStyle = '#0000ff';
            this._drawParam.lineWidth = 1;
        } else {
            this._drawParam.strokeStyle = '#000000';
            this._drawParam.lineWidth = 1;
        }
    }

    public static getDrawingOrder(names: string[]): number {
        let level_order = 6;
        let sub_level_order = 0;
        let is_found = false;
        for (let li in TGraphBasicConfigs.OnDrawingLevels) {

            for (let id in TGraphBasicConfigs.OnDrawingLevels[li]) {
                let name = TGraphBasicConfigs.OnDrawingLevels[li][id];

                if (compareNames(names, [name], false)) {
                    level_order = ~~li;
                    sub_level_order = ~~id;
                    is_found = true;
                    break;
                }
            }
            if (is_found) break;
        }

        return level_order + sub_level_order / 100;
    }
} 