import { <PERSON>, Mesh, Object3D, Per<PERSON><PERSON><PERSON><PERSON>, Raycaster, Vector2 } from "three";
import { UserDataKey } from "../const/UserDataKey";
import { Object3DBase } from "../object3d/Object3DBase";
import { EventType } from "../events/EventType";


/**
* @description 射线投射控制器
*/
export class RayCasterCtrl {
    private _camera: Camera;
    public domElement: HTMLCanvasElement;
    public raycaster: Raycaster;
    private _targetObjects: Object3D[] = [];
    private _minDistance: number = 600;

    public onSelectedObject?: (entity: Object3DBase) => void;

    constructor(camera: Camera, domElement: HTMLElement, targetObjects?: Object3D[]) {
        this._camera = camera;
        this.domElement = domElement as HTMLCanvasElement;
        this.raycaster = new Raycaster();
        this._targetObjects = targetObjects || [];
        this.register();
    }

    public get camera(): Camera {
        return this._camera;
    }

    public register() {
        this.domElement.addEventListener(EventType.MOUSE_UP, this._onMouseUp.bind(this));
    }

    public dispose(): void {
        this.domElement.removeEventListener(EventType.MOUSE_UP, this._onMouseUp.bind(this));
    }

    private _onMouseUp(event: MouseEvent) {
        let x = event.offsetX / (this.domElement.clientWidth);
        let y = event.offsetY / (this.domElement.clientHeight);
        x = x * 2 - 1.;
        y = 1. - y * 2;
        this.raycaster.setFromCamera(new Vector2(x, y), this.camera);

        const meshes: Mesh[] = [];
        this._targetObjects.forEach(obj => {
            obj.traverse((child: Object3D) => {
                if ((child as Mesh).isMesh) {
                    meshes.push(child as Mesh);
                }
            });
        });
        let res = this.raycaster.intersectObjects(meshes, false);
        if (res.length > 0) {
            if (this.camera && this.camera instanceof PerspectiveCamera) this._minDistance = this.camera.near + 100;
            res = res.filter(res => res.distance > this._minDistance);
            const object = RayCasterCtrl.findObjectFromMesh(res[0].object);
            if (object && this.onSelectedObject) {
                // console.log("RayCasterCtrl.onSelectedObject: ", object);
                this.onSelectedObject(object);
            }
        }
    }

    private static findObjectFromMesh(obj: Object3D | null): Object3DBase | null {
        if (!obj) return null;
        if (obj.userData[UserDataKey.EntityOfMesh]) {
            return obj.userData[UserDataKey.EntityOfMesh];
        }
        if (!obj.parent) {
            return null;
        }
        return RayCasterCtrl.findObjectFromMesh(obj.parent);
    }
}