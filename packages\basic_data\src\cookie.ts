export interface CookieParams {
  seconds?: number;
  path?: string;
  domain?: string;
}

/**
 * @description 获取cookie
 * @param {string} key - cookie名称
 */
export function getCookie(key: string): string {
  const reg = new RegExp('(^| )' + key + '=([^;]*)(;|$)');
  let arr: any = null;
  if (document.cookie.match(reg)) {
    arr = document.cookie.match(reg);
    return decodeURIComponent(arr[2]);
  }
  return '';
};

/**
 * @description 设置cookie
 * @param {string} key - cookie名称
 * @param {string} value - cookie值
 * @param {CookieParams} opt - 配置参数
 */
export function setCookie(key: string, value: string, opt: CookieParams = {}): void {
  let cookie = key + '=' + encodeURIComponent(value);

  if (opt.seconds) {
    const exp = new Date();
    exp.setTime(exp.getTime() + opt.seconds * 1000);
    cookie += ';expires=' + exp.toUTCString();
  }
  if (opt.path) {
    cookie += ';path=' + opt.path;
  } else {
    cookie += ';path=/';
  }
  if (opt.domain) {
    cookie += ';domain=' + opt.domain;
  } else {
    cookie += ';domain=' + document.domain.substring(document.domain.indexOf('.') + 1);
  }
  window.document.cookie = cookie;
};


/**
 * @description 删除cookie
 * @param {string} key - cookie名称
 */
export function delCookie(key: string, value: string, opt: any = {}): void {
  const exp = new Date();
  const cval = getCookie(key);

  exp.setTime(exp.getTime() - 1);
  if (cval != null) {
    document.cookie = key + '=' + cval + ';expires=' + exp.toUTCString();
  }
};
