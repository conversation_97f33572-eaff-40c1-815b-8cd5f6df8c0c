import React, { useEffect, useImperativeHandle, forwardRef, useState } from 'react';
import './index.less'
import { hxUrl } from '@/config';
import { LayoutAI_App, LayoutAI_Events,EventName} from '@layoutai/layout_scheme';
import { t } from 'i18next';
import { useStore } from '@/models';
const HxSearch = forwardRef((props, ref) => {
  const store = useStore();
  const [isShowHxSearch,setIsShowHxSearch] = useState<boolean>(false);
  const onModal = () => {
    let iframe = document.getElementById("searchIframe") as HTMLIFrameElement;
    
    if(!iframe) 
    {
        iframe =   document.createElement('iframe');
        iframe.src = hxUrl;
        iframe.id = 'searchIframe';
        iframe.setAttribute('frameborder', '0');
        iframe.setAttribute('scrolling', 'no');
        iframe.onload = function iframeLoaded() {
          setTimeout(() => {
            iframe.contentWindow?.postMessage({
              origin: 'uiapi.3dhouseplugin',
              data: {
                name: 'houseHome',
                action: 'open',
                params: {
                  hasCollection:true,
                  hasExcellentScheme:false,
                  houseCardBtnArr:[],
                  houseDetailBtnArr:[{label:t('开始设计'),value:'1'}],
                }
              }
            },'*')
          },1000)
        }

      }
      const iframeWrap = document.getElementById('iframe-wrap');
      iframeWrap?.appendChild(iframe);
  }
  const leaveModal = ()=>{
    const iframe = document.getElementById('searchIframe');
    // iframe && iframe.remove();
    setIsShowHxSearch(false);
  }



  useEffect(() => {
    LayoutAI_App.on(EventName.OpenHouseSearching,(t:boolean)=>{
      if(t)
      {
        onModal();
        setIsShowHxSearch(t);
      }
      else{
        leaveModal();
      }
    })
    window.addEventListener('message', (e) => {
      console.log('主线的e',e);
      
      if(e.data?.type === 'closeModal') {
        const iframe = document.getElementById('searchIframe');
        iframe && iframe.remove();
      }
      if(e?.data && e?.data.origin === 'uiapi.3dhouseplugin.ui') {
        const data: any = e.data.data;
        if(data?.action === 'onBtnClick') {
          const id = data.params.curHouseData.id;
          const name = data.params.curHouseData.buildingName + " " + data.params.curHouseData.roomTypeName + " " + data.params.curHouseData.area + "m²";
          const imagePath = data.params.curHouseData.imagePath;
          console.log(id,name,imagePath);
          LayoutAI_App.DispatchEvent(LayoutAI_Events.PostBuildingId, {id: id, name:name, imagePath: imagePath});
          const iframe = document.getElementById('searchIframe');
          iframe && iframe.remove();
          store.homeStore.setSunDEnterOpen(true);
          return;
        }
      }
      if(e?.data && e?.data.origin === 'uiapi.houseplugin') {
        const data: any = e.data.data;
        if(data?.action === 'onExcellentSchemeBtnClick') {
          const id = data.params.curHousetypeObj.id;
          const name = data.params.curHouseData.buildingName + " " + data.params.curHouseData.roomTypeName + " " + data.params.curHouseData.area + "m²" ;
          console.log(id);

          LayoutAI_App.DispatchEvent(LayoutAI_Events.PostBuildingId, {id: id, name:name});
          const iframe = document.getElementById('searchIframe');
          iframe && iframe.remove();
          store.homeStore.setSunDEnterOpen(true);
          return;
        }
      }
      if(e?.data?.id)
      {
        const id = e.data.id;
        LayoutAI_App.DispatchEvent(LayoutAI_Events.PostBuildingId, {id: id, name:''});
        console.log(id);
        const iframe = document.getElementById('searchIframe');
        iframe && iframe.remove();
        store.homeStore.setSunDEnterOpen(true);
        return;
      }
    })
  }, [])

  useImperativeHandle(ref, () => ({
    onModal
  }));

  return (
    <div className='hxsearch'>
      <div id='iframe-wrap'>
      </div>
    </div>
  )
})

export default HxSearch;