import { I_Window } from "@layoutai/basic_data";
import { TBaseRoomToolUtil, TGroupTemplate, TRoom, TRoomLayoutScheme } from "@layoutai/layout_scheme";
import { ZEdge, ZRect } from "@layoutai/z_polygon";
import { Vector3 } from "three";

const xDir: Vector3 = new Vector3(1, 0, 0);
const yDir: Vector3 = new Vector3(0, 1, 0);

export class TSimpleRoomRelationToolUtil {
    private static _instance: TSimpleRoomRelationToolUtil;

    public static curtainGroupCode: string = "窗帘区";

    public static cabinetGroupKeyCode: string = "柜";

    public static curtainDepth: number = 100;
    
    private constructor()
    {}

    public static get instance(): TSimpleRoomRelationToolUtil {
        if (!TSimpleRoomRelationToolUtil._instance) {
            TSimpleRoomRelationToolUtil._instance = new TSimpleRoomRelationToolUtil();
        }
        return TSimpleRoomRelationToolUtil._instance;
    }

    public postProcessResultSchemeList(resultSchemeList: TRoomLayoutScheme[], room: TRoom)
    {
        if(room.name == "厨房" || room.name == "入户花园" || room.name == "阳台")
        {
            return;
        }
        // 1. 添加窗帘
        for(let resultScheme of resultSchemeList)
        {
            addCurtainToScheme(resultScheme, room);
        }
    }
}

function addCurtainToScheme(resultScheme: TRoomLayoutScheme, room: TRoom)
{
    if(resultScheme.group_templates.length == 0)
    {
        return;
    }
    let curtainRectFromResults: ZRect[] = getRectFromResultScheme(resultScheme, TSimpleRoomRelationToolUtil.curtainGroupCode);
    let cabinetRectFromResults: ZRect[] = getRectFromResultScheme(resultScheme, TSimpleRoomRelationToolUtil.cabinetGroupKeyCode);
    let windows: I_Window[] = room.windows.filter(window => (window.type == "Window" && !window.room_names.includes("厨房")) || (window.realType == "SlidingDoor" && window.room_names.includes("阳台")));
    for(let window of windows)
    {
        let isExistCurtain: boolean = isWindowExistCurtain(window, curtainRectFromResults);
        if(isExistCurtain)
        {
            continue;
        }
        let layonRoomInfo: any = getWindowLayonRoomEdge(window, room);
        if(!layonRoomInfo)
        {
            continue;
        }
        let curtainNor: Vector3 = layonRoomInfo.roomEdge.nor.clone().negate();
        let curtainCenter: Vector3 = layonRoomInfo.windowEdge.center.clone();
        let roomEdgeProjectInfo: any = (layonRoomInfo.roomEdge as ZEdge).projectEdge2d({x: curtainCenter.x, y: curtainCenter.y});
        curtainCenter  = (layonRoomInfo.roomEdge as ZEdge).unprojectEdge2d({x: roomEdgeProjectInfo.x, y: 0});
        let curtainLen: number = layonRoomInfo.windowEdge.length;
        let curtainDepth: number = TSimpleRoomRelationToolUtil.curtainDepth;
        let curtainRect: ZRect = new ZRect(curtainLen, curtainDepth);
        curtainRect.nor = curtainNor;
        curtainRect.back_center = curtainCenter;
        curtainRect.updateRect();
        // TODO 重新修正窗帘数据
        if(!room.name.includes("卫生间"))
        {
            modifyCurtainRect(curtainRect, layonRoomInfo.roomEdge, cabinetRectFromResults);
        }

        // 创建窗帘模板
        let curtainGroupTemplate: TGroupTemplate = TGroupTemplate.makeGroupTemplateByGroupSpaceCategory(
            TSimpleRoomRelationToolUtil.curtainGroupCode, room.name, curtainRect, {consider_depth:false});
        if(curtainGroupTemplate)
        {
            // 布局方案没有进行同步更新
            resultScheme.group_templates.push(curtainGroupTemplate);
            resultScheme._from_graph._group_template_list = resultScheme.group_templates;
            resultScheme.recordFigures(resultScheme._from_graph.result_figure_list);
        }
    }
}

function getCurtainFromResultScheme(resultScheme: TRoomLayoutScheme)
{
    let  curtainRects: ZRect[] = [];
    for(let groupTemplat of resultScheme.group_templates)
    {
        if(groupTemplat.group_space_category != TSimpleRoomRelationToolUtil.curtainGroupCode)
        {
            continue;
        }
        curtainRects.push(groupTemplat._target_rect.clone());
    }
    return curtainRects;
}

function getRectFromResultScheme(resultScheme: TRoomLayoutScheme, spaceGroupCode: string)
{
    let  targetRects: ZRect[] = [];
    for(let groupTemplat of resultScheme.group_templates)
    {
        if(!groupTemplat.group_space_category.includes(spaceGroupCode))
        {
            continue;
        }
        targetRects.push(groupTemplat._target_rect.clone());
    }
    return targetRects;
}


function isWindowExistCurtain(window: I_Window, curtainRects: ZRect[]): boolean
{
    for(let curtainRect of curtainRects)
    {
        if(TBaseRoomToolUtil.instance.isLayOnPolygons(window.rect, curtainRect, null))
        {
            return true;
        }
    }
    return false;
}

function getWindowLayonRoomEdge(window: I_Window, room: TRoom): any
{
    let windowFrontEdge: ZEdge = window.rect.frontEdge;
    let windowBackEdge: ZEdge = window.rect.backEdge;
    let windowEdges: ZEdge[] = [windowFrontEdge, windowBackEdge];
    for(let windowEdge of windowEdges)
    {
        let layonRoomEdge: ZEdge = TBaseRoomToolUtil.instance.edgeOnPolygon(windowEdge, room.room_shape._poly, 200, 0.1);
        if(layonRoomEdge)
        {
            return {windowEdge: windowEdge,  roomEdge: layonRoomEdge};
        }
    }
    return null;
}

function modifyCurtainRect(curtainRect: ZRect, layonRoomEdge: ZEdge, cabinetRects: ZRect[], extendDist: number = 100, toLayonWallEndDistTol: number = 1000)
{
    let curtainBackEdge: ZEdge = curtainRect.backEdge;
    let projectInfo1: any = layonRoomEdge.projectEdge2d({x: curtainBackEdge.v0.pos.x, y: curtainBackEdge.v0.pos.y});
    let projectInfo2: any = layonRoomEdge.projectEdge2d({x: curtainBackEdge.v1.pos.x, y: curtainBackEdge.v1.pos.y});
    let projectXs: number[] = [projectInfo1.x, projectInfo2.x];
    projectXs.sort((a, b) => a - b);
    let projectStart: Vector3 = layonRoomEdge.unprojectEdge2d({x: projectXs[0], y: 0});
    let projectEnd: Vector3 = layonRoomEdge.unprojectEdge2d({x: projectXs[1], y: 0});
    let startSubVec: Vector3 = projectStart.clone().sub(layonRoomEdge.v0.pos);
    let endSubVec: Vector3 = layonRoomEdge.v1.pos.clone().sub(projectEnd);
    let roomEdgeDir: Vector3 = layonRoomEdge.dv.clone();
    let startDot: number = startSubVec.clone().dot(roomEdgeDir);
    let endDot: number = endSubVec.clone().dot(roomEdgeDir);
    if(startDot > 0)
    {
        if(startDot > toLayonWallEndDistTol)
        {
            startDot = extendDist; 
        }
        projectStart.add(roomEdgeDir.clone().multiplyScalar(-startDot));
    }
    if(endDot > 0)
    {
        if(endDot > toLayonWallEndDistTol)
        {
            endDot = extendDist;
        }
        projectEnd.add(roomEdgeDir.clone().multiplyScalar(endDot));
    }

    let xMin: number = Math.min(projectStart.x, projectEnd.x);
    let xMax: number = Math.max(projectStart.x, projectEnd.x);
    let yMin: number = Math.min(projectStart.y, projectEnd.y);
    let yMax: number = Math.max(projectStart.y, projectEnd.y);


    let curtainRectRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(curtainRect);
    let roomEdgeXDot: number = Math.abs(roomEdgeDir.clone().dot(xDir));
    let roomEdgeyDot: number = Math.abs(roomEdgeDir.clone().dot(yDir));
    if(roomEdgeXDot > 0.9)
    {
        curtainRectRange.xMin = xMin;
        curtainRectRange.xMax = xMax;
    }
    if(roomEdgeyDot > 0.9)
    {
        curtainRectRange.yMin = yMin;
        curtainRectRange.yMax = yMax;
    }

    // 柜体避让
    for(let cabinetRect of cabinetRects)
    {
        if(TBaseRoomToolUtil.instance.isParallelTwoEdges(cabinetRect.backEdge,  curtainRect.backEdge))
        {
            continue;
        }
        let cabinetRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(cabinetRect);
        if(!TBaseRoomToolUtil.instance.isOverlayRange2ds(cabinetRange, curtainRectRange, false))
        {
            continue;
        }
        let cabinetNor: Vector3 = cabinetRect.nor;
        let cabinetXDot: number = cabinetNor.clone().dot(xDir);
        let cabinetYDot: number = cabinetNor.clone().dot(yDir);
        if(cabinetXDot > 0.9)
        {
            curtainRectRange.xMin = cabinetRange.xMax;
        }
        else if(cabinetXDot < -0.9)
        {
            curtainRectRange.xMax = cabinetRange.xMin;
        }

        if(cabinetYDot > 0.9)
        {
            curtainRectRange.yMin = cabinetRange.yMax;
        }
        else if(cabinetYDot < -0.9)
        {
            curtainRectRange.yMax = cabinetRange.yMin;
        }
    }

    let curtainRectNor: Vector3 = curtainRect.nor.clone();
    let newCurtainRect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(curtainRectRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(curtainRectRange));
    let newCurtainCenter: Vector3 = newCurtainRect.rect_center.clone();
    if(Math.abs(newCurtainRect.nor.clone().dot(curtainRectNor)) > 0.9)
    {
        newCurtainRect.nor = curtainRectNor.clone();
    }
    else
    {
        let newCurtainRectLen: number = newCurtainRect.length;
        let newCurtainRectDepth: number = newCurtainRect.depth;
        newCurtainRect.length = newCurtainRectDepth;
        newCurtainRect.depth = newCurtainRectLen;
        newCurtainRect.nor = curtainRectNor.clone();
    }
    newCurtainRect.rect_center = newCurtainCenter.clone();
    newCurtainRect.updateRect();
    curtainRect.copy(newCurtainRect);
}