import { checkIsMobile } from '@/config';
import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ token, css }) => {
  // color: ${token.colorPrimary};
  let ismobile = checkIsMobile();
  return {
    root: css`
      position: relative;
      margin-top: 12px;
    `,
    rootItem: css`
      display: flex;
      justify-content: space-between;
      padding: 5px 16px 5px 12px;
    `,
    topInfo: css`
      display: flex;
      /* justify-content: space-between; */
      padding: 5px 12px;
      user-select: text;
      position: relative;
      #good {
        width: 64px;
        height: 64px;
        border-radius: 4px;
        background: #f2f3f5;
        /* border: 1px solid #0000000F; */
        margin-right: 8px;
      }
      #lock {
        position: absolute;
        width: 24px;
        height: 30px;
        cursor: pointer;
        top: 25px;
        right: 15px;
      }
    `,
    sliderHeight: css`
      padding: 12px 8px;
      background: #F4F5F5;
      margin-top: 12px;
      border-radius: 4px;
      margin-left: 12px;
      margin-right: 12px;
      .sliderTitle {
        font-size: 12px;
        font-weight: 600;
        color: #282828;
      }
    `,
    category: css`
      color: #282828;
      font-size: 12px;
      line-height: 24px;
      font-weight: 600;
    `,
    size: css`
      color: #a2a2a5;
      font-size: 12px;
      line-height: 20px;
      display: flex;
      align-items: center;
    `,
    sizeWarning: css`
      line-height: 20px;
      position: relative;
      display: inline-block;
      :after {
        content: '超出目标尺寸范围';
        visibility: hidden;
        width: 110px;
        background-color: yellow;
        color: red;
        text-align: center;
        border-radius: 6px;
        padding: 5px 5px 0px 0px;
        position: absolute;
        z-index: 1;
        font-size: 12px;
        bottom: 100%;
        left: 50%;
        opacity: 0;
        transition: opacity 0.3s;
      }
      :hover::after {
        visibility: visible;
        opacity: 1;
      }
    `,
    changeLine: css``,
    goodListRoot: css`
      height: calc(100vh - 390px);
      overflow-y: scroll;
    `,
    goodsList: css`
      display: flex;
      flex-wrap: wrap;
      align-items: stretch;
      padding: 2px 0 0 12px;
      position: relative;
    `,
    loading: css`
      height: 30px;
      display: flex;
      justify-content: center;
      color: #a2a2a5;
      font-size: 12px;
      line-height: 30px;
    `,
    goodsItem: css`
      margin: 0px 4px 56px 4px;
      border-radius: 4px;
      transition: all 0.3s;
      outline: 1px solid #fff;
      position: relative;
      ${ismobile
        ? `
        height: 100px;
        width: 45%;
      `
        : `
        height: 120px;
        width: 46%;
      `}
      img {
        width: 100%;
        height: 100%;
        background: #f2f3f5;
        transition: all 0.3s;
        padding: 2px;
        margin-bottom: 5px;
      }
      .repalceBtn {
        display: none;
        height: 30px;
        line-height: 30px;
        width: 100%;
        position: absolute;
        bottom: 0px;
        text-align: center;
        cursor: pointer;
        background: rgba(255, 255, 255, 0.5);
        font-weight: 700;
      }
      :hover {
        outline: 1px solid #147ffa;
        .repalceBtn {
          display: block;
        }
      }
    `,
    selected: css`
      outline: 1px solid #147ffa;
    `,
    selectIcon: css`
      background-color: #147ffa;
      width: 16px;
      height: 16px;
      position: absolute;
      right: 0px;
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
      border-top-right-radius: 2px;
    `,
    sizeInfo: css`
      color: #5b5e60;
      font-family: PingFang SC;
      font-weight: regular;
      font-size: 12px;
      line-height: 1.67;
      letter-spacing: 0px;
      text-align: left;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 120px;
    `,
    goodPopover: css`
      position: absolute;
      display: block;
      top: 0px;
      left: -325px;

      /* right: 0px; */
      width: 320px;
      height: 426px;
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 8px 24px 0 rgba(0, 0, 0, 0.16);
      img {
        width: 100%;
        height: 320px;
        background: #eeeff2;
      }
    `,
    goodPopoverInfo: css`
      height: 106px;
      padding: 16px;
      user-select: text;
    `,
    name: css`
      color: #282828;
      font-family: PingFang SC;
      font-weight: semibold;
      font-size: 14px;
      line-height: 1.57;
      font-weight: 600;
    `,
    sizes: css`
      color: #5b5e60;
      font-family: PingFang SC;
      font-weight: regular;
      font-size: 12px;
      line-height: 1.67;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    `,
    line: css`
      width: 85%;
      margin: 15px auto;
      height: 1px;
      background: #00000014;
    `,
    categoryTitle: css`
      color: #282828;
      font-family: PingFang SC;
      font-weight: semibold;
      font-size: 14px;
      line-height: 1.57;
      letter-spacing: 0px;
      text-align: left;
      font-weight: 600;
      padding: 0 0px 6px 12px;
    `,
    emptyInfo: css`
      text-align: center;
      margin-top: 120px;
      img {
        width: 60px;
        height: 60px;
        margin-bottom: 12px;
      }
      .desc {
        color: #a2a2a5;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 12px;
        line-height: 20px;
        letter-spacing: 0px;
        text-align: center;
      }
      button {
        margin-top: 12px;
      }
    `,
    item_locked: css`
      position: absolute;
      width: 100%;
      height: 100%;
      background: #33333388;
      z-index: 101;
    `,
    findInfo: css`
      display: flex;
      justify-content: space-between;
      padding-right: 4px;
      position: relative;
      padding-left: 12px;
      margin-bottom: 12px;
      align-items: center;
    `,
    container_input: css`
      width: 100%;
      border-radius: 30px;
      background: #f2f3f5;
      color: #000;
      font-family: PingFang SC;
      font-weight: regular;
      font-size: 12px;
      line-height: 20px;
      letter-spacing: 0px;
      text-align: left;
      width: 100%;
      height: 24px;
      margin: 5px 0 0 0px;
      padding-left: 25px;
      margin-right: 7px;
    `,
    Icon: css`
      position: absolute;
      top: 8px;
      left: 16px;
      cursor: pointer;
      z-index: 100;
    `
  };
});
