import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import useStyle from './style';
import { ColorPicker } from "@svg/antd";

const ColorWidget = (props: any) => {
    const { t } = useTranslation();
    const {styles} = useStyle();
    const [value,setValue] = useState<string>(props.value);


    

    const onSelectColor = (color:string) => {
        setValue(color);
    
        if(props?.schema?.onChange)
        {
            props.schema.onChange(color);
        }
    };

    useEffect(()=>{
        setValue(props.schema.defaultValue);
    },[props.schema.defaultValue])

    useEffect(()=>{
        setValue(props.schema.defaultValue);
    },[]);
    return (
        <div className={styles.root}>
            <label>{t('颜色')}</label>
            <ColorPicker disabledAlpha={true} presets={[
                {
                    label:true,
                    colors:["#66b8ff","#ffb866","#66ffb8"],
                    defaultOpen:true
                }
            ]}  
            defaultFormat="hex"
            value={value}
            onChange={(value,hex:string)=>{
                onSelectColor(hex);
            }}
            ></ColorPicker>
        </div>
    );
};
export default ColorWidget;