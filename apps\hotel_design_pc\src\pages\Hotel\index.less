

.main_3d_div {
    width: 1000px;
    height:  800px;
    border: 1px solid;
    margin-top: 10px;
}
.content {
    cursor: pointer;
}
.content:hover {
    background-color: rgba(0, 255, 255, 0.18);
}
.tree-children {
    display: none;
    margin-left: 10px;
    line-height: 30px;
}
.tree-children.isExpand {
    display: block;
}
.modelTreeContainer {
    position: absolute;
    left: 1020px;
    top: 10px;
    width: 500px;
    height: 800px;
    overflow-y: auto;
    border: 1px solid;
}
.MeshItem {
    cursor: pointer;
}
.MeshItem:hover {
    background-color: rgba(0, 255, 255, 0.18);
}
.MeshItem .MeshName {
    width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
}

.SeriesDiv {
    width: 300px;
    height: 60px;
    display: block;
    .data_input {
         width: 200px;
    }
}
.SeriesContainer {
    width: 300px;
    height: calc(100vh - 120px);
    overflow-y: auto;
    position: absolute;
    left : 0;
}
.leftPanelContainer {
    width: 300px;
    height: calc(100vh - 120px);
    overflow-y: auto;
    position: absolute;
    left : 0;  
}
.candidateItem {
    line-height:  24px;
    cursor: pointer;
}
.candidateItem:hover {
    background: #ccf;
}
.candidateItem.selected {
    background: #ccf;
}

.IsSolidNode {
    background-color: #fe3;
}
.imgItem {
    width: 300px;
    height:350px;
}
.imgItem img {
    width: 300px;
}

.header_topmenu {
    position: fixed;
    top: 0;
    width: 100%;
    text-align: center;
    height: 30px;
    line-height: 30px;
    background-color: #333;
    color:  #ccc;
}
.header_tab {
    width: 150px;
    font-size: 13px;
    cursor: pointer;
    display: inline-block;
}
.header_tab.active {
    background-color: #ccc;
    color:  #333;
}

.main_app {
    position: absolute;
    top: 30px;
}

.queryHistoryList {
    position: absolute;
    z-index: 10;
    width:250px;
    height:300px;
    background-color: rgba(255, 255, 255, 0.8);
    border: 1px solid #aaa;
}
.queryHistoryList .item {
    cursor: pointer;

}
.queryHistoryList .item:hover{
    background-color: rgb(128, 255, 255);
}