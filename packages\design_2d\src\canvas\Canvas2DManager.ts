import { Object2DManager } from "../object2d/Object2DManager";
import { CanvasAxisRenderer } from "./features/CanvasAxisRenderer";
import { CanvasEventHandler } from "./features/CanvasEventHandler";
import { IEventCenter } from "../events/IEventCenter";
import { Design2DContext } from "../Design2DContext";
import { ITransform } from "../types/ITransform";

/**
 * 2D画布管理器
 */
export class Canvas2DManager {
    private _isInit: boolean = false;
    private _container: HTMLDivElement | undefined;
    private _canvas: HTMLCanvasElement | undefined;
    private _ctx: CanvasRenderingContext2D | undefined;
    private _isRendering: boolean = false;
    private _scale: number = 0.05;
    private _offsetX: number = 0;
    private _offsetY: number = 0;
    private _axisRenderer: CanvasAxisRenderer = new CanvasAxisRenderer();
    private _eventHandler: CanvasEventHandler = new CanvasEventHandler();
    private _boundTransformCallback = this.TransformCanvas.bind(this);
    private _context: Design2DContext;

    constructor(context: Design2DContext) {
        this._context = context;
    }

    /**
     * 获取对象管理器
     */
    private get object2DManager(): Object2DManager|null {
        if (!this._context) {
            console.error('Context not set. Call setContext() first.');
        }
        return this._context?.object2DManager || null;
    }

    /**
     * 获取事件中心
     */
    private get eventCenter(): IEventCenter {
        if (!this._context) {
            throw new Error('Context not set. Call setContext() first.');
        }
        return this._context.eventCenter;
    }

    /**
     * 使用内置canvas
     * @param id 画布id
     */
    public createCtxByInternalCanvas(id: string): void {
        if (this._isInit) {
            return;
        }
        this._canvas = document.createElement('canvas');
        this._canvas.id = `canvas2d-${id}`;
        this._canvas.style.width = '100%';
        this._canvas.style.height = '100%';
        
        const ctx = this._canvas.getContext('2d');
        this._ctx = ctx || undefined;
        this._initializeComponents();
    }

    /**
     * 使用外部canvas
     * @param canvas 外部传入的canvas元素
     */
    public createCtxByExternalCanvas(canvas: HTMLCanvasElement): void {
        if (this._isInit) {
            return;
        }
        
        this._canvas = canvas;
        this._isInit = true;
        
        const ctx = this._canvas.getContext('2d');
        this._ctx = ctx || undefined;
        
        this._initializeComponents();
       
    }

    /** 
     * 绑定主div
     */
    public bindMainDiv(container: HTMLDivElement): boolean {
        if (!container) {
            return false;
        }
        this._container = container;
        if (this._canvas) {
            this._container.appendChild(this._canvas);
        }
        this._resizeCanvas();
         // 启动渲染
         return true;
    }

    /**
     * 获取canvas实例
     */
    public get canvas(): HTMLCanvasElement | undefined {
        return this._canvas;
    }

    /**
     * 初始化组件
     */
    private _initializeComponents(): void {
        if (this._ctx && this._canvas) {
            this._axisRenderer.init(this._ctx, this._canvas);
            this._eventHandler.init(this._canvas, this.eventCenter);
            this._eventHandler.setTransformCallback(this._boundTransformCallback);
        }
    }


    public startRender(): void {
        if (this._isRendering) {
            return;
        }
        this._isRendering = true;
        this._render();
    }

    public stopRender(): void {
        this._isRendering = false;
    }

    public clearCanvas(): void {
        if (!this._ctx || !this._canvas) {
            return;
        }
        this._ctx.clearRect(0, 0, this._canvas.width, this._canvas.height);
    }

    public updateCanvas(force: boolean = false): void {
        if (force || this._isRendering) {
            this._render();
        }
    }

    /**
     * 设置网格属性 默认关闭
     */
    public setGridConfig(config: {
        visible?: boolean;
        size?: number;
        color?: string;
        lineWidth?: number;
        backgroundColor?: string;
    } = {}): void {
        this._axisRenderer.setGrid(config);
        if (this._isRendering) {
            this.updateCanvas(true);
        }
    }

    private _resizeCanvas(): void {
        if (this._canvas && this._container) {
            const rect = this._container.getBoundingClientRect();
            this._canvas.width = rect.width;
            this._canvas.height = rect.height;
            
            // 重新计算原点位置
            this._centerOrigin();
        }
    }


    private TransformCanvas(args: {scale?: number, offsetX?: number, offsetY?: number, mouseX?: number, mouseY?: number}): void {
        if (args.scale !== undefined) {
            const oldScale = this._scale;
            this._scale *= args.scale;
            this._scale = Math.max(0.0001, Math.min(10, this._scale));
            
            // 如果提供了鼠标位置，实现以鼠标为中心的缩放
            if (args.mouseX !== undefined && args.mouseY !== undefined) {
                // 计算鼠标在画布坐标系中的位置（缩放前）
                const mouseCanvasX = (args.mouseX - this._offsetX) / oldScale;
                const mouseCanvasY = (args.mouseY - this._offsetY) / oldScale;
                
                // 计算新的偏移量，保持鼠标位置不变
                this._offsetX = args.mouseX - mouseCanvasX * this._scale;
                this._offsetY = args.mouseY - mouseCanvasY * this._scale;
            }
            
            this.updateCanvas(true);
        }
        
        if (args.offsetX !== undefined && args.offsetY !== undefined) {
            this._offsetX += args.offsetX;
            this._offsetY += args.offsetY;
            this.updateCanvas(true);
        }
    }

    public getTransform(): ITransform {
        return {
            scale: this._scale,
            offsetX: this._offsetX,
            offsetY: this._offsetY
        };
    }

    public centerOrigin(): void {
        this._centerOrigin();
    }

    private _centerOrigin(): void {
        if (!this._canvas) return;

        const rect = this._canvas.getBoundingClientRect();
        this._offsetX = rect.width / 2;
        this._offsetY = rect.height / 2;
    }

    public dispose(): void {
        this.stopRender();
        
        if (this._container && this._canvas ) {
            // 检查canvas是否真的是容器的子节点，避免removeChild错误
            if (this._container.contains(this._canvas)) {
                this._container.removeChild(this._canvas);
            }
        }
        
        this._eventHandler.dispose();
        
        this._isInit = false;
        this._container = undefined;
        this._canvas = undefined;
        this._ctx = undefined;
    }

    /**
     * 渲染方法，外部传入 painter 和 object2DManager
     */
    public _render(): void {
        if (!this._ctx || !this._canvas || !this.object2DManager) {
            return;
        }
        this.clearCanvas();
        this._ctx.save();
        this._axisRenderer.drawBackground();
        this._ctx.translate(this._offsetX, this._offsetY);
        this._ctx.scale(this._scale, this._scale);
        this._axisRenderer.drawGrid(this._offsetX, this._offsetY, this._scale);
        this.object2DManager.updateObject2D();
        this._ctx.restore();
        // 强制更新一次画布
        this._ctx.save();
        this._ctx.restore();
    }
} 