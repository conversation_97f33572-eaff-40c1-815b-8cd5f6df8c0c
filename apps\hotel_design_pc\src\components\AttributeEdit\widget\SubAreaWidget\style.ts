import { createStyles } from '@svg/antd/es/theme/utils'
export default createStyles(({ css }: any) => {
  return {
    root: css`
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    font-size: 12px;

    label {
        display: flex;
        align-items: center;
    }

    div {
        display: flex;
        align-items: center;
    }

    .svg-input-number-suffix {
        position: relative;
        top: 0px;
        right: 3px;
        height: 2px;
    }

    .svg-input-number-small {
        width: 73px;
        height: 24px;
        line-height: 24px;
        font-size: 18px;
        transform: translateX(-8px);
    }
    .btn{
        display:block;
        position:relative;
        margin: 8px auto;
        border-radius: 2px;
        font-size: 12px;
        font-weight: 500;
        width:150px;
        // border:1px solid;
        height:24px;
        line-height:24px;
        text-align:center;
        background:#f2f2f2;
        color:#262626;
        cursor:pointer;
        &:hover {
            background:#e2e2e2;
        }
    }
    .sub_btn {
        text-align:center;
        padding-left: 5px;
        padding-right: 5px;
        border-radius:2px;
        cursor:pointer;
        background:#f2f2f2;
        opacity : 0.8;
        &:hover {
            opacity:1.0;
        }
    }
`,
  }
})
