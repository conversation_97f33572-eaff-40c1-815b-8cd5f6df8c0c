# 状态机 (FSM) 使用文档

## 📁 文件结构

```
fsm/
├── FSM.ts                    # 基础状态机类
├── interfaces/               # 接口定义
│   └── IState.ts            # 状态接口
├── fsms/                    # 具体状态机实现
│   ├── MainAppFSM.ts        # 主应用状态机
│   ├── Design2DFSM.ts       # 2D设计状态机
│   └── Design3DFSM.ts       # 3D设计状态机
├── states/                  # 状态实现
│   ├── 2d/                  # 2D状态
│   │   ├── Idle2DState.ts   # 2D空闲状态
│   │   ├── Init2DState.ts   # 2D初始化状态
│   │   └── DrawRectState.ts # 2D绘制状态
│   └── 3d/                  # 3D状态
│       ├── Idle3DState.ts   # 3D空闲状态
│       └── Init3DState.ts   # 3D初始化状态
├── const/                   # 常量定义
│   └── FSMConst.ts          # 状态机常量
└── README.md                # 本文档
```

## 🏗️ 架构设计

### 状态机 = 状态树

本架构采用**树结构思维**，将状态机视为一棵状态树：

```
MainAppFSM (根节点)
├── Design2DFSM (子节点)
│   ├── idle2d (叶子节点)
│   ├── init2d (叶子节点)
│   └── drawRect (叶子节点)
└── Design3DFSM (子节点)
    ├── idle3d (叶子节点)
    └── init3d (叶子节点)
```

### 核心设计理念

1. **状态机就是树**：每个节点是状态，子节点是子状态机
2. **所有操作都是树遍历**：查找、切换、检查都是遍历这棵树
3. **添加状态就是向树加内容**：`addState` 就是添加节点
4. **FSM即状态**：FSM本身也是一个状态，支持无限嵌套

### 简化设计

- **一次遍历完成**：查找状态和检查转换权限在一次遍历中完成
- **统一转换逻辑**：所有转换都通过 `transitionTo` 方法
- **树遍历思维**：用树的思维简化复杂的状态管理

## 🔧 使用方法

### 1. 创建状态机

```typescript
import { FSM } from './FSM';
import { FSMNames } from './const/FSMConst';

export class MyFSM extends FSM {
  constructor() {
    super(FSMNames.MY_FSM);
    
    // 添加状态（向树添加节点）
    this.addState(new MyState1());
    this.addState(new MyState2());
    
    // 设置默认状态
    this.setDefaultState('state1');
    
    // 添加转换规则（配置树中的路径）
    this.addTransition('state1', 'state2');
    this.addTransition('state2', 'state1');
  }
}
```

### 2. 创建状态

```typescript
import type { IState } from '../interfaces/IState';

export class MyState implements IState {
  name: string;
  parent?: any;

  constructor(name: string = 'myState') {
    this.name = name;
  }

  onEnter(data?: any): void {
    console.log(`进入状态: ${this.name}`);
  }

  onExit(data?: any): void {
    console.log(`退出状态: ${this.name}`);
  }
}
```

### 3. 状态转换（树遍历）

```typescript
// 所有转换都是树遍历
fsm.transitionTo('targetState', data);

// 检查是否可以转换
const canTransition = fsm.canTransitionTo('targetState');
```

### 4. 嵌套状态机（树结构）

```typescript
// 创建子状态机（子节点）
const subFSM = new SubFSM();
mainFSM.addState(subFSM);

// 跨层切换（树遍历）
mainFSM.transitionTo('subState');  // 自动在树中查找
```

### 5. 通配符转换

```typescript
import { ANY_STATE } from './const/FSMConst';

// 从任何状态都可以切换到空闲状态
fsm.addTransition(ANY_STATE, 'idle');

// 从空闲状态可以切换到任何状态
fsm.addTransition('idle', ANY_STATE);
```

## 📋 API 参考

### FSM 类

#### 构造函数
```typescript
constructor(name: string)
```

#### 状态管理
```typescript
addState(state: IState): void
removeState(stateName: string): void
getState(stateName: string): IState | null
setDefaultState(stateName: string): void
getDefaultState(): IState | null
```

#### 转换管理
```typescript
transitionTo(stateName: string, data?: any): boolean
canTransitionTo(stateName: string): boolean
addTransition(from: string, to: string): void
removeTransition(from: string, to: string): void
```

### IState 接口

```typescript
interface IState {
  name: string;
  parent?: IFSM;
  
  onEnter(data?: any): void;
  onExit(data?: any): void;
}
```

## 🎯 最佳实践

### 1. 状态设计原则

- **单一职责**: 每个状态只负责一种行为模式
- **明确转换**: 状态转换应该有明确的条件
- **树结构思维**: 用树的思维组织状态关系
- **FSM即状态**: 充分利用FSM可以作为状态的设计

### 2. 状态命名规范

```typescript
// 使用描述性的状态名称
const states = {
  IDLE: 'idle',
  INIT: 'init',
  LOADING: 'loading',
  EDITING: 'editing',
  SAVING: 'saving'
};
```

### 3. 通配符使用

```typescript
// 让空闲状态成为"万能状态"
fsm.addTransition(ANY_STATE, 'idle');  // 任何状态都可以回到空闲
fsm.addTransition('idle', ANY_STATE);  // 空闲状态可以去任何地方
```

### 4. 调试技巧

```typescript
// 检查状态机结构
console.log('所有状态:', Array.from(fsm.states.keys()));

// 检查转换规则
console.log('转换规则:', fsm.transitions);
```

## 🔍 使用示例

### 完整的应用状态机示例

```typescript
import { FSM } from './FSM';
import { FSMNames, MainAppStates, ANY_STATE } from './const/FSMConst';

export class MainAppFSM extends FSM {
  constructor() {
    super(FSMNames.MAIN_APP);
    
    // 添加子状态机（FSM作为状态）
    this.addState(new Design2DFSM());
    this.addState(new Design3DFSM());
    
    // 设置默认状态
    this.setDefaultState(MainAppStates.DESIGN_2D);
    
    // 添加跨FSM转换
    this.addTransition(MainAppStates.DESIGN_2D, MainAppStates.DESIGN_3D);
    this.addTransition(MainAppStates.DESIGN_3D, MainAppStates.DESIGN_2D);
  }
}
```

### 状态树切换示例

```typescript
// 假设当前状态：MainAppFSM -> Design2DFSM -> idle2d

// 1. 同级切换：mainFSM.transitionTo('Design2D') - 切换到2D设计模式
// 2. 向下切换：mainFSM.transitionTo('idle2d') - 直接切换到2D空闲状态（树遍历）
// 3. 跨树切换：mainFSM.transitionTo('idle3d') - 从2D直接切换到3D空闲状态（树遍历）
```

### 状态实现示例

```typescript
export class Idle2DState implements IState {
  name: string;
  parent?: any;

  constructor(name: string = 'idle2d') {
    this.name = name;
  }

  onEnter(data?: any): void {
    console.log('激活2D空闲模式');
  }

  onExit(data?: any): void {
    console.log('清理2D空闲模式');
  }
}
```

### FSM作为状态的示例

```typescript
// 子FSM可以作为父FSM的状态
export class Design2DFSM extends FSM {
  constructor() {
    super('Design2D');
    
    // 添加具体状态
    this.addState(new Init2DState());
    this.addState(new Idle2DState());
    
    // 设置默认状态
    this.setDefaultState('Init2D');
    
    // 添加内部转换
    this.addTransition('Init2D', 'Idle2D');
  }
  
  // FSM的onEnter方法（作为状态时的进入逻辑）
  onEnter(data?: any): void {
    console.log('进入2D设计模式');
  }
  
  // FSM的onExit方法（作为状态时的退出逻辑）
  onExit(data?: any): void {
    console.log('退出2D设计模式');
  }
}
```

## 🚀 扩展指南

### 添加新状态

1. 创建状态类实现 `IState` 接口
2. 在对应的FSM中添加状态
3. 定义转换规则
4. 更新常量定义

### 添加新FSM（作为状态）

1. 创建FSM类继承基础FSM类
2. 实现`onEnter`和`onExit`方法（作为状态时的行为）
3. 在父FSM中添加该FSM作为状态
4. 定义跨FSM的转换规则

### 添加通配符转换

```typescript
// 添加通配符转换
fsm.addTransition(ANY_STATE, 'targetState');  // 从任何状态到目标状态
fsm.addTransition('sourceState', ANY_STATE);  // 从源状态到任何状态
```

## 🎯 设计优势

### 1. 简洁性
- **一次遍历**：查找和检查在一次遍历中完成
- **统一接口**：所有转换都通过 `transitionTo`
- **树结构思维**：用树的思维简化复杂逻辑

### 2. 灵活性
- **无限嵌套**：支持任意层级的嵌套状态机
- **跨层切换**：可以直接切换到任何层级的状态
- **通配符支持**：简化复杂的转换关系配置

### 3. 可维护性
- **清晰结构**：树结构让状态关系一目了然
- **模块化设计**：每个FSM都是独立的模块
- **类型安全**：完整的TypeScript类型支持

---

## 📚 相关文档

- [状态机设计模式](https://en.wikipedia.org/wiki/State_pattern)
- [有限状态机](https://en.wikipedia.org/wiki/Finite-state_machine)
- [树数据结构](https://en.wikipedia.org/wiki/Tree_(data_structure)) 