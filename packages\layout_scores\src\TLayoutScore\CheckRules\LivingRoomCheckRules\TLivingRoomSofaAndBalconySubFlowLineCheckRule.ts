import {
    DoorType,
    I_CheckRuleOptions,
    TBaseRoomToolUtil,
    TFigureElement,
    TLayoutScoreParamName,
    TRoom
} from "@layoutai/layout_scheme";
import { ZEdge, ZPolygon, ZRect } from "@layoutai/z_polygon";
import { TLivingRoomClassfierCheckRule } from "./TLivingRoomClassfierCheckRule";

export class TLivingRoomSofaAndBalconySubFlowLineCheckRule extends TLivingRoomClassfierCheckRule
{
    constructor(figure_categories:string[], options : I_CheckRuleOptions = {})
    {
        super(figure_categories, options);
    }

    private _sofaAndBalconyFlowMinDist: number;
    private _sofaAndBalconyBlockFlowScore: number;

    protected setParamConfig(): void {
        let groupName: string = TLayoutScoreParamName.flowGroup;
        let ruleName: string = TLayoutScoreParamName.livingRoomSofaAndBalconySubFlowLineRule;
        this._sofaAndBalconyFlowMinDist = this.parentJudge.ruleParamConfig.getValueByParamConfig(groupName, [ruleName, TLayoutScoreParamName.sofaAndBalconyFlowMinDistance]);
        this._sofaAndBalconyBlockFlowScore = this.parentJudge.ruleParamConfig.getValueByParamConfig(groupName, [ruleName, TLayoutScoreParamName.blockSofaAndBalconyFlowScore]);
    }
    
    checkFigureScore(room: TRoom, figure_element: TFigureElement, main_figures?: TFigureElement[]) {
        let roomPoly: ZPolygon = room.room_shape._poly.clone();
        let sofaFigures: TFigureElement[] = this.categoryFigures.get(this.sofaName);
        if(sofaFigures.length == 0 || !this.doorInfos.has(DoorType.k_balcony))
        {
            return {score: 0};
        }
        // 1. 沙发到阳台
        let sofaFigureRects: ZRect[] = [];
        sofaFigures.forEach(figure => {
            sofaFigureRects.push(figure.rect.clone().expandPolygon(this._sofaAndBalconyFlowMinDist / 2));
        });

        let fixedFigureRects: ZRect[] = [];
        this.categoryFigures.get(this.fixedName).forEach(figure => {
            fixedFigureRects.push(figure.rect.clone().expandPolygon(this._sofaAndBalconyFlowMinDist / 2));
        });

        let sofaAndBalconyPoly: ZPolygon = roomPoly.clone().expandPolygon(-this._sofaAndBalconyFlowMinDist / 2);
        let subSofaAndBalconyPolygons: ZPolygon[] = sofaAndBalconyPoly.substract_polygons(fixedFigureRects);
        let roomEdgeWithDoorInfos: Map<DoorType, ZEdge[]> = new Map<DoorType, ZEdge[]>();
        for(let poly of subSofaAndBalconyPolygons)
        {
            let tempDoorInfos: Map<DoorType, ZEdge[]> = TBaseRoomToolUtil.instance.getRoomEdgeWithDoorInfos(poly, this.doorInfos);
            for(let entry of tempDoorInfos.entries())
            {
                if(!roomEdgeWithDoorInfos.has(entry[0]))
                {
                    roomEdgeWithDoorInfos.set(entry[0], entry[1]);
                }
                else
                {
                    roomEdgeWithDoorInfos.get(entry[0]).push(...entry[1]);
                }
            }
        }
        
        let sofaPolyEdges: ZEdge[][] = this.getFiguresEdgeFromPolys(sofaFigureRects, subSofaAndBalconyPolygons);
        if(sofaPolyEdges.length == 0)
        {
            return {score: this._sofaAndBalconyBlockFlowScore};
        }
        // 声明下，这里基本上就是一些内部的
        let isContinue: boolean = false;
        for(let subPoly of subSofaAndBalconyPolygons)
        {
            // 判断是否有沙发TV，如果都有则不进行判断，如果没有其中一个则需要另外进行判断
            let isHasSofa: boolean = this.hasPolyEdgesBySubPoly(sofaPolyEdges, subPoly);
            let isHasBalcony: boolean = this.hasPolyEdgesBySubPoly([roomEdgeWithDoorInfos.get(DoorType.k_balcony)], subPoly);
            if(isHasSofa && isHasBalcony)
            {
                isContinue = true;
                break;
            }
            else if(isHasBalcony)
            {
                let tempPolygons: ZPolygon[] = subPoly.substract_polygons(sofaFigureRects);
                if(tempPolygons.length > 1 || (tempPolygons.length == 1 && this.isDifferentTwoPolygons(tempPolygons[0], subPoly)))
                {
                    isContinue = true;
                }
            }
        }
        if(!isContinue)
        {
            return {score: this._sofaAndBalconyBlockFlowScore};
        }
        return {score: 0};
    }
}