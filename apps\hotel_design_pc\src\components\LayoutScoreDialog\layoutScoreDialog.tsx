
import useCommonStyles from "../common_style/index";
import { useEffect, useRef, useState } from 'react';
import { LayoutAI_App } from '@/Apps/LayoutAI_App';
import { EventName } from '@/Apps/EventSystem';
import { PanelContainer } from '@svg/antd-cloud-design';
import { TAppManagerBase } from '@/Apps/AppManagerBase';
import { TLayoutEntityContainer } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TLayoutEntityContainter';
import { I_LayoutScore, TLayoutJudgeContainter } from '@/Apps/LayoutAI/Layout/TLayoutScore/TLayoutJudge';
import LayoutScoreCard from './layoutScoreCard';


const LayoutScoreDialog: React.FC = ()=>{

    const common_styles = useCommonStyles().styles;
    const [isDispaly, setIsDisplay] = useState<boolean>(false);
    const [layoutScoreList, setLayoutScoreList] = useState<I_LayoutScore[]>([]);
    const t = LayoutAI_App.t;
    const object_id = "LayoutScoreDialog";
    const timerRef = useRef(null);

    const register_events = ()=>{

    }

    register_events();

    let width = 265;
    let height = 600;

    let container : TLayoutEntityContainer = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    const update_layout_score = async ()=>{
        let selected_room = container._selected_room;
        
        if(selected_room)
        {
            let result = TLayoutJudgeContainter.ComputeScoreInRoom(selected_room);
            setLayoutScoreList(result);
            // console.log(result)
        }
    }

    const start_updating = ()=>{
        if(timerRef.current)
        {
            clearInterval(timerRef.current);
        }
        timerRef.current = setInterval(() => {
           update_layout_score();
        }, 1000);

    }
    const stop_updating = ()=>{
        if(timerRef.current)
        {
            clearInterval(timerRef.current);
        }
    }
    const showDialog = (t:boolean)=>{
        setIsDisplay(t);
    }
    useEffect(()=>{

        LayoutAI_App.on(EventName.ShowLayoutScoreDialog,(t:boolean=true)=>{
            showDialog(t);
            if(t)
            {
                update_layout_score();
            }

        });

        LayoutAI_App.on_M(EventName.UpdateLayoutScore,object_id,(t:boolean=true)=>{
            update_layout_score();
        })
        // 设置定时器
        // start_updating();

        // 返回一个清理函数
        return () => {
            // 组件卸载时清除定时器
            // clearInterval(timerRef.current);
        };
    },[]);

    return (
        <>
            {isDispaly && 
            <PanelContainer title={t("布局评分器")} right={250} width={width} height={height}  resizable={true} draggable={true} onClose={()=>{
                showDialog(false);
            }}  bodyStyle={{background:"#ffffff",border:"0",boxShadow:"0"}}> 
                <LayoutScoreCard layoutScoreList={layoutScoreList} style={0}></LayoutScoreCard>

            {/* <div className={common_styles.layoutScoreContent}>
                <div className='room_title'>
                     {t("当前空间")+"： "+ (t(container?._selected_room?.name)||"")}
                </div>
            </div> */}
            
          </PanelContainer>}
        </>

    )
}

export default LayoutScoreDialog;