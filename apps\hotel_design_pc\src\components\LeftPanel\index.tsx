import React, { useEffect, useState } from 'react';
import useStyles from './style';
import style from './index.module.less';
import { Segmented } from '@svg/antd';

import { useTranslation } from 'react-i18next'

import { observer } from "mobx-react-lite";
import { useStore } from '@/models';
import { AI2DesignBasicModes, EventName, LayoutAI_App, TBaseEntity } from '@layoutai/layout_scheme';
import { IRoomEntityType } from '@layoutai/basic_data';
import SchemeList from './components/SchemeList/schemeList';
import LeftMenu from './components/Menu/leftMenu';

/**
 * @description 左侧面板
 */
const LeftPanel: React.FC = () => {
  const { t } = useTranslation()
  const { styles } = useStyles();
  const store = useStore();
  const [value, setValue] = useState<string | number>(t('布局'));
  const [designMode,setDesignMode] = useState<string>(AI2DesignBasicModes.AiCadMode);
  const object_id = "LeftPanel_x0";
  useEffect(()=>{
    LayoutAI_App.on_M(EventName.AIDesignModeChanged,object_id,()=>{
      setDesignMode(LayoutAI_App.instance._current_handler_mode);
    });

  },[]);

  useEffect(() => {
    LayoutAI_App.on_M(EventName.SelectingTarget, 'LeftPanelValue',  (params, event, pp) => {
      
      let Entity: TBaseEntity = params || null;

      const entity_type: IRoomEntityType = Entity?.type || null;

      if (entity_type === "BaseGroup" || entity_type === "Furniture") {
        setValue(t('素材'));
      } else if (entity_type ==='RoomArea') {
        setValue(t('布局'));
      } else if (!store.homeStore.selectEntity) {
        setValue(t('布局'));
      }
    });
  }, [])
  
function panelItem(mode:string)
{
  switch(mode){
    case AI2DesignBasicModes.AiCadMode:
      return  <>
            <div>
              <Segmented value={value} onChange={setValue} block options={[t('布局'), t('素材')]} />  
            </div>
            <div style={{ display: value === t('布局') ? 'block' : 'none' }}>
              <SchemeList width={300} showSchemeName={true} />
            </div>
            <div style={{ display: value === t('布局') ? 'none' : 'block' }}>
              <LeftMenu />
            </div>
          </>;
    default:
      return <></>;
  }
}
  return (
    <div className={`${styles.root} ${style.root}`}>
        {panelItem(store.homeStore.designMode)}
    </div>
  );
};

export default observer(LeftPanel);
