.xml-viewer {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    background-color: #f8f8f8;
    padding: 15px;
    border-radius: 4px;
    border: 1px solid #ddd;
    white-space: pre;
    overflow-x: auto;
  }
  
  .xml-element {
    margin-left: 20px;
  }
  
  .xml-tag-start, .xml-tag-end {
    cursor: pointer;
    color: #268bd2;
    white-space: break-spaces;
  }
  
  .xml-tag-start:hover, .xml-tag-end:hover {
    background-color: #f0f0f0;
  }
  
  .xml-tag-name {
    color: #268bd2;
    font-weight: bold;
  }
  
  .xml-attribute-name {
    color: #b58900;
  }
  
  .xml-attribute-value {
    color: #2aa198;
  }
  
  .xml-text {
    color: #586e75;
    margin-left: 20px;
  }
  
  .xml-text-value {
    color: #859900;
  }
  
  .xml-angle-bracket {
    color: #93a1a1;
  }
  
  .xml-collapsed-placeholder {
    color: #93a1a1;
    cursor: pointer;
    margin-left: 5px;
  }
  
  .xml-collapsed-placeholder:hover {
    text-decoration: underline;
  }
  
  .xml-children {
    border-left: 1px dotted #ddd;
    padding-left: 15px;
  }
  
  .xml-error {
    color: #dc322f;
    padding: 10px;
    background-color: #f8f8f8;
    border-radius: 4px;
    border: 1px solid #ddd;
  }
  
  .xml-loading {
    color: #586e75;
    padding: 10px;
  }