{"name": "hotel_design_pc", "version": "1.0.0", "type": "module", "description": "", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "scripts": {"dev": "vite", "build": "vite build", "dev:debug": "cross-env ENV=prod concurrently \"cross-env DEBUG=1 npm run dev\"", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "buildIndex": "cti entrypoint ./src -b -o index.ts"}, "devDependencies": {"@babel/preset-env": "^7.14.8", "@babel/preset-react": "^7.14.5", "@types/css-modules": "^1.0.5", "@types/node": "^20.14.0", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@types/react-router-dom": "^5.3.3", "create-ts-index": "^1.14.0", "tsx": "^4.19.2", "typescript": "^5.6.3", "rollup": "^4.41.1", "three": "^0.171.0", "@types/three": "^0.171.0", "vite": "^5.2.0", "vite-plugin-externals": "^0.6.2", "vite-tsconfig-paths": "^5.1.4", "vite-plugin-html": "^3.2.2", "vite-plugin-dts": "^4.5.4", "copy-vite-plugin": "^1.0.1", "axios": "0.27.2", "axios-cookiejar-support": "^5.0.5", "less": "^4.2.0", "less-loader": "^12.2.0", "@vitejs/plugin-react": "^4.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "recharts": "2.9.2", "concurrently": "^8.2.2", "cross-env": "^7.0.3"}, "keywords": [], "author": "sunvega", "license": "ISC", "dependencies": {"@ant-design/cssinjs": "^1.21.0", "@ant-design/icons": "^5.4.0", "axios": "1.5.0", "i18next": "^21.9.1", "@svg/antd": "2.6.3", "@svg/antd-basic": "^5.6.8", "@svg/antd-cloud-design": "4.17.0", "@svg/lang": "^2.7.1", "@svg/oss-upload": "^2.0.3", "@svg/request": "^0.4.0", "mobx": "6.6.1", "mobx-react": "7.5.2", "mobx-react-lite": "^4.0.7", "react": "18.2.0", "react-dom": "18.2.0", "react-i18next": "11.18.4", "react-router-dom": "6.3.0", "three": "^0.175.0", "buffer": "^6.0.3", "workerpool": "^9.3.0", "@layoutai/z_polygon": "workspace:^", "@layoutai/basic_data": "workspace:^", "@layoutai/effects3d": "workspace:^", "@layoutai/model3d_api": "workspace:^", "@layoutai/series_matcher": "workspace:^", "@layoutai/layout_scheme": "workspace:^", "@layoutai/layout_scores": "workspace:^", "@layoutai/layout_solver": "workspace:^", "@layoutai/layout_basic_services": "workspace:^"}}