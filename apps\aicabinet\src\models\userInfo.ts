import { observable, action, makeObservable } from 'mobx';

interface IUserData {
  userId?: string;
  nickname?: string;
  mobile?: string;
  username?: string;
  tenantId?: string;
  headerPic?: string;
}

/**
 * @description 用户信息数据
 */
class UserInfo {
  
  constructor() {
    makeObservable(this); // mobx6.0之后必须要加上这一句
  }

  @observable
  userInfo: IUserData = {};

  @action
  setUserInfo = (data: IUserData) => {
    this.userInfo = data;
  };
}

const userInfo = new UserInfo();

export default userInfo;
