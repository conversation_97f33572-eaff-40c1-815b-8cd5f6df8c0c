
import useStyles from './style';
import React, { useEffect, useState } from 'react'
import { LayoutAI_App, LayoutAI_Events,EventName} from '@layoutai/layout_scheme';

import ProgressInfo from '../progress';
import { message, Modal, Progress } from '@svg/antd';
import { useTranslation } from 'react-i18next';
const perparingState = {
    opening : false,
    title : "",
    timeout : 0,
    startTime:0,
    seriesOpening:false
}
const Home_progress: React.FC = () => {
    const { t } = useTranslation();
    const { styles } = useStyles();
    const { confirm } = Modal;

    const [preparing, onSetPreparing] = useState<{opening:boolean; title:string;seriesOpening:boolean}>(perparingState);
    const [initializing, setInitializing] = useState<boolean>(false);
    const [openingScheme, setOpeningScheme] = useState<boolean>(false);
    const [progress, setProgress] = useState(0); // 新增进度状态
    const [seriesTitle, setSeriesTitle] = useState<string>("");
    const setPreparing = (params:{opening:boolean,title:string,timeout?:number})=>
    {
        Object.keys(params).forEach((key,index)=>{
          (perparingState as any)[key] = (params as any)[key]
        });
        onSetPreparing(params as any);
    }
    function initAttributeEvent() {
        LayoutAI_App.on(EventName.OnPreparingHandle, (params) => {

          if(!params.title)
          {
            params.title = t("CAD文件识别中");
          }
          setPreparing(params);
        });

        LayoutAI_App.on(EventName.OpeningImitateFileStart, () => {
          let params = {
              opening:true,
              title: t("正在识别临摹图")
          };
          setPreparing(params);
        });

        LayoutAI_App.on(EventName.OpeningImitateFileFail, () => {
          setPreparing({opening: false, title: t("临摹图识别失败")});
          // 显示提示
        let config = {
            duration: 2,
            content: t('无法识别临摹图').toString(),
            className: 'custom-class',
            style: {
                marginTop: '4vh',
            }
        }
        message.error(config);
        });

        LayoutAI_App.on(EventName.OpenCADDefaultHandle, (params) => {
          if(params.opening)
          {
              confirm({
                title: t('识别CAD失败'),    
                content: (
                  <span>
                    {t(params.content)}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;   
                    <a href="https://3vj-pano.3vjia.com/vr/aidesk/dwg/%E6%A0%87%E5%87%86CAD4.0-0.dwg">{t('下载CAD样例')}</a>
                  </span>
                ),
                okText: t('确定'),
                cancelText: t('取消'),
                onOk() {
                  console.log('OK');
                },
                onCancel() {
                  console.log('Cancel');
                },
              });
          }
        });

        LayoutAI_App.on(EventName.ApplySeriesSample, (params) => {
          if(!params) return;
          if(!params.timeout)
          {
            params.timeout = 15000;
          }
          setPreparing(params);

          if(params.seriesOpening)
          {
            startProgress(params.timeout); // 启动进度条
            setSeriesTitle(t(params.title)+'...')
          }
        });

        LayoutAI_App.on(EventName.Initializing, (params) => {
          setInitializing(params.initializing);
        });
    }

    const animateProgress = (startTime:number, duration:number) => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1); // 0~1
      setProgress(progress * 100);
      if (progress < 1) {
        requestAnimationFrame(() => animateProgress(startTime, duration));
      } else {
        // LayoutAI_App.emit(EventName.ApplySeriesSample, { 
        //   seriesOpening: false, 
        //   title: "" 
        // });
      }
    };
    
    const startProgress = (duration?:number) => {
      animateProgress(Date.now(), duration); // 12秒
    };
    useEffect(() => {
      initAttributeEvent();
    },[]);

    return (
    <>
      {preparing.seriesOpening && (
        <div className={styles.progressInfo}>
          <div className={styles.progressInfoContent}>
            <div>
              <img className="rotating" style={{width: '40px',height: '40px', marginBottom: '40px'}} src={require('@/assets/images/aidraw_logo.png')} alt="" />
            </div>
            <Progress showInfo={false} style={{width: '400px'}} percent={progress} />
            <div style={{marginTop: '10px', color: '#FFFFFFD8'}}>{t(seriesTitle)}</div>
          </div>

        </div>
      )}
      { preparing.opening ? 
        <div className={styles.progressInfo}>
          <ProgressInfo title={t(preparing.title)} color={'#FFFFFFD8'}></ProgressInfo>
        </div> : null
      }
      { openingScheme ? 
        <div className={styles.progressInfo}>
          <ProgressInfo title={t('正在生成3D方案，请稍等...')} color={'#FFFFFFD8'}></ProgressInfo>
        </div> : null
      }
      { initializing ? 
        <div className={styles.progressInfo}>
          <ProgressInfo title={t('初始化中...')} color={'#FFFFFFD8'}></ProgressInfo>
        </div> : null
      }
    </>
    )
}

export default Home_progress