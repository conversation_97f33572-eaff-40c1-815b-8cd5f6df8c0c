import {
  cad_file,
  checkIsMobile,
  ENV,
  EnvParams,
  import_Type,
  is_standalone_website,
  mini_APP
} from '@/config';
import {
  Arrangeable,
  DrawingFigureMode,
  FocusMap,
  I_SwjXmlScheme,
  MobileFocusMap
} from '@layoutai/basic_data';
import {
  AI2DesignBasicModes,
  CadBatchDrawingLayerType,
  CadDrawingLayerType,
  EventName,
  LayoutAI_App, LayoutAI_Commands,
  LayoutAI_Configs,
  LayoutAI_Events,
  LayoutSchemeXmlJsonParser, roomSubAreaService,
  T_DeleteElement,
  T_DimensionDWElement,
  T_DimensionElement,
  T_MoveElement,
  T_MoveSubAreaElement,
  T_MoveWinDoorElement, T_RotateElement,
  T_ScaleElement,
  TAppManagerBase,
  TBaseEntity, TBaseGroupEntity,
  TFigureElement,
  TFurnitureEntity, TGroupTemplateEntity,
  TRoom,
  TRoomEntity,
  TWindowDoorEntity
} from '@layoutai/layout_scheme';
import { Vector3 } from 'three';
import {
  compareNames,
  getShortenedFileName,
  I_MouseEvent,
  I_PainterTransform,
  ZRect
} from "@layoutai/z_polygon";
import { AI2BaseModeHandler } from './AI2BaseModeHandler';

import { AddFurnitureHandler } from './SubHandlers/AddFurnitureHandler';
import { AddSpaceAreaSubHandler } from './SubHandlers/AddSpaceAreaSubHandler';
import { CadBaseSubHandler } from './SubHandlers/CadBaseSubHandler';
import { EditSpaceAreaSubHandler } from './SubHandlers/EditSpaceAreaSubHandler';
import { MoveFurnitureSubHandler } from './SubHandlers/MoveFurnitureSubHandler';
import { MoveWinDoorHandler } from './SubHandlers/MoveWinDoorHandler';
import { RotateHandler } from './SubHandlers/RotateHandler';
import { ScaleFurnitureSubHandler } from './SubHandlers/ScaleFurnitureSubHandler';
import { MoveWallSubHandler } from './SubHandlers/EditWallsSubHandlers/MoveWallSubHandler';
import { I_UITemplates } from './UiTemplates/I_UITemplate';

export class AICadEditModeHandler extends AI2BaseModeHandler {
  protected _cad_data_changed = false;
  protected _test_data_id: number;

  _painter_ts: I_PainterTransform;
  _target_pos: Vector3;
  main_rect: ZRect;

  /**
   *  重置的矩形列表
   */
  protected _reset_furniture_entities: TFurnitureEntity[];

  /**
   *  重置的矩形列表
   */
  protected _reset_windoor_entities: TWindowDoorEntity[];

  constructor(manager: TAppManagerBase, name: string = 'AICadMode') {
    super(manager, name);
    this._test_data_id = 0;
    this._painter_ts = null;
    this._cad_default_sub_handler = new CadBaseSubHandler(this);

    this._sub_handlers[LayoutAI_Commands.AddFurniture] = new AddFurnitureHandler(this); // 从左侧拖拽图元进来
    this._sub_handlers[LayoutAI_Commands.Transform_Scaling] = new ScaleFurnitureSubHandler(this);
    this._sub_handlers[LayoutAI_Commands.Transform_Moving] = new MoveFurnitureSubHandler(this);
    this._sub_handlers[LayoutAI_Commands.Transform_MovingStruture] = new MoveWinDoorHandler(this); //移动结构件
    this._sub_handlers[LayoutAI_Commands.Transform_MovingWall] = new MoveWallSubHandler(this); //移动结构件

    // this._sub_handlers[LayoutAI_Commands.Transform_Dimension] = new DimensionFurnitureHandler(this);
    // this._sub_handlers[LayoutAI_Commands.Transform_Combination] = new CombinationHandler(this); //进入组合模式
    this._sub_handlers[LayoutAI_Commands.Transform_Rotate] = new RotateHandler(this); //进入旋转模式
    // this._sub_handlers[LayoutAI_Commands.RulerModeHandler] = new RulerModeHandler(this); //进入量尺模式

    // this._sub_handlers[LayoutAI_Commands.AI_RoomLayout] = new BasicRoomAILayoutSubHandler(this);
    // this._sub_handlers[LayoutAI_Commands.AI_KitchenLayout] = new KitchenLayoutSubHandler(this);

    this._sub_handlers[AddSpaceAreaSubHandler.handler_name] = new AddSpaceAreaSubHandler(this);
    this._sub_handlers[EditSpaceAreaSubHandler.handler_name] = new EditSpaceAreaSubHandler(this);

    this._reset_furniture_entities = [];
    this._transform_elements = [];

    this._transform_elements.push(new T_ScaleElement(1, -1, this._selected_target, this.manager.layout_container));
    this._transform_elements.push(
      new T_DeleteElement(1, -1, this._selected_target, this.manager.layout_container)
    );
    this._transform_elements.push(
      new T_ScaleElement(1, 0, this._selected_target, this.manager.layout_container)
    );
    this._transform_elements.push(
      new T_RotateElement(1, 1, this._selected_target, this.manager.layout_container)
    );

    this._transform_elements.push(
      new T_ScaleElement(0, -1, this._selected_target, this.manager.layout_container)
    );
    this._transform_elements.push(
      new T_ScaleElement(0, 1, this._selected_target, this.manager.layout_container)
    );

    this._transform_elements.push(
      new T_ScaleElement(-1, -1, this._selected_target, this.manager.layout_container)
    );
    this._transform_elements.push(
      new T_ScaleElement(-1, 0, this._selected_target, this.manager.layout_container)
    );
    this._transform_elements.push(
      new T_ScaleElement(-1, 1, this._selected_target, this.manager.layout_container)
    );

    this._transform_elements.push(new T_DimensionElement(0, this.manager.layout_container));
    this._transform_elements.push(new T_DimensionElement(1, this.manager.layout_container));
    this._transform_elements.push(new T_DimensionElement(2, this.manager.layout_container));
    this._transform_elements.push(new T_DimensionElement(3, this.manager.layout_container));
    // this._transform_elements.push(new T_RotateElement(this._selected_target, this.manager.layout_container));
    // this._transform_elements.push(new T_UpdateLengthElement(this._selected_target, this.manager.layout_container));

    this._transform_elements.push(new T_DimensionDWElement(this.manager));

    // 后面就把那四个编辑的点给加进去
    this._transform_moving_element = new T_MoveElement(
      this._selected_target,
      this.manager.layout_container
    );
    this._transform_moving_struture_element = new T_MoveWinDoorElement(
      this._selected_target,
      this.manager.layout_container
    );
    this._transform_elements.push(
      new T_MoveSubAreaElement(this._selected_target, this.manager.layout_container)
    );

    this._transform_elements.push(this._transform_moving_element);
    this._transform_elements.push(this._transform_moving_struture_element);
    this._initial_scheme_data = null; // 初始的方案数据
  }

  get candidate_rects() {
    return this._candidate_target_rects;
  }

  get exsorb_rects() {
    return this._exsorb_target_rects;
  }

  get transform_elements() {
    return this._transform_elements;
  }

  get transform_moving_element() {
    return this._transform_moving_element;
  }

  get painter() {
    return this.manager?.painter;
  }

  get furniture_entities() {
    return this.manager.layout_container._furniture_entities;
  }

  set furniture_entities(rects: TFurnitureEntity[]) {
    this.manager.layout_container._furniture_entities = rects;
  }
  get room_list() {
    return this.manager.layout_container._rooms;
  }
  public updateDomUI()
  {
    const t = LayoutAI_App.t;
    let uiTemplates : I_UITemplates = {
      topMenu :{
          centerBtnList:[
            {
              id: 'create',
              title: t('户型'),
              titleCn: '户型', //[i18n:ignore]
              type: 'label',
              icon: 'iconfile',
              onClick:()=>{
                LayoutAI_App.emit(EventName.OpenHouseSearching,true);
              }
            },
            {
              id: 'openCadFile',
              title: t('CAD'),
              titleCn: 'CAD', //[i18n:ignore]
              command_name: LayoutAI_Commands.OpenDwgFile,
              icon :'iconimportCAD',
              type: 'label'
            },
            {
              id: 'undoBtn',
              title: t('撤销'),
              titleCn: '撤销', //[i18n:ignore]
              icon: 'iconundo',
              type: 'label',
              disabled: true,
              command_name: LayoutAI_Commands.Undo
            },
            {
              id: 'redoBtn',
              title: t('恢复'),
              titleCn: '恢复', //[i18n:ignore]
              icon: 'iconredo',
              type: 'label',
              disabled: true,
              command_name: LayoutAI_Commands.Redo
            },
            {
              id: 'resetBtn',
              title: t('清空'),
              titleCn: '清空',
              icon: 'iconempty',
              type: 'label',
              command_name: LayoutAI_Commands.Reset
            },
            {
              id: 'toolBtn',
              title: t('显示'),
              titleCn: '显示', //[i18n:ignore]
              icon: 'icondisplay'
            },
            {
                  id: 'tools',
                  title: t('工具'),
                  titleCn: '工具', //[i18n:ignore]
                  icon: 'icontool',
                  type: 'label',
            },
            {
              id: 'export',
              title: t('导出'),
              titleCn: '导出', //[i18n:ignore]
              icon: 'iconexport',
              type: 'label',
            },
            
          ]
      }
    };
    LayoutAI_App.emit_M(EventName.UITemplateChanged,uiTemplates);
  }
  async prepare(load_test_data: boolean = true) {
    this.updateDomUI();
    // 从工作台跳转过来
    if (EnvParams.id && EnvParams.id.length > 0 && mini_APP) {
      // let res = await this.loadRoomTemplate(EnvParams.id, EnvParams.roomTemplate_RoomId);
      // if (res) return;
    }

    let hasInputCadData: boolean = cad_file != null && cad_file.length > 0;

    if (is_standalone_website) {
      // 独立站点进来
      if (load_test_data) {
        LayoutAI_App.emit(EventName.OnPreparingHandle, { opening: false, title: null });

        await this.prepareTestData();
      }
      LayoutAI_App.emit(EventName.OnPreparingHandle, { opening: false, title: null });
      return;
    } else if (import_Type === 'importHouse') {
      // 看看有什么数据要提前载入
      if (hasInputCadData) {
        this.EventSystem.emit(EventName.LayoutSchemeOpened, {
          id: null,
          name: getShortenedFileName(cad_file)
        });
        this.manager.layout_container._layout_scheme_name = getShortenedFileName(cad_file);
      } else if (load_test_data) {
        await this.prepareTestData();
      }

      LayoutAI_App.emit(EventName.OnPreparingHandle, { opening: false, title: null });
    } else {
      // 3D进来
      await this.prepare3DschemeData();

      /**
       *  后台静默请求即可
       */
      this.computeWholeLayoutSchemeList(false).then(() => {
        if (this.whole_layout_scheme_list && this.whole_layout_scheme_list.length > 0) {
          LayoutAI_App.DispatchEvent(LayoutAI_Events.ClickWholeLayoutScheme, {
            value: this.whole_layout_scheme_list[0],
            index: 0
          });
        }
      });
    }

    if (this.manager.layout_container) {
      // 重置改成方案保存的形式去做
      this._initial_scheme_data = this.manager.layout_container.toXmlSchemeData();
    }
  }
  async prepare3DschemeData() {
    this.logger.log('>>>>>> 开始准备3D方案数据 <<<<<<');
    this.manager.layout_container.focusCenter();


    // 判断空间是否可选
    this.isSelectRoom();
    this.update();
  }
  isSelectRoom() {
    for (let room of this.room_list) {
      // 新增判断是否可点击逻辑
      room.selectable = false;
      Arrangeable.forEach(supportedRoomType => {
        if (room.room_type.indexOf(supportedRoomType) > -1) {
          room.selectable = true;
        }
      });
    }
  }




  /**
   *  本地测试数据准备
   */
  async prepareTestData() {
    let has_ezdxf = null;
    
    if (!has_ezdxf && this.manager.layout_container._room_entities.length == 0) {
      let res = await this.manager._load_local_XmlSchemeData();
      if(res)
      {

      }
      // if (this.manager._load_local_XmlSchemeData()) {

      // }

    }
  }





  /**
   * 初始化控件, 在react下, 需要研究怎么跟组件化配合
   *
   */
  initWidget(): void {
    return; //
  }

  enter(state?: number): void {
    super.enter(state);
    this.updateDomUI();
    const route = window.location.pathname;
    this.manager.layout_container.drawing_figure_mode = route.includes('new3d')
      ? DrawingFigureMode.Outline
      : DrawingFigureMode.Figure2D; // 默认绘制线框图
    // this.manager.layer_CadFloorLayer.visible = true;
    // this.manager.layer_CadRoomFrameLayer.visible = true;
    // this.manager.layer_CadCabinetLayer.visible = true;
    // this.manager.layer_CadFurnitureLayer.visible = true;
    // this.manager.layer_CadCabinetLayer.visible = true;
    // this.manager.layer_OutLineLayer.visible = true;
    // this.manager.layer_CadRoomNameLayer.visible = true;
    // this.manager.layer_CadEzdxfLayer.visible = false;
    // this.manager.layer_LightingLayer.visible = false;
    // this.manager.layer_CeilingLayer.visible = false;
    // this.manager.layer_CadCopyImageLayer.visible = false;
    // this.manager.layer_CadSubAreaLayer.visible = LayoutAI_App.IsDebug ? true : false; // 分区默认在开发测试模式不显示先
    // // this.manager.layer_DimensionOutterWallLayer.visible = false
    let visibleLayers:CadDrawingLayerType[] = [CadDrawingLayerType.CadRoomStrucure,CadDrawingLayerType.CadCabinet,CadDrawingLayerType.CadFurniture,CadDrawingLayerType.CadFloorDrawing,
      CadDrawingLayerType.CadRoomName,CadDrawingLayerType.ExtDrawingDrawing,CadDrawingLayerType.CadSubRoomAreaDrawing,
      CadDrawingLayerType.CadOutLine
    ];
    let inVisibleLayers:CadDrawingLayerType[] = [
      CadDrawingLayerType.ExportCadDrawing, CadDrawingLayerType.CadLighting,CadDrawingLayerType.CadCopyImageDrawing,,CadDrawingLayerType.CadDecorates
    ]
    visibleLayers.forEach((layerName)=>{
      let layer = this.manager.drawing_layers[layerName];
      if(layer) layer.visible = true;
    });
    inVisibleLayers.forEach((layerName)=>{
      let layer = this.manager.drawing_layers[layerName];
      if(layer) layer.visible = false;  
    })

    this.manager.onLayerVisibilityChanged();
    this.runCommand(this._default_sub_handler_name);
    if (this._cad_default_sub_handler) {
      this._cad_default_sub_handler.updateCandidateRects();
    }

    if (
      this.manager._previous_mode === AI2DesignBasicModes.HouseDesignMode ||
      this.manager._previous_mode === AI2DesignBasicModes.DesignMode ||
      this.manager._previous_mode === AI2DesignBasicModes.RemodelingMode ||
      this.manager._previous_mode === AI2DesignBasicModes.HouseCorrectionMode ||
      this.manager._previous_mode === AI2DesignBasicModes.EzDxfEditMode
    ) {
      if(LayoutAI_Configs.Configs.prepare_auto_layout)
      {
        this.computeWholeLayoutSchemeList(false);
      }
    }
    if (this.manager._previous_mode === AI2DesignBasicModes.MeasurScaleMode) {
      if(LayoutAI_Configs.Configs.prepare_auto_layout)
      {
        this.computeWholeLayoutSchemeList(false).then(() => {
          if (this.whole_layout_scheme_list && this.whole_layout_scheme_list.length > 0) {
            LayoutAI_App.DispatchEvent(LayoutAI_Events.ClickWholeLayoutScheme, {
              value: this.whole_layout_scheme_list[0],
              index: 0
            });
          }
        });
      }

    }

    this.manager.layout_container.focusCenter();

    this.manager.layout_container._furniture_entities.forEach(entity => {
      if (entity instanceof TBaseGroupEntity) {
        entity.setMatchedVisible(false);
      }
    });

    if (this.manager?.scene3DManager) {
      this.manager.scene3DManager.bindOnSelectFigure(this._onSelectedFigure.bind(this));
      this.manager.scene3DManager.bindOnSelectRoom((room: TRoom) => {
        if (room && room._room_entity) {
          let center = room._room_entity._main_rect.rect_center;
          let ev: I_MouseEvent = { posX: center.x, posY: center.y, _ev: null };
          this._cad_default_sub_handler.updateRoomSelected(ev);
          this.EventSystem.emit_M(EventName.SelectingTarget, room._room_entity, null, null);
          this._cad_default_sub_handler.onRoomEntitySelected(room._room_entity);
          this._cad_default_sub_handler.selected_target.selected_rect = room._room_entity.rect;
          this._cad_default_sub_handler.selected_target.selected_entity = room._room_entity;
          LayoutAI_App.emit(EventName.selectRoom, room._room_entity);
        } else {
          if (this.manager.layout_container._drawing_layer_mode != 'SingleRoom') {
            this.manager.layout_container._selected_room = null;
          }
          LayoutAI_App.emit(EventName.selectRoom, null);
          this._cad_default_sub_handler.cleanSelection();
          this.EventSystem.emit_M(EventName.SelectingTarget, null, null, null);
        }
      });
    }
  }

  private _onSelectedFigure(ele: TFigureElement) {
    let _figure_element_selected = ele;
    LayoutAI_App.emit_M(EventName.FigureElementSelected, _figure_element_selected);
    if (_figure_element_selected && _figure_element_selected.furnitureEntity) {
      this.EventSystem.emit_M(
        EventName.SelectingTarget,
        _figure_element_selected.furnitureEntity,
        null,
        null
      );
      this._cad_default_sub_handler.selected_target.selected_entity =
        _figure_element_selected.furnitureEntity;
      if (this.manager.layout_container.drawing_figure_mode !== DrawingFigureMode.Figure2D) {
        this._cad_default_sub_handler.selected_target.selected_rect =
          _figure_element_selected.furnitureEntity.figure_element.matched_rect;
      } else {
        this._cad_default_sub_handler.selected_target.selected_rect =
          _figure_element_selected.furnitureEntity.rect;
      }
    } else if (compareNames([ele?.sub_category], ['地面', '墙面'])) {
    } else {
      this.EventSystem.emit_M(EventName.SelectingTarget, null, null, null);
      this._cad_default_sub_handler.cleanSelection();
    }
    let scene3D = LayoutAI_App.instance.scene3D;
    if (!scene3D) return;
    if (ele) {
      scene3D.setSelectionBox(ele);
    } else {
      LayoutAI_App.emit_M(EventName.FigureElementSelected, null);
      scene3D.clearSelectionBox();
    }
  }

  leave(state: number = 0) {
    super.leave(state);

    // 离开的时候, 同步房间数据
    this.transformGroup();
    this.manager.layout_container.updateRoomsFromEntities();
    // this.manager._save_to_local_model_rooms();

    // this.manager.layer_CadEzdxfLayer.visible = false;

    if (this.manager.layout_container) {
      this._initial_scheme_data = this.manager.layout_container.toXmlSchemeData();
    }
    this._cad_default_sub_handler.cleanSelection();
    this.manager.layout_container.cleanDimension();
    LayoutAI_App.emit_M(EventName.FigureElementSelected, null);
  }

  onmousedown(ev: I_MouseEvent): void {
    if (ev.ctrlKey && !this._active_sub_handler) {
      this.runCommand(LayoutAI_Commands.Transform_Combination);
    }
    if (this._active_sub_handler) {
      this._active_sub_handler.onmousedown(ev);
    } else if (this._cad_default_sub_handler) {
      // 默认方法mouse_down后, 有可能触发进入新的active_handler
      this._cad_default_sub_handler.onmousedown(ev);

      if (this._active_sub_handler) {
        this._active_sub_handler.onmousedown(ev);
        return;
      }
    }
    this._last_ev_pos = { x: ev._ev.pageX, y: ev._ev.pageY };

    this._is_painter_center_moving = false;
  }

  ondbclick(ev: I_MouseEvent): void {
    if (this._active_sub_handler) {
      this._active_sub_handler.ondbclick(ev);
    } else if (this._cad_default_sub_handler) {
      this._cad_default_sub_handler.ondbclick(ev);
    }
  }

  onmousemove(ev: I_MouseEvent): void {
    if (this._active_sub_handler) {
      super.onmousemove(ev);
    } else {
      if (this._cad_default_sub_handler) {
        this._cad_default_sub_handler.onmousemove(ev);
      }
      super.onmousemove(ev);
    }
  }
  onmouseup(ev: I_MouseEvent): void {
    if (this._active_sub_handler) {
      super.onmouseup(ev);
    } else {
      if (this._cad_default_sub_handler) {
        this._cad_default_sub_handler.onmouseup(ev);
      }
      super.onmouseup(ev);
    }
  }

  onwheel(ev: WheelEvent): void {
    if (this._active_sub_handler) {
      super.onwheel(ev);
      this._active_sub_handler.onwheel(ev);
    } else {
      if (this._cad_default_sub_handler) {
        this._cad_default_sub_handler.onwheel(ev);
      }
      super.onwheel(ev);
    }
  }

  drawCanvas(): void {
    if (this.manager._batch_drawing_layers[CadBatchDrawingLayerType.AICadDefaultBatch]) {
      let batchDirty = !this._is_painter_center_moving && !this._is_moving_element;
      // console.log(batchDirty);
      this.manager._batch_drawing_layers[CadBatchDrawingLayerType.AICadDefaultBatch].drawLayer(batchDirty);
    }

    if (this.manager._batch_drawing_layers[CadBatchDrawingLayerType.ExtDrawingBatch]) {
      let layer = this.manager._batch_drawing_layers[CadBatchDrawingLayerType.ExtDrawingBatch];
      layer.visible && (layer.drawLayer(false));
    }

    if (this._active_sub_handler) {
      this._active_sub_handler.drawCanvas();
    } else if (this._cad_default_sub_handler) {
      this._cad_default_sub_handler.drawCanvas();
    }
  }





  changeSpaceName(room: TRoomEntity, value: string) {
    if (room) {
      room.name = value;
      this.update();
    }
  }






  checkUnfurnishedRooms(): boolean {
    return false;
  }


  async handleEvent(event_name: string, event_param: any) {
    super.handleEvent(event_name, event_param);
    if (event_name === LayoutAI_Events.SelectedFurniture) {

      const furnitureTitle = event_param;
      if (furnitureTitle) {
        // 在这里做匹配逻辑
        // 1、先创建图元，但是目前没有全部图元的初始数据，只能从单个CAD已经识别出来的图元中获取
        this._cad_default_sub_handler.cleanSelection();
        this._cad_default_sub_handler.updateSelectionState();
        this.manager.layout_container.entity_creator.onFigureSelected(furnitureTitle, true);
      }
    }

    if (event_name === LayoutAI_Events.mobileAddFurniture) {
      const furnitureTitle = event_param;
      if (furnitureTitle) {
        // 在这里做匹配逻辑
        // 1、先创建图元，但是目前没有全部图元的初始数据，只能从单个CAD已经识别出来的图元中获取
        this._cad_default_sub_handler.cleanSelection();
        this._cad_default_sub_handler.updateSelectionState();
        this.manager.layout_container.entity_creator.onFigureSelected(furnitureTitle, true);
        this.manager.layout_container.entity_creator.addNewEntitiy();
      }
    }
    if (event_name === LayoutAI_Events.MoveFurniture) {
      // this.ai_cad_data._adding_figure_entity = null;
      this.update();
    }
    if (event_name === LayoutAI_Events.UpdateSize) {
      // this._select_target_handler.updateSize(event_param)
      this.update();
    }
    if (event_name === LayoutAI_Events.OpenIssueScheme) {
      let swj_scheme_json: string = event_param.schemeJson;

      if (swj_scheme_json) {
        const schemeJson = JSON.parse(event_param.schemeJson.replace(/'/g, '"'));

        if (schemeJson.scheme_id === this.manager.layout_container._scheme_id) {
          console.log('异常的方案正在处理中...');
          return;
        }

        LayoutSchemeXmlJsonParser.loadSwjSchemeXmlJson(schemeJson as I_SwjXmlScheme);
      }
    }
    if (event_name === LayoutAI_Events.HandleUnGroupTemplate) {
      let entity = TBaseEntity.getEntityOfRect(this._selected_target.selected_rect);
      if (this._cad_default_sub_handler && entity != null) {
        if (entity.type === 'BaseGroup') {
          this._cad_default_sub_handler.unBaseGroup((entity as TBaseGroupEntity) || null);
        }
        if (entity.type === 'Furniture') {
          this._cad_default_sub_handler.unGroupTemplate((entity as TGroupTemplateEntity) || null);
        }
      }
      this.EventSystem.emit_M(EventName.SelectingTarget, null);
    }

    if (event_name === LayoutAI_Events.CadAutoDecorations) {
      this.cad_auto_decorations();
    }
    if (event_name === LayoutAI_Events.ChangeSpaceName) {
      this.changeSpaceName(event_param.room, event_param.value);
    }

    if (event_name === LayoutAI_Events.SingleRoomLayout) {
      this._selectSingleRoom(event_param);
      this.update();
    }
    if (event_name === LayoutAI_Events.leaveSingleRoomLayout) {
      LayoutAI_App.emit(EventName.ShowSubHandlerBtn, false);
      this.manager.layout_container._drawing_layer_mode = 'FullHouse';
      this.manager.layout_container._room_entities.forEach(
        (item: TRoomEntity) => (item.isSingle = false)
      );
      this.painter.importTransformData(this._painter_ts, false);
      this.EventSystem.emit_M(EventName.SelectingTarget, null, null, null);
      this.update();
    }
    if (event_name === LayoutAI_Events.AddSubRoomArea) {
      if (!event_param?._room) return;
      if (this.manager.drawing_layers[CadDrawingLayerType.CadSubRoomAreaDrawing]) {
        this.manager.drawing_layers[CadDrawingLayerType.CadSubRoomAreaDrawing].visible = true;
      }
      this.manager.onLayerVisibilityChanged();
      this._selectSingleRoom(event_param);
      let room = event_param._room;
      roomSubAreaService.createSubAreaByRoom(room);
      this.update();
    }

    if (event_name === LayoutAI_Events.CreateCombination) {
      if (!event_param) return;
      // this.addBaseGroupEntity(event_param);
    }

    if (event_name === LayoutAI_Events.ReplaceEntity) {
      this.replaceEntity(event_param);
    }

    if (event_name === LayoutAI_Events.setFocus) {
      if (!checkIsMobile()) {
        const params = FocusMap[event_param.focus];
        this.painter._p_sc = params.focus;
      } else {
        const params = MobileFocusMap[event_param.focus];
        this.painter._p_sc = params.focus;
      }
      if (this.manager.layout_container._drawing_layer_mode === 'SingleRoom') {
        let center = this.main_rect.rect_center;
        this.painter.p_center = center;
        this.update();
        return;
      }
      this.manager.layout_container.focusCenter();

      this.update();
    }


    if (event_name == LayoutAI_Events.ShowLivingRoomSpace) {
      event_param._room_entity.isShowSpace = !event_param._room_entity.isShowSpace;

      this.update();
    }



    if (event_name === LayoutAI_Events.updateLast_pos) {
      this._last_ev_pos = null;
    }



    if (event_name === LayoutAI_Events.PrepareRestartFurnishRemaining) {
      this.room_list.forEach(room => room.prepareRestartFurnishRemaining());
    }

  }

  private _selectSingleRoom(event_param: any) {
    if (this.manager.layout_container._drawing_layer_mode === 'SingleRoom') {
      // 切换单空间
      this.manager.layout_container._selected_room = event_param.makeTRoom(
        this.manager.layout_container._furniture_entities,
        false
      );
      this._cad_default_sub_handler.selected_target.selected_entity = event_param;
      this._cad_default_sub_handler.selected_target.selected_rect = event_param._rect;
      // 切换空间关闭小白条
      this.EventSystem.emit_M(EventName.SelectingTarget, null, null, null);
      LayoutAI_App.emit(EventName.selectRoom, event_param);
    } else {
      this._painter_ts = this.painter.exportTransformData();
    }
    this.manager.layout_container._room_entities.forEach(
      (item: TRoomEntity) => (item.isSingle = false)
    );
    event_param.isSingle = true;
    this.manager.layout_container._drawing_layer_mode = 'SingleRoom';
    this._cad_default_sub_handler.updateCandidateRects();
    LayoutAI_App.emit(EventName.ShowSubHandlerBtn, true);
    this.main_rect = ZRect.computeMainRect(event_param._room.room_shape._poly);
    let center = this.main_rect.rect_center;

    let ww = Math.max(this.main_rect.w, this.main_rect.h);

    let canvasElement = this.painter._canvas;
    let scaleW = canvasElement.width / ww;
    let scaleH = canvasElement.height / ww;
    this._cad_default_sub_handler.onRoomEntitySelected(event_param);
    if (checkIsMobile()) {
      let _center = this.main_rect.rect_center.clone().setY(this.main_rect.rect_center.y + 500);
      this.painter.p_center = _center;
      this.painter._p_sc = (scaleW > scaleH ? scaleH : scaleW) * 0.4;
    } else {
      this.painter.p_center = center;
      this.painter._p_sc = (scaleW > scaleH ? scaleH : scaleW) * 0.7;
    }
  }

  replaceEntity(item: any) {

  }

  cad_auto_decorations() {
    this.manager.layout_container.updateRoomsFromEntities(false);
  }

  async runCommand(cmd_name: string): Promise<void> {
    if (!cmd_name) return;

    super.runCommand(cmd_name);

    // 删除
     if (cmd_name === LayoutAI_Commands.DeleteFurniture) {
      this._cad_default_sub_handler.deleteElement();
    } else if (cmd_name === LayoutAI_Commands.DeleteRuler) {
      this.manager.layout_container._ruler_entities.forEach((entity, index) => {
        if (entity.is_selected) {
          this.manager.layout_container._ruler_entities.splice(index, 1);
        }
      });
      this.update();
    }
    // 旋转
    else if (cmd_name === LayoutAI_Commands.RotateFurniture) {
      this._cad_default_sub_handler.rotate();
    }
    // 镜像
    else if (cmd_name === LayoutAI_Commands.FlipFurniture) {
      this._cad_default_sub_handler.flip();
    }
    // 镜像
    else if (cmd_name === LayoutAI_Commands.FlipFurnitureVertical) {
      this._cad_default_sub_handler.flipVertical();
    }
    // 复制
    else if (cmd_name === LayoutAI_Commands.CopyFurniture) {
      this._cad_default_sub_handler.copySelectedTarget();
    } else if (cmd_name === LayoutAI_Commands.ToAiCadMode) {
      this._cad_default_sub_handler.toAiCadMode();
    } else if (cmd_name === LayoutAI_Commands.Empty) {
      this._cad_default_sub_handler.cleanData();
    } else if (cmd_name === LayoutAI_Commands.Reset) {
      this.reset();
    }  
  }
  onkeydown(ev: KeyboardEvent): boolean {
    super.onkeydown(ev);

    if (ev.key == 'Delete') {
      console.log('delete...');
      this._cad_default_sub_handler.deleteElement();
    }
    if (ev.ctrlKey && ev.key == 'v') {
      this._cad_default_sub_handler.copySelectedTarget();
    }

    if (ev.ctrlKey && ev.key === 'F2') {
      this.EventSystem.emit(EventName.setIssueReportVisible, true);
    }
    if (ev.ctrlKey && ev.key === 'F3') {
      this.EventSystem.emit(EventName.setIssueReportVisible, false);
    }

    if (ev.ctrlKey && ev.shiftKey && ev.altKey && ev.key === 'P' && ENV != 'prod') {
      localStorage.setItem('enable_sensors_log_print', 'true');
    }
    if (ev.ctrlKey && ev.shiftKey && ev.altKey && ev.key === 'Q' && ENV != 'prod') {
      localStorage.setItem('enable_sensors_log_print', 'false');
    }
    return true;
  }
  onkeyup(ev: KeyboardEvent): boolean {
    super.onkeyup(ev);
    return true;
  }

  operateUndoRedo(): void {
    this._cad_default_sub_handler.cleanSelection();
    this._cad_default_sub_handler.updateCandidateRects();
  }

  undo(): void {
    this.operateUndoRedo();
  }

  redo(): void {
    this.operateUndoRedo();
  }
  reset() {
    this.manager.layout_container.fromXmlSchemeData(
      this._initial_scheme_data as any as I_SwjXmlScheme
    );
    this._cad_default_sub_handler.cleanSelection();
    this._cad_default_sub_handler.updateCandidateRects();
    this.update();
  }
}
