import { Design2DApiHub, Design2DContext } from "@layoutai/design_2d";

import type { FSM } from "./fsm/FSM";
import { MainAppFSM } from "./fsm/fsms/MainAppFSM";
import { DomainApiHub } from "@layoutai/design_domain";

/**
 * 应用上下文
 */
export class AppContext {
  private static _instance: AppContext;
  
  private _design2DContext: Design2DContext | null = null;
  private _mainFSM: FSM;

  public DESIGN2D_CONTEXT_ID = 'design2d';

  private constructor() {
    this._mainFSM = new MainAppFSM();
  }

  public static get instance(): AppContext {
    if (!AppContext._instance) {
      AppContext._instance = new AppContext();
    }
    return AppContext._instance;
  }

  public get design2DContext(): Design2DContext | null {
    return this._design2DContext;
  }
  
  public get mainFSM(): FSM {
    return this._mainFSM;
  }

  public async init(): Promise<void> {
    DomainApiHub.instance.init();
    this._design2DContext = Design2DApiHub.instance.createCtxByInternalCanvas(this.DESIGN2D_CONTEXT_ID);
    // 初始化FSM
    this._mainFSM.onEnter();

  }

  /**
   * 清理所有资源
   */
  public dispose(): void {
    this._mainFSM.onExit();
  }

}

// 导出单例实例
export const appContext = AppContext.instance; 

// 将 AppContext 挂载到浏览器全局对象上，方便调试
if (typeof window !== 'undefined') {
  (window as any).LayoutApp = AppContext;
} 