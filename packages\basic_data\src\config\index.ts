import { ENV, app_Id } from './env';
// 插件应用ID
// 本地启动服务时地址没有APP_ID，所有本地调试需要用到下面的APP_ID
const appIdMap: any = {
  dev: '23144848908',
  test: '23144848908',
  hws: '2320205929998',
  pre: '2320217937436',
  prod: '2320217937436',
}
export const APP_ID = app_Id || appIdMap[ENV];

// console.log('APP_ID',APP_ID);
export const debug:boolean = ENV != 'prod';

/**
 *  Added by: 士玮 2025/03/16
 *  是否是移动端的设置
 *   --- 解耦掉react-device-detect相关依赖
 *    --- 注意: 这个函数在App.tsx中被重载了！！！
 */
export const MobileConfig :{isMobile?:boolean,deviceType?:string, 
  func_checkIsMobile:()=>boolean,
  func_checkDeviceType:()=>string,
  [key:string]:any} = {
    func_checkIsMobile : ()=>
    {
      // 注意: 这个函数在App.tsx中被重载了！！！
      const userAgent = navigator.userAgent.toLowerCase();
      const mobileKeywords = ['android', 'iphone', 'ipad', 'ipod', 'windows phone'];
      const isMobileUserAgent = mobileKeywords.some(keyword => userAgent.includes(keyword));
      const isMobileScreen = window.matchMedia("(max-width: 767px)").matches;
      return isMobileUserAgent || isMobileScreen;
    },
    func_checkDeviceType : ()=>{
      const width = Math.min(window.innerWidth, window.innerHeight);
      const height = Math.max(window.innerWidth, window.innerHeight);
      const ratio = window.devicePixelRatio || 1;
    
      // 一般平板的较小边至少为768px，且像素密度通常小于手机
      if (width >= 768 && ratio < 2.5) {
          return 'tablet';
      }
      // 手机一般较小边小于768px，或像素密度较大
      return 'mobile';
    }
}

export function checkIsMobile(force:boolean=false) {
  if((MobileConfig.isMobile === undefined) || force)
  {
      MobileConfig.isMobile = MobileConfig.func_checkIsMobile();
  }
  return MobileConfig.isMobile;

}

export function checkDeviceType(force:boolean=false)
{
    if(!MobileConfig.deviceType || force)
    {
        MobileConfig.deviceType = MobileConfig.func_checkDeviceType();
    }
    return MobileConfig.deviceType;
}

export * from './env';

export * from './host';
