import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  return {
    root: css`
      margin-top: 16px;
      overflow-y: scroll;
      max-height: calc(95vh - 80px);
      &::-webkit-scrollbar {
        width: 0px;
      }
    `,
    itemBox: css`
        text-align: center;
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
    `,
    item: css`
        width: 32%;
        height: 60px;
        cursor: pointer;
        margin-bottom: 30px;
        margin-right: 1%;
    `,
    itemIcon: css`
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        img:hover{
          background:#eee;
          border-radius:4px;
        }
    `,

    desc: css`
        color: #000000;
        font-size: 12px;
        text-align: center;
        padding: 4px 0;
        height: 40px;
        line-height: 20px;
    `,
    title: css`
        font-size: 14px;
        color: #262626;
        font-weight: 600;
        position: relative;
        margin: 10px 0px;
    `,
    tab_container: css`
      width:100%;
      height:100%;
    `,
    layer_row: css`
      width:100%;
      height:30px;
      line-height:30px;
    `,
    figure: css`
    display: flex;
    // justify-content: space-evenly;
    flex: 1;
    flex-wrap: wrap;
    max-height: calc(95vh - 200px);
    /* padding-top: 16px; */
    overflow-y: scroll;
    padding-left: 0px;
    transition: all .3s;

    &::-webkit-scrollbar {
    width: 0px;
    }

    .item {
      flex-grow: 1;
      min-width: 30%;
      max-width: 33%;
      margin: 4px;
      cursor: pointer;
      transition: box-shadow 0.3s ease;
      user-select:none;
      &:hover {
        background: rgba(128,128,128,0.1);
        border-radius:4px;
      }
      .image {
          height: 120px;
          /* background: rgba(242,242,242,1); */
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;
          overflow:hidden;
          transition: all .3s;
          .ant-image-img {
          width: 100%;
          height: 120px;
          vertical-align: middle;
          user-select:none;
          pointer-events: none; 
          display: flex;
          align-items: center;
          justify-content: center;
          }
          .group_image.ant-image-img {
          width:120px;
          height: 120px;
          vertical-align: middle;
          user-select:none;
          pointer-events: none; 
          display: flex;
          margin-left:0%;

          }

    }

    .title {
        color: #000000;
        font-size: 12px;
        padding: 10px 0;
        height: 40px;
        line-height: 20px;
        text-align: center;
    }
    &:hover {
        /* box-shadow: 3px 3px 8px 0px rgba(0, 0, 0, 0.12); */
        .image {
        /* background-color: #DDDFE4; */
        
        }
    }
}
`
  };
});
