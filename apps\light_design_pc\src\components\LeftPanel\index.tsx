import React, { useEffect } from "react";
import useStyles from "./style";
import { HouseLayoutMenu } from "../../components";
import { observer } from "mobx-react-lite";
import { useStore } from "../../store/RootStore";

/**
 * @description 左侧面板
 */

export const AI2DesignBasicModes  = {
    AiCadMode: "AiCadMode",
    LayoutMode: "LayoutMode",
    SwjLayoutMode: "SwjLayoutMode",
    DesignMode: "DesignMode",
    HouseDesignMode: "HouseDesignMode",
    HotelLayoutMode: "HotelLayoutMode",
    EzDxfEditMode: "EzDxfEditMode",
    ExDrawingMode: "ExDrawingMode",
    MeasurScaleMode: "MeasurScaleMode",
    RulerMode: "RulerMode",
    RemodelingMode: "RemodelingMode",
    HouseCorrectionMode: "HouseCorrectionMode",
    AIViewEditMode: "AIViewEditMode",
    TrialModelMode: "TrialModelMode",
} as const

const LeftPanel: React.FC = () => {
    const { styles } = useStyles();
    const store = useStore();

    useEffect(() => {
        // const currentMode = LayoutAI_App.instance._current_handler_mode;
        store.homeStore.setDesignMode(AI2DesignBasicModes.HouseDesignMode);
    }, []);

    function panelItem(mode: string) {
        switch (mode) {
            case AI2DesignBasicModes.HouseDesignMode:
                return <HouseLayoutMenu></HouseLayoutMenu>;
        }
    }

    return <div className={`${styles.root}`}>{panelItem(store.homeStore.designMode)}</div>;
};

export default observer(LeftPanel);
