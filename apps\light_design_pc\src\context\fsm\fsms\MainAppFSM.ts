import { FSM } from '../FSM';
import { Design2DFSM } from './Design2DFSM';
import { Design3DFSM } from './Design3DFSM';
import { FSMNames, MainAppStates } from '../const/FSMConst';

/**
 * 主应用FSM
 */
export class MainAppFSM extends FSM {
  constructor() {
    super(FSMNames.MAIN_APP);
    
    // 添加子FSM作为状态
    this.addState(new Design2DFSM());
    this.addState(new Design3DFSM());
    
    // 设置默认状态
    this.setDefaultState(MainAppStates.DESIGN_2D);
    
    // 配置状态切换关系
    this.addTransition(MainAppStates.DESIGN_2D, MainAppStates.DESIGN_3D);
    this.addTransition(MainAppStates.DESIGN_3D, MainAppStates.DESIGN_2D);
  }
} 