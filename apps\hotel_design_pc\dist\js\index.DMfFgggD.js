(function(){const i=document.createElement("link").relList;if(i&&i.supports&&i.supports("modulepreload"))return;for(const e of document.querySelectorAll('link[rel="modulepreload"]'))u(e);new MutationObserver(e=>{for(const t of e)if(t.type==="childList")for(const n of t.addedNodes)n.tagName==="LINK"&&n.rel==="modulepreload"&&u(n)}).observe(document,{childList:!0,subtree:!0});function l(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),e.crossOrigin==="use-credentials"?t.credentials="include":e.crossOrigin==="anonymous"?t.credentials="omit":t.credentials="same-origin",t}function u(e){if(e.ep)return;e.ep=!0;const t=l(e);fetch(e.href,t)}})();const y="modulepreload",g=function(c,i){return new URL(c,i).href},h={},v=function(i,l,u){let e=Promise.resolve();if(l&&l.length>0){const n=document.getElementsByTagName("link"),r=document.querySelector("meta[property=csp-nonce]"),m=(r==null?void 0:r.nonce)||(r==null?void 0:r.getAttribute("nonce"));e=Promise.allSettled(l.map(o=>{if(o=g(o,u),o in h)return;h[o]=!0;const a=o.endsWith(".css"),p=a?'[rel="stylesheet"]':"";if(!!u)for(let f=n.length-1;f>=0;f--){const d=n[f];if(d.href===o&&(!a||d.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${o}"]${p}`))return;const s=document.createElement("link");if(s.rel=a?"stylesheet":y,a||(s.as="script"),s.crossOrigin="",s.href=o,m&&s.setAttribute("nonce",m),document.head.appendChild(s),a)return new Promise((f,d)=>{s.addEventListener("load",f),s.addEventListener("error",()=>d(new Error(`Unable to preload CSS for ${o}`)))})}))}function t(n){const r=new Event("vite:preloadError",{cancelable:!0});if(r.payload=n,window.dispatchEvent(r),!r.defaultPrevented)throw n}return e.then(n=>{for(const r of n||[])r.status==="rejected"&&t(r.reason);return i().catch(t)})};v(()=>import("./bootstrap.CFjiqtge.js").then(c=>c.b),[],import.meta.url);export{v as _};
