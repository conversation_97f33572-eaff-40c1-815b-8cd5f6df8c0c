import { TLayoutRelation } from "../TLayoutRelation";


export interface I_AttachedInfo 
{
    dir_type?:  number;
    /**
     *  法向偏移距离
     * 
     *  先简单点, 使用eval表示法
     * 
     *  三种类型:
     *    ---> 直接是数值
     *    ---> 直接是百分比
     *    ---> 括号开始的:   
     *       --- 默认参数有:  
     *            ---  ml,md (主元的长 主元的深)
     *            ---  gl,gd (组合的长 组合的深) 
     *       --- 0.5 * ml + 0.5 * td
     */
    nor_offset_dist?: string;
    /**
     *  切向偏移距离
     */
    dir_offset_dist?: string;


    keep_u_dv_flag?: number;

}
/**
 *   附件关系:  一般用于后处理加 地毯
 */
export class TAttachedRelation extends TLayoutRelation
{

}