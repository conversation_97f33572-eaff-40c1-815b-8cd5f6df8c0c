import { makeAutoObservable } from 'mobx';
import { getCabinetList, getCabinetImageList } from '@/services/user';

interface Params {
  trace_id: string;
  images_base64: string;
  width?: number;
  height?: number;
  depth?: number;
  organ_id: string | undefined;
}
/**
 * @description 主页数据
 */
class HomeStore {
  cabinetData: any = [];
  skeletonStatus: boolean = false;
  selectedCabinet: any = {};
  file: any = '';
  narrow: boolean = false;     //缩小
  loading: boolean = false;
  selectedTags: string[] = ['推拉门','平开门','懒人踢','换鞋凳','妆容镜','开放格','玻璃门'];
  organTags: string[] = [];
  side: number = 0;
  tabValue: string = '参数搭柜';
  previewLoading: boolean = false;
  submitParams: any = {};
  openside: boolean = false;
  canvasImg: any = null;
  info: any = {};
  typeNum: number = 0;
  hasInitSelect: boolean = false;
  fromPath: string = '';
  constructor() {
    makeAutoObservable(this, {}, {autoBind: true});
  }
  // action

  setHasInitSelect(data: boolean) {
    this.hasInitSelect = data;
  }
  setTypeNum(data: number) {
    this.typeNum = data;
  }
  setCabinetData(data: any) {
    this.cabinetData = data;
  }
  setSkeletonStatus(data: any) {
    this.skeletonStatus = data;
  }
  setSelectedCabinet(data: any) {
    data.wireframes_img_path = data.wireframes_img_path.replace('https://test-3vj-xelawk.oss-cn-shenzhen.aliyuncs.com', 'https://test-3vj-xelawk.oss-cn-shenzhen-internal.aliyuncs.com');
    this.selectedCabinet = data;
  }
  setFile(data: any)
  {
    this.file = data;
  }

  setNarrow(data: any)
  {
    this.narrow = data;
  }
  setLoading(data: any)
  {
    this.loading = data;
  }
  setSelectedTags(data: any)
  {
    this.selectedTags = data;
  }
  setSelectedOrganTags(data: any)
  {
    this.organTags = data;
  }
  setSide(data: any)
  {
    this.side = data;
  }
  setTabValue(data: any)
  {
    this.tabValue = data;
  }
  setPreviewLoading(data: any)
  {
    this.previewLoading = data;
  }
  setSubmitParams(data: any)
  {
    this.submitParams = data;
  }
  setOpenside(data: any)
  {
    this.openside = data;
  }
  setCanvasImg(data: any)
  {
    this.canvasImg = data;
  }
  setInfo(data: any)
  {
    this.info = data;
  }
  setFromPath(data: any)
  {
    this.fromPath = data;
  }

  generateTraceId = () => {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  }
  handleSubmit = async () => {
    const { width, height, depth, isSearchExample, userInfo, filterList } = this.submitParams;
    const submitFilterList = filterList.filter((item: any) => item.type === this.typeNum)
    this.setSkeletonStatus(true);
    if(this.tabValue === '参数搭柜') 
    {
      const accurate_dict = submitFilterList.reduce((acc: any, item: any) => {
        acc[item.key] = item.value;
        return acc;
      }, {});

      let label_dict = {
        long_clothes_num: this.typeNum === 1 ? accurate_dict['long_clothes_num'] : undefined,
        short_clothes_num: this.typeNum === 1 ? accurate_dict['short_clothes_num'] : undefined,
        pants_num: this.typeNum === 1 ? accurate_dict['pants_num'] : undefined,
        insert_drawer_num: this.typeNum === 1 && accurate_dict['insert_drawer_num'] ? accurate_dict['insert_drawer_num'] * 2 : undefined,
        out_drawer_num: this.typeNum === 1 && accurate_dict['out_drawer_num'] ? accurate_dict['out_drawer_num'] * 2 : undefined,
        stack_area_ratio: this.typeNum === 1 && accurate_dict['stack_area_ratio'] ? accurate_dict['stack_area_ratio'] * 0.2 : undefined,
        desk: this.typeNum === 1 && accurate_dict['desk'] ? 1 : 0,
        side: this.typeNum === 1 && this.openside ? 1 : 0,

        flattie_num: accurate_dict['flattie_num'],
        sneaker_num: accurate_dict['sneaker_num'],
        high_heeled_shoes_num: accurate_dict['high_heeled_shoes_num'],
        mid_calf_shoes_num: accurate_dict['mid_calf_shoes_num'],
        long_calf_shoes_num: accurate_dict['long_calf_shoes_num'],
        luggage_num: accurate_dict['luggage_num'],
        drawer_num: accurate_dict['drawer_num'],
        hang_clothes_num: accurate_dict['hang_clothes_num'],
        counter_top_num: accurate_dict['counter_top_num'],
        lazy_kick: this.typeNum === 2 && this.selectedTags.includes('懒人踢') ? 1 : 0,
        shoe_bench: this.typeNum === 2 && this.selectedTags.includes('换鞋凳') ? 1 : 0,
        dressing_mirror: this.typeNum === 2 && this.selectedTags.includes('妆容镜') ? 1 : 0,

        open_cell_num: this.typeNum === 3 && this.selectedTags.includes('开放格') ? 1 : 0,
        glass_door: this.typeNum === 3 && this.selectedTags.includes('玻璃门') ? 1 : 0,
        refrigerator_num: accurate_dict['refrigerator_num'],
        wine_rack_num: accurate_dict['wine_rack_num'],
        low_storage_num: accurate_dict['low_storage_num'],
        mid_storage_num: accurate_dict['mid_storage_num'],
        height_storage_num: accurate_dict['height_storage_num'],
      };
      
      const filtered_dict = Object.entries(label_dict).reduce((newDict, [key, value]) => {
        if (value !== 0) {
          newDict[key] = value;
        }
        return newDict;
      }, {} as { [key: string]: any });

      let nametags = [];
      if(this.typeNum === 1){
          if(this.selectedTags.includes('推拉门'))
          {
            nametags.push('sliding_door_wardrobe');
          }
          if(this.selectedTags.includes('平开门'))
          {
            nametags.push('swing_door_wardrobe');
          }
      }
      if(this.typeNum === 2){
        nametags.push('shoe_cabinet')
      }
      if(this.typeNum === 3){
        nametags.push('dining_cabinet')
      }
      let organ_id = [];
      if(this.organTags.includes('平台库'))
      {
        organ_id.push('C00000022');
      }
      if(this.organTags.includes('企业库'))
      {
        organ_id.push(userInfo?.tenantId);
      }
      const res = await getCabinetList({
        trace_id: this.generateTraceId(),
        category: nametags,
        width: Number(width),
        height: Number(height),
        depth: Number(depth),
        organ_id_list: organ_id,
        material_id_list: "",
        label_dict: {
          ...filtered_dict
        }
      })
      if(res && res.length > 0)
      {
        res.map((item: any) => {
          item.checked = false;
          item.main_img_path = item.main_img_path.replace('https://test-3vj-xelawk.oss-cn-shenzhen.aliyuncs.com', 'https://test-3vj-xelawk.oss-cn-shenzhen-internal.aliyuncs.com');
        })
        this.setCabinetData(res);
        console.log(res)
      } else 
      {
        this.setCabinetData([]);
      }
    }
    if(this.tabValue === '以图搭柜')
    {
      const params: Params = {
        trace_id: this.generateTraceId(),
        images_base64: this.file,
        width: width,
        height: height,
        depth: depth,
        organ_id: userInfo?.tenantId,
      }
      if(isSearchExample)
      {
        delete params['width'];
        delete params['height'];
        delete params['depth'];
      }
      const res = await getCabinetImageList(params)
  
      if(res && res.length > 0)
      {
        res.map((item: any) => {
          item.checked = false;
          item.main_img_path = item.main_img_path.replace('https://test-3vj-xelawk.oss-cn-shenzhen.aliyuncs.com', 'https://test-3vj-xelawk.oss-cn-shenzhen-internal.aliyuncs.com');
        })
        this.setCabinetData(res);
        this.setHasInitSelect(true)
      } else 
      {
        this.setCabinetData([]);
        this.setHasInitSelect(true)
      }
      console.log('以图搭柜结果', res)
    }
    this.setSkeletonStatus(false);
}


}

export default HomeStore;