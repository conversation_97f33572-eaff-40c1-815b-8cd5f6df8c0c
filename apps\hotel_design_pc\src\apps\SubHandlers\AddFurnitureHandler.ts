import { DrawingFigureMode } from "@layoutai/basic_data";
import { LayoutAI_App, LayoutAI_Commands } from "@layoutai/layout_scheme";
import { Vector3 } from "three";
import { I_MouseEvent } from "@layoutai/z_polygon";
import { AI2BaseModeHandler } from "../AI2BaseModeHandler";
import { CadBaseSubHandler } from "./CadBaseSubHandler";


export class AddFurnitureHandler extends CadBaseSubHandler
{

    _faraway_point : Vector3;
        constructor(cad_mode_handler:AI2BaseModeHandler)
    {
        super(cad_mode_handler);
        this.name = "AddFurniture";
        this._cad_mode_handler = cad_mode_handler;
        this._drawing_rect = null;

        this._faraway_point = new Vector3(-9999999,-9999999,0);
    }

    enter(state?: number): void {
        console.log("Enter AddFurniture");
        this._drawing_rect = null;
        this.cleanSelection();
        this.manager.layout_container.cleanDimension();
        this._cad_mode_handler._is_moving_element = false;
        this._cad_mode_handler._is_painter_center_moving = false;

        if(!this.adding_figure_entity) {
            this.runCommand(LayoutAI_Commands.LeaveSubHandler);
            return;
        }
        else{
            this.adding_figure_entity.rect.rect_center = this._faraway_point;
        }
        this.update();
        // this.manager.setCanvasCursorState(LayoutAI_CursorState.Drawing);
    }

    leave(state?: number): void {
        this._drawing_rect = null;
        console.log("Leave AddFurniture");

        // this.manager.setCanvasCursorState(LayoutAI_CursorState.Default);  
    }

    onmousedown(ev: I_MouseEvent): void {
        if(ev.buttons == 2)
        {
            this.adding_figure_entity= null;
            this.manager._runCommand(LayoutAI_Commands.LeaveSubHandler);
            return;
        }
        this.update();

    }

    onmousemove(ev: I_MouseEvent): void {
        
        this._cad_mode_handler._is_moving_element = true;

        if( this.adding_figure_entity)
        {
            this.updateAddFigureRect(ev);
            this.adding_figure_entity._rect.updateRect();  
            this.update();
        }
    }

    onmouseup(ev: I_MouseEvent): void {
        // 如果鼠标松开的时候在左侧的工具栏上，不做任何操作
        if(LayoutAI_App.IsMobile)
        {
            let scrollContainer = document.querySelector('#pad_left_panel');
            const containerRect = scrollContainer.getBoundingClientRect();
            // 横屏下 鼠标松开的时候不超过侧边栏的右侧 则离开状态
            if((LayoutAI_App.instance._is_landscape))
            {
                if(ev._ev.x <= containerRect.right && ev._ev.y > containerRect.top && ev._ev.y < containerRect.bottom)
                {
                    this.manager._runCommand(LayoutAI_Commands.LeaveSubHandler);
                    return;
                }
            } else 
            {
                // 竖屏下 鼠标松开的时候不超过侧边栏顶部 则离开状态
                if(ev._ev.y > containerRect.top)
                {
                    this.manager._runCommand(LayoutAI_Commands.LeaveSubHandler);
                    return;
                }
            }
        } else 
        {
            if(ev._ev.x < 340)
            {
                this.manager._runCommand(LayoutAI_Commands.LeaveSubHandler);
                return;
            }
        }
        this._cad_mode_handler._is_moving_element = false;
        if( this.adding_figure_entity)
        {
            // if (this.adding_figure_entity.realType == "SoftFurniture"  && !this.manager.layer_CadFurnitureLayer.visible ) {
            //     LayoutAI_App.emit(EventName.MessageTip,'当前布局中有隐藏的家具，可在显示进行管理')
            // }
            // if (this.adding_figure_entity.realType == "Cabinet" && !this.manager.layer_CadCabinetLayer.visible ) {
            //     LayoutAI_App.emit(EventName.MessageTip,'当前布局中有隐藏的定制柜，可在显示进行管理')
            // }
    
            this.addNewEntitiy();
        }
    }

    updateAddFigureRect(ev: I_MouseEvent)
    {

        
        let _adding_figure_rect = this.adding_figure_entity._rect;
        // 移动图元的操作
        if(this.adding_figure_entity)
        {
            let r_center = _adding_figure_rect.rect_center;
            r_center = new Vector3(ev.posX, ev.posY, 0);
            _adding_figure_rect.rect_center = r_center;
            _adding_figure_rect.updateRect();
            if(this.adding_figure_entity.matched_rect)
            {
                this.adding_figure_entity.matched_rect.rect_center = r_center;
                this.adding_figure_entity.matched_rect.updateRect();
            }
            this.adding_figure_entity.update();
            this.update();
        }
    }

    drawCanvas(): void {
        if(this.adding_figure_entity)
        {
            this.adding_figure_entity.drawEntity(this.painter, 
                {is_selected: true, 
                    is_draw_texture: this.container.drawing_figure_mode === DrawingFigureMode.Texture, 
                    is_draw_figure: true, 
                    is_draw_outline: this.container.drawing_figure_mode === DrawingFigureMode.Outline 
                });
        }

    }
}