import { Design3DStates } from '../../const/FSMConst';
import type { IState } from '../../interfaces/IState';
import { appContext } from '../../../AppContext';

/**
 * 3D初始化状态
 */
export class Init3DState implements IState {
  name: string;
  parent?: any;

  constructor(name: string = Design3DStates.INIT3D) {
    this.name = name;
  }

  onEnter(data?: any): void {
    
    // TODO: 这里可以添加实际的3D初始化逻辑
    // 比如创建3D场景、初始化3D渲染器等
    
    // 自动转换到下一个状态
    appContext.mainFSM.transitionTo(Design3DStates.IDLE3D);
  }

  onExit(data?: any): void {
    // 状态退出逻辑
  }
} 