import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
    return {
        root: css`
            display: flex;
            justify-content: space-between;
            margin-top: 8px;
            font-size: 12px;

            label {
                display: flex;
                align-items: center;
            }

            div {
                display: flex;
                align-items: center;
            }

            .svg-input-number-suffix {
                position: relative;
                top: 0px;
                right: 3px;
                height: 2px;
            }

            .svg-input-number-small {
                width: 73px;
                height: 24px;
                line-height: 24px;
                font-size: 18px;
                transform: translateX(-8px);
            }
        `,
        other_colors: css`
            position: absolute;
            width: 200px;
            height: 50px;
            left: 20px;
            top: 20px;
            border: 1px solid #eee;
            background: #fff;
            display: block !important;
            .sub_color {
                float: left;
                border: 2px solid #eee;
                margin: 2px;
                font-size: 20px;
                width: 20px;
                height: 20px;
                text-align: center;

                :hover {
                    border: 2px solid #aaa;
                    cursor: pointer;
                }
            }
        `
    }
});