

import { AI_PolyTargetType, DrawingFigureMode } from "@layoutai/basic_data";
import { EventName, LayoutAI_App, LayoutAI_Commands, LayoutAI_Events, Logger, TBaseGroupEntity, TFurnitureEntity, TSubSpaceAreaEntity, roomSubAreaService } from "@layoutai/layout_scheme";
import { Vector3 } from "three";
import { I_MouseEvent, ZRect } from "@layoutai/z_polygon";
import { AI2BaseModeHandler } from "../AI2BaseModeHandler";
import { CadBaseSubHandler } from "./CadBaseSubHandler";



export class ScaleFurnitureSubHandler extends CadBaseSubHandler
{
    _previous_rect : ZRect;
    _logger: Logger = null;
    _last_pos : Vector3;
    constructor(cad_mode_handler: AI2BaseModeHandler)
    {
        super(cad_mode_handler);
        this.name = LayoutAI_Commands.Transform_Scaling;
        this._previous_rect = null;
        this._logger = Logger.instance;
        this._last_pos = null;
    }

    onmousedown(ev: I_MouseEvent): void {
        if(!this.selected_target.selected_transform_element || !this.selected_target.selected_transform_element._target_rect)
        {
            this._cad_mode_handler.runCommand(LayoutAI_Commands.LeaveSubHandler);
            return;
        }
        if(this.selected_target && this.selected_target.selected_rect)
        {
            this._cad_mode_handler._is_moving_element = true;
        } 
        else 
        {
            this._cad_mode_handler._is_moving_element = false;
        }


        this._previous_rect = this.selected_target.selected_transform_element._target_rect.clone();
        this.selected_target.selected_transform_element.recordOriginRect(this.selected_target.selected_transform_element._target_rect);

        let entity = TBaseGroupEntity.getOrMakeEntityOfCadRect(this.selected_target.selected_rect) as TBaseGroupEntity;
        if(entity.combination_entitys && entity.combination_entitys.length > 0)
        {
            let combinationRects: TFurnitureEntity[] = entity.recursivelyAddCombinationRects();
            this.selected_target.selected_transform_element.recordOriginCombinationRect(combinationRects);
            this.selected_target.selected_combination_entitys = combinationRects;
        }
   
        this._last_pos = new Vector3(ev.posX,ev.posY,0);
        this.EventSystem.emit_M(EventName.SelectingTarget, null, ev._ev);

    }
    onmousemove(ev: I_MouseEvent): void {
        if(ev.buttons != 1) return;

        let transform_element = this.selected_target.selected_transform_element;
        if(!transform_element) return;
        let current_pos = new Vector3(ev.posX,ev.posY,0);

        let movement = current_pos.clone().sub(this._last_pos);
        
        transform_element.applyTransformByMovement(movement,this.exsorb_rects);
        this.updateTransformElements();

        this.update();

    }
    onmouseup(ev: I_MouseEvent): void {
        if(this.selected_target.selected_transform_element)
        {
            this.selected_target.selected_transform_element.doneTransform();
            let info = this.selected_target.selected_transform_element.updateOperaionInfo(this.manager);
            if(info)
            {
                this.manager.appendOperationInfo(info);
            }
            this.selected_target.selected_transform_element.recordOriginRect(null);
            this.selected_target.selected_transform_element.recordOriginCombinationRect(null);
            let group_entity = TBaseGroupEntity.getOrMakeEntityOfCadRect(this.selected_target.selected_rect) as TBaseGroupEntity;
            for(let t_rect of this.selected_target.selected_combination_entitys)
            {
                TBaseGroupEntity.recordGroupRectData(t_rect,group_entity);
            }
        }
        if(this.selected_target.selected_entity instanceof TSubSpaceAreaEntity)
        {
            roomSubAreaService.updateSubAreaLayoutScheme(this.selected_target.selected_entity,true);
        }
        this.updateAttributes("init");
        this._cad_mode_handler._is_moving_element = false;
        this.selected_target.selected_transform_element._align_line = false;
        // 拉伸后重新对套系内的素材进行排序
        if(this?.selected_target?.selected_entity?.type === AI_PolyTargetType.Furniture)
        {
            let figure_element = (this.selected_target.selected_entity as TFurnitureEntity).figure_element;
            // 拉伸后同步rect的长宽
            if(figure_element?.matched_rect && (this.manager.layout_container.drawing_figure_mode !== DrawingFigureMode.Figure2D))
            {
                figure_element.rect._w = figure_element?.matched_rect?.w;
                figure_element.rect._h = figure_element?.matched_rect?.h;
            }
            if(figure_element && figure_element.getMaterialID())
            {
                // TMatchingOrdering.instance.sortMaterialsOfFigureElement(figure_element, figure_element._candidate_materials, '');
            }
            LayoutAI_App.emit_M(EventName.FigureElementSelected, figure_element);
            LayoutAI_App.emit(LayoutAI_Events.UpdateFigureElement, figure_element);
        }

        this._cad_mode_handler.runCommand(LayoutAI_Commands.LeaveSubHandler);
        this.update();
    }

    drawCanvas(): void {
        super.drawCanvas();
        if(!this.selected_target.selected_rect) return;
        let rect = this.selected_target.selected_rect.clone();
        let minY = Math.min(...rect.vertices.map(v => v.pos.y));
        if(!rect.rect_center) 
        {
            // 选中的时候有时候会选中房间，房间的rect_center是null
            return;
        }
        let pp = {x: rect.rect_center.x, y: minY - (30 / this._p_sc), z: 0};
        this.painter.fillStyle = "#454647";
        let text =`${Math.round(rect._w)} X ${Math.round(rect._h)}`;
        this.painter.drawRectWithText(new Vector3(pp.x, pp.y,pp.z), '#fff', text, pp, 67)
    }
}