import { I_XmlCHandlePartEntity, I_XmlEntityBase } from "../IWardrobeEntityInterface";
import { XmlEntityBase } from "../XmlEntityBase";


export class XmlCHandlePartEntity extends XmlEntityBase implements I_XmlCHandlePartEntity {
    buckleMaterialIdS?: string;
    otherTypeS?: string;
    rotateDegressS?: string;
    offsetXN?: number;
    offsetYN?: number;
    offsetZN?: number;
    installTypeN?: number;
    isHorizontalB?: boolean;
    doorKeyVisibleB?: boolean;
    isSpyHoleB?: boolean;
    backInstallFlagB?: boolean;
    isBackInstallB?: boolean;
    isUnlimitedOffsetB?: boolean;
    canSetHandleSizeB?: boolean;
    CWhMaterialComponent?: Record<string, unknown>;

    constructor(data?: Partial<I_XmlCHandlePartEntity>) {
        super(data);
        this.buckleMaterialIdS = data.buckleMaterialIdS ?? "";
        this.otherTypeS = data.otherTypeS ?? "";
        this.offsetXN = data.offsetXN ?? 0;
        this.offsetYN = data.offsetYN ?? 0;
        this.offsetZN = data.offsetZN ?? 0;
        this.installTypeN = data.installTypeN ?? 0;
        this.isBackInstallB = data.isBackInstallB ?? false;
        this.isHorizontalB = data.isHorizontalB ?? false;
        this.backInstallFlagB = data.backInstallFlagB ?? false;
        this.isUnlimitedOffsetB = data.isUnlimitedOffsetB ?? false;
        this.canSetHandleSizeB = data.canSetHandleSizeB ?? false;
        this.CWhMaterialComponent = {};
        if(data.CWhMaterialComponent)
        {
            this.CWhMaterialComponent = {...data.CWhMaterialComponent};
        }

    }
}

XmlEntityBase.Generators["CHandlePartEntity"] = (data:I_XmlEntityBase)=>new XmlCHandlePartEntity(data);