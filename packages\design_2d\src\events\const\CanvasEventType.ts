

// 画布事件类型
export const CanvasEventType = {
    // 鼠标事件
    MOUSE_DOWN: 'mousedown',
    MOUSE_MOVE: 'mousemove',
    MOUSE_UP: 'mouseup',
    MOUSE_ENTER: 'mouseenter',
    MOUSE_LEAVE: 'mouseleave',
    MOUSE_OVER: 'mouseover',
    MOUSE_OUT: 'mouseout',
    WHEEL: 'wheel',

    // 触摸事件
    TOUCH_START: 'touchstart',
    TOUCH_MOVE: 'touchmove',
    TOUCH_END: 'touchend',
    TOUCH_CANCEL: 'touchcancel',

    // 键盘事件
    KEY_DOWN: 'keydown',
    KEY_UP: 'keyup',
    KEY_PRESS: 'keypress',

    CONTEXT_MENU: 'contextmenu',

    // 画布变换事件
    CANVAS_MOVE: 'canvas:move',
    CANVAS_ZOOM: 'canvas:zoom',
    CANVAS_TRANSFORM: 'canvas:transform'

} as const;

export type CanvasEventType = typeof CanvasEventType[keyof typeof CanvasEventType];
