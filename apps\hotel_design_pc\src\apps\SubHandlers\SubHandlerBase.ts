import { OperationInfo, TAppManagerBase, T_Handlerbase } from "@layoutai/layout_scheme";


export class SubHandlerBase extends T_Handlerbase
{
  

    manager : TAppManagerBase;
    constructor(manager:TAppManagerBase, name:string="")
    {
        super(name);
        this.manager = manager;
    }   


    get EventSystem()
    {
        return this.manager.EventSystem;
    }
    get painter()
    {
        return this.manager.painter;
    }

    updateTransformInfo() : OperationInfo
    {
        return null;
    }

    cleanState()
    {
        
    }

    update()
    {
        this.manager.update();
    }

    updateSidePanel()
    {

    }
    rotate()
    {
        
    }
    flip()
    {
        
    }

    deleteElement()
    {

    }
    updateSize(params?:any)
    {

    }
    toAiCadMode()
    {

    }
    cleanData()
    {
        
    }

    copySelectedTarget()
    {
        
    }

    updateEditNum()
    {
        
    }

    updateCandidateRects()
    {

    }

    cleanSelection()
    {
        
    }
}