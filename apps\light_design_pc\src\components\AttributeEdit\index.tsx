import React, { useEffect } from "react";
import useStyles from "./style";
import { observer } from "mobx-react-lite";
import { useStore } from "../../store/RootStore";
import { RightPropertyPanel } from "../../components";

/**
 * @description 属性面板
 */

const AttributeEdit: React.FC = () => {
    const { styles } = useStyles();
    const store = useStore();

    useEffect(() => {}, []);

    const ShowPanel = () => {
        return (
            <RightPropertyPanel 
                right={true}
                left={false}
                selectedEntity={store.homeStore?.selectObject?.entity}
            ></RightPropertyPanel>
        );
    };

    return <div className={styles.root}>{ShowPanel()}</div>;
};

export default observer(AttributeEdit);
