import { resolve } from 'path';
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
// import react from '@vitejs/plugin-react-swc'
import { viteExternalsPlugin } from 'vite-plugin-externals'
import { copy } from 'copy-vite-plugin'
import { createHtmlPlugin } from 'vite-plugin-html'

const {
  DEBUG = '',
} = process.env;

const isDebug = !!DEBUG;

/**
 * 将script标签移动到页面底部
 * @returns 返回一个包含transformIndexHtml函数的对象
 */
const jsToBody = () => {
  return {
    name: "entry-js-to-body",
    transformIndexHtml(html: any) {
      // html = html.replace(`type="module" crossorigin`, "")
      const scriptTag = html.match(/<script[^>]*>(.*?)<\/script[^>]*>/)[0]
      // console.log("\n SCRIPT TAG", scriptTag, "\n")
      html = html.replace(scriptTag, "")
      html = html.replace("<!-- # INSERT SCRIPT HERE -->", scriptTag)
      return html;
    }
  }
}

// https://vitejs.dev/config/
export default defineConfig({
  base: './',
  server: {
    port: isDebug ? 8081: 8083,
    host: true,
    https: {
      key: resolve(__dirname, './cert.key'),
      cert: resolve(__dirname, './cert.crt'),
    },
  },
  build: {
    rollupOptions: {
      output: {
        entryFileNames: 'js/[name].[hash].js',
        chunkFileNames: 'js/[name].[hash].js',
        assetFileNames: ({ name }) => {
          if (name?.endsWith('.css')) {
            return 'css/[name].[hash].[ext]';
          }
          return 'assets/[name].[hash].[ext]';
        },
      },
    },
  },
  plugins: [
    react(),
    viteExternalsPlugin({
      'react': 'React',
      'react-dom': 'ReactDOM',
      'react-dom/client': 'ReactDomClient',
      'mobx': 'mobx',
      'mobx-react-lite': 'mobxReactLite',
      'mobx-react': 'mobxReact',
    }),
    copy({
      pattern: [
        {
          from: './manifest.json',
          to: 'manifest.json'
        }
      ]
    }),
    createHtmlPlugin({
      inject: {
        data: {
          title: '三维家-在线家装设计软件',
          isDev: process.env.NODE_ENV === 'development',
          isDebug,
        },
      },
    }),
    jsToBody(),
  ],
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
      },
    },
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
    },
  },
})
