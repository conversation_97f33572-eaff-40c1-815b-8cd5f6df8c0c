import { TWholeLayoutScheme } from '@/Apps/LayoutAI/Layout/TLayoutScheme/TWholeLayoutScheme';
import { TSeriesSample } from '@/Apps/LayoutAI/Layout/TSeriesSample';
import { TrialStep } from '@/pages/Trial/trial';
import { makeAutoObservable } from 'mobx';
import { Vector3Like } from 'three';
/**
 * @description 体验版数据
 */

interface HouseData {
  id?: string;
  modelName?: string;
  buildingName?: string;
  area?: number;
  roomTypeName?: string;
  roomType?: string;
  imagePath?: string;
  province?: string;
  city?: string;
  district?: string;
  provinceName?: string;
  cityName?: string;
  districtName?: string;
  roomId?: string;
  schemeId?: string;
  orientationsType?: number;
  groundingTime?: string;
  openRoomId?: string;
  address?: string;
  roysceneVersion?: number;
}

class TrialStore {
  isProgressComplete = true;  //布局生成进度条
  houseData: HouseData = {};
  selectedSeries: TSeriesSample;
  selectedLayout: TWholeLayoutScheme;
  currentStep?: TrialStep = TrialStep.Home;
  current_pos?: Vector3Like;
  constructor() {
    makeAutoObservable(this, {}, {autoBind: true});
  }
  setIsProgressComplete(data: boolean) {
    this.isProgressComplete = data;
  }
  setHouseData(data: HouseData) {
    this.houseData = data;
  }
  setSelectedSeries(data: TSeriesSample) {
    this.selectedSeries = data;
  }
  setSelectedLayout(data: TWholeLayoutScheme) {
    this.selectedLayout = data;
  }
  setCurrentStep(data: TrialStep) {
    this.currentStep = data;
  }
  setCurrentPos(data: Vector3Like) {
    this.current_pos = data;
  }
}

export default TrialStore;