import { Vector3 } from "three";
import { DomainApiHub, EntityType, IEntityRoom } from "@layoutai/design_domain";

/**
* @description 布局工具
*/
export class LayoutUtils {

    // 获取最大房间的中心点坐标
    public static getLargestRoomCenter(): Vector3 | null{
        const roomEntities = DomainApiHub.instance.getEntitiesByType(EntityType.room) as IEntityRoom[];
        let room = roomEntities.reduce((maxRoom: IEntityRoom | null, currentRoom: IEntityRoom) => {
            if (!maxRoom) return currentRoom;
            return currentRoom.area > maxRoom.area ? currentRoom : maxRoom;
        }, null)
        if (room) {
            const center = room.mainRect.rect_center;
            return center || new Vector3(0, 0, 0);
        }
        return null;
    }
}