interface Context {
    [key: string]: number | { [subKey: string]: number };
  }
  
  export function evaluateUltimateExpression(
    expr: string,
    context: Context
  ): number {
    // 预处理
    expr = expr
      .replace(/&amp;&amp;/g, '&&')
      .replace(/&gt;/g, '>')
      .replace(/&lt;/g, '<')
      .replace(/\s/g, '')
      .replace(/^=/, '');
  
    // 处理ABS函数
    expr = resolveAbsFunctions(expr, context);
  
    // 处理嵌套条件（最多10层）
    expr = resolveDeepNesting(expr, context, 10);
  
    // 处理对象属性
    expr = resolveObjectProperties(expr, context);
  
    // 处理条件分支
    const branches = expr.split(';').filter(b => b.trim());
    for (const branch of branches) {
      const [value, condition] = parseBranch(branch);
      if (!condition || evaluateCondition(condition, context)) {
        return evaluateWithRPN(value, context);
      }
    }
  
    throw new Error(`无匹配分支: ${expr}`);
  }
  
  // ================ 逆波兰表达式核心 ================
  function evaluateWithRPN(expr: string, context: Context): number {
    const tokens = tokenize(expr);
    const rpn = convertToRPN(tokens);
    return evaluateRPN(rpn, context);
  }
  
  function tokenize(expr: string): string[] {
    const regex = /(\d+\.?\d*)|([a-zA-Z_]\w*(?:\.[a-zA-Z_]\w*)*)|([+\-*/()])/g;
    return Array.from(expr.matchAll(regex)).map(match => match[0]);
  }
  
  function convertToRPN(tokens: string[]): string[] {
    const output: string[] = [];
    const operators: string[] = [];
    const precedence: Record<string, number> = {
      '*': 3, '/': 3,
      '+': 2, '-': 2
    };
  
    for (const token of tokens) {
      if (/^\d+\.?\d*$/.test(token)) {
        output.push(token);
      } else if (/^[a-zA-Z_]/.test(token)) {
        output.push(token);
      } else if (token in precedence) {
        while (
          operators.length &&
          operators[operators.length - 1] !== '(' &&
          precedence[operators[operators.length - 1]] >= precedence[token]
        ) {
          output.push(operators.pop()!);
        }
        operators.push(token);
      } else if (token === '(') {
        operators.push(token);
      } else if (token === ')') {
        while (operators.length && operators[operators.length - 1] !== '(') {
          output.push(operators.pop()!);
        }
        operators.pop(); // 弹出左括号
      }
    }
  
    return output.concat(operators.reverse());
  }
  
  function evaluateRPN(rpn: string[], context: Context): number {
    const stack: number[] = [];
  
    for (const token of rpn) {
      if (/^\d+\.?\d*$/.test(token)) {
        stack.push(parseFloat(token));
      } else if (/^[a-zA-Z_]/.test(token)) {
        const value = getNestedValue(context, token.split('.'));
        if (typeof value !== 'number') throw new Error(`无效数值: ${token}`);
        stack.push(value);
      } else {
        const b = stack.pop()!;
        const a = stack.pop()!;
        switch (token) {
          case '+': stack.push(a + b); break;
          case '-': stack.push(a - b); break;
          case '*': stack.push(a * b); break;
          case '/': stack.push(a / b); break;
        }
      }
    }
  
    if (stack.length !== 1) throw new Error("无效表达式");
    return parseFloat(stack[0].toFixed(4));
  }
  
  // ================ 其他工具函数 ================
  function resolveAbsFunctions(expr: string, context: Context): string {
    return expr.replace(/ABS\(([^()]+)\)/g, (_, inner) => {
      return Math.abs(evaluateUltimateExpression(inner, context)).toString();
    });
  }
  
  function resolveDeepNesting(
    expr: string,
    context: Context,
    maxDepth: number
  ): string {
    let current = expr;
    for (let i = 0; i < maxDepth; i++) {
      const next = current.replace(/\{([^{}]+)\}/g, (_, inner) => {
        return evaluateUltimateExpression(inner, context).toString();
      });
      if (next === current) break;
      current = next;
    }
    return current;
  }
  
  function resolveObjectProperties(expr: string, context: Context): string {
    return expr.replace(/([a-zA-Z_]\w*(?:\.[a-zA-Z_]\w*)*)/g, match => {
      const value = getNestedValue(context, match.split('.'));
      if (typeof value !== 'number') throw new Error(`无效数值: ${match}`);
      return value.toString();
    });
  }
  
  function getNestedValue(obj: any, path: string[]): number {
    return path.reduce((acc, key) => {
      if (acc === undefined || acc === null) {
        throw new Error(`缺少属性: ${path.join('.')}`);
      }
      return acc[key];
    }, obj);
  }
  
  function parseBranch(branch: string): [string, string | null] {
    const match = branch.match(/^([^[]*)(?:\[([^\]]+)\])?$/);
    if (!match) throw new Error(`无效分支格式: ${branch}`);
    return [match[1], match[2]];
  }
  
  function evaluateCondition(cond: string, context: Context): boolean {
    // 处理逻辑或
    if (cond.includes('||')) {
      return cond.split('||').some(c => evaluateCondition(c.trim(), context));
    }
  
    // 处理逻辑与
    if (cond.includes('&&')) {
      return cond.split('&&').every(c => evaluateCondition(c.trim(), context));
    }
  
    // 处理比较运算
    const operators = ['==', '!=', '>=', '<=', '>', '<'];
    for (const op of operators) {
      const parts = cond.split(op);
      if (parts.length === 2) {
        const left = evaluateWithRPN(parts[0], context);
        const right = evaluateWithRPN(parts[1], context);
        
        switch (op) {
          case '==': return Math.abs(left - right) < 0.0001;
          case '!=': return Math.abs(left - right) >= 0.0001;
          case '>': return left > right;
          case '<': return left < right;
          case '>=': return left >= right;
          case '<=': return left <= right;
        }
      }
    }
    throw new Error(`无效条件: ${cond}`);
  }