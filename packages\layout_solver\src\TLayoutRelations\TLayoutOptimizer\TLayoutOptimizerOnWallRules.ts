import { I_FigureGroupConfigs, TFeatureShape, TGraphBasicConfigs, TGroupTemplate, TRoomShape } from "@layoutai/layout_scheme";
import { ZEdge, ZRect, compareNames } from "@layoutai/z_polygon";
import { I_OnLineFigureInterval, TLayoutOptimizer } from "./TLayoutOptimizer";

export interface I_OnWall_DP_Data{
            
    /**
     *  与总长度差值
     */
    total_sum_length_error : number;
    /**
     *  当前值下的长度差值
     */
    curr_sum_length_error : number;

    /**
     *   与期望-长度、中心的差值
     */
    expectation_error : number;

    /**
     *  总期望误差
     */
    total_expectation_error : number;
    /**
     *  所选的值列表
     */
    selected_lengths : {length:number, center_x:number}[];
 }

export class TLayoutOptmizerOnWallRules 
{
     /**
     * 二: 沿墙优化:   这里主要是针对在背靠同一面墙上的图元, 对齐依次排序
     * @param feature_shape 
     * @param group_templates 
     * @param room_name 
     */
     static optimize_groups_with_onwall_rules(feature_shape:TFeatureShape, t_group_templates:TGroupTemplate[],room_name:string,is_onwall_arrange:boolean = false)
     {
         let group_config = TGraphBasicConfigs.MainGroupFigureConfigs[room_name];
         if(!group_config) return;
 
         let group_templates = [...t_group_templates];
 
         // TsAI_app.log(group_templates);
         // 1、先对对组合进行优先级排序
         let get_template_order = (a:TGroupTemplate)=>{
             if(a._target_rect.ex_prop['opt_place_order'])
             {
                 return ~~a._target_rect.ex_prop['opt_place_order'];
             }
             let config = group_config[a.group_space_category];
             if(!config) return 0;
             return config.place_order;
         }
         if(is_onwall_arrange)
         {
             for(let edge of feature_shape._poly.edges)
             {
                let wins = TRoomShape.getWindowsOfEdge(edge);
                if(wins)
                {
                    for(let win of wins)
                    {
                        let pp =edge.projectEdge2d( win.rect.rect_center);

                        let t_pos = edge.unprojectEdge2d({x:pp.x,y:0});

                        let t_nor = edge.nor.clone().negate();


                        let rect = new ZRect(win.rect.w, 375);

                        rect.back_center = t_pos;
                        rect.nor = t_nor;

                        let group_template = TGroupTemplate.makeGroupTemplateByGroupSpaceCategory("窗前吊柜",room_name,rect);

                        group_templates.push(group_template);

                    }
                }
             }
         }
         group_templates.sort((a,b)=>{
             return get_template_order(b) - get_template_order(a);
         });
 
         
         let w_poly = feature_shape._w_poly;  
 
         let wall_edges = [...feature_shape._w_poly.edges];
 
 
 
         // 2、先把门内区域给裁剪掉
         wall_edges = TLayoutOptimizer.pre_sub_room_edges_inwall(feature_shape,wall_edges,room_name);


 
         // 上述过程的优化是相似的
         // 3、先用place_order 大于等于4 的图元裁剪掉墙
 
         let visited_group_template : TGroupTemplate[] = [];
         for(let group_template of group_templates)
         {
             let place_order = get_template_order(group_template);
 
             // TsAI_app.log(group_template.group_space_category,place_order);
 
             if(place_order < 3.999) break;
 
 
             visited_group_template.push(group_template);
             let rect = group_template._target_rect;
             let config =  group_config[group_template.group_space_category] || null;
             if(!config) continue;
             if(config.is_placeholder === false) {
                 continue; // 不是占位符
             }
 
             let t_rect = rect.clone();
             let align_side_dist = 100;
             if(config.placeholder_expand)
             {
                 if(config.placeholder_expand?.front_extend) // 向前扩展
                 {
                     t_rect._h += config.placeholder_expand?.front_extend || 0;
                     t_rect.updateRect();
                 }
 
                 if(config.placeholder_expand?.wall_side_extend)
                 {
                     let wall_side_extend = config.placeholder_expand?.wall_side_extend || 0;
 
                     align_side_dist = Math.max(align_side_dist,wall_side_extend);
 
                 }
             }
             TLayoutOptimizer.sub_edges_by_rect(t_rect, wall_edges, align_side_dist);
         }
 
         for(let group_template of group_templates)
         {
             let config =  group_config[group_template.group_space_category] || null;
             if(!config) continue;
             if(config.is_placeholder === false) {
                 visited_group_template.push(group_template);
             }
         }
        //  TsAI_app.log([...visited_group_template]);
 
         // 4、然后依次找每条边上有的组合
         let group_on_edge_list : {edge:ZEdge, group_templates:TGroupTemplate[],place_order:number}[] = [];
         for(let edge of wall_edges)
         {
             let t_edge_list : {edge:ZEdge, group_templates:TGroupTemplate[],place_order:number} = {edge:edge,group_templates:[],place_order:0};
             group_on_edge_list.push(t_edge_list);

         }
         for(let group_template of group_templates)
         {
            let rect = group_template._target_rect;

            let t_rect = rect.clone();

            t_rect.updateRect();

            let target_edge_data : {edge:ZEdge, group_templates:TGroupTemplate[],place_order:number} = null;
            let max_layon_len = 0;

            //  找到背靠这条边上最大的那个
            for(let t_edge_data of group_on_edge_list)
            {
                 let edge = t_edge_data.edge;
                 let  l_res :{layon_len?:number, ll?:number, rr?:number } = {}; 

                 if(edge.checkSameNormal(t_rect.nor.clone().negate(),false))
                 {
                    if(edge.islayOn(t_rect.backEdge,300,0.01,l_res))
                    {
                       if(l_res.layon_len > max_layon_len)
                       {
                           target_edge_data = t_edge_data;
                           max_layon_len = l_res.layon_len;
                       }
                    }
                 }

            }

            if(target_edge_data)
            {
                target_edge_data.group_templates.push(group_template);

            }
         }
 
         for(let t_edge_data of group_on_edge_list)
         {
            // 只有大于等于1的情况, 才需要处理
            let sum_length = 0;
            t_edge_data.group_templates.forEach((val)=>sum_length+=val._target_rect.length);
            if(compareNames([room_name],["厨房"]))
            {
                if( t_edge_data.group_templates.length >= 1)
                {
                    let place_order = 0;
    
                    for(let template of t_edge_data.group_templates)
                    {
                        place_order = Math.max(place_order,get_template_order(template));
                    }
                    t_edge_data.place_order = place_order;
                }
            }
            else {

                if(sum_length >= t_edge_data.edge.length || t_edge_data.group_templates.length > 1)
                {
                    let place_order = 0;
    
                    for(let template of t_edge_data.group_templates)
                    {
                        place_order = Math.max(place_order,get_template_order(template));
                    }
                    t_edge_data.place_order = place_order;
                }
                else{
                    let group_template = t_edge_data.group_templates[0];

                    if(group_template && t_edge_data.edge)
                    {

                        t_edge_data.place_order = get_template_order(group_template);

                        let t_rect = group_template._target_rect;

                        let t_edge = t_edge_data.edge;

                        let pp = t_edge.projectEdge2d(t_rect.rect_center);

                        let ll = pp.x - t_rect.w/2;
                        let rr = pp.x + t_rect.w/2;

                        // 大区域 以裁剪为主
                        if(compareNames([group_template.group_space_category],["沙发组合区","柜"]))
                        {
                            
                            if(ll < 0)
                            {
                                t_rect._w = t_rect.w+ll;
                                pp.x = t_rect.w/2;
                            }
                            else if(rr > t_edge.length)
                            {
                                t_rect._w = t_rect.w - (rr - t_edge.length); 
                                pp.x = t_edge.length - t_rect.w/2;
                            }
                            let pos = t_edge.unprojectEdge2d(pp);
                            t_rect.rect_center = pos;
                        }
                        else{
                            if(ll < 0)
                            {
                                pp.x = t_rect.w/2;
                            }
                            else if(rr > t_edge.length)
                            {
                                pp.x = t_edge.length - t_rect.w/2;
                            }
                            let pos = t_edge.unprojectEdge2d(pp);
                            t_rect.rect_center = pos;
                        }

                        if(t_edge_data.place_order >= 3. -1e-6)
                        {
                            TLayoutOptimizer.sub_edges_by_rect(t_rect, wall_edges, 200);

                        }
          
                    }
                    t_edge_data.group_templates.length = 0;

                }
            }

         }

 
         group_on_edge_list.sort((a,b)=>b.place_order - a.place_order);
         
         if(!is_onwall_arrange)
         {
            // console.log(group_on_edge_list);

         }
         let get_priority = (a:TGroupTemplate)=>{
             let config = group_config[a.group_space_category];
             if(!config) return 0;
             return config.priority || 0;
         }
         for(let g_list of group_on_edge_list)
         {
            if(g_list.group_templates.length == 0) continue;
             let intervals : I_OnLineFigureInterval[] = [];
             let t_edge = g_list.edge;
 
 
             if(wall_edges.indexOf(t_edge) < 0) // 说明被调整了
             {
                 let replace_edge :ZEdge = null;
                 for(let w_edge of wall_edges)
                 {
                     if(t_edge.islayOn(w_edge,10,0.8))
                     {
                         if(!replace_edge || w_edge.length > replace_edge.length)
                         {
                             replace_edge = w_edge;
                         }
                     }
                 }
                 t_edge = replace_edge;
 
             }
             if(!t_edge) continue;
             let  total_length = t_edge.length;
 
             g_list.group_templates.sort((a,b)=>{
                 let a_x = t_edge.projectEdge2d(a._target_rect.back_center).x;
                 let b_x = t_edge.projectEdge2d(b._target_rect.back_center).x;
 
                 return a_x - b_x;
             });
 
             if(get_priority(g_list.group_templates[0]) < get_priority(g_list.group_templates[g_list.group_templates.length-1]))
             {
                 let n_edge = new ZEdge(t_edge.v1,t_edge.v0);
                 n_edge._nor.copy(t_edge.nor);
                 t_edge = n_edge;
 
                 g_list.group_templates.sort((a,b)=>{
                     let a_x = t_edge.projectEdge2d(a._target_rect.back_center).x;
                     let b_x = t_edge.projectEdge2d(b._target_rect.back_center).x;
                     return a_x - b_x;
                 });
             }
 
             // console.log(g_list.group_templates);
             TLayoutOptmizerOnWallRules.optimize_groups_with_onwall_rules_onwall(t_edge, g_list.group_templates,room_name);
 
 
             for(let group_template of g_list.group_templates)
             {
                 
                 let t_rect = group_template._target_rect.clone();
                 let align_side_dist = 100;
                 if(room_name == "厨房") align_side_dist = 560;
                 TLayoutOptimizer.sub_edges_by_rect(t_rect, wall_edges, align_side_dist);
 
             }
         }
 
 
     }
 
     
     static make_interval_of_group_category(group_space_category:string,room_name:string)
     {
        let group_config = TGraphBasicConfigs.MainGroupFigureConfigs[room_name];
        let config : I_FigureGroupConfigs=  group_config[group_space_category] || null;
        let interval :I_OnLineFigureInterval = {
            name : group_space_category,
            result_length : 0,
            center_x : 0,
            target_length : 0,
            length_differ_weight : config?.differ_weight || 1.,
            center_diff_weight :  0,
            length_values : [],
            margin_value : 0,
        }

        if(config && config?.group_length_levels)
        {
            if(!config.group_length_levels.values)
            {
                let min = config.group_length_levels.min;
                let max = config.group_length_levels.max;
                let step = config.group_length_levels.step;
                let values : number[] = [];
                for(let val=min; val <=  max; val+=step)
                {
                    values.push(val);
                }
                if(max - values[values.length-1] > 1) values.push(max);

                config.group_length_levels.values = values;
            }



            interval.length_values = [...config.group_length_levels.values];


            interval.length_values.sort((a,b)=>a-b);

            if(interval.target_length < 1) // 相当于等于0
            {                
                interval.target_length = interval.length_values[interval.length_values.length-1];
            }
            
        }
        if(group_space_category.indexOf("见光")>=0 || 
            group_space_category.indexOf("收口")>=0 || 
            group_space_category.indexOf("转角")>=0) // 收口板
        {
            interval.zero_differ_weight = 10000;
        }
        return interval;
     }
     /**
      *   沿墙优化调整
      */
     static optimize_groups_with_onwall_rules_onwall(t_edge:ZEdge, on_wall_group_templates:TGroupTemplate[], room_name:string)
     {
         let group_config = TGraphBasicConfigs.MainGroupFigureConfigs[room_name];
         let intervals : I_OnLineFigureInterval[] = [];
         let  total_length = t_edge.length;
 
         for(let group_template of on_wall_group_templates)
         {
             let config : I_FigureGroupConfigs=  group_config[group_template.group_space_category] || null;
             let interval :I_OnLineFigureInterval = TLayoutOptmizerOnWallRules.make_interval_of_group_category(group_template.group_space_category,room_name);
             interval.target_length = group_template._target_rect._w;

             if(group_template._target_length !== undefined)
             {
                interval.target_length = group_template._target_length;
             }
             if(config && config?.group_margin_value)
             {
                interval.margin_value = config.group_margin_value;

                for(let i in interval.length_values)
                {
                    if(interval.length_values[i] >= 1)
                    {
                        interval.length_values[i] += interval.margin_value;
                    }
                }
                interval.target_length += interval.margin_value;
                interval.result_length = interval.target_length;

                // console.log(interval);
             }
             if(compareNames([group_template.group_space_category],["柜"]))
             {
                //interval.target_center_x = t_edge.projectEdge2d(group_template._target_rect.rect_center).x;
                let xx = t_edge.projectEdge2d(group_template._target_rect.rect_center).x;
                if(xx < t_edge.length/2)
                {
                    xx = 0;
                }
                else{
                    xx = t_edge.length;
                }
                interval.center_diff_weight = 0.1;
             }
             if(compareNames([group_template.group_space_category],["电视柜","沙发组合区","电视","床","梳妆台","书桌"]))
             {
                interval.target_center_x = t_edge.projectEdge2d(group_template._target_rect.rect_center).x;
                interval.center_diff_weight = 0.1;
             }
             
             intervals.push(interval);
         }


         this.optimize_online_intervals_dynamic_programming(intervals,total_length, compareNames([room_name],["厨房"],false)==1);

         if(!compareNames([room_name],["厨房"])) // 如果不是厨房
         {
            this.optimize_online_intervals_center_x(intervals, total_length);
         }
         for(let i=0; i < intervals.length; i++)
         {
             let group_template = on_wall_group_templates[i];
             let interval = intervals[i];
 
             let result_val = interval.result_length;
 
             let rect = group_template._target_rect;
             rect._w = result_val - (interval.margin_value || 0);
            
            //  if(rect._w < 0) {
            //     rect._w = 0;
            //  }
 
 
             let pp = t_edge.projectEdge2d(rect.back_center);
 
             pp.x = interval.center_x;
 
             let t_pos = t_edge.unprojectEdge2d(pp);
 
             rect._back_center.copy(t_pos);
 
             rect.updateRect();
 
         }
 
     }
  
    /**
     * 沿着墙的尺寸累加优化
     * @param intervals  
     * @param total_length 
     * @returns
     */
    static optimize_online_intervals(intervals:I_OnLineFigureInterval[], total_length : number, filling_wall:boolean = false)
    {

        let t_queue : {ids:number[], total_error ?: number,  differ_error ?: number,level:number}[] = [{ids:[],level:0}];
        
        for(let interval of intervals)
        {
            let ti = interval.length_values.length-1;
            for(let i=0; i < interval.length_values.length; i++)
            {
                if(interval.length_values[i] > interval.target_length - 0.1)
                {
                    ti = i;
                    break;
                }
            }
            interval.t_id = ti;
            interval.c_id = ti;
            t_queue[0].ids.push(ti);
            interval.result_length = interval.length_values[ti];
        }
        let sum_weight = 0;
        for(let interval of intervals)
        {
            sum_weight += interval.length_differ_weight || 0.1;
        }
        
        let query_error_func = (t_data:{ids:number[], total_error ?: number,  differ_error ?: number})=>{
            let differ_error = 0;
            let total_error = 1000000;
            let sum_length = 0;
             for(let i=0; i < t_data.ids.length; i++)
             {
                intervals[i].c_id = t_data.ids[i];
                let t_val = intervals[i].length_values[intervals[i].c_id];
                let diff_val = Math.abs(t_val - intervals[i].target_length) / 10; 
                diff_val = diff_val * diff_val  * intervals[i].length_differ_weight * 0.5;
                let diff_target_val =  Math.abs(t_val - total_length * intervals[i].length_differ_weight / sum_weight) * intervals[i].length_differ_weight * 1.;
                intervals[i].result_length = t_val;
                differ_error+=diff_val + diff_target_val;
                sum_length += t_val;
             }

 

             total_error = total_length - sum_length;

             t_data.differ_error = differ_error + Math.abs(total_error) * 10;
             t_data.total_error = total_error;
        }

        query_error_func(t_queue[0]);
        if(t_queue[0].total_error >= -0.1)
        {

            // 更新位置
            let left_val = 0;

            for(let i=0; i < intervals.length; i++)
            {
                let interval = intervals[i];
                let t_left_val = interval.center_x - interval.result_length / 2;

                if(t_left_val < left_val) // 说明有交
                {
                    interval.center_x = left_val + interval.result_length/2;
                }
                left_val = interval.center_x +  interval.result_length / 2;
            }

            let right_val = total_length;
            for(let i=intervals.length-1; i>=0; i--)
            {
                let interval = intervals[i];
                let t_right_val = interval.center_x + interval.result_length / 2;

                if(t_right_val > right_val)
                {
                    interval.center_x = right_val - interval.result_length/2;
                }
                right_val = interval.center_x - interval.result_length / 2;
            }

            if(!filling_wall)
            {
                return false;

            }

        }
        let ans_data : {ids:number[], total_error ?: number,  differ_error ?: number,level:number} = null;

        let ans_level = -1;

        let visited_codes : {[key:string]:number} = {};


        let encode_ids = (ids:number[])=>{
            let t_code = "";
            for(let id of ids)
            {
                t_code+="_"+id;
            }
            return t_code;
        }


        let find_opt_data_in_queue = (data: {ids:number[], total_error ?: number,  differ_error ?: number,level:number} , inc_or_desc:number=-1)=>
        {
            let queue = [data];
            for(let qi=0; qi < queue.length; qi++)
            {
                let q_data = queue[qi];
                visited_codes[encode_ids(q_data.ids)] = 1;
                query_error_func(q_data);
                if(q_data.total_error >= 0.1)
                {
                    if(!ans_data || q_data.differ_error < ans_data.differ_error)
                    {
                        ans_data = q_data;
                        ans_level = q_data.level;
                    }
                }
                for(let ti=0; ti < q_data.ids.length; ti++)
                {
                    let ids : number[] = [];
    
                    for(let id of q_data.ids)
                    {
                        ids.push(id);
                    }
                    if(ids[ti] > 0)
                    {
                        ids[ti]+=inc_or_desc;
    
                        let t_code = encode_ids(ids);
                        if(visited_codes[t_code]) continue;
                        visited_codes[t_code] = 1;
                        queue.push({ids:ids,level:q_data.level+1});
                    }
                }
                if(queue.length > 2000) break;
            }
    
        }

        find_opt_data_in_queue(t_queue[0],-1);
        // TsAI_app.log(queue,ans_data);
        if(filling_wall)
        {
            if(ans_data)
            {
                find_opt_data_in_queue(ans_data,1);
            }
        }

        if(ans_data)
        {
            // console.log(ans_data);
            query_error_func(ans_data);

            let left_val = 0;
            for(let interval of intervals)
            {
                interval.center_x = left_val + interval.result_length/2;
                left_val += interval.result_length;
            }
            return true;
        }        
        return false;
    }

    /**
     *  动态规划求解沿墙尺寸优化， 主要是求解尺寸, 尽可能铺满
     */
    static optimize_online_intervals_dynamic_programming(intervals:I_OnLineFigureInterval[], 
        total_length : number, filling_wall:boolean = false, step_len:number=10, result_dp_data:{result:I_OnWall_DP_Data[]}=null)
    {

        let f : I_OnWall_DP_Data[] = [];

        if(total_length < 0) return 99999999;
        let N = Math.floor(total_length / step_len);


        for(let i=0; i <= N; i++)
        {
            f.push({
                total_sum_length_error : N * step_len ,
                curr_sum_length_error : i * step_len,
                expectation_error : 0,
                total_expectation_error : 0,
                selected_lengths : []
            });
        }

        let g :I_OnWall_DP_Data[]= [];
        let copy_f = ()=>{
            let g :I_OnWall_DP_Data[] = [];
            for(let i=0; i <=N; i++)
            {
                g.push({
                    total_sum_length_error : f[i].total_sum_length_error,
                    curr_sum_length_error : f[i].curr_sum_length_error,
                    expectation_error : f[i].expectation_error,
                    total_expectation_error : f[i].total_expectation_error,
                    selected_lengths : [...f[i].selected_lengths]
                });
        
            }
            return g;
        }

        g= copy_f();
        let differ_error_func = (val : number, target_val:number,weight:number)=>{
            let differ_error = (target_val - val);
            differ_error = Math.abs(differ_error * differ_error) / 10 * weight;
            return differ_error;
        }
    
        for(let ti=0; ti < intervals.length; ti++)
        {
            let interval = intervals[ti];
            let target_center_x = interval.target_center_x || -1;
            let center_diff_weight = interval.center_diff_weight || 0;
    
            let zero_diff_weight = interval.zero_differ_weight || 0;
            for(let i=0; i <=N; i++)
            {
                let c_pos_val = i * step_len;
                f[i].expectation_error +=  differ_error_func(0,interval.target_length,interval.length_differ_weight + center_diff_weight + zero_diff_weight) ;
                f[i].selected_lengths.push({length:0,center_x:c_pos_val}); // 默认不选择
            }
            for(let length_val of interval.length_values)
            {
                let l_val = Math.floor(length_val / step_len);
                let differ_error = differ_error_func(length_val, interval.target_length,interval.length_differ_weight);

                for(let i=l_val; i<=N; i++)
                {
                    let g_differ_val = g[i-l_val].expectation_error + differ_error;
                    let c_pos_val = i * step_len;
                    if(center_diff_weight > 0.0001) 
                    {
                        let center_diff_val = differ_error_func(c_pos_val - length_val/2,target_center_x,center_diff_weight);
                        g_differ_val += center_diff_val;
                    }


                    if(g[i-l_val].curr_sum_length_error < f[i].curr_sum_length_error -0.1)  // 当累加长度更小的时候
                    {
                        f[i].curr_sum_length_error = g[i-l_val].curr_sum_length_error;
                        f[i].expectation_error = g_differ_val;
                        f[i].selected_lengths = [...g[i-l_val].selected_lengths,{length:length_val,center_x: c_pos_val - length_val/2}];
                    }
                    else if(Math.abs( g[i-l_val].curr_sum_length_error - f[i].curr_sum_length_error) < 0.1)  // 当累加长度跟当前相等
                    {
                        if(g_differ_val < f[i].expectation_error)
                        {
                            f[i].expectation_error = g_differ_val;
                            f[i].selected_lengths = [...g[i-l_val].selected_lengths,{length:length_val,center_x: c_pos_val - length_val/2}];
                        }
                    }
          
                }

            }
            for(let i=0; i <=N; i++)
            {
                f[i].total_sum_length_error = (N - i) * step_len + f[i].curr_sum_length_error;
            }

            g = copy_f();
        }
        for(let data of f )
        {
            data.total_expectation_error = data.total_sum_length_error * 100 + data.expectation_error;
        }

        // console.log(f);

        let ans_ti = N;
       
        if(result_dp_data)
        {
            result_dp_data.result = f;
        }
        let f_res = f[N];
        if(!f_res) return 9999999;
        for(let data of f)
        {
            if(data.total_expectation_error < f_res.total_expectation_error)
            {
                f_res = data;
            }
        }
        for(let i=0; i < f_res.selected_lengths.length; i++)
        {
            intervals[i].result_length = f_res.selected_lengths[i].length;
            intervals[i].center_x = f_res.selected_lengths[i].center_x;
        }       
        
        return f_res.curr_sum_length_error;
    }
    

    /**
     * 主要是求解位置
     * @param intervals  
     * @param total_length 
     * @returns
     */
    static optimize_online_intervals_center_x(intervals:I_OnLineFigureInterval[],total_length:number,step_len:number=10)
    {
        let sum_length = 0;
        intervals.forEach((val)=>sum_length+=val.result_length);

        if(sum_length > total_length - 200 ) return false;
        let N = Math.floor(total_length / step_len);
        interface DP_Data{
            /**
             *   与期望中心的差值
             */
            expectation_error : number;

            selected_center_x : number[];
         }
        let f : DP_Data[]=[];
        let g :DP_Data[]= [];

        for(let i=0; i <= N; i++)
        {
            f.push({
                expectation_error : 0,
                selected_center_x : []
            });
        }
        let copy_f = ()=>{
            let g :DP_Data[] = [];
            for(let i=0; i <=N; i++)
            {
                g.push({
                    expectation_error : f[i].expectation_error,
                    selected_center_x : [...f[i].selected_center_x]
                });
        
            }
            return g;
        }
        g = copy_f();
        for(let ti=0; ti < intervals.length; ti++)
        {
            let interval = intervals[ti];
            let target_center_x = interval.target_center_x || interval.center_x || -1;
            let center_diff_weight = interval.center_diff_weight || 0.01;
            let l_val = Math.floor(interval.result_length / step_len);

            for(let ii=0; ii <=N;ii++)
            {
                f[ii].expectation_error = total_length * 1000;
            }
            for(let ii=N; ii>=l_val; ii--)
            {
                let center_x = ii * step_len - interval.result_length/2;
                let center_diff_error = Math.abs(center_x- target_center_x) * center_diff_weight;

                let ri = ii - l_val;
                
                if( g[ri].expectation_error+center_diff_error <f[ii].expectation_error)
                {
                    f[ii].expectation_error = g[ri].expectation_error + center_diff_error;
                    f[ii].selected_center_x = [...g[ri].selected_center_x, center_x];
                }
            }

            for(let ii=l_val+1; ii<=N; ii++)
            {
                if(f[ii-1].expectation_error < f[ii].expectation_error)
                {
                    f[ii].expectation_error = f[ii-1].expectation_error;
                    f[ii].selected_center_x = [...f[ii-1].selected_center_x];
                }
            }
            g = copy_f();

        }

        let ref_f = f[N];

        if(ref_f.selected_center_x.length == intervals.length)
        {
            for(let i in intervals)
            {
                intervals[i].center_x = ref_f.selected_center_x[i];
            }
        }

        return true;

    }



    static testing_online_intervals_center_x()
    {
        let intervals : I_OnLineFigureInterval[] = [
            {
                target_length:1000,
                target_center_x : 70,
                result_length : 100,
                length_values:[
                    100,
                ],
                center_x : -1,
                length_differ_weight:1,
                center_diff_weight:1
            },
            {
                target_length:1000,
                target_center_x : 300,
                result_length : 200,
                length_values:[
                    100,
                ],
                center_x : -1,
                length_differ_weight:1,
                center_diff_weight:1
            }
        ]

        TLayoutOptmizerOnWallRules.optimize_online_intervals_center_x(intervals,400,10);
    }


}
