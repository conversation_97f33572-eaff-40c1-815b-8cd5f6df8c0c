import { makeAutoObservable } from 'mobx';

interface spaceData {
  // 参数还没定义
}

/**
 * @description 主页数据
 */
class DesignStore {
  singleSpaceData: spaceData = {};
  segmentedValue = '风格套系';
  showMenu = false;   //是否展示左侧列表
  quoteInfo: any = {};  //报价信息
  quoteExcel: any = '';  //报价信息Excel
  isShowOrderInfo = false;  //是否展示报价信息
  quoteParams: any = {};  //报价参数
  miniModal = '3';  //是否展示小窗口    '1'展示  '2'不展示   '3': 销毁
  selectedIndex = -1;  //右侧列表选中的商品
  constructor() {
    makeAutoObservable(this, {}, {autoBind: true});
  }
  // action
  setUserInfo(data: spaceData) {
    this.singleSpaceData = data;
  }
  
  setSegmentedValue(data: any) {
    this.segmentedValue = data;
  }
  setShow(data: any) {
    this.showMenu = data;
  }
  setQuoteInfo(data: any) {
    this.quoteInfo = data;
  }
  setQuoteExcel(data: any) {
    this.quoteExcel = data;
  }
  setIsShowOrderInfo(data: any) {
    this.isShowOrderInfo = data;
  }
  setQuoteParams(data: any) {
    this.quoteParams = data;
  }
  setMiniModal(data: any) {
    this.miniModal = data;
  }
  setSelectedIndex(data: any) {
    this.selectedIndex = data;
  }
}

export default DesignStore;