import { I_XmlCSwingdoorEntity, I_XmlEntityBase } from "../IWardrobeEntityInterface";
import { NumberVariable } from "../NumberVariable";
import { XmlCSwingdoorLeafEntity } from "./XmlCSwingdoorLeafEntity";
import { XmlEntityBase } from "../XmlEntityBase";

export class XmlCSwingdoorEntity extends XmlEntityBase implements I_XmlCSwingdoorEntity {
    isMovableB?: boolean;
    isFunctorSyncB?: boolean;
    placeRuleS?: string;
    isQuoteB?: boolean;
    isBoardPointEditUnlimitedB?: boolean;
    isQuoteSplitN?: number;
    isOutsourcingN?: number;
    ocTypeS?: string;
    standardCategoryS?: string;
    isSplitN?: number;
    isSetThI_XmlCkN?: number;
    canReplaceMaterialB?: boolean;
    canSetHandleSizeB?: boolean;
    doorTypeN?: number;
    isChangedFromSpaceBoardB?: boolean;
    Visible?: {
        MiddleVariable?: {
            nameS?: string;
            valueS?: string;
            valueExpressionS?: string;
        };
    };
    CWhMaterialComponent?: Record<string, unknown>;

    declare Children?: {
        CSwingdoorLeafEntity?: XmlCSwingdoorLeafEntity[];
    };
    EdgeComponent?: {
        edgeBandModeN?: number;
        edgeBandPlanInfoS?: string;
        edgeModifyByUserN?: number;
    };
    GenerationEntityVo?: {
        generatedBoardsLimitNamesS?: string;
        recordFunctorLimitNameArrS?: string;
        parentIdsS?: string;
    };

    constructor(data?: Partial<I_XmlCSwingdoorEntity>) {
        super(data);
        this.EdgeComponent = data.EdgeComponent || {};
        this.CWhMaterialComponent = data.CWhMaterialComponent || {};
        this.GenerationEntityVo = data.GenerationEntityVo || {};
        this.Visible = data.Visible || {};
        this.ocTypeS = data.ocTypeS || "";
        this.standardCategoryS = data.standardCategoryS || "";
        this.isFunctorSyncB = data.isFunctorSyncB ?? false;
        this.canReplaceMaterialB = data.canReplaceMaterialB ?? false;
        this.canSetHandleSizeB = data.canSetHandleSizeB ?? false;     
        this.isQuoteB = data.isQuoteB ?? false;
        this.isSplitN = data.isSplitN ?? 0;
        this.isSetThI_XmlCkN = data.isSetThI_XmlCkN ?? 0;
        this.doorTypeN = data.doorTypeN ?? 0;
        this.isChangedFromSpaceBoardB = data.isChangedFromSpaceBoardB ?? false;
    }
}
XmlEntityBase.Generators["CSwingdoorEntity"] = (data:I_XmlEntityBase)=>new XmlCSwingdoorEntity(data);