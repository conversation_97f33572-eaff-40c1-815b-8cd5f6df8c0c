
import type { IState } from '../../interfaces/IState';
import { Design2DStates } from '../../const/FSMConst';
import { appContext } from '../../../AppContext';
import { DesignControllerType } from '../../../ctrls/DesignControllerType';

/**
 * 2D绘制矩形状态
 */
export class DrawRectState implements IState {
  name: string;
  parent?: any;

  constructor(name: string = Design2DStates.DRAW_RECT) {
    this.name = name;
  }

  onEnter(data?: any): void {
    const design2DContext = appContext.design2DContext;
    if (design2DContext) {
        design2DContext.controllerManager.activateCtrl(DesignControllerType.DRAW_RECT_CTRL);
        design2DContext.controllerManager.deactivateCtrl(DesignControllerType.CANVAS_LMB_MOVE);
    }
    // 状态进入逻辑
  }

  onExit(data?: any): void {
    // 状态退出逻辑
    const design2DContext = appContext.design2DContext;
    if (design2DContext) {
        design2DContext.controllerManager.activateCtrl(DesignControllerType.CANVAS_LMB_MOVE);
        design2DContext.controllerManager.deactivateCtrl(DesignControllerType.DRAW_RECT_CTRL);
    }
  }

  /**
   * 切换到3D模式
   */
  switchTo3D(): void {

  }
} 