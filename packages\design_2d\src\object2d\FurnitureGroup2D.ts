import { IEntityFurniture, IEntityFurnitureGroup } from "@layoutai/design_domain";

import { TPainter } from "../draw/TPainter";
import { Object2DBase } from "./Object2DBase";
import { g_figure_alias_dict } from "../draw/FigureImagePaths";
import { Vector3 } from "three";


/**
* @description 组合家具 2D 对象
* <AUTHOR>
* @date 2025-06-27 17:20:59
* @lastEditTime 2025-06-27 17:20:59
* @lastEditors xuld
*/
export class FurnitureGroup2D extends Object2DBase {
    // 是否显示轮廓
    protected _spotLight: boolean = false;

    public get furniture(): IEntityFurnitureGroup {
        return this.entity as IEntityFurnitureGroup;
    }

    public update(): any | undefined {
        if (!this.furniture) {
            console.error("家具实体不存在，无法更新");
            return undefined;
        }
        return this.furniture;
    }

    public hitTest(point: Vector3): boolean {
        if(!this.furniture?.rect) {
            return false;
        }
        return this.furniture.rect.containsPoint(point);
    }
    
    public render(painter: TPainter): void {
        let white_style = painter._style_mapping['white'];
        let black_style = painter._style_mapping['black'];

        let children = Array.from(this.furniture.children);

        children.sort((a, b) => {
            let aEntity = a as IEntityFurniture;
            let bEntity = b as IEntityFurniture;
            let aOrder = Object2DBase.getDrawingOrder([aEntity.category, aEntity.subCategory]);
            let bOrder = Object2DBase.getDrawingOrder([bEntity.category, bEntity.subCategory]);
            return aOrder - bOrder;
        })

        for (let entity of this.furniture.children) {
            this.renderChild(painter, entity);
        }


        painter._style_mapping['white'] = white_style;
        painter._style_mapping['black'] = black_style;
    }

    private renderChild(painter: TPainter, furniture: IEntityFurniture) {
        let rect = furniture.rect;

        let line_color: string = "#777777";

        painter.strokeStyle = "#000";

        painter.fillStyle = this._drawParam.fillStyle;

        painter._context.lineWidth = this._drawParam.lineWidth;

        painter._style_mapping["black"] = line_color;

        let category = furniture.category;
        let subCategory = furniture.subCategory;
        let candidate_labels = [g_figure_alias_dict[category], subCategory, category];
        painter.drawFigureRect(rect, category || subCategory, candidate_labels);

        painter.fillStyle = "#000";

        if (this._spotLight) {
            painter.drawEdges(rect.edges, 0, "#f00");
        }
    }
} 