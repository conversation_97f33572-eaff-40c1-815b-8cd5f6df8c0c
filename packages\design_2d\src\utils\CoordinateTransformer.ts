import { Vector3 } from "three";
import { Design2DContext } from "../Design2DContext";

export class CoordinateTransformer {

    /**
     * 将屏幕坐标转换为画布坐标
     * @param screenPoint 屏幕坐标点
     * @param canvas 画布元素
     * @param transform 变换信息
     * @returns 画布坐标
     */
    public static screenToCanvas(screenPoint: Vector3,context: Design2DContext): Vector3 {
        const canvas = context.canvas2DManager.canvas;
        const transform = context.canvas2DManager.getTransform();
        if(!canvas) 
        {
            console.warn("canvas is not initialized");
            return screenPoint;
        }

        // 获取canvas相对于视口的位置
        const rect = canvas.getBoundingClientRect();
        
        
        // 计算相对于canvas的坐标
        const canvasRelativeX = screenPoint.x - rect.left;
        const canvasRelativeY = screenPoint.y - rect.top;
        // 应用画布变换：减去偏移量，然后除以缩放比例
        const canvasX = (canvasRelativeX - transform.offsetX) / transform.scale;
        const canvasY = (canvasRelativeY - transform.offsetY) / transform.scale;
        return new Vector3(canvasX,canvasY);
    }

    /**
     * 将画布坐标转换为世界坐标
     * @param canvasPoint 画布坐标点
     * @param context 设计2D上下文
     * @returns 世界坐标
     */
    public static canvasToGlobal(canvasPoint: Vector3,context: Design2DContext): Vector3 {
        if(!context.painter) 
        {
            console.warn("painter is not initialized");
            return canvasPoint;
        }
        const globalPoint = context.painter.project2D(canvasPoint);
        return new Vector3(globalPoint.x,globalPoint.y);
    }

    /**
     * 将世界坐标转换为画布坐标
     * @param globalPoint 世界坐标点
     * @param context 设计2D上下文
     * @returns 画布坐标
     */
    public static globalToCanvas(globalPoint: Vector3,context: Design2DContext): Vector3 {
        if(!context.painter) 
        {
            console.warn("painter is not initialized");
            return globalPoint;
        }

        const canvasPoint = context.painter.upproject2D(globalPoint);
        return new Vector3(canvasPoint.x,canvasPoint.y);
    }

} 