import '@svg/antd/es/style/reset.css';
import './assets/less/index.less';
import { APP_ID, ENV } from '@/config';
import SunvegaAPI from '@api/clouddesign';

const { Core, Platform } = SunvegaAPI;

Core.APIManager.init(APP_ID).then(() => {
  // 生命周期-监听关闭
  Platform.Application.bindOnAppClose(async () => {
    console.log(`plugin-demo appId=${APP_ID}, appEnv=${ENV}`, 'app.close')
  })
});

import('./bootstrap');
