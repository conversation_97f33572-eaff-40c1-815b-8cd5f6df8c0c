import { ControllerBase } from "../ControllerBase";
import { CanvasEventType } from "../../events/const/CanvasEventType";
import { ControllerType } from "../ControllerType";
import { Design2DContext } from "../../Design2DContext";

/**
 * 画布左键
 * 负责处理画布的移动操作
 * 鼠标左键拖拽移动
 * 继承自ControllerBase，通过事件发送到CanvasEventHandler去执行_transformCallback
 */
export class CanvasLMBMoveCtrl extends ControllerBase {
    // 移动状态
    private _isDragging = false;
    private _lastMouseX = 0;
    private _lastMouseY = 0;

    constructor(context: Design2DContext) {
        super(ControllerType.CANVAS_LMB_MOVE, context);
    }

    public activate(): void {
        super.activate();
        this._isDragging = false;
    }

    public deactivate(): void {
        super.deactivate();
        this._isDragging = false;
    }

    public onMouseDown(event: MouseEvent): void {
        if (event.button !== 0) {
            return;
        }
        this._isDragging = true;
        this._lastMouseX = event.clientX;
        this._lastMouseY = event.clientY;
    }

    public onMouseMove(event: MouseEvent): void {
        
        if (!this._isDragging) {
            return;
        }
        const deltaX = event.clientX - this._lastMouseX;
        const deltaY = event.clientY - this._lastMouseY;

        // 通过事件发送到CanvasEventHandler去执行_transformCallback
        this.context.eventCenter.emit(CanvasEventType.CANVAS_TRANSFORM, {
            offsetX: deltaX,
            offsetY: deltaY
        });

        this._lastMouseX = event.clientX;
        this._lastMouseY = event.clientY;
    }

    public onMouseUp(event: MouseEvent): void {
        this._isDragging = false;
    }

    public dispose(): void {
        this._isDragging = false;
        this._lastMouseX = 0;
        this._lastMouseY = 0;
        super.dispose();
    }
} 