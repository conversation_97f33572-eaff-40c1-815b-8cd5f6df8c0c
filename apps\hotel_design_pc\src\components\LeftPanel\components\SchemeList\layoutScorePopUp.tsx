import useCommonStyles from '../../../common_style';
import { useEffect, useState } from 'react';
import { LayoutAI_App,EventName,UI_FormatType,I_LayoutScore,TRoomLayoutScheme} from '@layoutai/layout_scheme';
import LayoutScoreCard from '@/components/LayoutScoreDialog/layoutScoreCard';

/**
 * 
 * 
 */
const LayoutScorePopUp: React.FC = ()=>{

    const common_styles = useCommonStyles().styles;
    const t = LayoutAI_App.t;
    const object_id = "layoutScorePopUp";
    const [topOffset,setTopOffset] = useState<number>(0);
    const [layoutScoreList,setLayoutScoreList] = useState<I_LayoutScore[]>([]);
    useEffect(()=>{
        LayoutAI_App.on(EventName.ShowPopUpLayoutScore,(props:{scheme:TRoomLayoutScheme, top:number})=>{
            setTopOffset(props.top);
            let scheme = props.scheme;
            if(!scheme?.layout_score_dict)
            {
                setLayoutScoreList([]);
            }else{
                let list = Object.keys(scheme.layout_score_dict).map((key)=>scheme.layout_score_dict[key]);
                let totalScore: number = 0;
                list.forEach(item => {
                    if (item.children) {
                        // 取消子组排序
                        // item.children.sort((a, b) => b.value - a.value);
                    }
                    totalScore += item.score;
                });
                if(LayoutAI_App.IsDebug)
                {
                    list.push({name: t("总分"), score: totalScore,  ui_format: [UI_FormatType.Name, UI_FormatType.Percentage, UI_FormatType.Grade]});
                }
                setLayoutScoreList(list);
            }
        })
    },[]);

    return (
        <div className={common_styles.schemeLayoutScorePopUp} style={{
            top:topOffset+"px",
            display:layoutScoreList.length>0?"block":"none",
            minWidth: "300px",
            minHeight: "400px"
        }}>
            <LayoutScoreCard layoutScoreList={layoutScoreList} style={LayoutAI_App.IsDebug?0:1}></LayoutScoreCard>
        </div>
    )
}

export default LayoutScorePopUp;