import { makeAutoObservable } from "mobx";
import { getCabinetListByVisitor, getCabinetImageList, createAiCabinetByAi } from "@/services/user";

interface Params {
  trace_id: string;
  images_base64: string;
  width?: number;
  height?: number;
  depth?: number;
  organ_id: string | undefined;
}

/**
 * @description 手机端
 */
class MobileStore {
  showFilter: boolean = false;
  showUploadImg: boolean = false;
  showParameterCabinet: boolean = false;
  showTopBar: boolean = true;
  submitParams: any = {};
  info: any = {};
  skeletonStatus: boolean = false;
  cabinetData: any = [];
  file: any = "";
  selectedCabinet: any = {};
  canvasImg: any = null;
  selectedCabinetType: string = "柜体类型";
  selectedStyle: string = "风格";
  wxOpenid: string = "";
  aggrTree: any = [];
  designerOpenId: string = "";
  designerId: string = "";
  fromParam: string = "";
  cabinetId: string = "";
  visitorOpenId: string = "";
  aiWrite: string = ""; // 柜体分析生成结果

  constructor() {
    makeAutoObservable(this, {}, { autoBind: true });
  }

  setShowFilter = (data: boolean) => {
    this.showFilter = data;
  };

  setAiWrite = (data: string) => {
    this.aiWrite = data;
  };

  setShowUploadImg = (data: boolean) => {
    this.showUploadImg = data;
  };

  setShowParameterCabinet = (data: boolean) => {
    this.showParameterCabinet = data;
  };

  setShowTopBar = (data: boolean) => {
    this.showTopBar = data;
  };

  setSubmitParams(data: any) {
    this.submitParams = data;
  }

  setInfo(data: any) {
    this.info = data;
  }

  setSkeletonStatus(data: any) {
    this.skeletonStatus = data;
  }

  setCabinetData(data: any) {
    this.cabinetData = data;
  }

  setFile(data: any) {
    this.file = data;
  }

  setSelectedCabinet(data: any) {
    this.selectedCabinet = data;
  }

  setCanvasImg(data: any) {
    this.canvasImg = data;
  }

  setSelectedCabinetType(data: string) {
    this.selectedCabinetType = data;
  }

  setSelectedStyle(data: string) {
    this.selectedStyle = data;
  }

  setWxOpenid(data: string) {
    this.wxOpenid = data;
  }

  setAggrTree(data: any) {
    this.aggrTree = data;
  }

  setDesignerOpenId(data: string) {
    this.designerOpenId = data;
  }

  setDesignerId(data: string) {
    this.designerId = data;
  }

  setFromPath(data: string) {
    this.fromParam = data;
  }

  setCabinetId(data: string) {
    this.cabinetId = data;
  }
  
  setVisitorOpenId(data: string) {
    this.visitorOpenId = data;
  }

  generateTraceId = () => {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  };
  handleSubmit = async () => {
    const {
      width,
      height,
      depth,
      searchType,
      userInfo,
      filterList,
      cabinet,
      isSearchExample,
      tagIdList,
    } = this.submitParams;
    this.setSkeletonStatus(true);
    if (searchType === "parameter") {
      const accurate_dict = filterList.reduce((acc: any, item: any) => {
        acc[item.key] = item.value;
        return acc;
      }, {});

      let label_dict = {
        long_clothes_num:
          cabinet === "衣柜" ? accurate_dict["long_clothes_num"] : undefined,
        short_clothes_num:
          cabinet === "衣柜" ? accurate_dict["short_clothes_num"] : undefined,
        pants_num: cabinet === "衣柜" ? accurate_dict["pants_num"] : undefined,
        insert_drawer_num:
          cabinet === "衣柜" && accurate_dict["insert_drawer_num"]
            ? accurate_dict["insert_drawer_num"] * 2
            : undefined,
        out_drawer_num:
          cabinet === "衣柜" && accurate_dict["out_drawer_num"]
            ? accurate_dict["out_drawer_num"] * 2
            : undefined,
        stack_area_ratio:
          cabinet === "衣柜" && accurate_dict["stack_area_ratio"]
            ? accurate_dict["stack_area_ratio"] * 0.2
            : undefined,
        desk: cabinet === "衣柜" && accurate_dict["desk"] ? 1 : 0,
        // side: cabinet === '衣柜' && this.openside ? 1 : 0,

        flattie_num: accurate_dict["flattie_num"],
        sneaker_num: accurate_dict["sneaker_num"],
        high_heeled_shoes_num: accurate_dict["high_heeled_shoes_num"],
        mid_calf_shoes_num: accurate_dict["mid_calf_shoes_num"],
        long_calf_shoes_num: accurate_dict["long_calf_shoes_num"],
        luggage_num: accurate_dict["luggage_num"],
        drawer_num: accurate_dict["drawer_num"],
        hang_clothes_num: accurate_dict["hang_clothes_num"],
        counter_top_num: accurate_dict["counter_top_num"],
        // lazy_kick: cabinet === '玄关柜' && this.selectedTags.includes('懒人踢') ? 1 : 0,
        // shoe_bench: cabinet === '玄关柜' && this.selectedTags.includes('换鞋凳') ? 1 : 0,
        // dressing_mirror: cabinet === '玄关柜' && this.selectedTags.includes('妆容镜') ? 1 : 0,

        // open_cell_num: cabinet === '餐边柜' && this.selectedTags.includes('开放格') ? 1 : 0,
        // glass_door: cabinet === '餐边柜' && this.selectedTags.includes('玻璃门') ? 1 : 0,
        refrigerator_num: accurate_dict["refrigerator_num"],
        wine_rack_num: accurate_dict["wine_rack_num"],
        low_storage_num: accurate_dict["low_storage_num"],
        mid_storage_num: accurate_dict["mid_storage_num"],
        height_storage_num: accurate_dict["height_storage_num"],
      };

      const filtered_dict = Object.entries(label_dict).reduce(
        (newDict, [key, value]) => {
          if (value !== 0) {
            newDict[key] = value;
          }
          return newDict;
        },
        {} as { [key: string]: any }
      );

      let nametags = [];
      if (cabinet === "衣柜") {
        // if(this.selectedTags.includes('推拉门'))
        // {
        //   nametags.push('sliding_door_wardrobe');
        // }
        // if(this.selectedTags.includes('平开门'))
        // {
        //   nametags.push('swing_door_wardrobe');
        // }
        nametags.push("sliding_door_wardrobe");
        nametags.push("swing_door_wardrobe");
      }
      if (cabinet === "玄关柜") {
        nametags.push("shoe_cabinet");
      }
      if (cabinet === "餐边柜") {
        nametags.push("dining_cabinet");
      }
      // let organ_id = [];
      // if(this.organTags.includes('平台库'))
      // {
      //   organ_id.push('C00000022');
      // }
      // if(this.organTags.includes('企业库'))
      // {
      //   organ_id.push(userInfo?.tenantId);
      // }
      const res = await getCabinetListByVisitor({
        trace_id: this.generateTraceId(),
        category: nametags,
        width: Number(width),
        height: Number(height),
        depth: Number(depth),
        // organ_id_list: organ_id,
        material_id_list: "",
        label_dict: {
          ...filtered_dict,
        },
      });
      if (res && res.length > 0) {
        res.map((item: any) => {
          item.checked = false;
          item.main_img_path = item.main_img_path.replace(
            "https://test-3vj-xelawk.oss-cn-shenzhen.aliyuncs.com",
            "https://test-3vj-xelawk.oss-cn-shenzhen-internal.aliyuncs.com"
          );
        });
        this.setCabinetData(res);
        console.log(res);
      } else {
        this.setCabinetData([]);
      }
    } else if (searchType === "aiCreate") {
      // AI生成搭柜
      const accurate_dict = filterList.reduce((acc: any, item: any) => {
        acc[item.key] = item.value;
        return acc;
      }, {});

      let label_dict = {
        long_clothes_num:
          cabinet === "衣柜" ? accurate_dict["long_clothes_num"] : undefined,
        short_clothes_num:
          cabinet === "衣柜" ? accurate_dict["short_clothes_num"] : undefined,
        pants_num: cabinet === "衣柜" ? accurate_dict["pants_num"] : undefined,
        insert_drawer_num:
          cabinet === "衣柜" && accurate_dict["insert_drawer_num"]
            ? accurate_dict["insert_drawer_num"] * 2
            : undefined,
        out_drawer_num:
          cabinet === "衣柜" && accurate_dict["out_drawer_num"]
            ? accurate_dict["out_drawer_num"] * 2
            : undefined,
        desk: cabinet === "衣柜" && accurate_dict["desk"] ? 1 : 0,
        cell_drawer_num: cabinet === "衣柜" ? accurate_dict["cell_drawer_num"] : undefined,
        // side: cabinet === '衣柜' && this.openside ? 1 : 0,
        flattie_num: accurate_dict["flattie_num"],
        sneaker_num: accurate_dict["sneaker_num"],
        high_heeled_shoes_num: accurate_dict["high_heeled_shoes_num"],
        mid_calf_shoes_num: accurate_dict["mid_calf_shoes_num"],
        long_calf_shoes_num: accurate_dict["long_calf_shoes_num"],
        luggage_num: accurate_dict["luggage_num"],
        drawer_num: accurate_dict["drawer_num"],
        hang_clothes_num: accurate_dict["hang_clothes_num"],
        counter_top_num: accurate_dict["counter_top_num"],
        // lazy_kick: cabinet === '玄关柜' && this.selectedTags.includes('懒人踢') ? 1 : 0,
        // shoe_bench: cabinet === '玄关柜' && this.selectedTags.includes('换鞋凳') ? 1 : 0,
        // dressing_mirror: cabinet === '玄关柜' && this.selectedTags.includes('妆容镜') ? 1 : 0,

        // open_cell_num: cabinet === '餐边柜' && this.selectedTags.includes('开放格') ? 1 : 0,
        // glass_door: cabinet === '餐边柜' && this.selectedTags.includes('玻璃门') ? 1 : 0,
        refrigerator_num: accurate_dict["refrigerator_num"],
        wine_rack_num: accurate_dict["wine_rack_num"],
        low_storage_num: accurate_dict["low_storage_num"],
        mid_storage_num: accurate_dict["mid_storage_num"],
        height_storage_num: accurate_dict["height_storage_num"],
      };
      const filtered_dict = Object.entries(label_dict).reduce(
        (newDict, [key, value]) => {
          if (value !== 0) {
            newDict[key] = value;
          }
          return newDict;
        },
        {} as { [key: string]: any }
      );
      let nametags = "";
      if (cabinet === "衣柜") {
        nametags = "wardrobe"
      } else if (cabinet === "玄关柜") {
        nametags = "shoe_cabinet";
      }else if (cabinet === "餐边柜") {
        nametags = "dining_cabinet";
      }
      const res =await createAiCabinetByAi({
        trace_id: this.generateTraceId(),
        output_num: 3, //输出数量
        cabinet_type: nametags,
        width: Number(width),
        height: Number(height),
        depth: Number(depth),
        label_dict: {
          ...filtered_dict,
        },
      })
      if (res) {
        // postmessage将数据传给后端 ？
        window.parent.postMessage({
          businessType: 'ParamAICabinet',
          handleType: 'Message',
          data: JSON.stringify(res?.data)
        }, '*');
        // 监听
        window.addEventListener('message', (event) => {
          console.log('收到消息', event.data);
        })
        console.log('ai生成', res);
      }
    } else if (searchType === "filter") {
      let nametags = [];
      if (this.selectedCabinetType === "衣柜") {
        // if(this.selectedTags.includes('推拉门'))
        // {
        //   nametags.push('sliding_door_wardrobe');
        // }
        // if(this.selectedTags.includes('平开门'))
        // {
        //   nametags.push('swing_door_wardrobe');
        // }
        nametags.push("sliding_door_wardrobe");
        nametags.push("swing_door_wardrobe");
      }
      if (this.selectedCabinetType === "玄关柜") {
        nametags.push("shoe_cabinet");
      }
      if (this.selectedCabinetType === "餐边柜") {
        nametags.push("dining_cabinet");
      }
      const res = await getCabinetListByVisitor({
        trace_id: this.generateTraceId(),
        category: nametags,
        // width: Number(width),
        // height: Number(height),
        // depth: Number(depth),
        // organ_id_list: organ_id,
        material_id_list: "",
        tag_id_list: tagIdList,
        label_dict: {
          // ...filtered_dict,
        },
      });
      if (res && res.length > 0) {
        res.map((item: any) => {
          item.checked = false;
          item.main_img_path = item.main_img_path.replace(
            "https://test-3vj-xelawk.oss-cn-shenzhen.aliyuncs.com",
            "https://test-3vj-xelawk.oss-cn-shenzhen-internal.aliyuncs.com"
          );
        });
        this.setCabinetData(res);
        console.log(res);
      } else {
        this.setCabinetData([]);
      }
    } else if (searchType === "img") {
      const params: Params = {
        trace_id: this.generateTraceId(),
        images_base64: this.file,
        width: width,
        height: height,
        depth: depth,
        organ_id: userInfo?.tenantId,
      };
      if (isSearchExample) {
        delete params["width"];
        delete params["height"];
        delete params["depth"];
      }
      const res = await getCabinetImageList(params);

      if (res && res.length > 0) {
        res.map((item: any) => {
          item.checked = false;
          item.main_img_path = item.main_img_path.replace(
            "https://test-3vj-xelawk.oss-cn-shenzhen.aliyuncs.com",
            "https://test-3vj-xelawk.oss-cn-shenzhen-internal.aliyuncs.com"
          );
        });
        this.setCabinetData(res);
      } else {
        this.setCabinetData([]);
      }
    }
    this.setSkeletonStatus(false);
  };
}

export default MobileStore;
