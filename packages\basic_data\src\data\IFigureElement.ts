import { I_ZRectData, <PERSON><PERSON><PERSON><PERSON>, ZRectShapeType } from "@layoutai/z_polygon";

export enum FigureDeformType {
    None = 0,
    ParamList = 1, // 备选参数列表, 尺寸链
    Scale = 2 // 变形
}

export interface I_MaterialMatchingItem {
    color: string;
    shape: string | null;
    type: string;
    length: number;
    width: number;
    height: number;
    index: number;
    modelFlag: string;
    modelId: string;
    name: string,
    modelLoc: string;
    nodeId: number;
    organizationId: string;
    ruleId: string;
    seriesId: string;
    seriesKgId: number;
    seedSchemeId: string;
    roomUid: string;
    publicCategoryId: number;
    publicCategoryName: string;
    scalable: boolean;
    offLandRule: string;
    topOffset: number;
    targetPosition: { x: number, y: number, z: number };
    targetSize: { width: number, height: number, length: number };
    targetRotation: { x: number, y: number, z: number };
    originalLength?: number;
    originalWidth?: number;
    originalHeight?: number;
    imageUrl: string;
    topViewImage: string;
    closeDirection: string;
    candidate: I_MaterialMatchingItem[];
    isAiCabinet?: boolean;
    // 被删掉的材质id
    deleted_model_id?: string;
    appendixMaterialId: string;
    applySpace?: string[];
    materialId: string;

    similarGlbUrl ?: string;
    figureElement ?:I_FigureElement;
    [key:string]:any;

}

export type DecorationType = "Lighting" | "Electricity" | "OnTable" | "OnWall" | "InCabinet" | "ViewCamera" | "";
export type FigureShapeType = "矩形" | "L形" | "圆形" | "一字形" | "弧形" | "U形" | "钻石形" | ZRectShapeType;
export type CloseDirectionType = "左收口" | "右收口" | "左右收口" | "左右见光";
export const TargetDefaultModelId = "target_default_model_id";
export interface I_KgMaterialItem{
    groupType:number;
    height:number;
    id:number;
    imagePath:string;
    is3d:number;
    isDirect:number;
    isValid:number;
    kgId:string;
    kgMaterialType:string;
    length:number;
    lengthMax:number;
    lengthMin:number;
    materialId:string;
    materialName:string;
    modelFlag:string;
    modelLoc:string;
    status:string;
    topViewImage:string;
    typeOnRelation:string;
    width:number;
    wireframeImage:string;
}
export interface I_FigureParams {
    length?: number; // 矩形长
    depth?: number; // 矩形深
    height?: number; // 模型高度
    l_ex_width?: number; // L形 挤出部分长
    l_ex_depth?: number;  // L形 挤出部分深
    shape?: ZRectShapeType; // 基本形状
    _rect_shape ?: ZRectShapeType;
    close_direction?: CloseDirectionType;
    off_land_rule?: string;
    topOffSet?: number;

    zval?: number;


    serialId?: string;  // 当前系列ID
    materialId?: string;  // 当前材质Id
    entityId?: number; //3D实体ID

    //  以下两个数据结构可能还会改
    serial_material_ids?: { [key: string]: string[] }; // serialId -> marerialId[] 可能会返回多个材质id
    materialIds?: string[]; // 备选的材质Id，以后可能有用

    [key: string]: any;
}
export interface I_DeformMethod {
    method_type?: FigureDeformType;

    params_list?: I_FigureParams[];

    width_range?: number[];
    height_range?: number[];

}
export interface I_FigureElement {
    uuid?:string;
    shape?: FigureShapeType;
    _rect_shape ?: ZRectShapeType;
    params?: I_FigureParams;
    category?: string;
    modelLoc?: string;
    public_category?: string; // 公共分类, 对齐平台
    material_name?: string;
    sub_category?: string;
    // 标签
    tags?: string[];
    priority?: number;
    image_path?: string;
    fill_color?: string;
    model_flag?: string;
    deform_method?: I_DeformMethod;
    mirror?: number;
    min_z?: number;
    max_z?: number;
    z_level?: number;
    _rect_data?: I_ZRectData;
    _matched_rect_data?: I_ZRectData;
    _matched_material?: I_MaterialMatchingItem;
    rect?: ZRect;
    _group_cate?: string;
    _group_uuid?: string;
    _group_main_figure?: boolean;
    /**
     *  是否是饰品图元
     */
    _is_decoration?: boolean;
    /**
     *  饰品类型
     */
    _decoration_type?: DecorationType;
    _is_sub_board?: boolean;
    _ex_prop?: { [key: string]: number | string };

    _candidate_materials?:I_MaterialMatchingItem[];

    decorations ?: I_FigureElement[];
    wireFrameImageUrl ?: string;

}

export function createSimpleFigureData(modelLoc:string,params?:I_FigureParams,options:{needs_make_rect?:boolean,shape?:FigureShapeType}={}){
    let data: I_FigureElement = {
        shape : options.shape || "矩形",
        modelLoc : modelLoc,
        category: modelLoc,
        public_category: modelLoc,
        sub_category: modelLoc,
        params: {
            length: 100,
            depth: 100,
            height: 10,
            ...(params||{})
        }
    };
    
    if(options.needs_make_rect)
    {
        data.rect = new ZRect(data.params.length,data.params.depth);
        data.rect.updateRect();

    }
    return data;
}


const customModelFlags = new Set(["10", "11", "12", "13", "22", "23"]);
const tileModelFlags = new Set(["14", "26", "22"]);
const textureModelFlags = new Set(["2", "1"]);

export function isCustomCabinet(material: I_MaterialMatchingItem): boolean {
    if (material == null) {
        return false;
    }
    let modelFlag: string = material["modelFlag"];
    if (customModelFlags.has(modelFlag)) {
        return true;
    } else {
        return false;
    }
}

export function isTexture(material: I_MaterialMatchingItem): boolean {
    if (material == null) {
        return false;
    }
    let modelFlag: string = material["modelFlag"];
    if (textureModelFlags.has(modelFlag)) {
        return true;
    } else {
        return false;
    }
}

export function isTile(material: I_MaterialMatchingItem): boolean {
    if (material == null) {
        return false;
    }
    let modelFlag: string = material["modelFlag"];
    if (tileModelFlags.has(modelFlag)) {
        return true;
    } else {
        return false;
    }
}

export function toStringForMaterialMatchingItem(item: I_MaterialMatchingItem): string {
    if (!item) return "null";

    let targetPostLog = "";
    let targetSize = "";
    if (item.targetPosition) {
        if (item.targetPosition.x != (item.figureElement ? item.figureElement.rect.rect_center.x : 0)
            || item.targetPosition.y != (item.figureElement ? item.figureElement.rect.rect_center.y : 0)
            || item.targetPosition.z != (item.figureElement ? item.figureElement.rect.rect_center.z : 0)) {
            let deltaX = item.targetPosition.x - (item.figureElement ? item.figureElement.rect.rect_center.x : 0);
            let deltaY = item.targetPosition.y - (item.figureElement ? item.figureElement.rect.rect_center.y : 0);
            let deltaZ = item.targetPosition.z - (item.figureElement ? item.figureElement.rect.rect_center.z : 0);
            let deltaXLog = deltaX != 0 ? ("[" + Number(deltaX.toFixed(2)) + "]") : "";
            let deltaYLog = deltaY != 0 ? ("[" + Number(deltaY.toFixed(2)) + "]") : "";
            let deltaZLog = deltaZ != 0 ? ("[" + Number(deltaZ.toFixed(2)) + "]") : "";
            targetPostLog = ",Pos=(" + Number(item.targetPosition.x.toFixed(2)) + deltaXLog
                + "," + Number(item.targetPosition.y.toFixed(2)) + deltaYLog
                + "," + Number(item.targetPosition.z.toFixed(2)) + deltaZLog + ")";
        }
    }
    if (item.targetSize) {
        if (item.targetSize.length != item.length ||
            item.targetSize.width != item.width ||
            item.targetSize.height != item.height) {
            let deltaL = item.targetSize.height - (item.figureElement ? item.figureElement.rect.w : 0);
            let deltaW = item.targetSize.width - (item.figureElement ? item.figureElement.rect.h : 0);
            let deltaH = item.targetSize.height - (item.figureElement ? item.figureElement.params.height : 0);
            let deltaLLog = deltaL != 0 ? ("[" + Number(deltaL.toFixed(2)) + "]") : "";
            let deltaWLog = deltaW != 0 ? ("[" + Number(deltaW.toFixed(2)) + "]") : "";
            let deltaHLog = deltaH != 0 ? ("[" + Number(deltaH.toFixed(2)) + "]") : "";
            targetSize = ",Size=(" + Number(item.targetSize.length.toFixed(2)) + deltaLLog
                + "," + Number(item.targetSize.width.toFixed(2)) + deltaWLog
                + "," + Number(item.targetSize.height.toFixed(2)) + deltaHLog + ")";
        }
    }

    return "MaterialMatchingItem  " + item.modelLoc
        + ":" + item.modelId
        + (item.name ? ("," + item.name) : "")
        + ",OriginSize:(" + Number(item.length.toFixed(2))
        + "," + Number(item.width.toFixed(2))
        + "," + Number(item.height.toFixed(2)) + ")"
        + (item.topOffset ? (",topOffset=" + item.topOffset) : "")
        + (item.roomUid ? (",roomUid=" + item.roomUid) : "")
        + (item.ruleId ? (",ruleId=" + item.ruleId) : "")
        + (item.seriesKgId ? (",seriesKgId=" + item.seriesKgId) : "")
        + (item.seedSchemeId ? (",seedSchemeId=" + item.seedSchemeId) : "")
        + (item.type ? (",type=" + item.type) : "")
        + (item.color ? (",color=" + item.color) : "")
        + (item.shape ? (",shape=" + item.shape) : "")
        + targetPostLog
        + targetSize
        + (item.publicCategoryName ? (",publicCategory=" + item.publicCategoryName) : "")
        + (item.closeDirection ? (",closeDirection=" + item.closeDirection) : "")
        + (item.offLandRule ? (",offLandRule=" + item.offLandRule) : "")
        + (item.modelFlag ? (",modelFlag=" + item.modelFlag + (isCustomCabinet(item) ? "(定制柜)" : "")) : "")
        + (item.organizationId ? ",organizationId=" + item.organizationId : "");
};

export interface I_DesignMaterialInfo {
    /**
     *  材质ID
     */
    MaterialId: string;
    /**
     *  材质名称
     */
    MaterialName: string;
    MaterialType: string;

    tenantId: string;
    ModelFlag: number;
    ProductCode: string;
    ProductId: string;

    DepthDefault: string;
    HeightDefault: string;
    WidthDefault: string;

    PICHeight: number;
    PICLength: number;
    PICSize: string;
    PICWidth: number;
    /**
     *  3d文件地址,其实是svg文件
     */
    A3dSource: string;
    /**
     *  柜体文件描述xml地址（一般是base64 txt)
     */
    ContentUrl: string;
    XmlContentUrl?:string;
    content: string;
    isGlb: number;
    // 纹理材质的图片路径
    ImagePath: string;
}

// 判断是否模型组合材质，类型文档 https://wiki.3weijia.com/pages/viewpage.action?pageId=50718830
export function isGroupMaterialMatchingItem(material: I_MaterialMatchingItem): boolean {
    return material.modelFlag === "15";
}

// 判断是否模型组合材质，类型文档 https://wiki.3weijia.com/pages/viewpage.action?pageId=50718830
export function isGroupDesignMaterialInfo(material: I_DesignMaterialInfo): boolean {
    return material.ModelFlag === 15;
}
