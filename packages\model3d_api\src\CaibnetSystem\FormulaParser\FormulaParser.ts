type Context = Record<string, any>;

interface ParamInfo {
    type: 'number' | 'object' | 'boolean' | 'string';
    properties?: Record<string, ParamInfo>;
}

interface FormulaBranch {
    valueExpr: string | number;
    condition?: string;
    isExpression: boolean;
    
    value?: string|number;
}
interface FormulaProgress extends FormulaBranch
{
    processedFormula ?: string;
    originFormula ?: string;
    formulaName ?: string;
}
export class FormulaParser {
    private branches: FormulaBranch[] = [];
    private detectedParams: Record<string, ParamInfo> = {};
    private subFormulas: Record<string, FormulaParser> = {};

    private static FormulaPrefix = "Formula";
    protected _formulaRecords : Record<string, FormulaParser> = null;

    protected _evalulateProgress:FormulaProgress[] = null;
    protected _formulaName : string = "";
    public recordProgress = false;

    private readonly MAX_RECURSION_DEPTH = 20;
    private recursionDepth = 0;

    private _originFormula: string;

    protected processedFormula : string;
    private _root :FormulaParser = null;
    constructor(formula: string, root:FormulaParser = null) {
        this._originFormula = formula;

        this._root = root || this;
        if(this._root === this)
        {
            this._formulaRecords = {};
            this._evalulateProgress = [];
        }
        // 第一阶段：提取并替换花括号子公式
        const { processedFormula, subFormulas } = this.extractSubFormulas(formula);
        this.subFormulas = subFormulas;
        
        this.processedFormula = processedFormula;
        // 第二阶段：解析主公式分支
        this.parseBranches(processedFormula);
        
        // 第三阶段：分析参数需求
        this.analyzeFormula();
    }

    // ================ 公共API ================
    public getRequiredParams(): Record<string, ParamInfo> {
        // 合并主公式和子公式的参数
        const allParams = { ...this.detectedParams };
        Object.values(this.subFormulas).forEach(parser => {
            Object.assign(allParams, parser.getRequiredParams());
        });
        return allParams;
    }

    public evaluate(context: Context): number | string {

        if(this == this._root)
        {
            this.evalulateProgress.length = 0;
        }
        // 先计算子公式
        if(this.subFormulas)
        {
            for(let key in this.subFormulas)
            {
                if(!context[key])
                {
                    let res = this.subFormulas[key].evaluate(context);
                    context[key]= res;
                }
            }
        }
        for (const branch of this.branches) {
            try {
                // 如果没有条件，或者条件满足
                if (!branch.condition || this.evaluateCondition(branch.condition, context)) {
                    const result = this.evaluateValue(branch.valueExpr, context);
               
                    if(this?._root?.recordProgress || this.recordProgress)
                    {
                        this.evalulateProgress.push({
                            formulaName : this._formulaName,
                            processedFormula : this.processedFormula,
                            value : result,
                            ...branch,
                            originFormula : this.originFormula,
                        })
                    }

                    if (typeof result === 'string' && /^[A-Z]+(-[A-Z]+)+$/.test(result)) {
                        return result;
                    }
                    return Number(result);
                }
            } catch (e) {
                console.error(`Error evaluating branch: ${e}`);
                continue;
            }
        }
        throw new Error("No matching branch found");
    }

    // ================ 第一阶段：子公式提取和替换 ================
    private extractSubFormulas(formula: string): {
        processedFormula: string;
        subFormulas: Record<string, FormulaParser>;
    } 
    {
        let counter = 1;
        const subFormulas: Record<string, FormulaParser> = {};
        let processedFormula = formula;
        const stack: { start: number; end: number, level : number}[] = [];
        let bracePairs: { start: number; end: number,level :number }[] = [];

        // 找出所有花括号对（支持嵌套）
        for (let i = 0; i < processedFormula.length; i++) {
            if (processedFormula[i] === '{') {
                stack.push({ start: i, end: -1,level:stack.length});
            } else if (processedFormula[i] === '}' && stack.length > 0) {
                const last = stack.pop()!;
                last.end = i;
                bracePairs.push(last); // 只找最外围的嵌套
            }
        }
        if(bracePairs.length == 0)
        {
            return { processedFormula, subFormulas };
        }
        bracePairs.sort((a,b)=>a.level - b.level);
        bracePairs = bracePairs.filter((a)=>a.level == bracePairs[0].level);
        // 从内到外处理花括号（倒序）
        bracePairs.sort((a, b) => b.start - a.start);

        for (const pair of bracePairs) {
            const subFormula = processedFormula.slice(pair.start + 1, pair.end);

            const {key, parser} = this.addSubFormula(subFormula);            
            // 创建子公式解析器
            subFormulas[key] =  parser;
            
            // 替换原公式中的花括号内容
            processedFormula = 
                processedFormula.slice(0, pair.start) + 
                key + 
                processedFormula.slice(pair.end + 1);
        }

        if(bracePairs[0].level > 0)
        {
            processedFormula = processedFormula.replaceAll("{","");
        }

        return { processedFormula, subFormulas };
    }
    protected get originFormula(): string {
        return this._originFormula;
    }
    protected set originFormula(value: string) {
        this._originFormula = value;
    }
    private addSubFormula(subFormula:string)
    {
        let formulaRecords = this._root._formulaRecords;
        for(let key in formulaRecords)
        {
            let parser = formulaRecords[key];
            if(parser.originFormula == subFormula)
            {
                return {key,  parser};
            }
        }

        let parser =  new FormulaParser(subFormula,this._root);
        // 注意顺序,  因为new FormulaPaser 会有递归调用,formulaRecords会发生改变, counter要写在下面
        let counter = Object.keys(formulaRecords).length;
        let key =  `${FormulaParser.FormulaPrefix}${++counter}`;
        formulaRecords[key] = parser;
        parser._formulaName = key;
        return {key,parser};
    }
    public get formulaRecords()
    {
        return this._root ? this._root._formulaRecords : this._formulaRecords;
    }

    public get evalulateProgress()
    {
        return this._root ? this._root._evalulateProgress : this._evalulateProgress;
    }

    // ================ 第二阶段：主公式分支拆解 ================
    private parseBranches(formula: string): void {
        const normalized = formula.replace(/\s+/g, ' ').trim();
        const parts = normalized.split(';').filter(part => part.trim().length > 0);

        this.branches = parts.map(part => {
            const trimmedPart = part.trim();
            const conditionMatch = trimmedPart.match(/^([^\[]+)\[(.+)\]$/);

            if (conditionMatch) {
                const value = conditionMatch[1].trim();
                return {
                    valueExpr: FormulaParser.isNumeric(value) ? parseFloat(value) : value,
                    condition: conditionMatch[2].trim(),
                    isExpression: !FormulaParser.isNumeric(value)
                };
            }

            const value = trimmedPart;
            return {
                valueExpr: FormulaParser.isNumeric(value) ? parseFloat(value) : value,
                isExpression: !FormulaParser.isNumeric(value)
            };
        });
    }

    // ================ 第三阶段：公式计算 ================
    private evaluateValue(value: string | number, context: Context): number | string {
        if (typeof value === 'number') return value;        
        // 处理普通表达式
        return this.evaluateExpression(value, context);
    }

    private evaluateExpression(expr: string, context: Context): number | string {
        // 处理连字符表达式（如D-QLTQS）
        if (/^[A-Z]+(-[A-Z]+)+$/.test(expr)) {
            return expr;
        }
        
        // 替换变量
        let evaluated = this.replaceVariables(expr, context);
        
        // 安全评估数学表达式
        return this.safeEvaluate(evaluated);
    }

    private evaluateCondition(condition: string, context: Context): boolean {
        return condition.split('&&').every(andPart => {
            return andPart.split('||').some(orPart => {
                const comparisonMatch = orPart.trim().match(/^(.+?)(==|!=|>|<|>=|<=)(.+)$/);
                if (!comparisonMatch) throw new Error(`Invalid condition: ${orPart}`);

                const left = this.evaluateSide(comparisonMatch[1].trim(), context);
                const right = this.evaluateSide(comparisonMatch[3].trim(), context);
                const operator = comparisonMatch[2];

                switch (operator) {
                    case '==': return left === right;
                    case '!=': return left !== right;
                    case '>': return left > right;
                    case '<': return left < right;
                    case '>=': return left >= right;
                    case '<=': return left <= right;
                    default: throw new Error(`Unsupported operator: ${operator}`);
                }
            });
        });
    }

    // ================ 第四阶段：参数分析 ================
    private analyzeFormula(): void {
        this.branches.forEach(branch => {
            // 分析条件中的参数
            if (branch.condition) {
                this.analyzeCondition(branch.condition);
            }
            
            // 分析值表达式中的参数（跳过子公式标记）
            if (branch.isExpression && typeof branch.valueExpr === 'string') {
                this.analyzePlainExpression(branch.valueExpr);
            }
        });
    }

    private analyzeCondition(condition: string): void {
        condition.split(/(&&|\|\|)/).forEach(part => {
            if (part === '&&' || part === '||') return;
            
            const comparisonMatch = part.match(/^(.+?)(==|!=|>|<|>=|<=)(.+)$/);
            if (comparisonMatch) {
                this.analyzeSide(comparisonMatch[1].trim());
                this.analyzeSide(comparisonMatch[3].trim());
            }
        });
    }

    private analyzePlainExpression(expr: string): void {
        const varRegex = /([A-Za-z_]\w*(?:\.[A-Za-z_]\w*)*)/g;
        let varMatch;
        
        while ((varMatch = varRegex.exec(expr))) {
            const varName = varMatch[1];
            if (varName.includes('.')) {
                const [obj, prop] = varName.split('.');
                this.addDetectedParam(obj, 'object', prop);
            } else if (!FormulaParser.isNumeric(varName)) {
                this.addDetectedParam(varName, 'number');
            }
        }
    }

    private analyzeSide(side: string): void {
        if (FormulaParser.isNumeric(side)) return;
        
        if (side.includes('.')) {
            const [obj, prop] = side.split('.');
            this.addDetectedParam(obj, 'object', prop);
        } else {
            this.addDetectedParam(side, 'number');
        }
    }

    // ================ 辅助方法 ================
    private addDetectedParam(name: string, type: 'number' | 'object' | 'boolean' | 'string', prop?: string): void {
        if (FormulaParser.isNumeric(name) && type !== 'object') return;
        if (name.startsWith(FormulaParser.FormulaPrefix)) return;
        if (type === 'object' && prop) {
            if (!this.detectedParams[name]) {
                this.detectedParams[name] = { type: 'object', properties: {} };
            }
            if (this.detectedParams[name].properties && !this.detectedParams[name].properties![prop]) {
                this.detectedParams[name].properties![prop] = { type: 'number' };
            }
        } else if (!this.detectedParams[name]) {
            this.detectedParams[name] = { type };
        }
    }

    private replaceVariables(expr: string, context: Context): string {
        return expr.replace(/\{([^{}]+)\}|([A-Za-z_]\w*(?:\.[A-Za-z_]\w*)*)/g, (match, braced, direct) => {
            const name = braced || direct;
            if (name.includes('[') || name.includes(';')) {
                return this.evaluateNestedExpression(name, context).toString();
            }
            const value = this.getNestedProperty(context, name);
            return value !== undefined ? value.toString() : '0';
        });
    }

    private evaluateNestedExpression(expr: string, context: Context): number | string {
        const parser = new FormulaParser(expr);
        return parser.evaluate(context);
    }

    private evaluateSide(side: string, context: Context): any {
        if (FormulaParser.isNumeric(side)) return parseFloat(side);
        if (side.includes('.')) return this.getNestedProperty(context, side);
        return context[side] ?? 0;
    }

    private getNestedProperty(obj: Context, path: string): any {
        return path.split('.').reduce((current, prop) => {
            if (current == null) throw new Error(`Cannot read property '${prop}' of undefined`);
            return current[prop];
        }, obj);
    }

    private safeEvaluate(expr: string): number {
        if (!/^[\d+\-*/()., Matha-zA-Z_]+$/.test(expr)) {
            throw new Error("Unsafe characters in expression");
        }
        return new Function(`return ${expr}`)();
    }

    static isNumeric(value: string): boolean {
        return /^-?\d+(\.\d+)?$/.test(value);
    }

    private checkRecursionDepth(): void {
        if (this.recursionDepth >= this.MAX_RECURSION_DEPTH) {
            throw new Error("Maximum recursion depth exceeded");
        }
    }
}

// ================== 测试用例 ==================
export function testAllFormulas() {
    console.log("===== 测试公式解析器 =====");
    
    // 测试1: 简单条件公式
    const formula1 = "1*W/2[19332.A==1&&19334.B==6];22";
    testFormula(formula1, {
        "W=10, 19332.A=1, 19334.B=6": { W: 10, "19332": { A: 1 }, "19334": { B: 6 } },
        "W=10, 19332.A=0, 19334.B=6": { W: 10, "19332": { A: 0 }, "19334": { B: 6 } }
    });
    
    // 测试2: 数学运算公式
    const formula2 = "1*W/2-((1/2)*BLT1.TPL_B_BLTW)";
    testFormula(formula2, {
        "W=10, BLT1.TPL_B_BLTW=5": { W: 10, BLT1: { TPL_B_BLTW: 5 } }
    });
    
    // 测试3: 复杂嵌套公式
    const formula3 = "D-DIBQS-{BBH[DBBLX==1&&BBJG==6];DIBHS}[TMDB==1];D-QLTQS-{BBH[DBBLX==1&&BBJG==6];DIBHS}-GTBH[TMDB==2&&QLTLX==1];D-QLTQS-{BBH[DBBLX==1&&BBJG==6];DIBHS}-LVD[QLTLX==2]";
    testFormula(formula3, {
        "TMDB=1, DBBLX=1, BBJG=6": { TMDB: 1, DBBLX: 1, BBJG: 6, DIBHS: 100, BBH: 50, D: 200 },
        "TMDB=2, QLTLX=1": { TMDB: 2, QLTLX: 1, DBBLX: 1, BBJG: 6, DIBHS: 100, BBH: 50, GTBH: 30, D: 200, QLTQS: 300 },
        "QLTLX=2": { QLTLX: 2, DBBLX: 1, BBJG: 6, DIBHS: 100, BBH: 50, LVD: 20, D: 200, QLTQS: 300 }
    });

    const formula4 = "{{W*2-{1*W/2[19332.A==1&&19334.B==6];22}[W>100];-{1*W/3[19332.A==1&&19334.B==6];22}}[T==2];{1*W/3[19332.A==1&&19334.B==6];22}[T==1];100"
    testFormula(formula4, {
        "1": { W: 10, "19332": { A: 1 }, "19334": { B: 6 },T:2 },
        "2": { W: 105, "19332": { A: 1 }, "19334": { B: 6 },T:2 }
    });

    const formula5= "W";
    testFormula(formula5, {
        "1": { W: 10, "19332": { A: 1 }, "19334": { B: 6 },T:2 },
    });
}

function testFormula(formula: string, testCases: Record<string, Context>) {
    console.log(`\n测试公式: ${formula}`);
    const parser = new FormulaParser(formula);
    parser.recordProgress = true;
    // console.log("识别参数:", parser.getRequiredParams());
    const contextText = function(context){
        let textList = [];
        for(let key in context)
        {
            if(context[key] instanceof Object)
            {
                for(let subKey in context[key])
                {
                    textList.push(`${key}.${subKey}=${context[key][subKey]}`);
                }
            }
            else{
                textList.push( `${key}=${context[key]}`);
            }
        }
        return textList.join(",");
    }

    for (const [desc, context] of Object.entries(testCases)) {
        try {
            console.log(`\n上下文:`,contextText(context));
            console.log("结果:", parser.evaluate(context));
            console.log([...parser.evalulateProgress].reverse());
        } catch (e) {
            console.error(`测试失败: ${e}`);
        }
    }
}

// 执行测试
// testAllFormulas();
