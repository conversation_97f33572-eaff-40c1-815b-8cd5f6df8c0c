import React, { useEffect, useState, useRef } from 'react';
import { Image, Tooltip } from '@svg/antd';
import useStyles from './style';
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_Events } from '@layoutai/layout_scheme';
import { DragEventListener } from '@svg/antd-cloud-design'
import { useStore } from '@/models';
import { observer } from "mobx-react-lite";
import { getPrefix } from '@/utils/common';
import { useTranslation } from 'react-i18next';
import { Icon } from '@svg/antd-cloud-design';
import { checkIsMobile } from '@/config';

interface Module {
  image: string;
  png: string;
  title: string;
  label: string;
  group_code?:string;
}

interface dataProps {
  label: string;
  figureList: Module[];
}

interface ImageGalleryProps {
  data: any[];
  filterName: string
}
const ImageGallery: React.FC<ImageGalleryProps> = ({ data, filterName }) => {
  const { t } = useTranslation()
  const { styles } = useStyles();
  const store = useStore();
  const [label, setLabel] = useState<string>('');
  const scrollContainerRef = React.useRef<HTMLDivElement>(null);
  const [collapsedSections, setCollapsedSections] = useState<boolean[]>()
  const figureItemRefs = useRef<(HTMLDivElement | null)[]>([]);
  useEffect(() => {
    const dragEventListener = new DragEventListener({
      // 展示缩略图
      isShowThumbnail: true,
      container: document.getElementById('side_pannel') as HTMLElement,
      // 打印
      log: false,
    })
    dragEventListener.bindDrag()
    return () => {
      dragEventListener.unbindDrag()
    }
  }, [])


  useEffect(() => {
    scrollToItem(filterName)
  },[filterName])

  useEffect(() => {
    setCollapsedSections(data.map(() => false));
  },[data])

  useEffect(() => {
    // 鼠标弹起的时候设置label为空
    const handleMouseUp = () => {
      if(label)
      {
        LayoutAI_App.RunCommand(LayoutAI_Commands.LeaveSubHandler);
        setLabel('');
      }
    };
    const handleTouchMove = (ev: TouchEvent) => {
      if (ev.touches[0].clientX > 240) {
        if (scrollContainerRef.current) {
          scrollContainerRef.current.style.overflow = 'hidden';
        }
      }
    };

    const handleTouchEnd = (ev: TouchEvent) => {
      if (scrollContainerRef.current) {
        scrollContainerRef.current.style.overflowY = 'scroll';
      }
    }
    window.addEventListener('mouseup', handleMouseUp);
    window.addEventListener('touchmove', handleTouchMove);
    window.addEventListener('touchend', handleTouchEnd);
    // 在组件卸载时移除事件监听器
    return () => {
      window.removeEventListener('mouseup', handleMouseUp);
      window.removeEventListener('touchmove', handleTouchMove);
      window.removeEventListener('touchend', handleTouchEnd);
    };


  }, [label]);


  useEffect(() => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTop = 0;  // 将滚动位置设置为 0
    }
  }, [data]);


  const refs = data.reduce((acc, value, index) => {
    acc[index] = React.createRef();
    return acc;
  }, {});

  const scrollToItem = (filterName: string) => {
    const index = data.findIndex(item => item.label === filterName);
    if (index !== -1 && refs[index].current && scrollContainerRef.current) {
      const top = refs[index].current.offsetTop;
      scrollContainerRef.current.scrollTop = top;
    }
  };
  const MemoizedImage = React.memo(Image);

  let startY = 0;


  return (
    <div className={`${styles.figure} ${checkIsMobile() ? styles.mobile : ""}`} ref={scrollContainerRef}>
      {data.map((item, index) => (
          <div style={{width: '100%'}} ref={refs[index]}  key={"figure_menu_key"+index}>
            <div className="fold">
              <Icon
                iconClass={collapsedSections?.[index] ? 'iconfill_down' : 'iconfill_up'}
                style={{
                  fontSize: '14px',
                  color: '#5B5E60', 
                  marginRight: '2px',
                  cursor: 'pointer',
                }}
                onClick={() => {
                  setCollapsedSections(collapsedSections.map((collapsed, i) => i === index ? !collapsed : collapsed));
                }}>      
              </Icon>
              {t(item.label)}
            </div>
            <div 
              className={`content ${collapsedSections?.[index] ? 'collapsed' : ''}`}
              >
              {item?.figureList?.map((item: Module, index: number) =>(
                <div 
                  key={index} 
                  className={`item ${checkIsMobile() ? 'mobile' : ''}`}
    
              >
                <div className="image"               
                  onPointerDown={(ev: React.PointerEvent)=>{
                    let label = item.title;
                    if(item.group_code)
                    {
                      label = "GroupTemplate:"+item.group_code;
                      // setLabel("GroupTemplate:"+item.group_code)
                    }

                    if(item.title.includes('单开门') || item.title.includes('推拉门') || item.title.includes('一字窗') ||  item.title.includes('飘窗')) /*[i18n:ignore]*/
                    {
                      LayoutAI_App.DispatchEvent(LayoutAI_Events.SelectedFurniture, label);
                      return;
                    } 
                    else 
                    {
                      LayoutAI_App.DispatchEvent(LayoutAI_Events.SelectedFurniture, label);
                    }
                  }}
                  >
                  <MemoizedImage 
                  src={(getPrefix() + `static/figures_imgs/${item.png}`)} 
                  preview={false} 
                  alt={item.title}
                  className={filterName === "结构件" ? 'structure-image' : ''}/>
                  {/* 下面这种方式可以实时绘制组合图元的图标 */}
                  {/* {item.group_code ? 
                    <Image src={(`${item.image}`)} preview={false} alt={item.title} className='group_image'/> 
                    : 
                    <Image src={(getPrefix() + `static/figures_imgs/${item.png}`)} preview={false} alt={item.title}/>
                  } */}
                </div>
                <Tooltip placement="rightBottom" title={t(item.title)}>
                  <div className="title">{t(item.title)}</div>
                </Tooltip>

                </div>
              ))}
            </div>
          </div>
  
        ))}  
    </div>
  );
};

export default observer(ImageGallery);
