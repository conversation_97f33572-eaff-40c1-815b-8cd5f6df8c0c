import React from 'react';
import userInfo from './userInfo';
import HomeStore from './homeStore';
import MobileStore from './mobileStore';
export const userInfoContext = React.createContext(userInfo);

/**
 * @description 用户信息数据
 */
export const useUserInfo = () => React.useContext(userInfoContext);


class RootStore {
    homeStore: HomeStore;
    mobileStore: MobileStore;
    constructor() {
        // 对子模块进行实例化操作
        this.homeStore = new HomeStore()
        this.mobileStore = new MobileStore()
    }
}

const rootStore = new RootStore();
const context = React.createContext(rootStore)

const useStore = () => React.useContext(context)

export { useStore }