import { I_SwjXmlScheme, LayoutSchemeData, SchemeSourceType } from "@layoutai/basic_data";
import { BaseModeHandler, CadDrawingLayerType, EventName, I_SelectedTarget, LayoutAI_App, LayoutAI_Commands, LayoutAI_Events, LayoutContainerUtils, LayoutSchemeXmlJsonParser, Logger, TAppManagerBase, TCadCopyImageLayer, TFurnitureEntity, TRoom, TRoomLayoutScheme, TWall, TWholeLayoutScheme, T_MoveElement, T_MoveWallElement, T_MoveWinDoorElement, T_TransformElement, base64ToUtf8, layoutSolverService } from "@layoutai/layout_scheme";
import { I_MouseEvent, ZRect } from "@layoutai/z_polygon";
import { CadBaseSubHandler } from "./SubHandlers/CadBaseSubHandler";
import { getCookie } from "@/utils";
import {BuildingService} from "@layoutai/layout_basic_services"


export class AI2BaseModeHandler extends BaseModeHandler {

    public _selected_target: I_SelectedTarget;


    public _whole_layout_selected_index: number = 0;
    /**
     *  待选择的对象矩形列表
     */
    protected _candidate_target_rects: ZRect[];
    /**
     *  用作吸附考虑的对象目标矩形列表
     */
    protected _exsorb_target_rects: ZRect[];
    protected _transform_elements: T_TransformElement[];
    protected _transform_moving_element: T_MoveElement;

    protected _transform_moving_struture_element: T_MoveWinDoorElement;

    protected _transform_moving_wall_element: T_MoveWallElement;

    protected _cad_default_sub_handler: CadBaseSubHandler;


    _last_mouse_down_event: I_MouseEvent;

    public _existingInput: HTMLInputElement;

    _logger: Logger = null;

    constructor(manager: TAppManagerBase, name: string = "AICadMode") {
        super(manager, name);
        this._selected_target = {
            hover_rect: null,
            selected_rect: null,
            selected_transform_element: null,
            hover_transform_element: null,
            selected_combination_entitys: []
        }
        this._candidate_target_rects = [];
        this._exsorb_target_rects = [];
        this._logger = Logger.instance;
        this._existingInput = null;

    }

    enter(state?: number): void {
        super.enter(state);
        if(this.manager.layout_container.entity_selector)
        {
            this.manager.layout_container.entity_selector.bindSelectedTarget(this._selected_target);
            this.manager.layout_container.entity_selector.bindTransformers(this.transform_elements);
        }
    }

    public updateDomUI()
    {

    }
    get manager() {
        return this._manager;
    }

    get whole_layout_scheme_list() {
        return this._manager.layout_container._whole_layout_scheme_list;
    }
    set whole_layout_scheme_list(list: TWholeLayoutScheme[]) {
        this._manager.layout_container._whole_layout_scheme_list = list;
    }
    get candidate_rects() {
        return this._candidate_target_rects;
    }

    get exsorb_rects() {
        return this._exsorb_target_rects;
    }

    get transform_elements() {
        return this._transform_elements;
    }


    get transform_moving_element() {
        return this._transform_moving_element;
    }

    get transform_moving_struture_element() {
        return this._transform_moving_struture_element;
    }

    get transform_moving_wall_element() {
        return this._transform_moving_wall_element;
    }

    get painter() {
        return this.manager?.painter;
    }

    get furniture_entities() {
        return this.manager.layout_container._furniture_entities;
    }

    set furniture_entities(rects: TFurnitureEntity[]) {
        this.manager.layout_container._furniture_entities = rects;
    }
    get wall_entities() {
        return this.manager.layout_container._wall_entities;
    }

    set wall_entities(rects: TWall[]) {
        this.manager.layout_container._wall_entities = rects;
    }
    get room_list() {
        return this.manager.layout_container._rooms;
    }
    get logger() {
        return this._logger;
    }

    protected get _initial_scheme_data(): I_SwjXmlScheme {
        return this.manager.layout_container._initial_scheme_data;
    }
    protected set _initial_scheme_data(data: I_SwjXmlScheme) {
        this.manager.layout_container._initial_scheme_data = data;
    }
    handleEvent(event_name: string, event_param: any) {
        if (this._active_sub_handler) {
            this._active_sub_handler.handleEvent(event_name, event_param);
        }
        else {
            if (this._cad_default_sub_handler) {
                this._cad_default_sub_handler.handleEvent(event_name, event_param);
            }
        }
        if (event_name === LayoutAI_Events.OpenMyLayoutSchemeData) {
            if (event_param != null) {
                Logger.instance.log("打开我的方案：" + event_param.layoutSchemeName + ", " + event_param.id);
                this.openLayoutSchemeData(event_param);
                this.manager.layout_container.aidraw_img = event_param.coverImage;
            }
        }

        if (event_name === LayoutAI_Events.OpenMyLayoutSchemeUrl) {
            let layoutSchemeUrl: string = event_param;
            if (layoutSchemeUrl) {
                fetch(layoutSchemeUrl).then((response) => {
                    response.text().then((base64str) => {
                        const layoutSchemeJsonStr = base64ToUtf8(base64str);
                        const schemeJson = JSON.parse(layoutSchemeJsonStr.replace(/'/g, '"'));
                        LayoutSchemeXmlJsonParser.loadSwjSchemeXmlJson(schemeJson);
                    });
                }).catch((e) => {
                    console.error(e);
                });
            }
        }

        if (event_name === LayoutAI_Events.Init) {
            this.manager.layout_container.clearSchemeInfo();
            layoutSolverService.upudateWholeLayoutSchemeList();
            this._cad_default_sub_handler.updateCandidateRects();
            this.update();
        }
        if (event_name === LayoutAI_Events.OnloadedXmlLayoutScheme) {
            this.onloadSwjSchemeJson(event_param);
        }
        if (event_name === LayoutAI_Events.PostBuildingId) {
            this.openSwjFileWithBuildingId(event_param.id, event_param.name, event_param.imagePath);
        }
    }
    async runCommand(cmd_name: string): Promise<void> {
        if (this._sub_handlers[cmd_name]) {
            this.setActiveHandler(cmd_name);
            // return;
        }
        if (cmd_name === LayoutAI_Commands.LeaveSubHandler) {
            this.setActiveHandler(this._default_sub_handler_name);

            LayoutAI_App.emit_M(EventName.SubHandlerLeaved, { new_handler: this._active_sub_handler });

            document.querySelector(".edit_input")?.remove();
        }
        if (cmd_name === LayoutAI_Commands.AcceptLeaveSubHandler) {
            this.setActiveHandler(this._default_sub_handler_name, 1);

            document.querySelector(".edit_input")?.remove();
        }
        else if (cmd_name === LayoutAI_Commands.Undo) {
            this.manager.undo();
            this.setActiveHandler(this._default_sub_handler_name);
        }
        else if (cmd_name === LayoutAI_Commands.Redo) {
            this.manager.redo();
            this.setActiveHandler(this._default_sub_handler_name);

        }
        else if (cmd_name === LayoutAI_Commands.SaveSwjJsonFile) {
            await this.saveSwjXmlSchemeJson();
        }
        else if (cmd_name === LayoutAI_Commands.SaveSwjJsonRoomTemplate) {
            this.saveRoomTemplateFile();
        }
        else if (cmd_name === LayoutAI_Commands.OpenSwjJsonRoomTemplate) {
            this.openRoomTemplateFile();
        }
        else if (cmd_name === LayoutAI_Commands.SaveRoomTemplates) {
            this.uploadRoomTemplates(false);
        }
        else if (cmd_name === LayoutAI_Commands.SaveAsRoomTemplates) {
            this.uploadRoomTemplates(true);
        }
        else if (cmd_name === LayoutAI_Commands.OpenSwjFileBySchemeId) {
            this.openSwjFileWithSchemeId();
        }
        else if (cmd_name === LayoutAI_Commands.OpenLayoutSchemeById) {
            this.openLayoutSchemeById();
        }
        else if (cmd_name === LayoutAI_Commands.ApplyLayout) {
            this.EventSystem.emit(EventName.SwitchIntoDesign);
        }
        else if (cmd_name === LayoutAI_Commands.WholeHouseAILayout) {
            if (!this._active_sub_handler) {
                this.computeWholeLayoutSchemeList(true);
            }
        }
        this.update();
    }

    async openSwjFileWithBuildingId(id: string, name: string, imagePath: string) {
        if (!id) return;
        let authCode = getCookie("authCode");
        let res = await BuildingService.getBuildingRoomSchemeById(id, authCode);
        LayoutAI_App.emit(EventName.LayoutSchemeOpened, { id: null, name: name });
        // 清空临摹图
        if (this._manager.layout_container.copyImageRect) {
            // this._manager.layer_CadCopyImageLayer.clean();
            let layer = this._manager.drawing_layers[CadDrawingLayerType.CadCopyImageDrawing];
            if(layer)
            {
                (layer as TCadCopyImageLayer).clean();
            }
        }
        if (res) {
            Logger.instance.log("打开户型库：" + id);
            LayoutSchemeXmlJsonParser.loadSwjSchemeXmlJson(res, { updateUid: true, layout_scheme_id: null, layout_scheme_name: name, schemeSource: SchemeSourceType.LayoutLibrary, clean_dxf_data: false });
            this.manager.layout_container.aidraw_img = imagePath;
            this.manager.layout_container.hxId = id
        }
        this.update();

    }

    /**
     *  载入方案完成
     */
    onloadSwjSchemeJson(options: { data?: I_SwjXmlScheme, clean_dxf_data?: boolean, 
        updateUid?: boolean, auto_layout?: boolean } = {}) {
        if (options.clean_dxf_data) {

            // this.manager.layer_CadEzdxfLayer._clean();
            // this.manager.layer_CadEzdxfLayer.ezdxf_data = null;
        }
        if (window.innerWidth < window.innerHeight * 0.8) {
            LayoutContainerUtils.focusCenterByWholeBox(this.manager.layout_container, 0.7);
        }
        else {
            LayoutContainerUtils.focusCenterByWholeBox(this.manager.layout_container, 0.6);
        }
        // if (checkIsMobile()) {
        //     const deviceType = checkDeviceType();

        //     if (deviceType === 'tablet') {
        //         this.painter._p_sc = 0.1;
        //     } else {
        //         this.painter._p_sc = 0.06;
        //     }

        // }
        // this.manager.layout_container.focusCenter();


        this._cad_default_sub_handler.updateCandidateRects();
        this._cad_default_sub_handler.cleanSelection();
        this.update();
        let current_mode = this.manager._current_handler_mode;
        let is_ai_matching_mode = this.manager.layout_container._drawing_layer_mode === "AIMatching";
        // if (this.manager.layout_container) {
        //     this.manager.layout_container._initial_scheme_data = this.manager.layout_container.toXmlSchemeData();
        //     this.manager.layout_container._initial_scheme_data.xml_str = data.xml_str;
        // }


        LayoutAI_App.emit(EventName.OnPreparingHandle, { opening: false, title: null });
        LayoutContainerUtils.updateAliasName();
        LayoutAI_App.emit_M(EventName.RoomList, this.room_list);

        if(options.auto_layout)
        {
            // 加载方案后计算全屋布局方案
            this.computeWholeLayoutSchemeList(false).then(() => {
                if (this.whole_layout_scheme_list && this.whole_layout_scheme_list.length > 0) {
                    LayoutAI_App.DispatchEvent(LayoutAI_Events.ClickWholeLayoutScheme, { value: this.whole_layout_scheme_list[0], index: 0 });
                    LayoutAI_App.emit(EventName.xmlSchemeLoaded, { mode: 'Finish'});
                }
            });
        }

        if(this.manager?.Configs?.update3d_when_xmlscheme_onloaded)
        {
            if(this.manager.scene3D && this.manager.scene3D.isValid())
            {
                this.manager.updateScene3D(true);
            }
        }
    
    }
    unGroupAllGroupTemplates() {
        this.manager.layout_container.unGroupOfAllFurnitureEntities();
    }
    unGroupAllBaseGroup() {
        this.manager.layout_container.unBaseGroupOfAllFurnitureEntities();
    }

    transformGroup() {
        this.manager.layout_container.transformGroup();
    }
    isSelectRoom() {
        for (let room of this.room_list) {
            // 新增判断是否可点击逻辑
            room.checkIsSelectable();
        }
    }
    async computeWholeLayoutSchemeList(append_furniture_entities: boolean = true) {

        await layoutSolverService.computeWholeLayoutSchemeList(append_furniture_entities);
        
        layoutSolverService.upudateWholeLayoutSchemeList();
        this.update();
    }



    loadSwjSchemeXmlJson(data: I_SwjXmlScheme, updateUid: boolean = false, layout_scheme_id: string = null,
        layout_scheme_name: string = null, clean_dxf_data: boolean = true, schemeSource: SchemeSourceType = null) {
        LayoutSchemeXmlJsonParser.loadSwjSchemeXmlJson(data, {
            updateUid: updateUid,
            layout_scheme_id: layout_scheme_id,
            layout_scheme_name: layout_scheme_name,
            clean_dxf_data: clean_dxf_data,
            schemeSource: schemeSource
        })
    }
    async openLayoutSchemeData(data: LayoutSchemeData) {
        return await LayoutSchemeXmlJsonParser.openLayoutSchemeData(data);
    }

    async queryModelRoomsFromServer() {
        let rooms: TRoom[] = this.room_list;
        // await this.manager.layout_graph_solver.queryModelRoomsFromServer(rooms, false, true);
    }

    protected printMaterialsFrom3D(materialIdPublicCategoryMap: Map<string, string>, houseStruture: I_SwjXmlScheme) {
        let multiLinelog = "";
        multiLinelog = "##### 来自3D方案的原有素材 #####";
        multiLinelog += "\nScheme ID: " + houseStruture.scheme_id + ", area=" + (houseStruture.area ? houseStruture.area.toFixed(2) : 0) + ", organizationId=" + houseStruture.organization_id;
        multiLinelog += "\nFurniture list:";
        if (houseStruture.furniture_list && houseStruture.furniture_list.length > 0) {
            houseStruture.furniture_list.forEach((f) => {
                multiLinelog += "\n    materialId:" + f.material_id + ", name:" + f.name + ", room:" + f.room_ind + (materialIdPublicCategoryMap.has(f.material_id) ? ", publicCategory:" + materialIdPublicCategoryMap.get(f.material_id) : "");
            });
        }
        if (houseStruture.cabinet_list && houseStruture.cabinet_list.length > 0) {
            multiLinelog += "\nCabinet list:";
            houseStruture.cabinet_list.forEach((f) => {
                multiLinelog += "\n    materialId:" + f.material_id + ", name:" + f.name + ", room:" + (f.room_id | f.room_ind) + (materialIdPublicCategoryMap.has(f.material_id) ? ", publicCategory:" + materialIdPublicCategoryMap.get(f.material_id) : "");
            });
        }
        if (houseStruture.furniture_group_list && houseStruture.furniture_group_list.length > 0) {
            multiLinelog += "\nFurniture group list:";
            houseStruture.furniture_group_list.forEach((f) => {
                multiLinelog += "\n    materialId:" + f.material_id + ", name:" + f.name + ", room:" + f.room_ind + (materialIdPublicCategoryMap.has(f.material_id) ? ", publicCategory:" + materialIdPublicCategoryMap.get(f.material_id) : "");
            });
        }
        if (multiLinelog.length > 0) this.logger.log(multiLinelog);
    }
    async saveSwjXmlSchemeJson() {
        this.manager.layout_container.updateRoomsFromEntities();
        let wireFrameImageJsonUrl = "";
        let scheme_data = this.manager.layout_container.toXmlSchemeData();
        scheme_data.wireFrameImageJsonUrl = wireFrameImageJsonUrl;
        const dataStr = JSON.stringify(scheme_data, null, 2);
        const blob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const schemeId = scheme_data.scheme_id || null;

        // 弹出输入框让用户输入文件名
        let fileName = prompt("当前布局数据数据即将保存到本地，请输入文件名：", 'layout_' + (schemeId ? schemeId : "null") + '.json');

        // 如果用户取消输入，则不进行下载
        if (fileName === null) return;

        // 创建临时的<a>标签用于下载
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName; // 使用用户输入的文件名

        document.body.appendChild(a);
        a.click();

        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    saveRoomTemplateFile() {
        this.manager.layout_container.updateRoomsFromEntities();

        // if (this._selected_target.selected_rect) {
        //     let entity = TBaseEntity.getEntityOfRect(this._selected_target.selected_rect);
            
        //     if (entity && entity.type === "RoomArea") {
        //         let swj_json_data = this.manager.layout_container.saveTRoomToJson((entity as TRoomEntity)._room);

        //         const dataStr = JSON.stringify(swj_json_data, null, 2);
        //         const blob = new Blob([dataStr], { type: 'application/json' });
        //         const url = URL.createObjectURL(blob);
        //         let schemeId = this.manager.layout_container._scheme_id + "_" + swj_json_data.swj_room_data.uid;

        //         let fileName = prompt("选中空间的布局模板保存到本地，请输入文件名：", 'room_template_' + (schemeId ? schemeId : "null") + '.json');

        //         // 如果用户取消输入，则不进行下载
        //         if (fileName === null) return;

        //         // 创建临时的<a>标签用于下载
        //         const a = document.createElement('a');
        //         a.href = url;
        //         a.download = fileName; // 使用用户输入的文件名

        //         document.body.appendChild(a);
        //         a.click();

        //         document.body.removeChild(a);
        //         URL.revokeObjectURL(url);

        //     }
        // }

    }


    async openRoomTemplateFile() {
        // const fileResult: FileOpenResult = await openFileInput(".json", "Text");
        // let text = fileResult.content;
        // if (!text) return;
        // let data = JSON.parse(text);
        // if (!data) return;

        // this.manager.layout_container.loadRoomEntityFromJson(data);
    }

    async openSwjFileWithSchemeId() {
        // let scheme_id = prompt("请输入方案ID");
        // if (!scheme_id) return;
        // let authCode = getCookie("authCode");

        // let res = await SchemeXmlParseService.getSchemeXmlBySchemeId(scheme_id, authCode, "code_debug");

        // if (res) {
        //     LayoutSchemeXmlJsonParser.loadSwjSchemeXmlJson(res);
        //     let furniture_list = this.manager.layout_container._furniture_entities;
        //     furniture_list.forEach((figure_element) => {
        //         figure_element.figure_element.loadWireFrameImage()
        //     });
        // }
    }

    async openLayoutSchemeById() {
        // let scheme_id = prompt("请输入2D布局方案ID");
        // if (!scheme_id) return;
        // let schemeData = await LayoutSchemeService.getLayoutSchemeById(scheme_id);
        // this.openLayoutSchemeData(schemeData);
    }

    uploadRoomTemplates(save_as: boolean = false) {

        // let selected_room_only = confirm(`保存选中的空间，还是全屋？ 确定-单空间(${this.manager.layout_container._selected_room?.roomname || "无"}), 取消-全屋`);

        // if (selected_room_only && !this.manager.layout_container._selected_room) {
        //     alert("未选中空间");
        //     return;
        // }


        // if (this.manager.layout_container._room_entities.length == 0) {
        //     window.alert("空数据不能保存");
        //     return;
        // }
        // let text = "保存 方案Id: " + this.manager.layout_container._scheme_id;
        // if (save_as || !this._manager.layout_container._scheme_id) {
        //     this.manager.layout_container.generateSchemeId();
        //     text = "另存为 方案Id: " + this.manager.layout_container._scheme_id;
        //     console.log(this.manager.layout_container._scheme_id);
        // }



        // let res = confirm(text);
        // if (!res) return;

        // TRoomTemplateSaver.saveRoomTemplates(selected_room_only).then((room_names: { [key: string]: { room_name: string, success: boolean } }) => {
        //     let text = "保存布局模板完成! ";

        //     if (selected_room_only) {
        //         let num = Object.keys(room_names).length;
        //         if (num == 0) {
        //             text = "单空间布局模板保存不成功, 布局库中存在相同的模板";
        //         }
        //     }
        //     console.log(room_names);
        //     for (let key in room_names) {
        //         let data = room_names[key];
        //         text += key + "-" + data.room_name + " " + (data.success ? "✔" : "X") + " ";
        //     }

        //     confirm(text);

        // })
    }





    protected postProcessWholeLayoutSchemeList() {
        this.whole_layout_scheme_list.forEach(wholeLayoutScheme => {
            wholeLayoutScheme.postProcess();
        });
    }

    updateRoomSelected(ev: I_MouseEvent) {
        let pos = { x: ev.posX, y: ev.posY, z: 0 };

        let room_list = this.room_list;
        let target_room: TRoom = null;
        let scope = this;
        // 点击空间
        if (room_list && room_list.length > 0) {
            for (let room of room_list) {
                let poly = room.room_shape._poly;
                let dist = poly.distanceToPoint(pos);
                if (dist < 0) {
                    target_room = room;
                    break;
                }
            }
        }

        // 判断是否点击

        if (target_room && !target_room.selectable) {
            LayoutAI_App.emit(EventName.PerformFurnishResult, { progress: "error", message: "暂不支持其他空间的布置" });
            return;
        }
      
    }
}



