import { makeAutoObservable } from 'mobx';

interface spaceData {
  // 参数还没定义
}

/**
 * @description 主页数据
 */
class LayoutStore {
  singleSpaceData: spaceData = {};
  showSelectLayout = false;
  segmentedValue = '风格套系'; /*[i18n:ignore]*/
  constructor() {
    makeAutoObservable(this, {}, {autoBind: true});
  }
  // action
  setUserInfo(data: spaceData) {
    this.singleSpaceData = data;
  }
  
  setShowSelectLayout(data: boolean) {
    this.showSelectLayout = data;
  }

  setSegmentedValue(data: any) {
    this.segmentedValue = data;
  }

}

export default LayoutStore;