import { FSM } from '../FSM';
import { Idle3DState } from '../states/3d/Idle3DState';
import { Init3DState } from '../states/3d/Init3DState';
import { FSMNames, Design3DStates } from '../const/FSMConst';

/**
 * 3D设计FSM
 */
export class Design3DFSM extends FSM {
  constructor() {
    super(FSMNames.DESIGN_3D);
    
    // 添加状态
    this.addState(new Init3DState(Design3DStates.INIT3D));
    this.addState(new Idle3DState(Design3DStates.IDLE3D));
    
    // 设置默认状态
    this.setDefaultState(Design3DStates.INIT3D);
    
    // 配置状态切换关系
    this.addTransition(Design3DStates.INIT3D, Design3DStates.IDLE3D);
  }
} 