import { I_XmlCRealFurnitureEntity, I_XmlEntityBase } from "../IWardrobeEntityInterface";
import { XmlEntityBase } from "../XmlEntityBase";

export class XmlCRealFurnitureEntity extends XmlEntityBase  {

    materialAttributeS: string;
    mirrorTypeS: string;
    mirrorPointS: string;
    mirrorNormalS: string;
    generationTypeS: string;
    RoomIdS: string;
    enabledB: boolean;
    affectDiffuseB: boolean;
    affectSpecularB: boolean;
    invisibleB: boolean;
    lightGroupS: string;
    isAutoIntensityB: boolean;
    autoIntensityN: number;
    depthN?: number;
    colorN?: number;
    intensityN?: number;
    unitsN?: number;
    xN?: number;
    yN?: number;
    zN?: number;
    widthN?: number;
    heightN?: number;
    lengthN?: number;
    constructor(data?: I_XmlCRealFurnitureEntity) {
        super(data);
        this.xN = data.xN ?? 0;
        this.yN = data.yN ?? 0;
        this.zN = data.zN ?? 0;
        this.widthN = data.widthN ?? 0;
        this.heightN = data.heightN ?? 0;
        this.lengthN = data.lengthN ?? 0;
        
        this.enabledB = data.enabledB;
        this.materialAttributeS = data.materialAttributeS ?? "";
        this.lightGroupS = data.lightGroupS ?? "";
        this.isAutoIntensityB = data.isAutoIntensityB ?? false;
        this.affectDiffuseB = data.affectDiffuseB ?? false;
        this.affectSpecularB = data.affectSpecularB ?? false;
        this.mirrorTypeS = data.mirrorTypeS ?? "";
        this.mirrorPointS = data.mirrorPointS ?? "";
        this.mirrorNormalS = data.mirrorNormalS ?? "";
        this.colorN = data.colorN ?? 0;
        this.unitsN = data.unitsN ?? 0;
        this.intensityN = data.intensityN ?? 0;
        this.invisibleB = data.invisibleB ?? false;
        this.generationTypeS = data.generationTypeS ?? "Default";
        this.autoIntensityN = data.autoIntensityN ?? 0;
        this.RoomIdS = data.RoomIdS ?? "";
    }
    protected initAttributes(data?: I_XmlCRealFurnitureEntity): void {
        super.initAttributes(data);    
    }
    
}

XmlEntityBase.Generators["CRealFurnitureEntity"] = (data:I_XmlEntityBase)=>new XmlCRealFurnitureEntity(data);