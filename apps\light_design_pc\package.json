{"name": "light_design_pc", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@layoutai/design_2d": "workspace:^", "@layoutai/design_3d": "workspace:^", "@layoutai/design_domain": "workspace:^", "@layoutai/basic_data": "workspace:^", "@svg/sso-plus": "^1.0.68", "@svg/antd": "2.6.3", "@svg/antd-basic": "^5.6.8", "@svg/antd-cloud-design": "4.17.0", "axios": "0.27.2", "lodash": "^4.17.21", "mobx": "6.6.1", "mobx-react": "7.5.2", "mobx-react-lite": "^4.0.7", "react": "18.2.0", "react-dom": "18.2.0", "react-i18next": "^11.18.4", "react-router-dom": "6.3.0", "three": "^0.171.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/lodash": "^4.17.18", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/three": "^0.171.0", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.0", "vite": "^6.3.5"}}