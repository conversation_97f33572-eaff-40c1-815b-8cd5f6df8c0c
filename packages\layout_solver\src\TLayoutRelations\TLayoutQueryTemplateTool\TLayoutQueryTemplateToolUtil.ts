import { BasicRequest } from "@layoutai/basic_data";

// 专门封装从组合入库中查询模板数据
export class TLayoutQueryTemplateToolUtil
{
    private static _instance: TLayoutQueryTemplateToolUtil;

    private constructor()
    {}

    public static get instance(): TLayoutQueryTemplateToolUtil
    {
        if (!TLayoutQueryTemplateToolUtil._instance)
        {
            TLayoutQueryTemplateToolUtil._instance = new TLayoutQueryTemplateToolUtil();
        }
        return TLayoutQueryTemplateToolUtil._instance;
    }

    public async queryMetaCategory(params: any)
    {
        const result = await BasicRequest.openApiRequest(
            {
                method: 'post',
                url: 'api/njvr/layoutMetaCategory/listMetaImage',
                data:  {
                    params,
                },
                timeout: 60000,
            }
        ).catch((err: any): null => {
            return null;
        });
        return result;
    }


    public async queryMetaItemSize(params: any)
    {
        const result = await BasicRequest.openApiRequest(
            {
                method: 'post',
                url: 'api/njvr/layoutMetaImageSize/listByPage',
                data:  {
                   params,
                },
                timeout: 60000,
            }
        ).catch((err: any): null => {
            return null;
        });
        return result;
    }
}