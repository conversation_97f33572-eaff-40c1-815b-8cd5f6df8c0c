import useCommonStyles from "../common_style/";
import { Rate } from '@svg/antd';
import { GradeStarsDict, UI_FormatType, GradeImageDict, GradeClassDict, GradeText,I_LayoutScore,LayoutAI_App,EventName} from '@layoutai/layout_scheme';
import useStyles from './style';
import {
    CheckCircleOutlined
  } from '@ant-design/icons'
import React, { useEffect, useState } from 'react';
import { Radar, RadarChart, PolarGrid, PolarAngleAxis, ResponsiveContainer } from 'recharts';
import { checkIsMobile } from '@/config';
import IconFont from "../IconFont/iconFont";

/**
 * 
 * 布局评分卡片
 */
const LayoutScoreCard: React.FC<{layoutScoreList:I_LayoutScore[], style:number}> = (props:{layoutScoreList:I_LayoutScore[],style:number})=>{
    const { styles } = useStyles();
    const common_styles = useCommonStyles().styles;
    const t = LayoutAI_App.t;
    const object_id = "LayoutScoreCard";
    const layoutScoreList = props.layoutScoreList || [];
    const ui_style = props.style || 0;    
    const [chartReady, setChartReady] = useState(false);
    
    useEffect(() => {
        // 延迟渲染图表，确保容器已经准备好
        const timer = setTimeout(() => {
            setChartReady(true);
        }, 300);
        
        return () => clearTimeout(timer);
    }, []);
    
    const makeScoreRowContentDiv = (layout_score : I_LayoutScore,row_index:number, level:number)=>{
        if(layout_score.ui_format && ui_style == 0)
        {
            return <>
                <div  key={"format_sub_"+row_index+"_0"}className='score_label div_Name'>
                    {t(layout_score.name)}
                </div>
                <div  key={"format_sub_"+row_index+"_1"} className='score_label div_Score'>
                    {layout_score.score}
                </div>
            </>
        }
    }

    const makeScoreRowDiv = (layout_score:I_LayoutScore, index:number,level:number)=>{
        if(level >= 1 && ui_style)
        {
            if(index > 2)
            {
                return <></>
            }
        }

        return <div key={"layout_score_"+level+"_"+index} className={"layout_score_row "+("score_level_"+level)}>
            {makeScoreRowContentDiv(layout_score,index,level)}
            {layout_score.children && layout_score.children.length > 0 && <div className={"sub_layout_score score_level_"+level}>
                {makeListDiv(layout_score.children,level+1)}
            </div>
            }
        </div>
    }
    const makeListDiv = (layout_score_list : I_LayoutScore[],level = 0)=>{
        return layout_score_list.map((layout_score,index)=>makeScoreRowDiv(layout_score,index,level)
        )
    }

    const data = [
        {
          subject: t('收纳占比'),
          percentage: 20,
          fullMark: 100,
        },
        {
          subject: t('布局'),
          percentage: 20,
          fullMark: 100,
        },
        {
          subject: t('空间利用率'),
          percentage: 25,
          fullMark: 100,
        },
        {
          subject: t('动线'),
          percentage: 30,
          fullMark: 100,
        },
      ];
    const radarData = data.map(item => ({
        subject: item.subject,
        A: (item.percentage / 100) * item.fullMark, // 将百分比转换为数值
        fullMark: item.fullMark,
      }));

    let showScoreList = layoutScoreList.filter((item)=>item.name === '家居布局')[0]?.children || []; /*[i18n:ignore]*/

    const calculatePercent = (item: any) => {
        if (item.ui_format?.includes(UI_FormatType.Percentage) && item.name === '空间利用率')  /*[i18n:ignore]*/
        {
            const obj = item.children.find((item: any)=>item.name === '家具利用率') /*[i18n:ignore]*/
            if(obj)
            {
                return (obj.percent* 100).toFixed(0) + "%"
            }
        }
        else if(item.ui_format?.includes(UI_FormatType.Percentage))
        {

            return (item.percent * 100).toFixed(0) + "%"
        }
        {
            return '' 
        }
    }

    const calculateGrade= (item: any) => {
        if (item.ui_format?.includes(UI_FormatType.Grade) && item.name === '空间利用率')  /*[i18n:ignore]*/
        {
            const obj = item.children.find((item: any)=>item.name === '家具利用率') /*[i18n:ignore]*/
            if(obj)
            {
                return obj.score;
            }
        }
        else if(item.ui_format?.includes(UI_FormatType.Grade))
        {

            return item.grade;
        }
        {
            return '' 
        }
    }

    let totalGrade = 0;
    let rate = 0;
    layoutScoreList.forEach((item) => {
        totalGrade += item.grade;
    })
    if(totalGrade)
    {
        rate = Math.round(totalGrade / layoutScoreList.length);
    }
    return (
        ui_style == 0 ?
        <div className={common_styles.layoutScoreCard+" layoutScoreCard"}> 
            {checkIsMobile () && 
                <IconFont
                    className={styles.cloneIcon}
                    type="icon-a-tianchongFace-1"
                    style={{
                    fontSize: '16px',
                    color: '#ccc', 
                    }}
                    onClick={() => {
                        LayoutAI_App.emit(EventName.ShowPopUpLayoutScore, {scheme:null, top: 0})
                    }}
                >
                </IconFont>
            }
            {makeListDiv(layoutScoreList,0)}
        </div> :
        <div className={styles.sceneBox}>
            {/* 生产模式下的显示 */}
            {checkIsMobile () && 
                <IconFont
                    className={styles.cloneIcon}
                    type="icon-a-tianchongFace-1"
                    style={{
                    fontSize: '16px',
                    color: '#ccc', 
                    }}
                    onClick={() => {
                        LayoutAI_App.emit(EventName.ShowPopUpLayoutScore, {scheme:null, top: 0})
                    }}
                >
                </IconFont>
            }
            <div className={styles.title}>
                {/* <img src={'https://3vj-fe.3vjia.com/layoutai/scene/sceneTilte.svg'} alt="" /> */}
                {t('综合评分')}
                <Rate disabled allowHalf value={GradeStarsDict[rate]} />
            </div>
            {
                layoutScoreList.map((item, index) => {
                    return (
                        <div key={index} className={styles.scoreContent}>
                            <div className={styles.name}>{t(item.name)}</div> 
                            <div className='subContent'>
                                <div className={styles.grade}>{item.ui_format?.includes(UI_FormatType.Grade) && 
                                    <div className={`${GradeClassDict[calculateGrade(item)]}`}>{t(GradeText[GradeClassDict[calculateGrade(item)]])}</div>}
                                </div>
                                <div className={styles.percent}>{calculatePercent(item)}</div>
                                {item.ui_format?.includes(UI_FormatType.Stars) 
                                    && 
                                <div className={styles.start}>
                                <Rate disabled allowHalf value={GradeStarsDict[item.grade]} /></div>}
                            </div>
                        </div>
                    )
                })
            }
            <div className={styles.descBox}>
            {showScoreList.slice(0,3).map((item, index) => {
                return (
                        <div className='item' key={index}>
                            <div className='name'>{t(item.name)}</div>
                            <CheckCircleOutlined />
                        </div>
                    )
                })
            }
            <div className={styles.chartsBox}>
                {chartReady && (
                    <RadarChart width={300} height={200} cx={150} cy={100} outerRadius={80} data={radarData}>
                        <PolarGrid />
                        <PolarAngleAxis dataKey="subject" />
                        {/* <PolarRadiusAxis domain={[0, 100]} /> */}
                        <Radar name="Mike" dataKey="A" stroke="#FFC978" fill="#FFC978" fillOpacity={0.6} />
                    </RadarChart>
                )}
            </div>
          
            </div>
  
        </div>
    )
}

export default LayoutScoreCard;