.hxsearch{
  position: fixed;
  z-index: 9999;
}
.hx_background {
  position: fixed;
  width: 100%;
  height: 100%;
  background: #00000077;
  top: 0;
  left: 0;
}
.demo {
  position: fixed;
  z-index: 99;
}
#searchIframe {
  width: 979px;
  height: 650px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0px 0px 10px 5px rgb(0 0 0 / 10%);
  background-color: #fff;
  border-radius: 4px;
  @media screen and (max-height: 600px) {
    height: 530px;
    width: 890px;
  }
  @media screen and (max-width: 800) {
    height: 530px;
    width: 890px;
  }
}

#searchIframe-page {
  width: 100%;
  height: 99vh;
}

.hide {
  display: none;
}

.upload_hx {
  position: fixed;
  width: 48px;
  height: 90px;
  bottom: calc( 50% - 325px + 36px);
  right :  calc(50% - 490px + 40px);
  border-radius: 24px;
  background: #FFFFFF;
  box-shadow: 0px 4px 12px 0px #00000026;
  text-align: center;
  cursor: pointer;
  @media screen and (max-height: 600px){
    bottom: calc(50% - 297px + 36px);
    right: calc(50% - 482px + 40px);
  }
}
.upload_hx:hover {
  background: #f2f2f277;

}
.upload_huxing_logo {
  font-size: 30px !important;
  margin-top: 16px;
  font-weight: 900;
  color: #8517FC;
  width: 100%;
}
.upload_title {
  margin-top: 8px;
  font-size: 10px;
  font-weight: 700;
}
.my_title_div {
  width: 939px;
  height: 26px;
  position: fixed;
  top: calc( 50% - 335px + 24px);
  left :  calc(50% - 500px);
  line-height: 26px;
  @media screen and (max-height: 600px){
    top: calc(50% - 277px + 24px);
    left: calc(50% - 546px);
  }
}
.right_btns{
  float:right;
  font-size: 16px;
}
.right_btns .btn {
  cursor: pointer;
  padding:2px;
}
.right_btns .btn:hover{
  background: #77777726;
  border-radius: 10px;
}