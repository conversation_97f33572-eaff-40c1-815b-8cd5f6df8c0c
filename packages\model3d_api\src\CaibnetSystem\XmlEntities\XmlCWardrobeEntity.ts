import { I_XmlCWardrobeEntity, I_XmlEntityBase } from "../IWardrobeEntityInterface";
import { XmlEntityBase } from "../XmlEntityBase";
import { XmlStdWardrobeEntity } from "./XmlStdWardrobeEntity";


export class XmlCWardrobeEntity extends XmlStdWardrobeEntity implements I_XmlCWardrobeEntity {
    constructor(data?: Partial<I_XmlCWardrobeEntity>) {
        super(data);
    }
}
XmlEntityBase.Generators["CWardrobeEntity"] = (data:I_XmlEntityBase)=>new XmlCWardrobeEntity(data);