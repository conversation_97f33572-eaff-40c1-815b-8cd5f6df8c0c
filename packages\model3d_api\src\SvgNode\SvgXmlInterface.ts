/**
 * 柜体部件配置 - 带I前缀的TypeScript接口
 * 采用匈牙利命名法，所有接口均以I开头
 */

// ==================== 基础类型 ====================

/** 门板开启方向类型 */
export type TDoorOpeningDirection = 'left' | 'right' | 'up' | 'down' | 'drawer_front' | 'fake_door';

/** 部件基础类型 */
export type TPartType = 'door_board' | 'cabinet_body' | 'shelf' | 'drawer' | 'panel' | string;

// ==================== 核心接口 ====================

/** 完整的尺寸表达式接口 */
export interface IValueExpressions {
  /* 基础尺寸 */
  W?: string; // 宽度表达式 (如 "W-25")
  D?: string; // 深度表达式 (如 "H+10")
  H?: string; // 高度表达式 (如 "MAX(H1,H2)")
  
  /* 定位坐标 */
  PX?: string; // X轴位置 (如 "25[MBKMFX==6];0")
  PY?: string; // Y轴位置 
  PZ?: string; // Z轴位置
  
  /* 旋转角度 */
  RX?: string; // X轴旋转 (如 "-90")
  RY?: string; // Y轴旋转
  RZ?: string; // Z轴旋转
  
  /* 边缘余量 */
  BLM?: string; // 左边缘余量
  BRM?: string; // 右边缘余量
  BUM?: string; // 上边缘余量
  BDM?: string; // 下边缘余量
  BFM?: string; // 前边缘余量
  BBM?: string; // 后边缘余量
  
  /* 扩展字段 */
  [key: string]: string | undefined;
}

/** 完整的实际尺寸接口 */
export interface IPartValues {
  /* 基础尺寸 */
  W?: number | null; // 实际宽度(mm)
  D?: number | null; // 实际深度(mm)
  H?: number | null; // 实际高度(mm)
  
  /* 定位坐标 */
  PX?: number | null; // X轴位置(mm)
  PY?: number | null; 
  PZ?: number | null;
  
  /* 旋转角度 */
  RX?: number | null; // X轴旋转(度)
  RY?: number | null;
  RZ?: number | null;
  
  /* 边缘余量 */
  BLM?: number | null; // 左边缘余量(mm)
  BRM?: number | null;
  BUM?: number | null;
  BDM?: number | null;
  BFM?: number | null;
  BBM?: number | null;
}


/** 变量选项接口 */
export interface IVariableOption {
  value: string | number;
  alias?: string;
}

/** 部件变量接口 */
export interface IVariable {
  name: string;
  value?: number;
  valueExpression?: string | number;
  unitType: 0 | 1 | 2 | 3;
  minValue?: number;
  maxValue?: number;
  minValueExpress?: string | number;
  maxValueExpress?: string | number;
  isShowSize?: 0 | 1;
  alias?: string;
  isGlobal?: 0 | 1;
  ValueArray?: {
    ExpOption: IVariableOption[];
  };
}

/** 子部件接口 */
export interface ISubPart {
  materialId?: number;
  isQuote?: boolean;
  isMovable?: boolean;
  isDeleteAble?: boolean;
  limitName?: string;
  materialMapVoId?: number;
  standardCategory?: string;
  isSplit?: -1 | 0 | 1 | boolean;
  isQuoteSplit?: -1 | 0 | 1 | boolean;
  isOutsourcing?: -1 | 0 | 1 | boolean;
  isNotPutOutCabinetNum?: boolean;
  canAddAccessory?: boolean;
  uid?: string;
  isReversePlane?: 0 | 1;
  ValueExpressions?: IValueExpressions;
  Values?: IPartValues;
  EdgeInfo?: {
    edgeModifyByUser: 0 | 1;
  };
}

/** 柜体部件主接口 */
export interface ICabinetPart {
  areaExpression: string;
  lengthExpression: string;
  name: string;
  nameExpression: string;
  type: TPartType;
  canAddAccessory: boolean;
  limitName: string;
  partNumber: string;
  uid: string;
  standardCategory: string;
  placeRule: string;
  isQuote: boolean;
  isMovable: boolean;
  isDeleteAble: boolean;
  isSplit: boolean;
  isQuoteSplit: boolean;
  isOutsourcing: boolean;
  solidWood: 0 | 1;
  specialVariableGroups: string;
  version: string;
  Variables: {
    Variable: IVariable[];
  };
  RelateInfo: Record<string, unknown>;
  EdgeInfo: {
    fatEdgeStart: -1 | number;
    fatEdgeEnd: -1 | number;
  };
  Part: ISubPart[];
  Logs: {
    userID: string;
    version: string;
  };
}

// ==================== 实用工具 ====================

/** 门方向数字编码 */
type TDoorOpeningDirectionCode = 4 | 6 | 8 | 2 | 5 | -1;

/** 方向编码映射表 */
const DoorDirectionMap: Record<TDoorOpeningDirectionCode, TDoorOpeningDirection> = {
  4: 'left',
  6: 'right',
  8: 'up',
  2: 'down',
  5: 'drawer_front',
  [-1]: 'fake_door'
};

/** 类型守卫 - 检查是否为有效的门方向编码 */
function isDoorOpeningDirectionCode(code: number): code is TDoorOpeningDirectionCode {
  return [4, 6, 8, 2, 5, -1].includes(code);
}

