import { I_EffectPostProcess } from "@layoutai/effects3d";
import { AmbientLight, Box3, BoxGeometry, DirectionalLight, Group, Intersection, LineBasicMaterial, Mesh, MeshBasicMaterial, Object3D, PerspectiveCamera, Raycaster, Scene, Vector2, Vector2Like, Vector3, WebGLRenderer } from "three";
import { GLTFExporter, OrbitControls } from "three/examples/jsm/Addons.js";
import { EdgesBuilder, Model3dDisposer, SimpleGlbParser } from ".";



export class Model3dViewer
{
    protected _parent_div : HTMLDivElement;
    protected _scene : Scene;

    protected _renderer : WebGLRenderer;

    protected _lights_group : Group;

    protected _perspetive_camera : PerspectiveCamera;

    protected _orbit_controls : OrbitControls;

    protected _model_group : Group;

    protected _is_rendering : boolean = false;

    protected _selection_box : Mesh;

    protected _selection_object:Object3D = null;

    private static _instance : Model3dViewer = null;

    protected _raycaster : Raycaster;

    protected _postProcessor : I_EffectPostProcess = null;

    protected _fps_counter : number;

    protected _fps_dom : HTMLDivElement;
    constructor()
    {
        this.init();
    }

    static get instance()
    {
        if(!Model3dViewer._instance)
        {
            return Model3dViewer._instance = new Model3dViewer();
        }
        return Model3dViewer._instance;

    }

    set postProcessor(p:I_EffectPostProcess)
    {
        this._postProcessor = p;
    }
    get postProcessor()
    {
        return this._postProcessor;
    }
    init()
    {
        this._scene = new Scene();

        this._renderer = new WebGLRenderer({
            preserveDrawingBuffer:true,
            antialias : true,
            alpha:true,
        });
        this._renderer.setPixelRatio(1);
        SimpleGlbParser.setRenderer(this._renderer);

        this._lights_group = new Group();

        this._lights_group.add(new AmbientLight(0x222222,1));

        const direction_light = new DirectionalLight(0xffffff,0.7);
        direction_light.position.set(2500,-5000,5000);
        this._lights_group.add(direction_light);

        
        this._scene.add(this._lights_group);

        this._perspetive_camera = new PerspectiveCamera(75,1,100,50000);

        this._perspetive_camera.position.set(0,-5000,1500);
        this._perspetive_camera.up.set(0,0,1);

        this._perspetive_camera.updateMatrix();

        this._orbit_controls = new OrbitControls(this._perspetive_camera, this._renderer.domElement);


        this._model_group = new Group();
        this._scene.add(this._model_group);

        this._selection_box = new Mesh(new BoxGeometry(1,1,1), new MeshBasicMaterial({color:"#147FFA",transparent:true,opacity:0.2}));
        EdgesBuilder.makeEdgesOfObject(this._selection_box,20,new LineBasicMaterial({ color: "#147FFA", linewidth: 0.5 }) );
        this._scene.add(this._selection_box);

        this._raycaster = new Raycaster();
    }


    initFpsDom()
    {
        
    }
    public setSize(width: number, height: number, pixelRatio: number = -1) {

        if(pixelRatio < 0)
        {
            pixelRatio = this._renderer.getPixelRatio();
        }
        if(this._perspetive_camera)
        {
            this._perspetive_camera.aspect = width / height;
            this._perspetive_camera.updateProjectionMatrix();
        }
        this._renderer.setPixelRatio(pixelRatio);
        this._renderer.setSize(width, height);
    
        if(this.postProcessor)
        {
            this.postProcessor.onResize(width,height);
        }
      }
    public onResize(force: boolean = false) {
        if (this._parent_div) {
          let width = this._parent_div.clientWidth;
          let height = this._parent_div.clientHeight;
          this.setSize(width, height);
      }
    }
    bindParentDiv(div:HTMLDivElement)
    {
    
        if(this._renderer && div)
        {
            this._parent_div = div;
            div.appendChild(this._renderer.domElement);

            this.onResize(true);

            const scope = this;
            this._parent_div.addEventListener("resize",()=>{
                scope.onResize(true);
            },{passive:true});
        }
    }

    /**
     *  设置主模型
     */
    setMainGroup(group_node:Group, options:{delete_old_model:boolean}={delete_old_model:false})
    {
        if(!group_node) return;
        if(this._model_group && this._model_group.children)
        {
            if(options.delete_old_model)
            {
                this._model_group.children.forEach((ele)=>Model3dDisposer.deleteObject(ele));
            }
            this._model_group.remove(...this._model_group.children);
        }
        this._model_group.add(group_node);

        this.updateSelectionTarget(this._model_group);
    }


    updateSelectionTarget(object:Object3D)
    {
        let box = new Box3().setFromObject(object);
        let center = box.getCenter(new Vector3());
        let size = box.getSize(new Vector3());

        this._selection_box.scale.copy(size);
        this._selection_box.position.copy(center);
        this._selection_object = object;
    }
    get seletionTarget()
    {
        return this._selection_object;
    }
    get camera()
    {
        return this._perspetive_camera;
    }

    get controls()
    {
        return this._orbit_controls;
    }
    get renderer()
    {
        return this._renderer;
    }
    render()
    {
        if(!this._renderer) return;
        this.controls.update();
        if(this.postProcessor)
        {
            this.postProcessor.makeDirty();
            this.postProcessor.render();
        }
        else{
            this._renderer.render(this._scene,this.camera);
        }
    }

    raySelectMesh(v2:Vector2Like){
        this._raycaster.setFromCamera(new Vector2().copy(v2),this.camera);

        let ints :Intersection[]= this._raycaster.intersectObject(this._model_group);

        return ints.filter((it)=>(it.object as Mesh).isMesh);
    }
    async downloadModel(name:string="model")
    {
        let exporter = new GLTFExporter();
        await new Promise((resolve, reject) => {
            exporter.parse(this._model_group.children[0], (gltf_blob) => {
                const blob = new Blob([gltf_blob as any], { type: "application/octet-stream" });

                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = (name) + '.glb';
                link.click();
                resolve(true);
            },
            (error)=>{
                console.log(error);
            },
            {
                binary: true,
                trs: true,
                onlyVisible:true
            })
        }).catch(e => null);

    }
    startRender() {
        if (!this._scene) return;
        this._is_rendering = true;
        this._renderer.setAnimationLoop(this.render.bind(this));
    }
    stopRender() {
        this._is_rendering = false;
        this._renderer.setAnimationLoop(null);
    }
    get scene()
    {
        return this._scene;
    }
}