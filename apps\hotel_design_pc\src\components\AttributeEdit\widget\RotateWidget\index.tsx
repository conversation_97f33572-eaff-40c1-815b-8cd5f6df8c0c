import { Icon  } from '@svg/antd-cloud-design'
import { InputNumber } from "@svg/antd";
import useStyle from './style';
import { useEffect, useState } from 'react';
import { LayoutAI_App, LayoutAI_Commands } from '@layoutai/layout_scheme';
import { useTranslation } from 'react-i18next';

const RotateWidget = (props: any) => {
    const { t } = useTranslation();
    const {styles} = useStyle();
    const {angle} = props;
    const [currentAngle, setCurrentAngle] = useState<number>(270);

    useEffect(() => {
        setCurrentAngle(Math.round(angle.defaultValue));
    }, [angle]);

    const handleRotate = () => {
        LayoutAI_App.RunCommand(LayoutAI_Commands.RotateFurniture);
    };

    const handleOnStep = (value: number|string, info: { offset: number|string, type: 'up' | 'down' }) => {
        angle.onChange(value);
        setCurrentAngle(value as number);
    };

    const handleOnBlur = (e: any) => {
        let value = e.target.value;
        if (value > 360) {
            value = 360;
        }
        angle.onChange(value);
        setCurrentAngle(value);
    };

    return (
        <div className={styles.root}>
            <label>{t('旋转')}</label>
            <div>
                <Icon iconClass="iconrotate45" className={styles.rotate} onClick={handleRotate}/>
                <InputNumber min={0} max={360} value={currentAngle} suffix={angle.props.suffix} size='small' onStep={handleOnStep} onBlur={handleOnBlur}/>
            </div>
        </div>
    );
};
export default RotateWidget;
