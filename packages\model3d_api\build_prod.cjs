const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const packageJsonPath = path.resolve(__dirname, 'package.json');
const packageJson = require(packageJsonPath);
const packageName = "model3d_api";

const mainFileName = "dist/"+packageName+".js";
const mainFileMapName = "dist/"+packageName+".js.map";
const mainTypesName =  "dist/"+packageName+".d.ts"

// 重新组合版本号
packageJson.main = mainFileName;
packageJson.types =  mainTypesName;

fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));

console.log("npm run viteBuild");
execSync("npm run viteBuild",{stdio:"pipe"});


console.log("copy file to build");
const targetLayoutAiDirNames = ["D:/js_projects/layout_ai/packages/model3d_api/"];

[mainFileName,mainFileMapName,mainTypesName].forEach((filename)=>{
    if(fs.existsSync(filename))
    {
        let targetFileName = filename.replace("dist","build");
        fs.copyFileSync(filename,targetFileName);


        targetLayoutAiDirNames.forEach((targetLayoutDir)=>{
            if(fs.existsSync(targetLayoutDir))
            {
                
                console.log("Exists "+targetLayoutDir, "copy...", filename);
                let targetLayoutFilename = filename.replace("dist",targetLayoutDir);
                fs.copyFileSync(filename,targetLayoutFilename)
                console.log("Exists "+targetLayoutDir, "copy...", filename," Done!");

            }
        })

    }
});
packageJson.main = "dist/src/index.js";
packageJson.types =  "dist/src/index.d.ts"

fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
execSync("npm run build",{stdio:"pipe"});
