import { ENV } from './env';

export * from './env';

const gatewayMap: any = {
  dev: 'https://magiccube-gateway.3weijia.com',
  test: 'https://magiccube-gateway-test.3weijia.com',
  hws: 'https://hws-magiccube-gateway.3weijia.com',
  pre: 'https://pre-magiccube-gateway.3vjia.com',
  prod: 'https://magiccube-gateway.3vjia.com',
};

const openMap: any = {
  dev: 'https://pre-open.3vjia.com',
  test: 'https://pre-open.3vjia.com',
  hws: 'https://pre-open.3vjia.com',
  pre: 'https://pre-open.3vjia.com',
  prod: 'https://open.3vjia.com',
};

const turingMap: any = {
  dev: 'https://pre-open.3vjia.com/api/turing',
  test: 'https://pre-open.3vjia.com/api/turing',
  hws: 'https://pre-open.3vjia.com/api/turing',
  pre: 'https://pre-open.3vjia.com/api/turing',
  prod: 'https://open.3vjia.com/api/turing',
};

const visitorMap: any = {
  dev: 'https://pre-turing-service.3vjia.com',
  test: 'https://pre-turing-service.3vjia.com',
  hws: 'https://pre-turing-service.3vjia.com',
  pre: 'https://pre-turing-service.3vjia.com',
  prod: 'https://turing-service.3vjia.com',
};

const layoutHostDomainMap: any = {
  hws: 'https://hws-layoutai.3weijia.com',
  pre: 'https://pre-layoutai.3vjia.com',
  prod: 'https://layoutai.3vjia.com',
};

// 仿真： hws-open.3weijia.com
// 预发： pre-open.3vjia.com
// 生产：open.3vjia.com
const layoutworkuiServiceMap: any = {
  hws: 'https://hws-open.3weijia.com/',
  pre: 'https://pre-open.3vjia.com/',
  prod: 'https://open.3vjia.com/',
};

const cabinet3DMap: any = {
  hws: 'https://pre-xr.3vjia.com',
  pre: 'https://pre-xr.3vjia.com',
  prod: 'https://xr.3vjia.com',
};

export const gatewayUrl = gatewayMap[ENV];
export const openUrl = openMap[ENV];  //图灵接口只有预发和生产
export const turingUrl = turingMap[ENV];  //图灵接口只有预发和生产
export const layoutUrl = layoutHostDomainMap[ENV];
export const layoutworkuiServiceUrl = layoutworkuiServiceMap[ENV];
export const visitorUrl = visitorMap[ENV];
export const cabinet3DUrl = cabinet3DMap[ENV];
