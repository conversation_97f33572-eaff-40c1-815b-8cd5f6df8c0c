import React, { useContext } from 'react';
import UserStore from './userStore';
import HomeStore from './homeStore';
import LayoutStore from './layoutStore';
import DesignStore from './designStore';
import TrainingStore from './trainingStore';
import SchemeStatusStore from './schemeStatusStore';
import AiLightStore from './AiLightStore';

class RootStore {
    userStore: UserStore;
    homeStore: HomeStore;
    layoutStore: LayoutStore;
    designStore: DesignStore;
    trainingStore: TrainingStore;
    schemeStatusStore: SchemeStatusStore;
    aiLightStore: AiLightStore;

    constructor() {
        // 对子模块进行实例化操作
        this.userStore = new UserStore()
        this.homeStore = new HomeStore()
        this.layoutStore = new LayoutStore()
        this.designStore = new DesignStore()
        this.trainingStore = new TrainingStore()
        this.schemeStatusStore = new SchemeStatusStore()
        this.aiLightStore = new AiLightStore()
    }
}

const rootStore = new RootStore();
const context = React.createContext(rootStore)

const useStore = () => useContext(context)

export { useStore }
