import { I_LayoutOptRule, TFeatureShape, TGraphBasicConfigs, TGroupTemplate } from "@layoutai/layout_scheme";
import { ZRect } from "@layoutai/z_polygon";
import { IRuleReferTargets, TLayoutOptimizer } from "./TLayoutOptimizer";


export class TLayoutOptimizerWithRules
{
    /**
     * 一: 吸附优化: 能吸附到背靠墙则吸附; 能贴墙放缩则贴墙拉伸;  能自适应调整深度, 则自适应调整深度
     * ../../../../../..param feature_shape  
     * ../../../../../..param group_templates 
     */
    static optimize_groups_with_opt_rules(feature_shape:TFeatureShape, group_templates:TGroupTemplate[]
        ,room_name:string,consider_placeholder:boolean = true)
    {
        let group_config = TGraphBasicConfigs.MainGroupFigureConfigs[room_name];
        if(!group_config) return;

        // 1、先对对组合进行优先级排序
        let get_template_order = (a:TGroupTemplate)=>{
            let config = group_config[a.group_space_category];
            if(!config) return 0;
            return config.place_order;
        }
        group_templates.sort((a,b)=>{
            return get_template_order(b) - get_template_order(a);
        });



        let w_poly = feature_shape._w_poly;  

        let wall_edges = [...feature_shape._w_poly.edges];



        // 2、先把门内区域给裁剪掉
        wall_edges = TLayoutOptimizer.pre_sub_room_edges_inwall(feature_shape,wall_edges,room_name);



        // TsAI_app.log(group_templates);

        // 3、依次优化

        let main_rect_shape =feature_shape?._room?.max_R_shape;
        let main_rect = main_rect_shape?main_rect_shape.getRect():null;
        let placeholder_rects : ZRect[] = [];
        for(let group_template of group_templates)
        {
            let refer_targets : IRuleReferTargets = TLayoutOptimizer.fill_refer_targets_of_group(wall_edges,group_template,room_name,placeholder_rects);
            if(!refer_targets) continue;
            refer_targets.main_rect = main_rect;
            let config =  group_template.getFigureGroupConfigs(room_name);
            let rules = config.opt_rules || null;
            let rect = group_template._target_rect;

            for(let rule of rules)
            {            
                if(rule.ruleType == "BackToTarget" || rule.ruleType =="LengthByTarget")
                {
                    refer_targets.back_group_rect = TLayoutOptimizer.getTargetRect(rule,feature_shape,group_templates, rect.back_center);
                }
                else{
                    refer_targets.back_group_rect = null;
                }

                TLayoutOptimizerWithRules.optimize_rect_with_opt_rule(refer_targets,rect,rule);


            }
            // TsAI_app.log(group_template.group_space_category,group_template._target_rect.w,backwall_edge?.length,[...placeholder_rects]);

            if(config.is_placeholder === false) continue; // 不是占位符

            if(!consider_placeholder) continue;

            // 如果是占位符, 则要在备选边内, 裁剪对应的边

            let t_rect = rect.clone();
            let align_side_dist = 100;
            if(config.placeholder_expand)
            {
                if(config.placeholder_expand?.front_extend) // 向前扩展
                {
                    t_rect._h += config.placeholder_expand?.front_extend || 0;
                    t_rect.updateRect();
                }

                if(config.placeholder_expand?.wall_side_extend)
                {
                    let wall_side_extend = config.placeholder_expand?.wall_side_extend || 0;

                    align_side_dist = Math.max(align_side_dist,wall_side_extend);

                }
            }

            TLayoutOptimizer.sub_edges_by_rect(t_rect, wall_edges, align_side_dist);
            placeholder_rects.push(t_rect);
        }
    }


    static check_rect_with_opt_rule(refer_targets:IRuleReferTargets, rect:ZRect, rule:I_LayoutOptRule)
    {
        if(rule.ruleType === "BackToTarget")
        {
            let back_group_rect = refer_targets.back_group_rect;
            let back_to_target_dist = -1; // 找不到
            if(back_group_rect) 
            {
                if(Math.abs(back_group_rect.nor.dot(rect.nor)) > 0.9)
                {
                    let dist = Math.abs(Math.abs(rect.project(back_group_rect.rect_center,true).y) - back_group_rect.h/2);
                    back_to_target_dist = dist;
                }

            }

            let check_val:boolean = eval(rule.params.check_back_target_dist) || false;

            return check_val;
        }

        return true;
    }

      
    static optimize_rect_with_opt_rule(refer_targets : IRuleReferTargets, rect:ZRect, rule:I_LayoutOptRule)
    {
        if(rule.ruleType == "BackToWall")
        {
            
            this.optimize_rect_with_BackToTarget_rule(refer_targets,rect,rule);
        }
        else if(rule.ruleType == "LengthByWall")
        {
            this.optimize_rect_with_LengthByTarget_rule(refer_targets,rect,rule);
        }
        else if(rule.ruleType == "SideToWall")
        {
            this.optimize_rect_with_SideToWall_rule(refer_targets,rect,rule);
        }
        else if(rule.ruleType == "DepthExpand")
        {
            this.optimize_rect_with_Depth_rule(refer_targets,rect,rule);
        }
        else if(rule.ruleType == "BackToTarget")
        {
            this.optimize_rect_with_BackToTarget_rule(refer_targets,rect,rule);

        }
        else if(rule.ruleType == "LengthByTarget")
        {
            
            this.optimize_rect_with_LengthByTarget_rule(refer_targets,rect,rule);

        }
        else if(rule.ruleType == "AlignFront")
        {
            this.optimize_rect_with_FrontTraget_rule(refer_targets,rect,rule);
        }
        else if(rule.ruleType == "FaceAlignByMainRect")
        {
            this.optimize_rect_with_FaceMainRect_rule(refer_targets,rect,rule);
        }
        else if(rule.ruleType === "NearTarget")
        {

        }
        
    }
    static optimize_rect_with_NearTarget_rule(refer_edges : IRuleReferTargets, rect:ZRect,rule:I_LayoutOptRule)
    {
        


    }
    static optimize_rect_with_Depth_rule(refer_edges : IRuleReferTargets, rect:ZRect,rule:I_LayoutOptRule)
    {
        let w_edge = refer_edges.back_w_edge || rect.backEdge;
        /**
         *  a_wd 背靠墙到正对墙的距离
         */
        let a_wd = (refer_edges?.front_w_edge)? Math.abs(refer_edges.front_w_edge.center.clone().sub(w_edge.center).dot(w_edge.nor)) : refer_edges?.side_w_edge?.length || 10000;
        /**
         *  a_fd 物体的back_center到正对墙的距离
         */
        let a_fd = (refer_edges.front_w_edge)? refer_edges.front_w_edge.center.clone().sub( rect.back_center).dot(rect.nor) : a_wd;
        /**
         *  a_td 物体到其依赖的物体（比如 地毯->床) 前面端点的距离
         */
        let a_td = a_fd;
        if(refer_edges.back_group_rect)
        {
            a_td = refer_edges.back_group_rect.front_center.clone().sub(rect.back_center).dot(rect.nor);
        }
        let target_depth_val = eval(rule?.params?.target_depth_val || '0');

        rect._h = target_depth_val;

        rect.updateRect();


    }
    static optimize_rect_with_BackToTarget_rule(refer_targets : IRuleReferTargets, rect:ZRect,rule:I_LayoutOptRule)
    {
        if(refer_targets.back_group_rect && rule.params.target_alignment == "align-center")
        {
            rect._h = rect._w;
            rect.rect_center = refer_targets.back_group_rect.rect_center;
            return;
        }
        let w_edge = refer_targets.back_w_edge;
        if(!w_edge) return;
        let dist = w_edge.projectEdge2d(rect._back_center).y;

        let adsorb_backwall_dist = (parseFloat(rule?.params?.adsorb_back_target_dist)||0);

        // console.log(rect.ex_prop, adsorb_backwall_dist, dist);
        if(Math.abs(dist) > adsorb_backwall_dist)
        {
            return;
        }

        // TsAI_app.log(w_edge,rect.ex_prop);
                    
        // 对贴墙方向相反的情况, 要做一个处理
        if(rect.nor.dot(w_edge.nor) > 0)  // 组合区域朝向对外了
        {
            let rect_center = rect.rect_center;
            let dv = rect.dv;
            rect.nor.negate();
            rect.u_dv = dv;
            rect.rect_center = rect_center;

        }
        let a_wl = w_edge.length;  // 有可能eval里面会用到

        let a_wd = (refer_targets?.front_w_edge)? Math.abs(refer_targets.front_w_edge.center.clone().sub(w_edge.center).dot(w_edge.nor)) : refer_targets?.side_w_edge?.length || 10000;

        let b_wd = -dist;
        let r_l = rect.w;
        let r_d = rect.h;

        let target_l = -1;
        let target_d = -1;

        if(refer_targets.back_group_rect)
        {
            target_l = refer_targets.back_group_rect.w;
            target_d = refer_targets.back_group_rect.h;

        }
        let target_backwall_dist = eval(rule?.params?.target_back_dist_val || '0');


        // rect._back_center.add(nor_offset);

        let  pp = w_edge.projectEdge2d(rect._back_center);

        pp.y = -target_backwall_dist; // 要反向设置
        let target_center = w_edge.unprojectEdge2d(pp);
        rect._back_center.copy(target_center);

        if(refer_targets.back_group_rect && rule.params.target_alignment == "align-center")
        {
            rect._h = rect._w;
            rect.rect_center = refer_targets.back_group_rect.rect_center;
        }
        if(refer_targets.back_group_rect && rule.params.target_alignment == "align-back_center")
        {
            let pp = refer_targets.back_group_rect.project(rect.back_center);
            pp.x = 0;
            let pos = refer_targets.back_group_rect.unproject(pp);
            rect.back_center = pos;
            rect.updateRect();
        }
        if(refer_targets.back_group_rect && rule.params.target_alignment == "attached_center")
        {
            rect._w = refer_targets.back_group_rect.w;
            // TsAI_app.log(rect._w,refer_targets.back_group_rect.w,rect);

            if(rect.nor.dot(refer_targets.back_group_rect.nor) > 0)
            {
                rect.back_center = refer_targets.back_group_rect.front_center;
            }
            else{
                rect.back_center = refer_targets.back_group_rect.back_center;

            }

        }
        rect.updateRect();
    }

    static optimize_rect_with_LengthByTarget_rule(refer_targets : IRuleReferTargets,rect:ZRect,rule:I_LayoutOptRule)
    {
        let w_edge = refer_targets.back_w_edge;
        if(!w_edge) return;
        let a_wl = w_edge.length;
        let f_wl = refer_targets.front_w_edge?.length || a_wl;
        let r_l = rect.w;
        let r_d = rect.h;
        let ratio = rect.w / a_wl;
        let target_l = -1;
        let target_d = -1;
        let t_w_len = a_wl;

        if(refer_targets.back_group_rect)
        {
            target_l = refer_targets.back_group_rect.w;
            target_d = refer_targets.back_group_rect.h;
            let t_dist = w_edge.projectEdge2d(refer_targets.back_group_rect.back_center).x;

            t_w_len = Math.min(t_dist,w_edge.length - t_dist) * 2;

        }
        let adsorb_length_ratio = eval(rule?.params?.adsorb_length_ratio || '1');


        if(ratio < adsorb_length_ratio) {
            // console.log(rect.ex_prop);
            return;
        }
        let check_attached_wall_dist = eval(rule?.params?.check_attached_wall_dist || '-1');


        let pp = w_edge.projectEdge2d(rect._back_center);
        let check_attached_wall = true;
        if(check_attached_wall_dist > 0)
        {
           let dist =  pp.y;
           check_attached_wall = Math.abs(dist) <= check_attached_wall_dist;

        }

        let margin_length = eval(rule?.params?.margin_length_val || '0');

        let target_length_val = eval(rule?.params?.target_length_val || ''+a_wl);


    
        rect._w = target_length_val;


        rect.updateRect();

        if(refer_targets.back_group_rect)
        {

            if(check_attached_wall)
            {


                pp.x = w_edge.projectEdge2d(refer_targets.back_group_rect.back_center).x;
                let ll = pp.x - target_length_val/2;
                let rr = pp.x + target_length_val/2;
                if(ll < margin_length/2)
                {
                    ll = margin_length/2;
                }
                if(rr > w_edge.length - margin_length/2)
                {
                    rr = w_edge.length - margin_length/2;
                }

                rect._w = rr - ll;
                pp.x = (ll+rr) / 2;
                rect._back_center.copy(w_edge.unprojectEdge2d(pp));
                rect.updateRect();
            }
            else{
            }


            return;
        }
                
        // 自动调整在边内, 端点侧与其对齐

        if(target_length_val > (w_edge.length -margin_length-1.))
        {
            let target_pp = {x: w_edge.length/2, y:pp.y};



            let target_center = w_edge.unprojectEdge2d(target_pp);
    
            rect._back_center.copy(target_center);
            rect.updateRect();
        }


    }
    static optimize_rect_with_SideToWall_rule(refer_edges : IRuleReferTargets, rect: ZRect, rule:I_LayoutOptRule)
    {
        let w_edge = refer_edges.side_w_edge;
        if(!w_edge) return;
        
        let dist = (-w_edge.projectEdge2d(rect._back_center).y - rect.w/2);

        let adsorb_sidewall_dist = (parseFloat(rule?.params?.adsorb_sidewall_dist)||0);

        if(Math.abs(dist) > adsorb_sidewall_dist)
        {
            return;
        }

        let a_wl = w_edge.length;  // 有可能eval里面会用到

        let target_sidewall_dist = eval(rule?.params?.target_sidewall_dist_val || '0');

        let t_back_edge = refer_edges.back_w_edge;
        if(t_back_edge)
        {
            let e_dist = -w_edge.projectEdge2d(t_back_edge.center).y;
            e_dist = e_dist - t_back_edge.length / 2;
            if(e_dist > 0)
            {
                target_sidewall_dist += e_dist;
            }
     
        }

        // rect._back_center.add(nor_offset);

        let  pp = w_edge.projectEdge2d(rect._back_center);

        pp.y = -(target_sidewall_dist + rect.w/2); // 要反向设置
        let target_center = w_edge.unprojectEdge2d(pp);
        rect._back_center.copy(target_center);
        rect.updateRect();
    }

    static optimize_rect_with_FrontTraget_rule( refer_edges: IRuleReferTargets, rect: ZRect, rule:I_LayoutOptRule)
    {
        if(!refer_edges.front_w_edge) return;

        if(rule.params.target_alignment =="align-center")
        {
            let front_center = refer_edges.front_w_edge.center;

            let t_dist = front_center.clone().sub(rect.back_center).dot(rect.dv);

            let adsorb_t_dist = eval(rule.params.adsorb_front_target_dist || '1000');

            if(Math.abs(t_dist) > adsorb_t_dist) return;
            rect._back_center.add(rect.dv.clone().multiplyScalar(t_dist));

            rect.updateRect();
        }




    }

    static optimize_rect_with_FaceMainRect_rule( refer_edges: IRuleReferTargets, rect: ZRect, rule:I_LayoutOptRule)
    {
        if(!refer_edges.main_rect) return;

        if(rule.params.target_alignment =="align-center")
        {
            let front_center = refer_edges.main_rect.rect_center;

            let t_dist = front_center.clone().sub(rect.back_center).dot(rect.dv);

            let adsorb_t_dist = eval(rule.params.adsorb_front_target_dist || '1000');

            if(Math.abs(t_dist) > adsorb_t_dist) return;
            rect._back_center.add(rect.dv.clone().multiplyScalar(t_dist));

            rect.updateRect();
        }




    }
}