import { I_XmlCBoardPartEntity, I_XmlCWhSpacePartComponent } from "../IWardrobeEntityInterface";
import { XmlEntityBase } from "../XmlEntityBase";

export class XmlCBoardPartEntity extends XmlEntityBase implements I_XmlCBoardPartEntity {
    isMovableB?: boolean;
    isFunctorSyncB?: boolean;
    isQuoteB?: boolean;
    isBoardPointEditUnlimitedB?: boolean;
    isQuoteSplitN?: number;
    isOutsourcingN?: number;
    ocTypeS?: string;
    standardCategoryS?: string;
    isSplitN?: number;
    isSetThI_XmlCkN?: number;
    canReplaceMaterialB?: boolean;
    canSetHandleSizeB?: boolean;
    Visible?: {
        MiddleVariable?: {
            nameS?: string;
            valueS?: string;
            valueExpressionS?: string;
        };
    };
    CWhMaterialComponent?: {
        isReversePlaneN?: number;
        isRotateN?: number;
        materialMapVoIdS?: string;
    };

    EdgeComponent?: {
        fatEdgeStartN?: number;
        fatEdgeEndN?: number;
        edgeBandModeN?: number;
        edgeBandPlanInfoS?: string;
        edgeBandArrS?: string;
        edgeModifyByUserN?: number;
    };
    CWhTagComponent?: {
        CWhTagFrameSpace?: Record<string, unknown>;
        CWhTagBackBoard?: {
            bbEnumS?: string;
            backDataUidS?: string;
        };
    };

    constructor(data?: Partial<I_XmlCBoardPartEntity>) {
        super(data);

        this.EdgeComponent = data.EdgeComponent || {};
        this.CWhTagComponent = data.CWhTagComponent || {};

        this.CWhMaterialComponent = data.CWhMaterialComponent || {};
        this.Visible = data.Visible || {};
        this.ocTypeS = data.ocTypeS || "";
        this.standardCategoryS = data.standardCategoryS || "";
        this.isFunctorSyncB = data.isFunctorSyncB ?? false;
        this.canReplaceMaterialB = data.canReplaceMaterialB ?? false;
        this.canSetHandleSizeB = data.canSetHandleSizeB ?? false;     
        this.isQuoteB = data.isQuoteB ?? false;
        
    }
}
XmlEntityBase.Generators["CBoardPartEntity"] = (data:I_XmlCBoardPartEntity)=>new XmlCBoardPartEntity(data);