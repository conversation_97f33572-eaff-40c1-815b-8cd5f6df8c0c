import { FSM } from '../FSM';
import { Idle2DState } from '../states/2d/Idle2DState';
import { Init2DState } from '../states/2d/Init2DState';
import { FSMNames, Design2DStates, ANY_STATE } from '../const/FSMConst';
import { DrawRectState } from '../states/2d/DrawRectState';
/**
 * 2D设计FSM
 */
export class Design2DFSM extends FSM {
  constructor() {
    super(FSMNames.DESIGN_2D);
    
    // 添加状态
    this.addState(new Init2DState(Design2DStates.INIT2D));
    this.addState(new Idle2DState(Design2DStates.IDLE2D));
    this.addState(new DrawRectState(Design2DStates.DRAW_RECT));
    
    // 设置默认状态
    this.setDefaultState(Design2DStates.INIT2D);
    
    // 配置状态切换关系
    this.addTransition(Design2DStates.INIT2D, Design2DStates.IDLE2D);
    this.addTransition(Design2DStates.IDLE2D, Design2DStates.INIT2D);
    // 通配符转换 - 让空闲状态成为"万能状态"
    // 从任何状态都可以返回到空闲状态
    this.addTransition(ANY_STATE, Design2DStates.IDLE2D);
    // 从空闲状态可以切换到任何状态
    this.addTransition(Design2DStates.IDLE2D, ANY_STATE);

    // 状态树切换示例：
    // 1. 同级切换：mainFSM.transitionTo('Design2D') - 切换到2D设计模式
    // 2. 向下切换：mainFSM.transitionTo('idle2d') - 直接切换到2D空闲状态（树遍历）
    // 3. 跨树切换：mainFSM.transitionTo('idle3d') - 从2D直接切换到3D空闲状态（简化切换）
  }
} 