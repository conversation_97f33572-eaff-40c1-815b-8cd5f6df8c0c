# FSM状态监听的优雅解决方案

## 问题背景
原来使用定时器轮询来监听FSM状态变化：
```typescript
// ❌ 不优雅的定时器轮询方式
const interval = setInterval(() => {
    const newFSMState = appContext.mainFSM.getCurrentLeafState().name;
    if (newFSMState !== currentFSMState) {
        setCurrentFSMState(newFSMState);
    }
}, 100);
```

## 解决方案

### 方案1：事件监听器模式 ⭐⭐⭐
最直接的解决方案，在FSM中添加事件系统。

#### 使用方式：
```typescript
// 添加监听器
const handleStateChange = (event: StateChangeEvent) => {
    console.log('状态变化:', event);
    setCurrentFSMState(event.toState);
};

appContext.mainFSM.addStateChangeListener(handleStateChange);

// 清理监听器
appContext.mainFSM.removeStateChangeListener(handleStateChange);
```

#### 优点：
- 简单直接，性能好
- 事件驱动，实时响应
- 无需额外依赖

### 方案2：React自定义Hook ⭐⭐⭐⭐
更React化的解决方案，封装成自定义Hook。

#### 使用方式：
```typescript
// 在组件中使用
const { currentState } = useFSMState();

// 监听特定FSM
const design2DState = useSpecificFSMState('design2d');
```

#### 优点：
- 符合React最佳实践
- 自动处理生命周期
- 可复用性强
- 代码更简洁

### 方案3：RxJS Observable ⭐⭐⭐⭐⭐
最强大的响应式解决方案，提供丰富的操作符。

#### 使用方式：
```typescript
// 基础使用
const { currentState } = useFSMObservable();

// 监听特定状态进入
useStateEnter('idle2d', (event) => {
    console.log('进入2D空闲状态');
});

// 监听状态模式
useStatePattern(/^design2d/, (event) => {
    console.log('进入2D设计相关状态');
});

// 监听所有状态变化
useAllStateChanges((event) => {
    console.log('状态变化:', event);
});
```

#### 优点：
- 功能最强大
- 支持复杂的状态监听逻辑
- 内置防抖、去重等功能
- 可组合性强

## 实际应用示例

### Home组件改造前后对比

#### 改造前（定时器轮询）：
```typescript
useEffect(() => {
    const checkFSMState = () => {
        const newFSMState = appContext.mainFSM.getCurrentLeafState().name;
        if (newFSMState !== currentFSMState) {
            setCurrentFSMState(newFSMState);
        }
    };
    const interval = setInterval(checkFSMState, 100);
    return () => clearInterval(interval);
}, [currentFSMState]);
```

#### 改造后（自定义Hook）：
```typescript
const { currentState: currentFSMState } = useFSMState();
```

### 高级用法示例

#### 监听特定状态转换：
```typescript
// 监听从任何状态到idle2d的转换
useStateEnter('idle2d', (event) => {
    console.log(`从 ${event.fromState} 切换到 idle2d`);
    // 执行特定逻辑
});

// 监听离开drawRect状态
useStateExit('drawRect', (event) => {
    console.log('退出绘制模式，清理资源');
    // 清理绘制相关资源
});
```

#### 复杂状态监听：
```typescript
// 监听所有2D相关状态
useStatePattern(/^(idle2d|init2d|drawRect)$/, (event) => {
    console.log('2D状态变化:', event.toState);
    // 更新2D相关UI
});

// 监听跨FSM切换
useAllStateChanges((event) => {
    if (event.fromState?.includes('2d') && event.toState.includes('3d')) {
        console.log('从2D切换到3D');
        // 执行模式切换逻辑
    }
});
```

## 性能对比

| 方案 | CPU占用 | 内存占用 | 响应延迟 | 代码复杂度 |
|------|---------|----------|----------|------------|
| 定时器轮询 | 高 | 中 | 0-100ms | 低 |
| 事件监听器 | 低 | 低 | <1ms | 低 |
| React Hook | 低 | 低 | <1ms | 中 |
| RxJS Observable | 低 | 中 | <1ms | 中 |

## 推荐使用

1. **简单项目**：使用方案1（事件监听器）
2. **React项目**：使用方案2（自定义Hook）
3. **复杂状态逻辑**：使用方案3（RxJS Observable）

## 迁移指南

1. 移除所有定时器轮询代码
2. 选择合适的方案
3. 更新组件代码
4. 测试状态监听是否正常工作

所有方案都已实现并可以立即使用！
