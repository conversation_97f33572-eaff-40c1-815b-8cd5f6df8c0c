import SchemeList from '@/components/LeftPanel/components/SchemeList/schemeList';
import useCommonStyles from "../common_style/index";
import { useEffect, useState } from 'react';
import { EventName, LayoutAI_App } from '@layoutai/layout_scheme';

const LeftSchemePanel: React.FC = ()=>{

    const common_styles = useCommonStyles().styles;
    const [isDisplay, setIsDisplay] = useState<boolean>(false);


    const object_id = "LeftSchemePanel";
    const register_events = ()=>{
        LayoutAI_App.on(EventName.ShowSchemeTestingLeftPanel, (t:boolean)=>{
            setIsDisplay(t);
        });
    }

    register_events();

    
    useEffect(()=>{
    },[]);

    return (
      <div  className={common_styles.leftPanel} style={{display:isDisplay?"block":"none"}}>
        {isDisplay && <div className='leftPanel'>
            <SchemeList width={400} showSchemeName={true}></SchemeList>
        </div>
        }
      </div>
    )
}

export default LeftSchemePanel;