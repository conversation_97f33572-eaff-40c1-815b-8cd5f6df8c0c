import { I_XmlCWhSpaceEntity, I_XmlEntityBase } from "../IWardrobeEntityInterface";
import { XmlEntityBase } from "../XmlEntityBase";
import { XmlSpace } from "./XmlSpace";


export class XmlCWhSpaceEntity extends XmlSpace implements I_XmlCWhSpaceEntity {

    layoutDvoIdS?: string;
    layoutNameS?: string;
    layoutVerS?: string;


    constructor(data?: Partial<I_XmlCWhSpaceEntity>) {
        super(data);
        this.layoutDvoIdS = data.layoutDvoIdS || "";
        this.layoutNameS = data.layoutNameS ?? "";
        this.layoutVerS = data.layoutVerS ?? "";
    }
}

XmlEntityBase.Generators["CWhSpaceEntity"] = (data:I_XmlEntityBase)=>new XmlCWhSpaceEntity(data);