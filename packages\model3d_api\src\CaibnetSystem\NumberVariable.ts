import { IExpOption, INumberVariable } from "./IWardrobeEntityInterface";


export class NumberVariable implements INumberVariable {
    nameS?: string;
    valueN?: number;
    valueExpressionS?: string;
    isUserChangedB?: boolean;
    isShowSizeB?: boolean;
    aliasS?: string;
    minValueN?: number;
    maxValueN?: number;
    minValueExpressS?: string;
    maxValueExpressS?: string;
    isShowInLayoutB?: boolean;
    visibleExpressionS?: string;
    remarkS?: string;
    isGlobalB?: boolean;
    ExpOption?: IExpOption[];

    constructor(data?: Partial<INumberVariable>) {
        if (data) {
            Object.assign(this, data);
        }
    }
}
