import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ token, css }) => {
  // color: ${token.colorPrimary};
  return {
    root: css`

      position: fixed;
      right: 0;
      z-index: 99;
      height: calc(100vh - 50px);
      background-color: #fff;
      box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.12);
      overflow-y: scroll;
    `,
    line: css`
      position: absolute;
      top: 0;
      bottom: 0;
      left: -4px;
      width: 6px; /* 可拖动区域的宽度 */
      /* background-color: red; */
      cursor: col-resize;
      z-index: 998; 
    `,
    rootTitle: css`
      border-radius: 2px 0px 0px 0px;
      background: #F2F3F5;
      width: 100%;
      height: 40px;
      display: flex;
      align-items: center;
      padding-left: 16px;
      margin-bottom: 10px;
      span{
        color: #282828;
        font-family: PingFang SC;
        font-weight: semibold;
        font-size: 16px;
        line-height: 24px;
        font-weight: 600;
      }
    `
  }
});
