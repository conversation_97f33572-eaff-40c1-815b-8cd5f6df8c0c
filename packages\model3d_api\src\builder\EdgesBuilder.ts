import { EdgesGeometry, LineSegments, Material, Mesh, Object3D } from "three";
import { MaterialManager } from "..";

const keyIsSegments = "IsSegments";

export class EdgesBuilder
{
    
    static cleanEdgesOfObject(model_group:Object3D)
    {
        let lineSegments : LineSegments[] = [];
        model_group.traverse((node)=>{
            let line = node as LineSegments;
            if(line.isLineSegments || line.userData[keyIsSegments])
            {
                lineSegments.push(line);
            }
        });
        lineSegments.forEach((line)=>{
            line.parent.remove(line);
            line.geometry.dispose();
            if(line.material !== MaterialManager.box_edges_material)
            {
                (line.material as Material).dispose();
            }
        })
        
    }
    static makeEdgesOfObject(model_group:Object3D,thresholdAngleDegree:number=20, lineMaterial:Material = null)
    {
        EdgesBuilder.cleanEdgesOfObject(model_group);
        model_group.traverseVisible((node)=>{
            let mesh = node as Mesh;
            if(!mesh.isMesh) return;

            let edgeGeometry = new EdgesGeometry(mesh.geometry,thresholdAngleDegree);
            lineMaterial = lineMaterial || MaterialManager.box_edges_material;
            let edgeMesh = new LineSegments(edgeGeometry,lineMaterial);
            edgeMesh.userData[keyIsSegments] = true;
            mesh.add(edgeMesh);

        })

    }
}