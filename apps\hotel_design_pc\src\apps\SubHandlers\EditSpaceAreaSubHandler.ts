

import { AI_PolyTargetType } from "@layoutai/basic_data";
import { EventName, LayoutAI_App, LayoutAI_Commands, TSubSpaceAreaEntity, roomSubAreaService } from "@layoutai/layout_scheme";
import { Vector3 } from "three";
import { I_MouseEvent, ZRect } from "@layoutai/z_polygon";
import { AI2BaseModeHandler } from "../AI2BaseModeHandler";
import { CadBaseSubHandler } from "./CadBaseSubHandler";



export class EditSpaceAreaSubHandler extends CadBaseSubHandler
{
    _area_entity : TSubSpaceAreaEntity;

    // 记录初始状态，包括分区实体和其内部家具的初始位置
    _initial_state: {
        area_rect: ZRect;
        furniture_positions: Map<string, Vector3>;
    } | null = null;

    _src_rect : ZRect;
    _start_pos : Vector3;
    _end_pos : Vector3;


    _boundary_dist : number = 100;
    static handler_name : string =  "EditSpaceAreaSubHandler";
    private _isLongPress: boolean = false;
    constructor(cad_mode_handler: AI2BaseModeHandler) {
        super(cad_mode_handler);
        this.name = EditSpaceAreaSubHandler.handler_name;
        this._area_entity = null;
    }
    enter(state?: number): void {
        console.log("Enter "+this.name);

        LayoutAI_App.emit_M(EventName.SubHandlerChanged,{is_default:false, name:this.name});

        if(this.selected_target.selected_entity 
            && this.selected_target.selected_entity.type === AI_PolyTargetType.RoomSubArea)
        {
            this._area_entity = this.selected_target.selected_entity as any;

            this._src_rect = this._area_entity.rect.clone();

            if(this._cad_mode_handler._last_mouse_down_event)
            {
                this.onmousedown(this._cad_mode_handler._last_mouse_down_event);
            }

        }
        else{
            LayoutAI_App.RunCommand(LayoutAI_Commands.LeaveSubHandler);
        }

    }
    leave(state?: number): void {
        console.log("Leave "+this.name);

        this._area_entity = null;
        this._src_rect = null;
        this._initial_state = null;
        LayoutAI_App.emit_M(EventName.SubHandlerChanged,{is_default:true, name:this.name});

    }

    onmousedown(ev: I_MouseEvent): void {
        this._start_pos = new Vector3(ev.posX,ev.posY,0);
        this._end_pos = new Vector3(ev.posX,ev.posY,0);
        this._src_rect = this._area_entity.rect.clone();
        this._isLongPress = false;
        // 记录初始状态，包括分区实体和内部家具的位置
        this._initial_state = {
            area_rect: this._area_entity.rect.clone(),
            furniture_positions: new Map()
        };
        // 记录所有家具的初始位置
        if (this._area_entity && this._area_entity.furniture_entities) {
            this._area_entity.furniture_entities.forEach(entity => {
                if (entity && entity.rect) {
                    this._initial_state.furniture_positions.set(entity._uuid, entity.rect.rect_center.clone());
                }
            });
        }
        this.EventSystem.emit_M(EventName.SelectingTarget, this._area_entity, null);
        this.update();
    }
    
    onmousemove(ev: I_MouseEvent): void {
        if(this._start_pos)
        {
            this._end_pos = new Vector3(ev.posX,ev.posY,0);
            this.transformByMouseMoving();
            let curPos = new Vector3(ev.posX,ev.posY,0);
            if(curPos.distanceTo(this._start_pos) > this._longPressThreshold)
            {
                this._isLongPress = true;
            }
        }
        this._cad_mode_handler._is_moving_element = true;
    }

    onmouseup(ev: I_MouseEvent): void {
        this._start_pos = null;
        this._cad_mode_handler._is_moving_element = false;
        if (this.selected_target.selected_transform_element) {
            let info = this.selected_target.selected_transform_element.updateOperaionInfo(this.manager);
            if (info) {
                this.manager.appendOperationInfo(info);
            }
            this.selected_target.selected_transform_element.recordOriginRect(null);
            this.selected_target.selected_transform_element.recordOriginCombinationRect(null);
        }
        let is_changed = false;
    
        if(!this._isLongPress)
        {
            // 点击
            // this.updateSelectedRect(new Vector3(ev.posX, ev.posY, 0));
            // this.EventSystem.emit_M(EventName.SelectingTarget, this.selected_target.selected_entity, ev._ev);
        }else{
            // 移动
            if(this._area_entity)
            {
                this._area_entity._updateSubAreaByParentRoomEntity();
                this._area_entity.updateSpaceAreaTRoom();

                is_changed = !this._area_entity.rect.checkSamePoly(this._src_rect,10);
                this.EventSystem.emit_M(EventName.SelectingTarget, this._area_entity, ev._ev);
            }
        }
        roomSubAreaService.updateSubAreaLayoutScheme(this._area_entity,is_changed);
        this._initial_state = null;
        LayoutAI_App.RunCommand(LayoutAI_Commands.LeaveSubHandler);
        super.onmouseup(ev);
    }

    protected transformByMouseMoving()
    {
        if(this._end_pos && this._start_pos && this._area_entity)
        {
            // 计算偏移量
            // let offset = this._end_pos.clone().sub(this._start_pos);
            // if (this._initial_state) {
            //     // 移动分区实体
            //     const newRect = this._initial_state.area_rect.clone();
            //     newRect.rect_center = newRect.rect_center.clone().add(offset);
            //     this._area_entity.rect.copy(newRect);
                
            //     // 同步移动分区内的所有家具
            //     this._area_entity.furniture_entities.forEach(entity => {
            //         const initialPos = this._initial_state.furniture_positions.get(entity._uuid);
            //         if (initialPos) {
            //             const newPos = initialPos.clone().add(offset);
            //             let t_rect = entity.rect.clone();
            //             t_rect.rect_center = newPos;
            //             entity.rect.copy(t_rect);

            //         }
            //     });
                
            //     // 更新分区的状态和空间关系
            //     this._area_entity._updateSubAreaByParentRoomEntity();
                
            //     this.update();
            // }
        }
    }

    onkeydown(ev: KeyboardEvent): boolean {
        return true;
    }
    onkeyup(ev: KeyboardEvent): boolean {
        return true;
    }

    drawCanvas(): void {
        super.drawCanvas();
        if(this._area_entity)
        {
            this._area_entity.drawEntity(this.painter,{is_hovered:true,is_selected:true,is_draw_figure:true});
        }
    }
}