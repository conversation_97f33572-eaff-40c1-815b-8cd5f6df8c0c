import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ token, css }) => {
  return {
    root: css`
        background-color: #fff;
        display: flex;
        flex-direction: column;
        width: 100%;
        box-sizing: border-box;
        overflow: hidden;
        transform: translateZ(0);
        -webkit-transform: translateZ(0);
        .ant-tabs {
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: hidden;
          min-height: 0;
          width: 100%;
          transform: translateZ(0);
          -webkit-transform: translateZ(0);
        }
        .ant-tabs-content-holder {
          flex: 1;
          overflow: hidden;
          min-height: 0;
          width: 100%;
        }
        .ant-tabs-content {
          height: 100%;
          width: 100%;
        }
        .ant-tabs-tabpane {
          height: 100%;
          padding: 0 12px;
          width: 100%;
          box-sizing: border-box;
          &::-webkit-scrollbar {
            width: 6px;
          }
          &::-webkit-scrollbar-thumb {
            background-color: #ccc;
            border-radius: 3px;
          }
          &::-webkit-scrollbar-track {
            background-color: #f5f5f5;
          }
        }
        .ant-tabs-nav {
          background-color: #f5f5f5;
          margin: 0;
          padding: 0;
          border-bottom: 1px solid #e8e8e8;
          flex-shrink: 0;
          width: 100%;
          box-sizing: border-box;
          transform: translateZ(0);
          -webkit-transform: translateZ(0);
          .ant-tabs-nav-wrap {
            width: 100%;
            margin: 0;
            padding: 0;
          }
          .ant-tabs-nav-list {
            width: 100%;
            display: table;
            table-layout: fixed;
            margin: 0;
            padding: 0;
            border-collapse: collapse;
            .ant-tabs-tab {
              display: table-cell;
              margin: 0;
              text-align: center;
              box-sizing: border-box;
              border: 1px solid #e8e8e8;
              border-right: none;
              border-bottom: none;
              background: #f5f5f5;
              vertical-align: middle;
              float: none;
              .ant-tabs-tab-btn {
                width: 70px;
                text-align: center;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                font-size: 14px;
                display: block;
              }
              &:last-child {
                border-right: 1px solid #e8e8e8;
              }
              &.ant-tabs-tab-active {
                background-color: #fff;
                border-bottom: 1px solid #fff;
                border-right: 1px solid #e8e8e8;
                margin-bottom: -1px;
                & + .ant-tabs-tab {
                  border-left: none;
                }
              }
            }
          }
          .ant-tabs-ink-bar {
            display: none !important;
          }
          .ant-tabs-nav-operations
          {
            display: none !important;
          }
        }
    `,
    menu_box: css`
        width: 100%;
        box-sizing: border-box;
        padding-right: 0;
        transition: all .3s;
        display: flex;
        flex-direction: column;
        min-height: 0; /* 防止flex子项溢出 */
      ul,
      li {
        padding: 0;
        margin: 0;
        list-style: none;
      }
      .menu {
        padding: 0 12px;
        flex: 1;
        overflow: hidden;
        &_item {
          padding: 4px 0;
          li {
            padding: 0 16px 0 22px;
            color: rgba(0, 0, 0, 0.65);
            font-size: 13px;
            line-height: 28px;
            height: 28px;
            letter-spacing: 0px;
            text-align: left;
            cursor: pointer;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            user-select:none;
            &:hover {
              background-color: #d9d9d9;
            }
          }
        }
      }
      .icon {
        color: #8B8B8B;
      }
      .label {
        color: rgba(0, 0, 0, 0.85);
        font-weight: 600;
        font-size: 14px;
        line-height: 22px;
        letter-spacing: 0px;
        text-align: left;
        height: 36px;
        display: flex;
        align-items: center;
        cursor: pointer;
        user-select:none;
        &_name {
          margin-left: 5px;
        }
      }
    `,
    Icon: css`
      position: absolute;
      top: 65px;
      left: 90px;
    `,
    figureListInfo: css`
    `,
    figure_item: css`
      display: flex;
      height: 64px;
      margin: 12px 0px 12px 0px; 
      align-items: center;
      width: 100%;
      position: relative;
    `,
    figure_item_title:css`
      height:20px;
      margin-left:5px;
      margin-top:0;
      margin-bottom:0;
      font-size:13px;
      font-weight:700;
      align-items: left;
      text-align:center;
    `,
    figure_item_fg: css`
      margin-top:10px;
      position: relative;
      .ant-image-img{
        width: 64px;
        height: 64px;
        /* background-color: #F2F3F5; */
        margin-right: 8px;
        /* padding: 8px; */
      }
      .clear_btn {
        display:none; 
        width: 60px;
        height: 30px;
        font-size: 11px;
        line-height:18px;
        background: #DDDFE4;
        color :#2b2b2b;
        border-radius: 2px;
        border: 1px solid #DDDFE4; 
        transition: all .3s;
        cursor: pointer;
      }
      :hover .clear_btn {
        display:block;
      }
      :hover .lock_icon {
        display:block;
      }
      .lock_icon.iconlock_fill {
        display:block;
      }

    `,
    figure_item_qt: css`
      .ant-image-img{
        width: 64px;
        height: 64px;
        background-color: #F2F3F5;
        margin-right: 8px;
        padding: 0px;
        transition: all .3s;
      }
      
      .imgTitle {
        position: absolute;
        width: 50px;
        text-align: center;
        height: 20px;
        background: #33333366;
        color: #eee;
        line-height: 20px;
        left: 7px;
        opacity: 0.9;
      }
      :hover{
        background-color: #F2F3F5;
        .imgTitle {
          color:#fff;
          opacity:1;
        }
        .lock_icon {
          display:block;
          right:3px;
          top:-10px;
          font-size:20px;
          z-index:1001;
          :hover {
            color:#aaa;
          }
        }
        .lock_icon.iconlock_fill {
          display:block;
          font-size:20px;
          color:#fff;
          :hover {
            color:#fff;
          }
        
        }
      }
      
    `,
    figure_item_sp: css`
      transition: all .3s;
      cursor: pointer;
      :hover{
        background-color: #F2F3F5;
      }
    `,
    figure_item_checked: css`
      background-color:rgba(206, 184, 249,0.5);
    `,
    figure_item_locked: css`
      :hover {
        background:none;
      }
    `,
    mask_item_locked: css`
      position: absolute;
      width: 95%;
      background-color: #33333377;
      display: block;
      height: 76px;
      z-index: 99;
      opacity:0.8;
  `,
    category: css`
      display: flex; 
      justify-content: flex-start;
      color: #282828;
      font-size: 12px;
      margin-top: 4px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    `,
    category_container: css`
      display: flex;
      flex-direction: column; /* 子元素从上到下排列 */
      justify-content: center; /* 垂直方向居中 */
      align-items: left; /* 水平方向居中 */
    `,
    category_span: css`
      margin-right: 5px;
    `,
    room_name: css`
      width: 100%;
      color: black;
      font-weight: bold;
      font-size: 14px;
      flex-grow: 0;
      flex-shrink: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    `,
    room_area: css`
      width: 100%;
      color: black;
      font-size: 12px;
      flex-grow: 0;
      flex-shrink: 0;
    `,
    room_base_info: css`
      display: flex;
      flex-direction: column;
      width: 70px;
      height: 90%;
      justify-content: space-between;
      button:disabled {
          background-color: #cccccc; /* 设置背景颜色为灰色 */
          color: #666666; /* 设置文本颜色为灰色 */
          cursor: not-allowed; /* 更改鼠标指针为不可用状态 */
          opacity: 0.6; /* 设置透明度，使其看起来不可用 */
      }
    }
    `,
    room_clear:css`
    `,
    module_series: css`
      margin-left: 10px;
      width: 65%;
      position: relative;
    `,
    size: css`
      color: #a2a2a5;
      font-size: 12px;  
      line-height: 20px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    `,
    lock_icon: css`
      font-size:12px;
      position:absolute;
      right:0;
      color:#aaa;
      display:none;
      cursor:pointer;
    `,
    delete_icon: {
      position: 'absolute',
      top: '3px',
      left: '3px',
      width: '12px',
      height: '12px',
      borderRadius: '50%',
      backgroundColor: '#ff4d4f',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      cursor: 'pointer',
      zIndex: 1,
      '&::before, &::after': {
        content: '""',
        position: 'absolute',
        width: '6px',
        height: '1px',
        backgroundColor: 'white',
        transform: 'rotate(45deg)',
      },
      '&::after': {
        transform: 'rotate(-45deg)',
      },
    },
  };
});
