import { LayoutAI_App, LayoutAI_Configs, TAppManagerBase, T<PERSON>ainter, TRoomLayoutScheme, TSubAreaLayoutScheme, TWholeLayoutScheme } from "@layoutai/layout_scheme";
import { useEffect, useRef} from 'react';
import { observer } from 'mobx-react-lite';
import useStyles from './style';
const DrawSchemeOnCanvas = (layout_scheme: TRoomLayoutScheme|TWholeLayoutScheme|TSubAreaLayoutScheme, canvasElement: HTMLCanvasElement, painter: TPainter) => {

    if(!layout_scheme) return;
    layout_scheme.drawOnCanvas(painter,canvasElement,576,576);
}

const RoomLayoutSchemeList : React.FC<{ width: number, showSchemeName:boolean, scheme_list:TRoomLayoutScheme[],selectedIndex:number}> = (props:{ width: number, showSchemeName:boolean, scheme_list:TRoomLayoutScheme[],selectedIndex:number})=>{
    let schemeList = props.scheme_list;
    let selectedIndex = props.selectedIndex || 0;
    const { styles } = useStyles();
    const showSchemeName = props.showSchemeName || false;
    const t = LayoutAI_Configs.t;
    if(schemeList.length > 0)
    {
        let painter = (LayoutAI_App.instance as TAppManagerBase).painter;
        schemeList.forEach((layout_scheme,index)=>{
            if(!layout_scheme._drawn_image)
            {
              let canvas = document.createElement("canvas") as HTMLCanvasElement;
              DrawSchemeOnCanvas(layout_scheme,canvas,painter);
              layout_scheme._drawn_image = new Image();
              layout_scheme._drawn_image.src = canvas.toDataURL()
              layout_scheme._drawn_image.crossOrigin = "anonymous";
              canvas = null;

            };
        });
    }
    useEffect(()=>{
        schemeList.forEach((layout_scheme: TRoomLayoutScheme) => {

        let totalScore = 0;
        layout_scheme._layout_scores.forEach((item: any) => {
            totalScore += item.score;
        });
        layout_scheme.totalScore = totalScore;
        });
        
        let firstElementIsDIY = schemeList[0]?._scheme_name?.includes('DIY');
        let firstElement = firstElementIsDIY ? schemeList.shift() : null;
        
        // 根据 totalScore 对 schemeList 进行排序，由大到小
        // schemeList.sort((a, b) => b.totalScore - a.totalScore);
        // 如果第一个元素是 DIY 布局，将其添加回数组的开头
        if (firstElementIsDIY && firstElement) {
        schemeList.unshift(firstElement);
        }
    },[])
    return <>
        {schemeList.length > 0 ? schemeList.map((scheme,index)=><div className={"scheme_div"} key={"roomScheme"+index}>
            <img className={(index==selectedIndex)?styles.active:''} src={scheme._drawn_image?.src || ""} crossOrigin="anonymous" alt={t("布局方案")}></img>
            {index===selectedIndex && <div id="active_div" className={(index==selectedIndex)?styles.activeTitle: styles.activeTitleNone}>{t("正在使用")}</div>}
        </div>):
        <div>
            <div className={styles.letfEmpty}>
                <div className={styles.letfEmptyItem}>
                    <img className='emptyImg' src={('assets/images/Empty.png')} alt="" />
                    <div className={styles.text}>
                       {t('暂无内容')},{t('请选择其他空间')}
                    </div>
                </div>
            </div>
        </div>}
    </>
}

export default observer(RoomLayoutSchemeList);