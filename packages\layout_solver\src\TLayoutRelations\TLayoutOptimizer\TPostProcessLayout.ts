import { I_LayoutOptRule, TF<PERSON>ureShape, TFigureElement, TGroupTemplate, TLayoutOptimizerWithRules, TRoom, TRoomShape, WPolygon } from "@layoutai/layout_scheme";
import { Vec3toMeta, ZEdge, ZPolygon, ZR<PERSON>t, compareNames } from "@layoutai/z_polygon";
import { IRuleReferTargets, TLayoutOptimizer } from "./TLayoutOptimizer";

export class TPostProcessLayout
{
       /**
     *  四：后处理优化
     */
   static post_process_group_by_opt_rule(feature_shape:TFeatureShape, post_group_templates:TGroupTemplate[], group_templates:TGroupTemplate[],room_name:string)
   {
       if(!post_group_templates || post_group_templates.length == 0) return;

       let wall_edges = [...feature_shape._w_poly.edges];
       wall_edges = TLayoutOptimizer.pre_sub_room_edges_inwall(feature_shape,wall_edges,room_name);


       let placeholder_rects :ZRect[] = [];

       for(let group_template of group_templates)
       {
            let config = group_template.getFigureGroupConfigs(room_name);

            // if(config.place_order < 3) break;

            group_template.updateByTargetRect();

            if(config.place_order > 3 && config.is_placeholder!==false)
            {
                placeholder_rects.push(group_template._target_rect);
                TLayoutOptimizer.sub_edges_by_rect(group_template._target_rect,wall_edges,50);
            }
       }


       for(let group_template of post_group_templates)
       {
            let refer_targets : IRuleReferTargets = TLayoutOptimizer.fill_refer_targets_of_group(wall_edges,group_template,room_name,placeholder_rects);
            if(!refer_targets) continue;
            let config =  group_template.getFigureGroupConfigs(room_name);
            let rules = config.opt_rules || null;
            let rect = group_template._target_rect;

            if(!rules) continue;
            for(let rule of rules)
            {            
                if(rule.ruleType == "BackToTarget" || rule.ruleType =="LengthByTarget")
                {
                    refer_targets.back_group_rect = TLayoutOptimizer.getTargetRect(rule,feature_shape,group_templates, rect.back_center);
                    if(!refer_targets.back_group_rect)
                    {
                        rect._w = 0;
                        continue;
                    }
                }                
                else{
                    refer_targets.back_group_rect = null;
                }
                TLayoutOptimizerWithRules.optimize_rect_with_opt_rule(refer_targets,rect,rule);
            }

            let check_rules = config.check_rules || null;

            if(check_rules)
            {
                let is_checked = true;
                for(let rule of check_rules)
                {

                    refer_targets.back_group_rect = TLayoutOptimizer.getTargetRect(rule,feature_shape,group_templates, rect.back_center);

                    is_checked = is_checked && TLayoutOptimizerWithRules.check_rect_with_opt_rule(refer_targets,rect,rule);
                }

                if(!is_checked)
                {
                    group_template._target_rect._w = 0;
                    group_template._target_rect.updateRect();
                }
            }
            // TsAI_app.log(group_template.group_space_category,group_template._target_rect.w,backwall_edge?.length,[...placeholder_rects]);

            // if(config.is_placeholder === false) continue; // 不是占位符
       }


   }

   static post_back_to_wall_rule_with_src_poly(feature_shape:TFeatureShape, group_templates:TGroupTemplate[],roon_name:string)
   {
        let poly = feature_shape._room.room_shape._poly;
        if(compareNames([roon_name],["卫生间"]))
        {
            poly = feature_shape._room.room_shape._feature_shape._w_poly;
        }

        for(let group_template of group_templates)
        {
            let config = group_template.getFigureGroupConfigs(roon_name);

            let rules = config?.opt_rules;
            if(!rules) continue;

            let back_to_wall_rule : I_LayoutOptRule = null;

            for(let rule of rules)
            {
                if(rule.ruleType == "BackToWall") back_to_wall_rule = rule;
            }

            if(!back_to_wall_rule) continue;


            let rect = group_template._target_rect;
            let back_edge = rect.backEdge;
            let target_w_b_edge : ZEdge = null;
            for(let edge of poly.edges)
            {
                if(edge.islayOn(back_edge,300, 0.5))
                {
                    target_w_b_edge = edge;
                    break;
                }
            }
            // console.log(poly,group_template.group_space_category,target_w_b_edge);

            if(!target_w_b_edge) continue;

            let refer_targets : IRuleReferTargets  = {
                back_w_edge : target_w_b_edge
            }
            TLayoutOptimizerWithRules.optimize_rect_with_BackToTarget_rule(refer_targets,rect,back_to_wall_rule);
        }
   }

   static post_process_group_by_wall_occulsion(feature_shape:TFeatureShape, group_templates:TGroupTemplate[],room_name:string)
   {

    let wall_rects : ZRect[] = [];

    let room_poly = feature_shape._room.room_shape._poly.clone();

    if(room_poly.orientation_z_nor.z < 0)
    {
        room_poly.invertOrder();
    }

    let door_rects : ZPolygon[] = [];

    for(let door of feature_shape._room.windows)
    {
        if(door.type !="Door") continue;
        if(door.realType === "SlidingDoor") continue;

        let rect = door.rect;
        if(rect)
        {
            let t_rect = rect.clone();
            t_rect._h += Math.min(t_rect.w,600);
            t_rect.updateRect();

            if(t_rect.orientation_z_nor.z <0)
            {
                t_rect.u_dv = t_rect.dv.clone().negate();
                t_rect.updateRect();
            }
            door_rects.push(t_rect);
        }
    }
    for(let group_template of group_templates)
    {

        if(compareNames([group_template.group_space_category],["地毯","卧床区","餐桌区"])==0) continue;

        let needs_check_door = true;

        if(compareNames([group_template.group_space_category],["地毯"])){
            needs_check_door = false;
        }
        let target_rect = group_template._target_rect.clone();

        if(target_rect.orientation_z_nor.z < 0)
        {
            target_rect.u_dv = target_rect.dv.negate();
            target_rect.updateRect();
        }
        let intersects = target_rect.intersect(room_poly);

        if(!intersects || intersects.length == 0)
        {
            group_template._target_rect._w = 0;
            group_template._target_rect.updateRect();
        }
        else {
            let t_poly = intersects[0];

            if(door_rects.length > 0 && needs_check_door)
            {

                let sub_polys = t_poly.substract_polygons(door_rects);

                if(sub_polys && sub_polys.length > 0)
                {
                    t_poly = sub_polys[0];
                }
            }

            if(!t_poly.containsPoly(target_rect,30))
            {        
                WPolygon.optmizePoly(t_poly);
                let rect :ZRect = null;
                if(t_poly.vertices.length <= 8)
                {
                    // 说明是L形

                    let ll = -target_rect.w/2;
                    let rr = target_rect.w/2;


                    let dd = target_rect.h/2;
                    for(let v of t_poly.vertices)
                    {
                        let xx = target_rect.project(v.pos).x;
                        if(xx < 0)
                        {
                            ll = Math.max(ll,xx);
                        }
                        else{
                            rr = Math.min(rr,xx);
                        }
                        let yy = target_rect.project(v.pos).y;

                        if(yy > 0)
                        {
                            dd = Math.min(yy,dd);
                        }
                    }

                    if(rr-ll > target_rect.w/2)
                    {
                        let t_center = target_rect.unproject({x:(ll+rr)/2,y:0});
                        rect = target_rect.clone();
                        rect._w = (rr - ll);
                        rect.rect_center = t_center;
                    }
                    else{
                        rect = target_rect.clone();
                        rect._h = rect._h/2 + dd;
                        rect.updateRect();
                    }

                }
                else {
                    let t_new_rect = TRoomShape.computeMaxRectBySplitShape(t_poly);
                    rect = ZRect.fromZRect(t_new_rect,group_template._target_rect.nor,group_template._target_rect.u_dv_flag);
                }

                group_template._target_rect.copy(rect);
            }
        }
    }
    

   }

   static UpdateCabinetsNeighbors(room:TRoom,floor_cabinets:TGroupTemplate[])
   {
       let attached_element_dist :number = 50;
       for(let cabinet0 of floor_cabinets)
       {
          let rect0 = cabinet0._target_rect;
          for(let cabinet1 of floor_cabinets)
          {
              if(cabinet0 == cabinet1) continue;
              let rect1 = cabinet1._target_rect;

              // 保持同方向
              if(!rect0.checkSameNormal(rect1.nor,false)) continue;

              let pp = rect0.project(rect1.rect_center);

              if(Math.abs(pp.y) > rect0.h) continue;
              let t_width = rect0.w/2 + rect1.w/2;

              const OnsideElementName = pp.x > 0?TFigureElement.RightElement:TFigureElement.LeftElement;

              if(Math.abs(pp.x)-t_width <+attached_element_dist)
              {
                  let t_dist = Math.abs(pp.x) - t_width;
                  let right_element:TGroupTemplate = rect0._attached_elements[OnsideElementName] || null;
                  let r_dist = attached_element_dist;
                  if(right_element)
                  {
                      r_dist = Math.abs(rect0.project(right_element._target_rect.rect_center).x) -t_width;
                  }

                  if(t_dist < r_dist)
                  {
                      rect0._attached_elements[OnsideElementName] = cabinet1;
                  }
              }

          }
       }

       let attached_wall_dist : number = 110;

       for(let cabinet of floor_cabinets)
       {
          let rect = cabinet._target_rect;
          for(let edge of room.room_shape._feature_shape._w_poly.edges)
          {
              if(!rect.checkSameNormal(edge.dv,true)) continue;  // 斜墙处理可能有问题

              if(rect.w <= 0.1) continue;
              let pp = rect.project(edge.center);

              let rect_side_edge = pp.x >0 ? rect.rightEdge:rect.leftEdge;
              const OnSideWallEdge = pp.x > 0?TFigureElement.LeftWallEdge:TFigureElement.RightWallEdge;

              if(edge.islayOn(rect_side_edge, attached_wall_dist,0.2) && rect_side_edge.checkSameNormal(edge.nor,false))
              {   
                  let t_old_edge : ZEdge = rect._attached_elements[OnSideWallEdge] || null;

                  let side_dist = attached_wall_dist;

                  if(t_old_edge)
                  {
                      side_dist = Math.abs(rect.project(t_old_edge.center).x);
                  }
                  if(Math.abs(pp.x) < side_dist)
                  {
                      rect._attached_elements[OnSideWallEdge] = edge;
                  }
              }
          }
       }
   }
   static post_process_kitchen_orientaion(room:TRoom,group_templates:TGroupTemplate[])
   {
        if(!compareNames([room.roomname],["厨房"])) return;

        let floor_cabinets = group_templates.filter((ele)=>compareNames([ele.group_code],["地柜"]));
        let hangon_cabinets = group_templates.filter((ele)=>compareNames([ele.group_code],["吊柜"]));
        TPostProcessLayout.UpdateCabinetsNeighbors(room,floor_cabinets);
        TPostProcessLayout.UpdateCabinetsNeighbors(room,hangon_cabinets);
        // 处理转角柜

        floor_cabinets.forEach((cabinet)=>{
           if(compareNames([cabinet.group_code],["转角","收口板","见光板"]))
           {
               let rect = cabinet._target_rect;
               let left_element:TGroupTemplate = rect._attached_elements[TFigureElement.LeftElement];
               let right_element:TGroupTemplate = rect._attached_elements[TFigureElement.RightElement];

               let left_wall :ZEdge = rect._attached_elements[TFigureElement.LeftWallEdge];
               let right_wall :ZEdge = rect._attached_elements[TFigureElement.RightWallEdge];

               let left_check = 0;
               if(left_element && !left_element.group_code.endsWith("板"))
               {
                   left_check = 1;
               }
               let right_check = 0;
               if(right_element && !right_element.group_code.endsWith("板"))
               {
                   right_check = 1;
               }

               if(right_check) // 左边连结了非板件的柜子
               {
                   rect._u_dv_flag = -rect._u_dv_flag;
                   rect.updateRect();
                   rect._attached_elements[TFigureElement.LeftElement] = right_element;
                   rect._attached_elements[TFigureElement.RightElement] = left_element;
                   rect._attached_elements[TFigureElement.LeftWallEdge] = right_wall;
                   rect._attached_elements[TFigureElement.RightWallEdge] = left_wall;

               }
               else if(left_check)
               {

               }
               
           }
        });


        // 处理收口板

        
        hangon_cabinets.forEach((cabinet)=>{
            if(compareNames([cabinet.group_code],["收口板","见光板"]))
            {
                let rect = cabinet._target_rect;
                let left_element:TGroupTemplate = rect._attached_elements[TFigureElement.LeftElement];
                let right_element:TGroupTemplate = rect._attached_elements[TFigureElement.RightElement];
 
                let left_wall :ZEdge = rect._attached_elements[TFigureElement.LeftWallEdge];
                let right_wall :ZEdge = rect._attached_elements[TFigureElement.RightWallEdge];
 
                let left_check = 0;
                if(left_element && !left_element.group_code.endsWith("板"))
                {
                    left_check = 1;
                }
                let right_check = 0;
                if(right_element && !right_element.group_code.endsWith("板"))
                {
                    right_check = 1;
                }
 
                if(right_check) // 左边连结了非板件的柜子
                {
                    rect._u_dv_flag = -rect._u_dv_flag;
                    rect.updateRect();
                    rect._attached_elements[TFigureElement.LeftElement] = right_element;
                    rect._attached_elements[TFigureElement.RightElement] = left_element;
                    rect._attached_elements[TFigureElement.LeftWallEdge] = right_wall;
                    rect._attached_elements[TFigureElement.RightWallEdge] = left_wall;
 
                }
                else if(left_check)
                {
 
                }


                if(!rect._attached_elements[TFigureElement.LeftWallEdge] && !rect._attached_elements[TFigureElement.RightWallEdge])
                {
                    if(rect._attached_elements[TFigureElement.RightElement])
                    {
                        let lighting_view_board_width = 18;
                        let r_center = rect.unproject({x:rect.w/2-lighting_view_board_width/2,y:0});
                        rect._w = lighting_view_board_width;
                        rect.rect_center = r_center;

                        cabinet.category = cabinet.category.replace("收口板","见光板");
                        cabinet.group_space_category = cabinet.group_space_category.replace("收口板","见光板");

                        cabinet.updateByTargetRect();
                    }
                    
                }
                
            }
         });
   }

   private static createPatchingBoard (wall:ZEdge, isLeftWall:boolean, wardrobe:TFigureElement)
   { 
        let categoryName = (isLeftWall ? "右" : "左") + "收口板";
        let board = new TFigureElement({
            category: categoryName,
            sub_category : categoryName,
            _is_sub_board : true,
            params :{
                length : 50,
                depth : 600,
                height: 2600
            }
        })
        board.rect.nor = wardrobe.rect.nor;
        board.params.height = wardrobe.params.height;
        board.params.depth = wardrobe.params.depth;

        let max_hh = 0;
        for(let v of wardrobe.rect.positions)
        {
            max_hh = Math.max(- wall.projectEdge2d(v).y,max_hh);
        }

        let v_x = wall.projectEdge2d(wardrobe.rect.back_center).x;

        board.rect._back_center = wall.unprojectEdge2d({x:v_x,y:-(board.rect.w/2)});
        board.rect.updateRect();

        board.rect.updateRect();
        board.matched_rect = board.rect.clone();
        return board;
    }

   /**
    * 后处理封板---添加收口板
    */
   static post_add_patching_boards(room:TRoom)
   {
    
        if(compareNames([room.roomname],["卧室"])==0)
        {
            return;
        }
        let poly = room.room_shape?._poly;

        if(!poly) return;

        let patching_boards : TFigureElement[] = [];
        let wardrobes : TFigureElement[] = [];
        for(let ele of room._furniture_list)
        {
            if(compareNames([ele.category],["收口板"]))
            {
                patching_boards.push(ele);
            }
            if(compareNames([ele.category],["衣柜"]))
            {
                wardrobes.push(ele);
            }
        }

        for(let ele of patching_boards)
        {
            let id = room._furniture_list.indexOf(ele);
            if(id >= 0) room._furniture_list.splice(id,1);
        }

        patching_boards.length = 0;

        for(let wardrobe of wardrobes)
        {
            wardrobe.rect.updateRect();

            let rect_left_edge : ZEdge = wardrobe.rect.edges[2];
            let rect_right_edge : ZEdge = wardrobe.rect.edges[0];
            let rect_back_edge : ZEdge = wardrobe.rect.backEdge;

            let back_wall : ZEdge = null;
            let left_wall:ZEdge= null;
            let right_wall :ZEdge= null;

            for(let edge of poly.edges)
            {
                if(edge.islayOn(rect_back_edge,10,0.2))
                {
                    back_wall = edge;
                }
                if(edge.islayOn(rect_left_edge,100,0.2))
                {
                    left_wall = edge;
                }
                if(edge.islayOn(rect_right_edge,100,0.2))
                {
                    right_wall = edge;
                }
            }

            if(left_wall)
            {
                let left_board = TPostProcessLayout.createPatchingBoard(left_wall, wardrobe.rect._u_dv_flag > 0, wardrobe);
                room.addFurnitureElement(left_board);
                patching_boards.push(left_board);
            }

            if(right_wall)
            { 
                let right_board = TPostProcessLayout.createPatchingBoard(right_wall, wardrobe.rect._u_dv_flag < 0, wardrobe);
                room.addFurnitureElement(right_board);
                patching_boards.push(right_board);
            }
        }
   }

   /**
    *  后处理封板---去掉收口板
    */
   static post_remove_patching_boards(room:TRoom)
   {
    
        if(compareNames([room.roomname],["卧室"])==0)
        {
            return;
        }
        let poly = room.room_shape?._poly;

        if(!poly) return;

        let patching_boards : TFigureElement[] = [];
        let wardrobes : TFigureElement[] = [];
        for(let ele of room._furniture_list)
        {
            let rect = ele.rect;
            if(compareNames([ele.category],["收口板"]))
            {
                patching_boards.push(ele);
            }
            if(compareNames([ele.category],["衣柜"]))
            {
                wardrobes.push(ele);
            }
        }

        for(let ele of patching_boards)
        {
            let id = room._furniture_list.indexOf(ele);
            if(id >= 0) room._furniture_list.splice(id,1);
        }

        patching_boards.length = 0;
        for(let ele of wardrobes)
        {
            if( ele.rect._attached_elements[TFigureElement.AdjustRect])
            {
                delete ele.rect._attached_elements[TFigureElement.AdjustRect];
            }
        }
   }

   /**
    * 
    * @param room 
    */
   static post_adjust_patching_boards(room:TRoom){
        let poly = room.room_shape?._poly;

        if(!poly) return;

        let patching_boards : TFigureElement[] = [];
        let wardrobes : TFigureElement[] = [];
        for(let ele of room._furniture_list)
        {
            let rect = ele.rect;
            if(compareNames([ele.category],["收口板"]))
            {
                patching_boards.push(ele);
            }
            if(compareNames([ele.category],["衣柜"]))
            {
                wardrobes.push(ele);
            }
        }

        for(let board of patching_boards)
        { 
            if (!board._matched_material) continue;
            let target_wardrobe :TFigureElement = null;
            for(let wardrobe of wardrobes)
            {
                if(board.rect.nor.dot(wardrobe.rect.nor)<0.9) continue;

                let pp = wardrobe.rect.project(board.rect.rect_center);

                if(Math.abs(pp.y) > wardrobe.depth) continue;

                if(Math.abs(pp.x) < wardrobe.length / 2 + board.length / 2 + 100)
                {
                    target_wardrobe = wardrobe;
                }
            }
            // 匹配到右收口侧的衣柜，则暂时屏蔽收口板图元，不显示，不去3D布置
            if (target_wardrobe && target_wardrobe._matched_material 
                && target_wardrobe._matched_material.closeDirection != null) {
                    board._ex_prop['is_deleted'] = '1';
                    continue;
            }
            if(target_wardrobe && target_wardrobe.matched_rect && target_wardrobe._matched_material?.modelFlag ==="1")
            {
                let t_rect = target_wardrobe.matched_rect;
                let b_rect = board.rect;

                let ll = -t_rect.w/2;
                let rr = t_rect.w/2;

                let pp = t_rect.project(b_rect.rect_center);
                let xx = target_wardrobe.matched_rect.w/2;

                if(pp.x < 0)
                {
                    ll = Math.min(pp.x-b_rect.w/2,ll);
                    xx = ll + target_wardrobe.matched_rect.w/2;
                }
                else{
                    rr = Math.max(pp.x+b_rect.w/2,rr);
                    xx = rr - target_wardrobe.matched_rect.w/2;
                }

                board._ex_prop['is_deleted'] = '1';
            } else {
                if(board._ex_prop['is_deleted'])
                {
                    delete board._ex_prop['is_deleted'];
                }
 
                if(target_wardrobe && target_wardrobe.haveMatchedMaterial() && board.matched_rect)
                {
                    board.matched_rect.depth = target_wardrobe._matched_material.targetSize.width;
                    board.matched_rect.updateRect();
                    if(board._matched_material?.targetSize?.width)
                    {
                        board._matched_material.targetSize.width = board.matched_rect.depth;
                        board._matched_material.targetSize.height = target_wardrobe._matched_material?.targetSize.height || 2600;
                        board._matched_material.targetPosition =Vec3toMeta(board.matched_rect.rect_center);
                    }
                }

                if(target_wardrobe && target_wardrobe.matched_rect && target_wardrobe.matched_rect.checkIntersection(board.rect, 5) && board._matched_material.targetSize != null)
                {
                    target_wardrobe.matched_rect.moveRight(board._matched_material.targetSize.length);
                    target_wardrobe.updateMaterialTargetSizeByMatchedRect();
                }
            }
        }
   }
}