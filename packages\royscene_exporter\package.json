{"name": "@layoutai/royscene_exporter", "version": "1.0.0", "type": "module", "description": "", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "scripts": {"build": "tsc", "watch": "tsc -watch", "buildIndex": "cti entrypoint ./src -b -o index.ts", "update": "pnpm run buildIndex && pnpm run build"}, "devDependencies": {"@types/three": "^0.171.0", "create-ts-index": "^1.14.0", "rollup": "^4.41.1", "three": "^0.171.0", "tsx": "^4.19.2", "typescript": "^5.6.3", "vite": "^5.2.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-dts": "^4.5.4", "vite-plugin-externals": "^0.6.2", "vite-plugin-html": "^3.2.2", "vite-tsconfig-paths": "^5.1.4"}, "dependencies": {"@svg/request": "^0.4.0", "axios": "0.27.2", "axios-cookiejar-support": "^5.0.5", "fflate": "^0.8.2", "buffer": "^6.0.3", "@clouddesign/design-designmaterial": "^1.7.8", "@clouddesign/design-framework": "1.0.1", "@clouddesign/royscene_threejs_extend": "0.0.142", "@sd/plugin_svgsocketclient": "3.0.12-0", "@sd/roybase": "^3.0.38-0", "@sd/roysvgapi": "^3.0.24-0", "js-cookie": "^3.0.5", "@layoutai/model3d_api": "workspace:^"}, "keywords": [], "author": "sunvega", "license": "ISC"}