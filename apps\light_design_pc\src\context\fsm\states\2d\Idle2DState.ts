import type { IState } from '../../interfaces/IState';
import { Design2DStates } from '../../const/FSMConst';
import { appContext } from '../../../AppContext';
import { DesignControllerType } from '../../../ctrls/DesignControllerType';
import { ControllerType } from '@layoutai/design_2d';
/**
 * 2D空闲状态
 */
export class Idle2DState implements IState {
  name: string;
  parent?: any;

  constructor(name: string = Design2DStates.IDLE2D) {
    this.name = name;
  }

  onEnter(data?: any): void {
    // 状态进入逻辑
    appContext.design2DContext?.controllerManager.activateCtrl(DesignControllerType.IDLE2D_CTRL);
    appContext.design2DContext?.controllerManager.activateCtrl(DesignControllerType.CANVAS_LMB_MOVE);
  }

  onExit(data?: any): void {
    // 状态退出逻辑
    appContext.design2DContext?.controllerManager.deactivateCtrl(DesignControllerType.IDLE2D_CTRL);
    appContext.design2DContext?.controllerManager.deactivateCtrl(DesignControllerType.CANVAS_LMB_MOVE);
  }

  /**
   * 切换到3D模式
   */
  switchTo3D(): void {
    // 发送事件到主FSM进行跨FSM切换
  }
} 