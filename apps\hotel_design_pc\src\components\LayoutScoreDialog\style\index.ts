import { checkIsMobile } from '@/config';
import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ token, css }) => {
  return {
    sceneBox: css`
        border-radius: 8px;
        background: linear-gradient(180deg, #FFF3DCFF 0%, #FFFFFFFF 35%);
        box-shadow: 0px 8px 24px 0px #00000028;
        height: auto;
        min-height: 550px;
        padding: 20px;
        position: relative;
        display: block;
        @media (max-width: 450px) {
            width: 300px !important;
        }
        ${checkIsMobile() ? 
        `
            width: 400px;
        ` : 
        `
            width: 350px
        `}
    
    `,
    title: css`
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        font-weight: 600;
        font-size: 16px;
        color: #502e00;
        img {
            width: 80px;
            height: 40px;
        }
    `,
    scoreContent: css`
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        justify-content: space-between;
        .subContent
        {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex: 3;
        }
        ${checkIsMobile() &&
            `
                margin-bottom: 0px;
            `
        }
    `,
    name: css`
        color: #282828;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 14px;
        line-height: 1.57;
        letter-spacing: 0px;
        text-align: left;
        flex: 2;
    `,
   grade: css`
       width: 48px;
       height: 48px;
       display: flex;
       align-items: center;
       font-weight: 600;
       .excellent {
            width: auto;
            padding: 0 4px;
            height: 20px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #FF9D00;
            color: #fff;
        }
       
       .good {
            width: auto;
            height: 20px;
            padding: 0 4px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #c8e6f8;
            color: #416d8b;
        }
       
       .qualified {
            width: auto;
            height: 20px;
            padding: 0 4px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #007AFF;
            color: #fff;
        }
       
       .commonly {
            width: auto;
            height: 20px;
            padding: 0 4px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #dedede;
            color: #282828;
        }
       
       .difference {
            width: auto;
            height: 20px;
            padding: 0 4px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;    
            background: #fbb;
            color: #921313;
        }
   `,
    percent: css`
        color: #282828;
        font-family: PingFang SC;
        font-weight: semibold;
        font-size: 12px;
        line-height: 1.67;
        letter-spacing: 0px;
        text-align: left;
        font-weight: 600;
    `,
    start: css`
    `,
    descBox: css`
        border-radius: 4px;
        background: #FAFAFA;
        height: auto;
        min-height: 350px;
        padding: 12px;
        position: relative;
        display: block;
        .name
        {
            color: #5B5E60;
            font-family: PingFang SC;
            font-weight: regular;
            font-size: 12px;
            line-height: 1.67;
            letter-spacing: 0px;
            text-align: left;
        }
        .item
        {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            .anticon-check-circle
            {
                color: #52C41A;
            }
        }

    `,
    chartsBox: css`
        height: 230px;
        width: 300px;
        min-height: 230px;
        min-width: 300px;
        display: block;
        position: relative;
    `,
    cloneIcon: css`
        position: absolute;
        top: 5px;
        right: 5px;
    `
  };
});
