// import { magiccubeDpAiWebRequest, magiccubeRequest, openApiRequest } from '@/utils';

import { BasicRequest } from "@layoutai/basic_data";


let currentUserInfoData : any = null;
let currentVrUserInfoData : any = null;
let currentTenantAuthorizationData : Map<string, boolean> = new Map();

/**
 * @description 获取用户信息
 */
export async function getUserInfo() {
  if (currentUserInfoData != null) {
    return currentUserInfoData;
  }
  const res = await BasicRequest.magiccubeRequest({
    method: 'get',
    url: '/sso-plus/fastLogin/getLoginUserInfo',
  }).catch((e:any)=>{
    console.error(e);
  }) as any;
  if (res?.success && res.data) {
    currentUserInfoData = res.data;
  }
  return currentUserInfoData;
}

/**
 * @description 获取用户信息
 */
export async function getVrUserInfo() {
  if (currentVrUserInfoData != null) {
    return currentVrUserInfoData;
  }
  const res = await BasicRequest.openApiRequest({
    method: 'get',
    url: `/api/njvr/vrusers/getUser`,
  }).catch((e:any) => {
    console.error(e);
  }) as any;
  if (res?.success && res.result) {
    currentVrUserInfoData = res.result;
  }
  return currentVrUserInfoData;
}

/**
 * @description 获取标准空间类型名称映射表
 */
 export async function getaiCongig() {
  const res = await BasicRequest.magiccubeDpAiWebRequest({
    method: 'post',
    url: '/dp-ai-web/getSpaceTypeMap',
  }).catch((e:any): null=>{
    return null;
  }) as any;
  return res?.success && res.data ? res.data : null;
}

/**
 * @description 获取所有权限码列表
 */
export async function listAuthorizationCode() {
  const res = await BasicRequest.openApiRequest({
    method: 'post',
    url: `/api/sdapi/operation/list`,
  }).catch((e:any): null=>{
    return null;
  }) as any;
  return res?.success && res.result ? res.result : null;
}


/**
 * @description 上传文件获取签名
 * @param type 
 * @param fileName 
 * @returns 
 */
export async function getSign(type:number,fileName:string) {
  const res = await BasicRequest.openApiRequest({
    method: 'post',
    url: `/api/njvr/sts/getSignMultiSurvive`,
    data: {
      type,
      fileName
    }
  }).catch((e:any): null=>{
    return null;
  }) as any;
  return res?.result;
}


/**
 * @description 通过模块code 获取当前用户是否可见相关模块
 * @param type 
 * @param fileName 
 * @returns 
 */
export async function getModule() {
  const res = await BasicRequest.openApiRequest({
    method: 'post',
    url: `/api/njvr/vrusers/moduleSettings`,
    data:{
      modules: ["internaltesting"]
    }
  }).catch((e:any): null=>{
    return null;
  }) as any;
  return res?.result;
}

export async function checkTenantAuthorization(authorizationCode: string) : Promise<boolean> {
  if (currentTenantAuthorizationData.has(authorizationCode)) {
    return currentTenantAuthorizationData.get(authorizationCode);
  }
  const res = await BasicRequest.magiccubeDpAiWebRequest({
    method: 'post',
    url: '/dp-ai-web/checkTenantAuthorization',
    data: {
      tenantId: currentVrUserInfoData?.tenantId,
      authorizationCode: authorizationCode
    }
  }).catch((e:any)=>{
    console.error(e);
  }) as any;
  let hasPermission:boolean = null;
  if (res?.success && res.data != null) {
    hasPermission = res.data;
    currentTenantAuthorizationData.set(authorizationCode, hasPermission);
  }
  return hasPermission;
}

// 走联合登录获取用户信息
export async function getTokenByCode(params:any) {
  const res = await BasicRequest.magiccubeRequest({
    method: 'get',
    url: `/sunvega-open-oauth2/joinAuth/token?appId=${params.appId}&code=${params.code}`,
  }) as any;
  return res;
}

