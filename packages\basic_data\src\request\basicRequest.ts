import { AxiosInstance } from 'axios';
import { APP_ID, RequestHosts, _magiccubeToken } from '../config';
import createProRequest from './request';


export class BasicRequest {
  private _magiccubeRequest: any;

  private _magiccubeDpAiWebRequest: any;

  private _openApiRequest: any;

  private static _instance : BasicRequest = null;
  constructor()
  {
    this.init();
  }
  public static get instance()
  {
    if(!BasicRequest._instance)
    {
      BasicRequest._instance = new BasicRequest();
    }
    return BasicRequest._instance;
  }
  init()
  {
    const magiccubeRequest = createProRequest();
    magiccubeRequest.defaults.withCredentials = true;
    magiccubeRequest.defaults.baseURL = `${RequestHosts.gatewayUrl}`;
    magiccubeRequest.defaults.headers = {
      'Content-Type': 'application/json',
      'sysCode': APP_ID,
      'Magiccube-App-Id': APP_ID,
      'Magiccube-Token': _magiccubeToken,
      'Joint-Token': null,
    };
    this._magiccubeRequest = magiccubeRequest;
    /**
     * @description magiccube网关DpAiWeb网关应用接口请求
     */
    const magiccubeDpAiWebRequest = createProRequest();
    magiccubeDpAiWebRequest.defaults.withCredentials = true;
    magiccubeDpAiWebRequest.defaults.baseURL = `${RequestHosts.dpAiWebHost}`;
    magiccubeDpAiWebRequest.defaults.headers = {
      'Content-Type': 'application/json',
      'sysCode': APP_ID,
      'Magiccube-App-Id': APP_ID,
      'Magiccube-Token': _magiccubeToken,
      'Joint-Token': null,
    };
    this._magiccubeDpAiWebRequest = magiccubeDpAiWebRequest;
    /**
     * @description openApi网关接口请求
     */
    const openApiRequest = createProRequest();
    openApiRequest.defaults.withCredentials = true;
    openApiRequest.defaults.baseURL = `${RequestHosts.openApiHost}`;
    openApiRequest.defaults.headers = {
      'Content-Type': 'application/json',
      'sysCode': APP_ID,
      'Magiccube-App-Id': APP_ID,
      'Magiccube-Token': _magiccubeToken,
      'Joint-Token': null,
    };
    this._openApiRequest = openApiRequest;
  }
  public static Init()
  {
      BasicRequest.instance.init();
  }
  public static get magiccubeRequest(): AxiosInstance {
    return BasicRequest.instance._magiccubeRequest;
  }
  public static get openApiRequest(): AxiosInstance {
    return BasicRequest.instance._openApiRequest;
  }
  public static get magiccubeDpAiWebRequest(): AxiosInstance {
    return BasicRequest.instance._magiccubeDpAiWebRequest;
  }
  public static set magiccubeRequest(t:any) {
     BasicRequest.instance._magiccubeRequest = t;
  }
  public static set openApiRequest(t:any) {
     BasicRequest.instance._openApiRequest = t;
  }
  public static set magiccubeDpAiWebRequest(t:any) {
     BasicRequest.instance._magiccubeDpAiWebRequest = t; 
  }

}
