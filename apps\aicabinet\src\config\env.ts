const envTypes = [
  'dev',
  'test',
  'hws',
  'pre',
  'prod',
] as const;

export type EnvType = typeof envTypes[number];

let env: EnvType = 'prod';

// 默认的 appId 配置
const defaultAppIds = {
  dev: 'wx03d4d0722275e596',
  test: 'wx03d4d0722275e596',
  hws: 'wx6afb5e6b46d604c5',
  pre: 'wx18cde44f73ec8ff7',
  prod: 'wx18cde44f73ec8ff7',
} as const;

let appId = '';

// 判断当前环境
const hostname: string = window?.location?.hostname || '';
if (hostname.indexOf('hws') > -1) {
  env = 'hws';
} else if (hostname.indexOf('pre') > -1) {
  env = 'pre';
} else if (hostname.indexOf('test') > -1) {
  env = 'test';
}

// 获取URL参数，环境参数appEnv优先级高
if (window?.URLSearchParams) {
  const urlParams = new URLSearchParams(window.location.search);
  const urlAppId = urlParams.get('appId');
  appId = urlAppId || defaultAppIds[env]; // 优先使用 URL 中的 appId，否则使用默认配置
  const appEnv = urlParams.get('appEnv') as EnvType;
  if (envTypes.includes(appEnv)) {
    env = appEnv;
  }
  if(window.location.hostname?.indexOf('hws') > -1) {
    env = 'hws';
  }
  if(window.location.hostname?.indexOf('pre') > -1) {
    env = 'pre';
  }
} else {
  appId = defaultAppIds[env]; // 如果不支持 URLSearchParams，直接使用默认配置
}

export const APP_ID = appId;

export const ENV: EnvType = env;
