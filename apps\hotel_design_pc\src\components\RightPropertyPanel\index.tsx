import React, { useEffect, useState, useRef, useContext } from 'react';
import { PanelContainer } from '@svg/antd-cloud-design';
import useStyles from './style';
import { observer } from "mobx-react-lite";
import FloorProperties from './widget/FloorProperties/index';
import RoomProperties from './widget/RoomProperties/index';
import GoodsProperties from './widget/GoodsProperties/index';
import { useStore } from '@/models';
import EntityProperties from './widget/EntityProperties/EntityProperties';
import { TRoomEntity,TRoom,TSeriesSample,LayoutAI_App,TFigureElement, EventName} from '@layoutai/layout_scheme';
import { AI_PolyTargetType } from '@layoutai/basic_data';
interface RoomSeriesPlanProps {
  right: boolean;
  left: boolean;
  selectedFigureElement: TFigureElement;
}


// 属性面板
const RightPropertyPanel: React.FC<RoomSeriesPlanProps> = ({ right, left, selectedFigureElement }) => {
  const t = LayoutAI_App.t;

  const { styles }: any = useStyles();
  let divRef = useRef(null);
  // 当前属性面板的实体类型
  const [curType, setCurType] = useState<string>('default');
  // 是否显示布局列表
  const [showLayoutList, setShowLayoutList] = useState<boolean>(true);   // 区分选布局还是选搭配
  const store = useStore();
  const selectedRoomEntity = store.homeStore.selectEntity instanceof TRoomEntity ? store.homeStore.selectEntity : null;
  const selectedTRoom = selectedRoomEntity?._room || null;

  // 属性面板的类型名称 类型与面板标题 对应关系
  // 默认是楼层信息
  const typeNameMap: Map<string, string> = new Map([
    [AI_PolyTargetType.RoomSubArea, t("分区信息")],
    [AI_PolyTargetType.Wall, t("墙体信息")],
    [AI_PolyTargetType.Door, t("门信息")],
    [AI_PolyTargetType.Window, t("窗信息")],
    [AI_PolyTargetType.StructureEntity, t("结构体信息")],
    [AI_PolyTargetType.RoomArea, t("空间信息")],
    [AI_PolyTargetType.Furniture, t("模型位信息")],
    ["default", t("楼层信息")]
  ])


  useEffect(() => {
    if (store.homeStore.room2SeriesSampleArray?.length > 0) {
      const allItems = store.homeStore.room2SeriesSampleArray?.every?.((item: [TRoom, TSeriesSample]) => {
        const room = item[0];
        return room._furniture_list.length > 0;
      });
    } else {
    }
  }, [store.homeStore.room2SeriesSampleArray]);

  useEffect(() => {

    // 清理之前可能存在的事件监听器
    const handleRoom2SeriesSample = (array: [TRoom, TSeriesSample][]) => {
      store.homeStore.setRoom2SeriesSampleArray(array);
      // 直接在这里也设置 arrangeable 状态，确保状态立即更新

    };

    LayoutAI_App.on(EventName.Room2SeriesSampleRoom, handleRoom2SeriesSample);

    return () => {
      // 组件卸载时清理事件监听器
      LayoutAI_App.off(EventName.Room2SeriesSampleRoom);
    };
  }, []);

  useEffect(() => {
    if (store.homeStore.selectEntity?.type === "RoomArea") {
      setCurType(AI_PolyTargetType.RoomArea);
    } else {
      if (typeNameMap.has(store.homeStore.selectEntity?.type)) {
        setCurType(store.homeStore.selectEntity?.type);
      }
      else {
        setCurType("default");
      }
    }
    if (selectedFigureElement && selectedFigureElement.modelLoc != "相机" && selectedFigureElement.modelLoc != "人物")  //[i18n:ignore]
    {
      setCurType(AI_PolyTargetType.Furniture);
    }
    if (selectedTRoom &&
      (selectedTRoom.isSelectSeries
        || selectedTRoom.haveSomeMatchedMaterial())) {
      setShowLayoutList(false);
    } else {
      setShowLayoutList(true);
    }
  }, [selectedFigureElement?.category,
  store.homeStore.selectEntity,
    selectedFigureElement, store.homeStore.room2SeriesSampleArray]);

  useEffect(() => {
    // 在预览3D时，楼层属性面板不显示
    if (curType === "default" && store.homeStore.preview3D) {
      store.homeStore.setIsShowRightSide(true);
    }
    else {
      store.homeStore.setIsShowRightSide(false);
    }
  }, [store.homeStore.preview3D, curType]);

  // 在组件的渲染逻辑中添加条件判断
  if (curType === "default" && store.homeStore.preview3D) {
    return null;
  }

  const calcHeight = () => {
    return document.documentElement.clientHeight - 48;
  }


  return (
    <PanelContainer
      right={right ? 0 : 'null'}
      left={left ? 0 : 'null'}
      height={calcHeight()}
      width={250}
      draggable={true}
      title='基本'
      showHeader={false}
    >
      <div ref={divRef}>
        <div className={styles.rootTitle}>
          <span>
            {typeNameMap.get(curType)}
          </span>
        </div>

        {/* 空间信息 //[i18n:ignore] */}
        {curType === "default" && (
          <FloorProperties showLayoutList={showLayoutList} roomInfos={store.homeStore.roomInfos} />
        )}
        {typeNameMap.has(store.homeStore.selectEntity?.type)
          && store.homeStore.selectEntity
          && curType == AI_PolyTargetType.RoomSubArea
          && (
            <EntityProperties Entity={store.homeStore.selectEntity} />
          )}
        {curType === AI_PolyTargetType.RoomArea && selectedTRoom && (
          <RoomProperties
            showLayoutList={showLayoutList}
            roomInfos={store.homeStore.roomInfos}
            roomInfo={selectedTRoom}
            currenScheme={store.homeStore.currenScheme}
            selectedFigureElement={selectedFigureElement}
          />
        )}

        {curType === AI_PolyTargetType.Furniture && selectedFigureElement && (
          <GoodsProperties selectedFigureElement={selectedFigureElement}></GoodsProperties>
        )}
      </div>
    </PanelContainer>
  );
};


export default observer(RightPropertyPanel);
