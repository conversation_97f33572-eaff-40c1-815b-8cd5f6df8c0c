import { openApiRequest } from "@/utils/request/index";
import {
  gatewayUrl,
  ENV,
  APP_ID,
} from "@/config";

/**
 * 预分享
 * @param {object} params GetContentWaitShareInfoParam
 * @param {string} params.articleCover 用户自定义文章封面图
 * @param {string} params.contentId 内容ID
 * @param {string} params.description 用户自定义描述
 * @param {string} params.houseTypeImg 用户自定义户型图
 * @param {string} params.schemeCoverImgUrl 用户自定义方案封面图
 * @param {object} params.shareContentType 内容类型, 1：3D方案，2：视频，4：文章，5：施工日志， 6：工地案例， 7：报价器，8：方案生成器
 * @param {object} params.shareEntrance 分享入口 1: app首页, 2: 搜索页, 3：名片页, 4: 我的方案页, 5: 我的视频页, 6: 小区营销楼盘详情页, 7: 分享列表页，8：分享页
 * @param {object} params.shareParam 分享埋点参数
 * @param {string} params.title 用户自定义标题
 * @returns
 */

// https://magiccube-gateway.3weijia.com/doc.html#/mj-message-b-web/%E3%80%90%E5%86%85%E5%AE%B9%E5%88%86%E4%BA%AB%E3%80%91%E6%8E%A5%E5%8F%A3/shareContentUsingPOST
// 参数压缩成uuid
export async function mix_preShareToLogId(data: any) {
  const res = await openApiRequest({
    method: "post",
    url: `${gatewayUrl}/mj-message-b-web/share/content/share/prepareNoShareUser`,
    data,
  });

  if (res.code === 0) {
    return res.data;
  }

  return null;
}

// 海报默认配置
const DEFAULT_CONFIG = {
  // 浏览器配置（截图宽度）
  viewport: {
    width: 750,
  },
  // 模板分组
  group: "wisdom-app",
  // shotOptions: {
  //     type: 'png',
  //     quality: 70
  // }
};

// 海报生成
export async function createPoster(params: any) {
  const res = await openApiRequest({
    method: "post",
    url: `${gatewayUrl}/motu/createImg`,
    data: { ...params },
  });
  return res;
}

// 海报后取
export default async function getPoster({ templateName, data }: any) {
  const res = await createPoster({
    ...DEFAULT_CONFIG,
    templateName,
    data,
  });

  if (res.success) {
    return res.data.url;
  }
  return "";
}

/**
 * 创建小程序码（根据SaaS租户ID）
 * @param {object} params param
 * @param {boolean} params.autoColor 是否自动配置线条颜色。默认为 false
 * @param {boolean} params.cancelQrcode 取消二维码生成
 * @param {boolean} params.isHyaline 是否需要透明底色，is_hyaline 为 true 时，生成透明底色的小程序码。默认为 false
 * @param {object} params.lineColor auto_color 为 false 时生效，使用 rgb 设置颜色 例如 {"r":"xxx","g":"xxx","b":"xxx"} 十进制表示
 * @param {string} params.logoUrl logo图片地址
 * @param {string} params.page 扫描跳转页面，允许为空（此时默认跳转到首页），非空则必须是已经发布的小程序存在的页面（否则报错）
 * @param {string} params.sceneInfo 小程序码场景信息  （额外参数在这里呆？？？
 * @param {number} params.width 小程序宽度，默认430
 * @returns
 */

async function generateMinAppCode(params: any) {
  return await openApiRequest({
    method: "post",
    url: `${gatewayUrl}/mj-wx-b-web/ma/saas/createQrCodeNoLogin`,
    data: params,
    headers: {
      'wx-app-id': APP_ID
    }
  });
}

// wxappid带上请求头，且后端要配置
export async function getSceneAndMiniCode(params = {}) {
  const res = await generateMinAppCode({
    ...params,
    logoUrl:
      ENV === "test"
        ? undefined
        : "https://3vj-fe.3vjia.com/wisdom-app/logo-3dxiu-430.png",
    isHyaline: false,
    cancelQrcode: true,
  });

  if (!res.success) {
    return;
  }

  console.log(`${gatewayUrl}/mj-wx-c-web/ma/qrcode/createImage?id=${res.data.id}`);

  return {
    codeUrl: `${gatewayUrl}/mj-wx-c-web/ma/qrcode/createImage?id=${res.data.id}`,
    scene: res.data.id,
  };
}
