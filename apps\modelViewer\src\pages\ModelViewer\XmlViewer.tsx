import React, { useState } from 'react';
import './XmlViewer.less'; // 我们稍后会创建这个CSS文件

interface XmlNode {
  tag: string;
  attributes: Record<string, string>;
  children: (XmlNode | string)[];
  isOpen?: boolean;
}

interface XmlViewerProps {
  xmlString: string;
}

const XmlViewer: React.FC<XmlViewerProps> = ({ xmlString }) => {
  const [xmlData, setXmlData] = useState<XmlNode | null>(null);
  const [error, setError] = useState<string | null>(null);

  // 解析XML字符串
  const parseXml = (xmlStr: string): XmlNode | null => {
    try {
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(xmlStr, 'text/xml');
      
      if (xmlDoc.getElementsByTagName('parsererror').length > 0) {
        throw new Error('Invalid XML format');
      }
      
      return parseNode(xmlDoc.documentElement);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to parse XML');
      return null;
    }
  };

  // 递归解析XML节点
  const parseNode = (node: Element): XmlNode => {
    const attributes: Record<string, string> = {};
    for (let i = 0; i < node.attributes.length; i++) {
      const attr = node.attributes[i];
      attributes[attr.name] = attr.value;
    }

    const children: (XmlNode | string)[] = [];
    for (let i = 0; i < node.childNodes.length; i++) {
      const child = node.childNodes[i];
      if (child.nodeType === Node.ELEMENT_NODE) {
        children.push(parseNode(child as Element));
      } else if (child.nodeType === Node.TEXT_NODE && child.textContent?.trim()) {
        children.push(child.textContent.trim());
      }
    }

    return {
      tag: node.tagName,
      attributes,
      children,
      isOpen: false,
    };
  };

  // 初始化解析
  React.useEffect(() => {
    if (xmlString) {
      const parsed = parseXml(xmlString);
      setXmlData(parsed);
    }
  }, [xmlString]);

  // 切换节点展开/折叠
  const toggleNode = (node: XmlNode) => {
    node.isOpen = !node.isOpen;
    setXmlData({ ...(xmlData as XmlNode) });
  };

  // 渲染XML节点
  const renderNode = (node: XmlNode | string, index: number) => {
    if (typeof node === 'string') {
      return (
        <div key={index} className="xml-text">
          <span className="xml-text-value">"{node}"</span>
        </div>
      );
    }

    const hasChildren = node.children.length > 0;
    const isComplex = hasChildren && node.children.some(child => typeof child !== 'string');

    return (
      <div key={index} className="xml-element">
        <div 
          className="xml-tag-start" 
          onClick={() => hasChildren && toggleNode(node)}
        >
          <span className="xml-angle-bracket">&lt;</span>
          <span className="xml-tag-name">{node.tag}</span>
          {Object.entries(node.attributes).map(([name, value]) => (
            <React.Fragment key={name}>
              <span className="xml-attribute-name"> {name}</span>
              <span className="xml-equals">=</span>
              <span className="xml-attribute-value">"{value}"</span>
            </React.Fragment>
          ))}
          <span className="xml-angle-bracket">
            {!hasChildren ? ' />' : '>'}
          </span>
        </div>

        {hasChildren && node.isOpen && (
          <div className="xml-children">
            {node.children.map((child, i) => renderNode(child, i))}
          </div>
        )}

        {hasChildren && (
          <div 
            className="xml-tag-end" 
            onClick={() => hasChildren && toggleNode(node)}
            style={{ display: node.isOpen ? 'block' : 'none' }}
          >
            <span className="xml-angle-bracket">&lt;/</span>
            <span className="xml-tag-name">{node.tag}</span>
            <span className="xml-angle-bracket">&gt;</span>
          </div>
        )}

        {hasChildren && !node.isOpen && (
          <span className="xml-collapsed-placeholder" onClick={() => toggleNode(node)}>
            ...
          </span>
        )}
      </div>
    );
  };

  if (error) {
    return <div className="xml-error">Error: {error}</div>;
  }

  if (!xmlData) {
    return <div className="xml-loading">Loading XML...</div>;
  }

  return (
    <div className="xml-viewer">
      {renderNode(xmlData, 0)}
    </div>
  );
};

export default XmlViewer;