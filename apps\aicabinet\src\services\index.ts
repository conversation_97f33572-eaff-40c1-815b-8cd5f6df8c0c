import { openApiRequest } from "@/utils/request/index";
import { gatewayUrl, openUrl } from "@/config";

interface AggrTreeParams {
  categoryId: number;
  materialType: number;
  size: number;
  sort: number;
  sourceTag: number;
}

// 获取素材广场筛选项
export async function aggrTreeBySearch(params: AggrTreeParams) {
  const res = await openApiRequest({
    method: 'post',
    url: `${gatewayUrl}/sd-material/api/materialLibrary/tag/v1/aggrTreeBySearch`,
    data: {
      ...params,
    },
  }).catch((e: any)=>{
    console.log(e);
    return null;
  });
  return res.success && res.data ? res.data : null;
}

// 点赞 https://open.3vjia.com/api/njvr/aiwxDesignerStatistics/insert
export async function likeCabinet(params: any) {
  const res = await openApiRequest({
    method: "post",
    url: `${openUrl}/api/njvr/aiwxDesignerStatistics/insert`,
    data: {
      ...params,
    },
  });
  return res.success && res.result ? res.result : null;
}

// 查询点赞 
export async function listPraised(params: any) {
  const res = await openApiRequest({
    method: "post",
    url: `${openUrl}/api/njvr/aiwxDesignerStatistics/listPraised`,
    data: {
      ...params,
    },
  });
  return res.success && res.result ? res.result : null;
}

// 取消点赞
export async function cancelPraise(params: { dataId: string }) {
  const res = await openApiRequest({
    method: "post",
    url: `${openUrl}/api/njvr/aiwxDesignerStatistics/cancelPraise`,
    data: {
      ...params,
    },
  });
  return res.success ? res.result : null;
}

