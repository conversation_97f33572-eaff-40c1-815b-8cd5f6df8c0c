import { makeAutoObservable } from 'mobx';

/**
* @description AI灯光数据
* <AUTHOR>
* @date 2025-02-18 11:09:40
* @lastEditTime 2025-02-18 11:09:40
* @lastEditors wangyq
*/
class AiLightStore {
    currentAILightImageID = '' as string;
    isEdit = false as boolean;
    isLoading = false as boolean;
    allowCommit = false as boolean;
    isSelectPerspective = true as boolean;
    refreshAtlas = false as boolean;
    constructor() {
        makeAutoObservable(this, {}, {autoBind: true});
    }
    setCurrentAILightImageID(data: string) {
        this.currentAILightImageID = data;
    }
    setIsEdit(data: boolean) {
        this.isEdit = data;
    }
    setAllowCommit(data: boolean) {
        this.allowCommit = data;
    }
    setIsLoading(data: boolean) {
        this.isLoading = data;
    }
    setIsSelectPerspective(data: boolean) {
        this.isSelectPerspective = data;
    }
    setRefreshAtlas(data: boolean) {
        this.refreshAtlas = data;
    }
}

export default AiLightStore;