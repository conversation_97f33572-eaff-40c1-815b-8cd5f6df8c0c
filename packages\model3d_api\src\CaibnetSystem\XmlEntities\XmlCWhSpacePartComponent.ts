import { I_XmlCWhSpacePartComponent } from "../IWardrobeEntityInterface";
import { NumberVariable } from "../NumberVariable";

export class XmlCWhSpacePartComponent {
    spaceUidN: number;
    roundSpaceExUidN: number;
    spacePlaceRuleS?: string;
    spacePlacedefaultTypeS?: string;
    splitTypeN?: number;
    SpaceVariableComponet?: {
        NumberVariable?: NumberVariable[];
    };
    constructor(data?:I_XmlCWhSpacePartComponent)
    {
        data = data ?? {};
        this.spaceUidN = data.spaceUidN || 0;
        this.roundSpaceExUidN = data.roundSpaceExUidN || 0;
        this.spacePlaceRuleS = data.spacePlaceRuleS || "";
        this.spacePlacedefaultTypeS = data.spacePlacedefaultTypeS || "";
        this.splitTypeN = data.splitTypeN || 0;
        this.SpaceVariableComponet = {
            NumberVariable : []
        };
        if(data.SpaceVariableComponet?.NumberVariable)
        {
            this.SpaceVariableComponet.NumberVariable = data.SpaceVariableComponet.NumberVariable.map((nv)=>new NumberVariable(nv));
        }
    }
}