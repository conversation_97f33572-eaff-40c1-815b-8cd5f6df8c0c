export declare type TopMenuType = 'label' | 'button' | 'buttonGray' | 'checkbox' | 'image' | 'custom';
export declare type TopMenuTagType = 'vip' | 'download' | string;
/**
 *  一些UI接口, 解耦前端架构
 */
export interface I_TopMenuItem 
{
    id: string;
    title: string;
    icon?: string;
    iconType?: '' | 'svg';
    disabled?: boolean;
    checked?: boolean | 1 | 2 | 3;
    indeterminate?: boolean;
    type?: TopMenuType;
    tag?: TopMenuTagType;
    badged?: boolean;
    divider?: boolean;
    isAtlas?: boolean;
}
export interface I_CommandTopMenuItem extends I_TopMenuItem {
    command_name?: string;
    titleCn?: string;
    subList?: I_CommandTopMenuItem[];
    onClick?: () => void;

}

export interface I_TopMenuProps {
    logo?: string;
    title?: string;
    centerBtnList?: I_CommandTopMenuItem[];
    rightBtnList?: I_CommandTopMenuItem[];
    backIcon?: boolean;
    getPopupContainer?: (triggerNode?: HTMLElement) => HTMLElement;
    onBack?: (e?:any) => void;
    onBtnClick?: (id: string, item: I_TopMenuItem) => void;
    onBtnHover?: (id: string, item: I_TopMenuItem) => void;
    onBtnLeave?: (id: string, item: I_TopMenuItem) => void;
    onUpdateMenu?: (menuId: string, items: I_TopMenuItem[]) => void;
}