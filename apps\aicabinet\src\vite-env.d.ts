/// <reference types="vite/client" />
/// <reference types="node" />
declare module '@svg/sso-plus' {
    export interface LogOutParams {
      appId: string;
    }
  
    export interface SSOUrlParams {
      type: string;
    }
  
    export class SsoPlus {
      getSsoUrl(params: SSOUrlParams): Promise<string>;
      toLogOut(params: LogOutParams): Promise<any>;
    }
  
    export function svgSsoPlus(options: { appId: string; env: any }): SsoPlus;
  }