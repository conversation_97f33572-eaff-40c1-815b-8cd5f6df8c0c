import type { IState } from '../../interfaces/IState';
import { Design2DStates, Design3DStates } from '../../const/FSMConst';
import { appContext } from '../../../AppContext';

/**
 * 3D空闲状态
 */
export class Idle3DState implements IState {
  name: string;
  parent?: any;

  constructor(name: string = Design3DStates.IDLE3D) {
    this.name = name;
  }

  onEnter(data?: any): void {
    // 状态进入逻辑
  }

  onExit(data?: any): void {
    // 状态退出逻辑
  }

  /**
   * 切换到2D模式
   */
  switchTo2D(): void {
    // 发送事件到主FSM进行跨FSM切换
    appContext.mainFSM.transitionTo(Design2DStates.IDLE2D);
  }
} 