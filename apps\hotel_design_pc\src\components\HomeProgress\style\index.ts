import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  return {
    progressInfo: css`
      position: absolute;
      top: 0%;
      padding-top: 20%;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.3); /* 设置背景颜色为半透明的黑色 */
      z-index: 9999; /* 确保蒙层在其他元素之上 */
    `,
    progressInfoContent: css`
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      width: 100%;
      @keyframes rotate {
        0% {
            transform: rotate(0deg);
        }
        25% {
            transform: rotate(90deg);
        }
        50% {
            transform: rotate(180deg);
        }
        75% {
            transform: rotate(270deg);
        }
        100% {
            transform: rotate(360deg);
        }
      }
      .rotating {
          animation: rotate 2s linear infinite; /* 2秒完成一次旋转，线性动画，循环 */
      }
    `
  };
});
