import { Vector3 } from "three";

export class ZInputDimension  {
    private _input: HTMLInputElement | null = null;
    private _onValueChange: ((value: number) => void) | null = null;
    private _onEnterPressed: ((value: number) => void) | null = null;
    private _start: Vector3;
    private _end: Vector3;
    public isSelected: boolean = false;
    private _isVisible: boolean = true;

    constructor(start: Vector3, end: Vector3) {
        this._start = start.clone();
        this._end = end.clone();
    }

    get start(): Vector3 {
        return this._start;
    }

    get end(): Vector3 {
        return this._end;
    }

    setPoints(start: Vector3, end: Vector3): void {
        this._start.copy(start);
        this._end.copy(end);
    }

    private createInput(): HTMLInputElement {
        const input = document.createElement('input');
        input.id = 'dimension_input';
        input.style.position = 'absolute';
        input.style.border = 'none';
        input.style.outline = 'none';
        input.style.textAlign = 'center';
        input.style.color = '#2b2b2b';
        input.style.fontSize = '14px';
        input.style.fontFamily = 'Arial';
        input.style.padding = '2px 4px';
        input.style.borderRadius = '2px';
        input.style.border = '1px solid #d2d3d4';
        input.style.cursor = 'pointer';
        input.style.textShadow = `
            -1px -1px 0 #fff,
            1px -1px 0 #fff,
            -1px 1px 0 #fff,
            1px 1px 0 #fff,
            0px 0px 2px #fff,
            0px 0px 3px #fff
        `;
        input.autocomplete = "off";
        
        this.setupInputEvents(input);
        return input;
    }

    private setupInputEvents(input: HTMLInputElement) {
        input.onkeypress = (ev: KeyboardEvent) => {
            if (!/^\d$/.test(ev.key) && !ev.ctrlKey && !ev.metaKey) {
                ev.preventDefault();
            }
        };

        input.onkeydown = (ev: KeyboardEvent) => {
            ev.stopPropagation();
            if (ev.key === "Enter") {
                const value = this.evaluateInputValue(input.value);
                if (value !== null) {
                    if(this._onValueChange){
                        this._onValueChange(value)
                    }
                    if (this._onEnterPressed) {
                        this._onEnterPressed(value);
                    }
                }
            }
        };

        input.onfocus = () => {
            input.style.border = '1px solid #04f';
            input.select();
        };

        input.onblur = () => {
            input.style.border = 'none';
            this.handleInputConfirm();
        };

        input.oninput = () => {
            input.value = input.value.replace(/\D/g, '');
            input.style.width = ((input.value.length + 1) * 8) + 'px';
        };
    }

    private handleInputConfirm() {
        if (!this._input || !this._onValueChange) return;
        
        const value = this.evaluateInputValue(this._input.value);
        if (value !== null) {
            this._onValueChange(value);
        }
    }

    private evaluateInputValue(str: string): number | null {
        const cleanStr = str.replace(/\D/g, '');
        
        if (!cleanStr) {
            return null;
        }

        const value = parseInt(cleanStr, 10);
        return isNaN(value) ? null : value;
    }

    setOnValueChange(callback: (value: number) => void) {
        this._onValueChange = callback;
    }

    setOnEnterPressed(callback: (value: number) => void) {
        this._onEnterPressed = callback;
    }

    showInput(painter: any, value: number, isEditing: boolean = false,canEdit: boolean = true) {
        if (!this._input || !this._input.parentNode || !document.getElementById('dimension_input')) {
            if (this._input) {
                this.dispose();
            }
            this._input = this.createInput();
            document.getElementById('Canvascontent')?.appendChild(this._input);
        }

        this._isVisible = true;

        const center = this.getCenter();
        const screenPos = painter.worldToScreen(center);

        if(this._input.parentElement)
        {
            let t_rect = this._input.parentElement.getBoundingClientRect();
            screenPos.x -= t_rect.left;
            screenPos.y -= t_rect.top;
        }
        let input_width = ((this._input.value.length + 2) * 8);
        // this.inpout.clientWidth 与 clientHeight 都为） 所以这里换成input_width 
        this._input.value = value.toFixed(0);
        this._input.style.width = input_width + 'px';
        this._input.style.left = (screenPos.x - input_width/2) + "px";
        this._input.style.top = (screenPos.y-10) + "px";
        this._input.style.display = 'block';

        if (isEditing && canEdit) {
            this._input.focus();
            this._input.select();
        }

        // 设置是否可编辑
        this._input.readOnly = !canEdit;
        // 如果不可编辑，移除背景和边框，并改变鼠标样式
        if (!canEdit) {
            this._input.style.background = 'transparent';
            this._input.style.border = 'none';
            this._input.style.pointerEvents = 'none';  // 禁用所有鼠标事件
        } else {
            this._input.style.background = '#fff';
            this._input.style.border = '1px solid #d2d3d4';
            this._input.style.cursor = 'pointer';
        }
    }

    hideInput() {
        if(!this._input) return;
        this._input.style.display = 'none';
        this._isVisible = false;
    }

    dispose() {
        if (this._input && this._input.parentNode) {
            this._input.parentNode.removeChild(this._input);
        }
        this._input = null;
        this._onValueChange = null;
        this._onEnterPressed = null;
        this._isVisible = false;
    }

    private getCenter(): Vector3 {
        return new Vector3(
            (this._start.x + this._end.x) / 2,
            (this._start.y + this._end.y) / 2,
            (this._start.z + this._end.z) / 2
        );
    }

    drawDimension(painter: any) {
        if (!painter || !painter._context) return;
        if(!this._isVisible) {
            this.hideInput();
            return;
        };
        const ctx = painter._context;
        
        // 获取起点和终点的屏幕坐标
        const startPoint = painter.project2D(this._start);
        const endPoint = painter.project2D(this._end);
        
        ctx.beginPath();
        ctx.strokeStyle = "#2b2b2b";
        ctx.lineWidth = 1;
        
        // 计算标尺线的角度
        const angle = Math.atan2(endPoint.y - startPoint.y, endPoint.x - startPoint.x);
        
        // 绘制主线
        ctx.moveTo(startPoint.x, startPoint.y);
        ctx.lineTo(endPoint.x, endPoint.y);
        
        // 标记长度设置
        const markLength = 8;
        
        // 计算垂直和45度角的方向向量
        const perpAngle = angle + Math.PI/2;  // 垂直方向
        const slantAngle = angle + Math.PI/4;  // 45度角
        
        // 在起点绘制垂直标记
        ctx.moveTo(
            startPoint.x - Math.cos(perpAngle) * markLength/2,
            startPoint.y - Math.sin(perpAngle) * markLength/2
        );
        ctx.lineTo(
            startPoint.x + Math.cos(perpAngle) * markLength/2,
            startPoint.y + Math.sin(perpAngle) * markLength/2
        );
        
        // 在起点绘制45度标记
        ctx.moveTo(
            startPoint.x - Math.cos(slantAngle) * markLength/2,
            startPoint.y - Math.sin(slantAngle) * markLength/2
        );
        ctx.lineTo(
            startPoint.x + Math.cos(slantAngle) * markLength/2,
            startPoint.y + Math.sin(slantAngle) * markLength/2
        );
        
        // 在终点绘制垂直标记
        ctx.moveTo(
            endPoint.x - Math.cos(perpAngle) * markLength/2,
            endPoint.y - Math.sin(perpAngle) * markLength/2
        );
        ctx.lineTo(
            endPoint.x + Math.cos(perpAngle) * markLength/2,
            endPoint.y + Math.sin(perpAngle) * markLength/2
        );
        
        // 在终点绘制45度标记
        ctx.moveTo(
            endPoint.x - Math.cos(slantAngle) * markLength/2,
            endPoint.y - Math.sin(slantAngle) * markLength/2
        );
        ctx.lineTo(
            endPoint.x + Math.cos(slantAngle) * markLength/2,
            endPoint.y + Math.sin(slantAngle) * markLength/2
        );
        
        ctx.stroke();
    }

    render(renderer: any) {
        if (renderer.ctx) {
            this.draw(renderer.ctx);
        }
    }

    draw(ctx: any) {
        this.drawDimension(ctx);
    }
} 