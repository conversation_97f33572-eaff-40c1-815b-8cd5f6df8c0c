import { makeAutoObservable } from 'mobx';

/**
 * @description 布局方案状态数据
 */
class SchemeStatusStore {
  private _pendingOpenSchemeIn3D: boolean = false;
  private _pendingCreateDreamerScheme: boolean = false;
  private _pendingRenderPanorama: boolean = false;
  private _layoutSchemeSaved: boolean = false;
  
  constructor() {
    makeAutoObservable(this, {}, {autoBind: true});
  }
  
  public set pendingOpenSchemeIn3D(data: boolean)
  {
    this._pendingOpenSchemeIn3D = data;
  }
  public set layoutSchemeSaved(data: boolean)
  {
    this._layoutSchemeSaved = data;
  }
  public set pendingCreateDreamerScheme(data: boolean)
  {
    this._pendingCreateDreamerScheme = data;
  }
  public set pendingRenderPanorama(data: boolean)
  {
    this._pendingRenderPanorama = data;
  }

  public get pendingOpenSchemeIn3D()
  {
    return this._pendingOpenSchemeIn3D;
  }
  public get layoutSchemeSaved()
  {
    return this._layoutSchemeSaved;
  }
  public get pendingCreateDreamerScheme()
  {
    return this._pendingCreateDreamerScheme;
  }
  public get pendingRenderPanorama()
  {
    return this._pendingRenderPanorama;
  }
}

export default SchemeStatusStore;