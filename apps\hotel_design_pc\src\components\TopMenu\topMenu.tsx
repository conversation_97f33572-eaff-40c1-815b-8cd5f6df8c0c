import {AI2DesignBasicModes, EventName,LayoutAI_App, LayoutAI_Commands, 
  LayoutAI_Events,OperationManager,CadDrawingLayerType,TLayoutEntityContainer,TRoomEntity,TAppManagerBase, LayoutAI_Configs
} from '@layoutai/layout_scheme';
import HxSearch from '@/components/HxSearch';
import {
  _from,
  AIDeskUrl,
  APP_ID,
  is_dreamer_mini_App,
  is_standalone_website,
  mini_APP,
  workDomainMap
} from '@/config';
import { useStore } from '@/models';
import { message, Modal, Tooltip } from '@svg/antd';
import { TopMenu } from '@svg/antd-cloud-design';
import type { ITopMenuItem, TopMenuType } from '@svg/antd-cloud-design/lib/TopMenu';
import { observer } from 'mobx-react-lite';
import React, { useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import Icon from '@/components/Icon/icon';
import { checkIsMobile } from '@/config';
import { useTranslation } from 'react-i18next';
import useStyles from './style/index';
import { Vector3 } from 'three';
import { DrawingFigureMode } from '@layoutai/basic_data';
import { I_UITemplates } from '@/apps/UiTemplates/I_UITemplate';

/**
 *  扩展带命令得顶部元素
 */
interface ICommandTopMenuItem extends ITopMenuItem {
  command_name?: string;
  titleCn?: string;
  onClick?: () => void;
  subList?: ICommandTopMenuItem[];
}
const { confirm } = Modal;
interface TopMenuProps {
  handler: (cmd: string) => void;
  title: React.ReactNode;
  create3DLayout: () => void;
}

const MyComponent: React.FC<TopMenuProps> = ({ handler, title, create3DLayout }) => {
  const t  = LayoutAI_App.t;
  const location = useLocation();
  const currentPath = location.pathname;
  const store = useStore();

  const {
    designMode,
    viewMode,
    selectedRoom,
    shareVisible,
    setShowWelcomePage,
    setShareVisible,
    setShowDreamerPopup,
    setDesignMode,
    setShowSaveLayoutSchemeDialog,
    setMenuKey,
    setShowHouseSchemeAddForm,
    setPreview3D,
    setViewMode
  } = store.homeStore;

  const { styles } = useStyles();
  const [redo_disabled, set_redo_disabled] = useState<boolean>(true);
  const [undo_disabled, set_undo_disabled] = useState<boolean>(false);
  const [lockCopyImage, setLockCopyImage] = useState<boolean>(true);
  const [rightBtnList, setRightBtnList] = useState<any[]>([]);
  const getCurrentLayerState = (): { [key: string]: boolean } => {
    let layer_state_data: { [key: string]: boolean } = {};
    let appManager = (LayoutAI_App.instance as TAppManagerBase);
    if(!appManager?.drawing_layers)
    {
      layer_state_data[CadDrawingLayerType.CadEzdxfDrawing] = false;
      layer_state_data[CadDrawingLayerType.CadRoomStrucure] = true;
      layer_state_data[CadDrawingLayerType.CadFurniture] = true;
      layer_state_data[CadDrawingLayerType.CadCabinet] = true;
      layer_state_data[CadDrawingLayerType.CadOutLine] = true;
      layer_state_data[CadDrawingLayerType.CadLighting] = false;
      layer_state_data[CadDrawingLayerType.CadCeiling] = false;
      layer_state_data[CadDrawingLayerType.CadDecorates] = false;
      layer_state_data[CadDrawingLayerType.CadSubRoomAreaDrawing] = LayoutAI_App.IsDebug ? true : false;
      layer_state_data[CadDrawingLayerType.CadDimensionWallElement] = false;
      layer_state_data[CadDrawingLayerType.CadDimensionOutterWallElement] = false;
      layer_state_data[CadDrawingLayerType.RulerDrawing] = true;
      layer_state_data[CadDrawingLayerType.CadRoomName] = true;
      return layer_state_data;
    }else
        layer_state_data[CadDrawingLayerType.CadEzdxfDrawing] = appManager.drawing_layers[CadDrawingLayerType.CadEzdxfDrawing]?.visible;
        layer_state_data[CadDrawingLayerType.CadRoomStrucure] = appManager.drawing_layers[CadDrawingLayerType.CadRoomStrucure]?.visible;
        layer_state_data[CadDrawingLayerType.CadFurniture] = appManager.drawing_layers[CadDrawingLayerType.CadFurniture]?.visible;
        layer_state_data[CadDrawingLayerType.CadCabinet] = appManager.drawing_layers[CadDrawingLayerType.CadCabinet]?.visible;
        layer_state_data[CadDrawingLayerType.CadOutLine] = appManager.drawing_layers[CadDrawingLayerType.CadOutLine]?.visible;
        layer_state_data[CadDrawingLayerType.CadLighting] = appManager.drawing_layers[CadDrawingLayerType.CadLighting]?.visible;
        layer_state_data[CadDrawingLayerType.CadCeiling] = appManager.drawing_layers[CadDrawingLayerType.CadCeiling]?.visible;
        layer_state_data[CadDrawingLayerType.CadDecorates] = appManager.drawing_layers[CadDrawingLayerType.CadDecorates]?.visible;
        layer_state_data[CadDrawingLayerType.CadSubRoomAreaDrawing] = LayoutAI_App.IsDebug ? true : false;
        layer_state_data[CadDrawingLayerType.CadDimensionWallElement] = appManager.drawing_layers[CadDrawingLayerType.CadDimensionWallElement]?.visible;
        layer_state_data[CadDrawingLayerType.CadDimensionOutterWallElement] = appManager.drawing_layers[CadDrawingLayerType.CadDimensionOutterWallElement]?.visible;
        layer_state_data[CadDrawingLayerType.RulerDrawing] = appManager.drawing_layers[CadDrawingLayerType.RulerDrawing]?.visible;
        layer_state_data[CadDrawingLayerType.CadRoomName] = appManager.drawing_layers[CadDrawingLayerType.CadRoomName]?.visible;
    return layer_state_data; 
  }
  const layer_state_data = getCurrentLayerState();
  const layoutContainer: TLayoutEntityContainer = (LayoutAI_App.instance as TAppManagerBase)
    .layout_container;
  const [layerBtnState, setLayerBtnState] = useState<{ [key: string]: boolean }>(layer_state_data);
  const navigate = useNavigate();
  const hxSearchRef = useRef<any>();
  const IsMobile = checkIsMobile();
  const object_id = 'home_top_menu';
  const [btnList, setBtnList] = useState<ICommandTopMenuItem[]>([]);


  const is_dream_house_mode = is_dreamer_mini_App;


  useEffect(() => {

  }, [designMode, layerBtnState]);

  // 用于显示的右按钮列表
  let rightBtnListShow: ICommandTopMenuItem[] = [];

  if (mini_APP) {
    rightBtnListShow = [
      {
        id: 'shareBtn',
        title: t('分享'),
        titleCn: '分享', //[i18n:ignore]
        icon: 'iconcontrollerrotateleft',
        type: 'label' as TopMenuType
      }
    ];
  } else {
    rightBtnListShow = rightBtnList;
  }

  useEffect(() => {

  }, [store.userStore.userInfo, designMode]);





  useEffect(() => {
    LayoutAI_App.DispatchEvent(LayoutAI_Events.HandleLockCopyImage, lockCopyImage);
  }, [lockCopyImage]);

  useEffect(() => {
    message.config({
      top: 50, // 距离顶部的距离，单位像素
      duration: 2, // 持续时间，单位秒
      maxCount: 3 // 最大显示数量
    });

    LayoutAI_App.on_M(EventName.AIDesignModeChanged, object_id, () => {
      setDesignMode((LayoutAI_App.instance as TAppManagerBase)._current_handler_mode);
    });
    set_undo_disabled(true);
    set_redo_disabled(true);

    if (LayoutAI_App.instance) {
      LayoutAI_App.instance.updateSlot('TopMenu_RedoableSlot', {
        ui_name: 'RedoDisabled',
        target: LayoutAI_App.instance,
        callback: (t: boolean) => {
          set_redo_disabled(!t);
        }
      });

      LayoutAI_App.instance.updateSlot('TopMenu_UndoableSlot', {
        ui_name: 'UndoDisabled',
        target: LayoutAI_App.instance,
        callback: (t: boolean) => {
          set_undo_disabled(!t);
        }
      });

      // LayoutAI_App.on(EventName.OpenHouseSearching,(t:boolean)=>{
      //   if(t)
      //   {
      //     onModal_HxSearch();
      //   }
      // })
      LayoutAI_App.instance.connect_obj(
        OperationManager.signalRedoable,
        LayoutAI_App.instance,
        'TopMenu_RedoableSlot'
      );
      LayoutAI_App.instance.connect_obj(
        OperationManager.signalUndoable,
        LayoutAI_App.instance,
        'TopMenu_UndoableSlot'
      );

      LayoutAI_App.on_M(EventName.SwitchDrawingLayer,object_id, (state_data: { [key: string]: boolean }) => {
      // LayoutAI_App.on(EventName.SwitchDrawingLayer, (state_data: { [key: string]: boolean }) => {
        let state = { ...layerBtnState, ...state_data };

        setLayerBtnState(state);
      });
      LayoutAI_App.on(EventName.ClearLayout, () => {
        confirm({
          title: t('清空布局'),
          content:
            t(`您将清空`) +
            (selectedRoom?.roomname ? `【${t(selectedRoom?.roomname)}】` : '单空间') +
            t('布局，此操作不可恢复。是否确认清空?'),
          okText: t('取消'),
          cancelText: t('确认清空'),
          onOk() {},
          onCancel() {
            LayoutAI_App.DispatchEvent(LayoutAI_Events.ClearLayout, this);
          }
        });
      });


      LayoutAI_App.on_M(EventName.UITemplateChanged,object_id,(data:I_UITemplates)=>{
        if(data.topMenu)
        {
            if(data.topMenu.centerBtnList)
            {
              setBtnList(data.topMenu.centerBtnList);
            }
        }
      })
      // console.log(LayoutAI_App.instance);
    }
    return ()=>{
      LayoutAI_App.off_M_All({object_id:object_id});
    }
  }, []); // 空数组-> 渲染完成后第一次启动

  useEffect(() => {
    let state = { ...layerBtnState };
    state[CadDrawingLayerType.RulerDrawing] = true;

    setLayerBtnState(getCurrentLayerState());
  }, [designMode]);

  let menuItemKeySet = new Set();
  // 添加key处理函数
  const addKeysToMenuItems = (items: ITopMenuItem[]): ITopMenuItem[] => {
    return items
      .map(item => {
        // 使用id作为key
        const itemWithKey = { ...item, key: item?.id };
        if (itemWithKey.key == null) {
          return itemWithKey;
        } else if (menuItemKeySet.has(itemWithKey.key)) {
          console.error('Duplicate ITopMenuItem id:', item);
        } else {
          menuItemKeySet.add(itemWithKey.key);
        }

        // 递归处理子菜单
        if (item.subList) {
          itemWithKey.subList = addKeysToMenuItems(item.subList);
        }

        return itemWithKey;
      })
      .filter(item => item.key != null); // 过滤掉空值
  };
  const onMenuItemClick = async (id: string, item: ICommandTopMenuItem) => {
    if(item.onClick)
    {
      item.onClick();
    }
    else if(item.command_name)
    {
      LayoutAI_App.RunCommand(item.command_name);
    }
    else{
      
    }
  }

  return (
    <div className={styles.topmenuInfo}>


      <HxSearch ref={hxSearchRef} />

      <TopMenu
        logo={
          !store.userStore.aihouse
            ? 'https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/logo.png'
            : 'https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/en_logo.png'
        }
        backIcon={
          is_standalone_website &&
          AI2DesignBasicModes.HouseDesignMode !== designMode &&
          !is_dreamer_mini_App && (
            <Tooltip title={t('返回首页')}>
              <div
                onClick={() => {
                  if (layoutContainer._room_entities.length == 0) {
                    window.location.href = workDomainMap;
                  } else {
                    LayoutAI_App.DispatchEvent(LayoutAI_Events.autoSave, null);
                    message.loading(t('保存中...'));
                    setTimeout(() => {
                      message.destroy();
                      window.location.href = workDomainMap;
                    }, 1500);
                  }
                }}
              >
                <Icon
                  iconClass="iconhouse"
                  style={{
                    fontSize: '18px',
                    color: '#fff',
                    marginLeft: '5px'
                  }}
                ></Icon>
              </div>
            </Tooltip>
          )
        }
        onBack={() => {
          console.log('back');
        }}
        title={title}
        centerBtnList={addKeysToMenuItems(btnList)}
        rightBtnList={addKeysToMenuItems(rightBtnListShow)}
        onBtnClick={onMenuItemClick}
      />
    </div>
  );
};
export default observer(MyComponent);
