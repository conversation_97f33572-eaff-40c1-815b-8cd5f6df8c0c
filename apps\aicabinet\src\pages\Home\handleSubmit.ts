import { getCabinetList, getCabinetImageList } from '@/services/user';
import { useStore } from '@/models';

interface Params {
    trace_id: string;
    images_base64: string;
    width?: number;
    height?: number;
    depth?: number;
    organ_id: string | undefined;
}

const store = useStore();
const generateTraceId = () => {
return Math.random().toString(36).substring(2) + Date.now().toString(36);
}
export const handleSubmit = async (width: number, height: number, depth: number, isSearchExample: boolean, userInfo: any, filterList: any) => {
    store.homeStore.setSkeletonStatus(true);
    if(store.homeStore.tabValue === '参数搭柜') 
    {
      const accurate_dict = filterList.reduce((acc: any, item: any) => {
        acc[item.key] = item.value;
        return acc;
      }, {});

      let label_dict = {
        long_clothes_num: accurate_dict['long_clothes_num'],
        short_clothes_num: accurate_dict['short_clothes_num'],
        pants_num: accurate_dict['pants_num'],
        insert_drawer_num: accurate_dict['insert_drawer_num'] * 2,
        out_drawer_num: accurate_dict['out_drawer_num'] * 2,
        stack_area_ratio: accurate_dict['stack_area_ratio'] * 0.2,
        desk: accurate_dict['desk'] ? 1 : 0,
        side: store.homeStore.selectedTags.includes('开放侧边柜') ? 1 : 0,
      };
      
      const filtered_dict = Object.entries(label_dict).reduce((newDict, [key, value]) => {
        if (value !== 0) {
          newDict[key] = value;
        }
        return newDict;
      }, {} as { [key: string]: any });

      let tags = store.homeStore.selectedTags.filter((item) => item !== '开放侧边柜');
      let nametags = [];
      if(tags.includes('推拉门'))
      {
        nametags.push('sliding_door_wardrobe');
      }
      if(tags.includes('平开门'))
      {
        nametags.push('swing_door_wardrobe');
      }
      let organ_id = [];
      if(store.homeStore.organTags.includes('平台库'))
      {
        organ_id.push('C00000022');
      }
      if(store.homeStore.organTags.includes('企业库'))
      {
        organ_id.push(userInfo?.tenantId);
      }
      const res = await getCabinetList({
        trace_id: generateTraceId(),
        category: nametags,
        width: width,
        height: height,
        depth: depth,
        organ_id_list: organ_id,
        material_id_list: "",
        label_dict: {
          ...filtered_dict
        }
      })
      if(res && res.length > 0)
      {
        res.map((item: any) => {
          item.checked = false;
          item.main_img_path = item.main_img_path.replace('https://test-3vj-xelawk.oss-cn-shenzhen.aliyuncs.com', 'https://test-3vj-xelawk.oss-cn-shenzhen-internal.aliyuncs.com');
        })
        store.homeStore.setCabinetData(res);
      } else 
      {
        store.homeStore.setCabinetData([]);
      }
    }
    if(store.homeStore.tabValue === '以图搭柜')
    {
      const params: Params = {
        trace_id: generateTraceId(),
        images_base64: store.homeStore.file,
        width: width,
        height: height,
        depth: depth,
        organ_id: userInfo.tenantId,
      }
      if(isSearchExample)
      {
        delete params['width'];
        delete params['height'];
        delete params['depth'];
      }
      const res = await getCabinetImageList(params)
  
      if(res && res.length > 0)
      {
        res.map((item: any) => {
          item.checked = false;
          item.main_img_path = item.main_img_path.replace('https://test-3vj-xelawk.oss-cn-shenzhen.aliyuncs.com', 'https://test-3vj-xelawk.oss-cn-shenzhen-internal.aliyuncs.com');
        })
        store.homeStore.setCabinetData(res);
      } else 
      {
        store.homeStore.setCabinetData([]);
      }
    

    }
    store.homeStore.setSkeletonStatus(false);
}