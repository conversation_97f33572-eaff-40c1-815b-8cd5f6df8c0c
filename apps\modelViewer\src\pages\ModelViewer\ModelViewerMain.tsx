
import "./index.less";

import { useState } from "react";

import ModelViewer from "./ModelViewer";
import SeriesModelViewer from "./SeriesModelViewer";
import XmlContentViewer from "./XmlContentViewer";


/**
 * @description 查看器主页面(模型查看器、套系查看器)
 */
const ModelViewerMain: React.FC = () => {

    const [currentId, setCurrentId] = useState<string>("SeriesViewer");
    const menuItems = [
        {
            id: "ModelViewer",
            title: "Id查模型",
        },
        {
            id: "SeriesViewer",
            title: "套系查模型",
        },{
            id : "XmlViewer",
            title : "查看XML"
        }
    ];
    return <>
        <div className="header_topmenu">
            {menuItems.map((item, index) => <div key={"item" + index} className={"header_tab " + (currentId === item.id ? "active" : "")} onClick={() => {
                setCurrentId(item.id);
            }}>{item.title}</div>)}
        </div>
        <div className="main_app">
            {currentId === "ModelViewer" && <ModelViewer></ModelViewer>}
            {currentId === "SeriesViewer" && <SeriesModelViewer></SeriesModelViewer>}
            {currentId === "XmlViewer" && <XmlContentViewer></XmlContentViewer>}

        </div>
    </>

}

export default ModelViewerMain;