import { openApiRequest } from '@/utils/request/index';
import { openUrl, turingUrl, layoutworkuiServiceUrl, visitorUrl } from '@/config';
/**
 * @description 获取用户信息
 */
export async function getUserInfo() {
  const res = await openApiRequest({
    method: 'get',
    url: `${openUrl}/api/njvr/vrusers/getUser`,
  });
  return res.success && res.result ? res.result : null;
}
/**
 * @description 获取标准空间类型名称映射表
 */
export async function getaiCongig() {
  const res = await openApiRequest({
    method: 'post',
    url: '/dp-ai-web/getSpaceTypeMap',
  }).catch((e: any)=>{
    console.log(e);
    return null;
  });
  return res?.success && res.data ? res.data : null;
}

/**
 * @description 获取柜体列表
 */
export async function getCabinetList(params: any) {
  const res = await openApiRequest({
    method: 'post',
    url: `${turingUrl}/cabinet_label/api/query/v1`,
    data: {
      ...params,
    },
  }).catch((e: any)=>{
    console.log(e);
    return null;
  });
  // return res?.status_code == 200 && res.data ? res.data : null;
  // 向公库请求得到的柜体列表数据，需要自行添加organ_type字段，Al库请求另有接口
  if (res?.status_code == 200 && res.data) {
    const modifiedData = res.data.map((item: any) => ({
      ...item,
      organ_type: "platform",
    }));
    return modifiedData;
  } else {
    return null;
  }
}

/**
 * @description 游客获取柜体列表
 */
export async function getCabinetListByVisitor(params: any) {
  const res = await openApiRequest({
    method: 'post',
    url: `${turingUrl}/cabinet_label/api/query/v1`,
    data: {
      ...params,
    },
  }).catch((e: any)=>{
    console.log(e);
    return null;
  });
  // return res?.status_code == 200 && res.data ? res.data : null;
  // 向公库请求得到的柜体列表数据，需要自行添加organ_type字段
  if (res?.status_code == 200 && res.data) {
    const modifiedData = res.data.map((item: any) => ({
      ...item,
      organ_type: "platform",
    }));
    return modifiedData;
  } else {
    return null;
  }
}

/**
 * @description 以图搜图
 */
export async function getCabinetImageList(params: any) {
  const res = await openApiRequest({
    method: 'post',
    url: `${turingUrl}/api/CabinetSearch/cabinet_search`,
    data: {
      ...params,
    },
  }).catch((e: any)=>{
    console.log(e);
    return null;
  });
  return res.result ? res.result : null;
}

/**
 * @description 创建搭柜收藏
 */
export async function insertCabinet(params: any) {
  const res = await openApiRequest({
    method: 'post',
    url: `${layoutworkuiServiceUrl}/api/njvr/LayoutWardrobeFavorite/insert`,
    // url: 'https://hws-open.3weijia.com/api/njvr/LayoutWardrobeFavorite/insert',
    data: {
      ...params,
    },
  }).catch((e: any)=>{
    console.log(e);
    return null;
  });
  return res.result ? res.result : null;
}

/**
 * @description 获取ai搭柜收藏详情
 * */
export async function getAiCabinetDetail(params: any) {
  const res = await openApiRequest({
    method: 'post',
    url: `${layoutworkuiServiceUrl}/api/njvr/LayoutWardrobeFavorite/get`,
    data: {
      ...params,
    },
  }).catch((e: any)=>{
    console.log(e);
    return null;
  });
  return res.result ? res.result : null;
}

/**
 * @description AI生成搭柜
 * */
export async function createAiCabinetByAi(params: any) {
  const res = await openApiRequest({
    method: 'post',
    url: `${visitorUrl}/cabinet_label/api/generate_cabinet/v1`,
    data: {
      ...params,
    },
  }).catch((e: any)=>{
    console.log(e);
    return null;
  })
  return res ? res : null;
}

/**
 * @description Ai帮我写
 * */
export async function aiWrite(params: any) {
  const res = await openApiRequest({
    method: 'post',
    url: `${layoutworkuiServiceUrl}api/njvr/aiwxAssist/writeForBiz`,
    // url: 'https://hws-open.3weijia.com/api/njvr/aiwxAssist/writeForBiz',
    data: {
      ...params,
    },
  }).catch((e: any)=>{
    console.log(e);
    return null;
  })
  return res ? res : null;
}
