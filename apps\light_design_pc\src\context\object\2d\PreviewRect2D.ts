
import { DrawingManager, Object2DBase } from "@layoutai/design_2d";
import { TPainter } from "@layoutai/design_2d";


/**
 * 预览矩形 2D 对象
 */
export class PreviewRect2D extends Object2DBase {
    private _width: number = 0;
    private _height: number = 0;
    private _x: number = 0;
    private _y: number = 0;

    constructor(uuid: string) {
        super(uuid);
    }

    /**
     * 设置矩形大小
     * @param width 宽度
     * @param height 高度
     */
    public setSize(width: number, height: number): void {
        this._width = width;
        this._height = height;
    }

    /**
     * 设置位置
     * @param x X坐标
     * @param y Y坐标
     */
    public setPosition(x: number, y: number): void {
        this._x = x;
        this._y = y;
    }

    /**
     * 获取X坐标
     */
    public get x(): number {
        return this._x;
    }

    /**
     * 设置X坐标
     */
    public set x(value: number) {
        this._x = value;
    }

    /**
     * 获取Y坐标
     */
    public get y(): number {
        return this._y;
    }

    /**
     * 设置Y坐标
     */
    public set y(value: number) {
        this._y = value;
    }

    /**
     * 更新对象状态
     */
    public update(): any | undefined {
        // 预览矩形不需要实体，直接返回自身
        return this;
    }

    public registerRenderCommands(drawingManager: DrawingManager): void {
        drawingManager.addRenderCommand(this.uuid, null, this.render.bind(this));
    }

    /**
     * 绘制预览矩形
     * @param painter 绘制器
     */
    public render(painter: TPainter): void {
        let ctx = painter._context;

        // 保存画布状态
        ctx.save();

        // 绘制矩形
        ctx.beginPath();
        ctx.rect(this.x, this.y, this._width, this._height);

        // 设置矩形样式
        ctx.fillStyle = '#3498db'; // 预览矩形填充色
        ctx.globalAlpha = 0.3; // 增加填充透明度
        ctx.fill();
        ctx.globalAlpha = 1.0; // 边框完全不透明
        ctx.strokeStyle = '#2980b9'; // 更深的蓝色边框
        ctx.lineWidth = 3; // 增加边框宽度
        ctx.stroke();

        // 恢复画布状态
        ctx.restore();
    }
} 