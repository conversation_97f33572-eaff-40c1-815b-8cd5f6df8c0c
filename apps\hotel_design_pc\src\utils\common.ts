
import { hxUrl } from '@/config';
interface Method {
  module: string; // 模块
  instance?: string; // 分层
  panel?: string; // 面板
  method: string; // api
  data?: any;
  deconstruct?: boolean; // 是否解构数组
  callback?: (props: any) => void;
  struct?: boolean; // 是否返回整个结构体
}
let sunvega: any = (window as any).sunvega;
// const listenThrottleMap: Map<string, any[][]> = new Map();


/**
 * @description 3dsdk接口调用
 */
export async function emitMessageToSd({
  module: moduleName,
  instance: instanceName = '',
  method: methodName,
  panel:panelName,
  data,
  deconstruct = true,
  callback,
  struct
}: Method) {
  if (!sunvega) sunvega = (window as any).parent.sunvega;
//   if (!sunvega) sunvega = (window as any).parent.sunvega;
  if (!sunvega) throw new Error(`window中找不到sunvega对象`);
  const module = sunvega[moduleName];
  if (!module) {
    throw new Error(`在sunvega中找不到${moduleName}`);
  }
  let instance = module[instanceName];

  if (!instance) {
    throw new Error(`在${module}中找不到${instanceName}`);
  }

  if(panelName) {
    instance = instance[panelName]
    if(!instance) {
      throw new Error(`在${module}中找不到${panelName}`);
    }
  }
  const method = instance[methodName];
 
 
  if (!method) {
    throw new Error(`在${moduleName}中找不到${methodName}`);
  }
  if(method  === 'ShiftEncryptFor3D') {
  }
  
  const res: any = data
    ? (data instanceof Array) && deconstruct
      ? await method.call(instance, ...data)
      : await method.call(instance, data)
    : typeof data == 'number'
      ? await method.call(instance, data) 
      : await method.call(instance);
  if (!res) console.log(`调用${methodName}方法没有返回值`);
  // if (res.code !== 200) {
  //   throw new Error(`调用${methodName}方法没有成功`);
  // }
  
  callback && callback(struct ? res : (res?.data || ''));
  return struct ? res : (res?.data  || '');
}


export function getPrefix() {
  if (process.env.NODE_ENV === 'development') {
      return "";
  }
  return (globalThis as any).__PUBLIC_PATH__ || ''
}


export function onModal_HxSearch () {
  const iframe = document.createElement('iframe');
  iframe.src = hxUrl;
  iframe.id = 'searchIframe';
  iframe.setAttribute('frameborder', '0');
  iframe.setAttribute('scrolling', 'no');
  iframe.onload = function iframeLoaded() {
    setTimeout(() => {
      iframe.contentWindow?.postMessage({
        origin: 'uiapi.3dhouseplugin',
        data: {
          name: 'houseHome',
          action: 'open',
          params: {
            hasCollection:true,
            hasExcellentScheme:false,
            houseCardBtnArr:[],
            houseDetailBtnArr:[{label:'开始设计',value:'1'}],
          }
        }
      },'*')
    },1000)

  }
  const iframeWrap = document.getElementById('iframe-wrap');
  iframeWrap?.appendChild(iframe);


}