import { createStyles } from "@svg/antd/es/theme/utils";

export default createStyles(({ css }: any) => {
  return {
    root: css`
      position: absolute;
      .swj-businessComponent-propsPanelContainer {
        .svg-a3kxyp {
          height: 100vh !important;
          background-color: #fff;
        }
        .swj-baseComponent-Containersbox-body > div {
          height: calc(100vh - 48px) !important;
          overflow: hidden !important;
        }
        .swj-property-slide-item-container {
          margin-top: 8px;
        }
        .ant-select-sm {
          width: 100%;
        }
        .svg-input-number-suffix {
          background-color: #fff !important;
        }
        .info-top {
          overflow-x: hidden;
        }
        .ant-input-disabled {
          background-color: #fff !important;
          color: #000;
          border: 0;
          cursor: text;
        }
        .ant-input-number-input {
          color: #000 !important;
          cursor: text;
          width: 37px !important;
        }
        .svg-letx3n {
          /* text-align: left; */
        }
        .svg-3n6b0s {
          width: 75px;
        }
      }
      textarea {
        height: 300px !important;
        min-height: 300px !important;
      }

      form .fr-obj-col {
        .swj-property-collapse-item-name {
          font-weight: 600;
        }
        .ant-row .swj-property-collapse-item-name {
          font-size: 12px;
          font-weight: initial;
        }

        .fr-obj-col .ant-row .ant-col .ant-form-item {
          background-color: #f4f4f4;
          padding: 0 8px;
        }
      }
    `,
    name: css`
      position: fixed;
      top: 50px;
      right: 20px;
      z-index: 1200;
    `,
    SideSceneView: css`
      width: 420px;
      height: 320px;
      position: fixed;
      right: 0;
      top: 48px;
      background: #fff;
    `,
    ProductInfoLock: css`
      position: fixed;
      z-index: 1000;
      top: 130px;
      right: 20px;
      width: 24px;
      cursor: pointer;
    `,
  };
});
