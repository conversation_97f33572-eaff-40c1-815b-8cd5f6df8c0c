import { ControllerBase } from "../ControllerBase";
import { CanvasEventType } from "../../events/const/CanvasEventType";
import { ControllerType } from "../ControllerType";
import { Design2DContext } from "../../Design2DContext";

/**
 * 画布变换控制器
 * 负责处理画布的缩放、旋转、平移等变换操作
 * 鼠标中键拖拽移动，鼠标滚轮缩放
 * 继承自ControllerBase，通过事件发送到CanvasEventHandler去执行_transformCallback
 */
export class CanvasTransformCtrl extends ControllerBase {
    // 移动状态
    private _isDragging = false;
    private _lastMouseX = 0;
    private _lastMouseY = 0;

    constructor(context: Design2DContext) {
        super(ControllerType.CANVAS_TRANSFORM, context);
    }

    public onMouseDown(event: MouseEvent): void {
        if (event.button !== 1) {
            return;
        }
        this._isDragging = true;
        this._lastMouseX = event.clientX;
        this._lastMouseY = event.clientY;
    }

    public onMouseMove(event: MouseEvent): void {
        
        if (this._isDragging) {
            const deltaX = event.clientX - this._lastMouseX;
            const deltaY = event.clientY - this._lastMouseY;

            // 通过事件发送到CanvasEventHandler去执行_transformCallback
            this.context.eventCenter.emit(CanvasEventType.CANVAS_TRANSFORM, {
                offsetX: deltaX,
                offsetY: deltaY
            });

            this._lastMouseX = event.clientX;
            this._lastMouseY = event.clientY;
        }
    }

    public onMouseUp(event: MouseEvent): void {
        this._isDragging = false;
    }

    public onWheel(event: WheelEvent): void {
        if (!this._isActive) return;
        
        event.preventDefault();

        const zoomFactor = event.deltaY > 0 ? 0.9 : 1.1;

        // 获取鼠标在canvas中的位置
        const canvas = event.currentTarget as HTMLCanvasElement;
        const rect = canvas.getBoundingClientRect();
        const mouseX = event.clientX - rect.left;
        const mouseY = event.clientY - rect.top;

        // 通过事件发送到CanvasEventHandler去执行_transformCallback，包含鼠标位置
        this.context.eventCenter.emit(CanvasEventType.CANVAS_TRANSFORM, {
            scale: zoomFactor,
            mouseX: mouseX,
            mouseY: mouseY
        });
    }

    public dispose(): void {
        this._isDragging = false;
        this._lastMouseX = 0;
        this._lastMouseY = 0;
        super.dispose();
    }
} 