import { ENV } from "@svg/request";


/**
* @description 处理和实验室应用之间的消息
* 将消息派发到不同应用
* RoyEngine渲染
* AI搭柜
* <AUTHOR>
* @date 2025-04-11
* @lastEditTime 2025-04-11 14:15:59
* @lastEditors xuld
*/
export class RoyMsgEngine {
    private static _isInitEventListener: boolean = false;
    public static HOST_PRE = "https://pre-xr.3vjia.com";
    public static HOST = "https://xr.3vjia.com";
    public static PATH = "/appAILightDesign/";

    private static getHost() {
        if (ENV === "prod") {
            return RoyMsgEngine.HOST;
        } else {
            return RoyMsgEngine.HOST_PRE;
        }
    }

    public static getUrl(params: Record<string, string | number | boolean>): string {
        return RoyMsgEngine.buildUrl(RoyMsgEngine.getHost(), RoyMsgEngine.PATH, params);
    }

    public static buildUrl(host: string, path: string, params: Record<string, string | number | boolean>): string {
        let queryString = "";
        for (let key in params) {
            if (queryString) {
                queryString += "&";
            }
            queryString += `${key}=${params[key]}`;
        }
        // 标准 URL 形式 scheme://host:[port]/path?query#[fragment]
        // scheme: 协议，形如 http, https, ws, wss
        // host: 主机名，形如 www.example.com
        // port: 端口号，可选，形如 8080
        // path: 路径，形如 /path/to/resource
        // query: 查询参数，形如 ?key=value&key=value
        // fragment: 片段，可选，形如 #section
        return `${host}${path}?${queryString}`;
    }


}

(globalThis as any).RoyMsgEngine = RoyMsgEngine;