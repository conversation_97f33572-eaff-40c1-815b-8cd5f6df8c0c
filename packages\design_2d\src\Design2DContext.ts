import { EventCenter } from './events/EventCenter';
import { IEventCenter } from './events/IEventCenter';
import { ControllerManager } from './controller/ControllerManager';
import { Object2DManager } from './object2d/Object2DManager';
import { Canvas2DManager } from './canvas/Canvas2DManager';
import { TPainter } from './draw/TPainter';
import { SelectionManager } from './selection/SelectionManager';

/**
 * 2D设计应用上下文
 * 每个画布都有自己的独立上下文，包含所有管理器的实例
 */
export class Design2DContext {
  private _id: string; // 上下文ID
  private _eventCenter: IEventCenter; // 事件中心
  private _controllerManager: ControllerManager; // 控制器管理器
  private _object2DManager: Object2DManager; // 对象管理器
  private _canvas2DManager: Canvas2DManager; // 画布管理器
  private _selectionManager: SelectionManager; // 选中管理器
  private _painter: TPainter | undefined; // 绘制器
  private _canvas: HTMLCanvasElement | undefined; // 画布
  private _initialized: boolean = false; // 是否初始化

  constructor(id: string) {
    this._id = id;
    
    this._eventCenter = new EventCenter();
    this._object2DManager = new Object2DManager(this);
    this._canvas2DManager = new Canvas2DManager(this);
    this._controllerManager = new ControllerManager(this);
    this._selectionManager = new SelectionManager(this);
  }

  public get id(): string {
    return this._id;
  } 

  /**
   * 创建2D应用上下文（使用外部canvas）
   * @param canvas HTMLCanvasElement 画布元素
   * @param container HTMLDivElement 容器元素
   */
  public createCtxByExternalCanvas(canvas: HTMLCanvasElement): boolean {
    if (this._initialized) {
      console.warn('Design2DContext 已经初始化过了');
      return false;
    }

    this._canvas = canvas;

    //1. 创建painter实例（唯一创建点）
    this._painter = new TPainter(canvas);

    // 2. 初始化事件中心
    (this._eventCenter as EventCenter).init(canvas);

    // 4. 初始化画布管理器（传入painter）
    this._canvas2DManager.createCtxByExternalCanvas(canvas);

    // 5. 初始化各个管理器
    this._object2DManager.init();

    // 6. 初始化控制器管理器
    this._controllerManager.init();

    this._initialized = true;
    return true;
  }

  /**
   * 初始化应用上下文（使用内置canvas）
   * @param container HTMLDivElement 容器元素
   */
  public createCtxByInternalCanvas(id: string): boolean {
    if (this._initialized) {
      console.warn('Design2DContext 已经初始化过了');
      return false;
    }


    // 2. 初始化画布管理器（会创建内置canvas）
    this._canvas2DManager.createCtxByInternalCanvas(id);

    // 3. 获取创建的canvas和painter
    this._canvas = this._canvas2DManager.canvas;
    if (!this._canvas) {
      console.warn('Canvas创建失败');
      return false;
    }
    this._painter = new TPainter(this._canvas);

    if (!this._painter) {
      console.warn('Painter创建失败');
      return false;
    }

    // 4. 初始化事件中心
    (this._eventCenter as EventCenter).init(this._canvas);

    // 5. 初始化各个管理器
    this._object2DManager.init();
    // 6. 初始化控制器管理器
    this._controllerManager.init();

    this._initialized = true;
    return true;
  }

  public bindMainDiv(div: HTMLDivElement): void {
    this._canvas2DManager.bindMainDiv(div);
  }

  /**
   * 获取事件中心
   */
  public get eventCenter(): IEventCenter {
    return this._eventCenter;
  }

  /**
   * 获取控制器管理器
   */
  public get controllerManager(): ControllerManager {
    return this._controllerManager;
  }

  /**
   * 获取对象管理器
   */
  public get object2DManager(): Object2DManager {
    return this._object2DManager;
  }

  /**
   * 获取画布管理器
   */
  public get canvas2DManager(): Canvas2DManager {
    return this._canvas2DManager;
  }

  /**
   * 获取选中管理器
   */
  public get selectionManager(): SelectionManager {
    return this._selectionManager;
  }

  /**
   * 获取画布元素
   */
  public get canvas(): HTMLCanvasElement | undefined {
    return this._canvas;
  }

  /**
   * 获取绘制器
   */
  public get painter(): TPainter | undefined {
    return this._painter;
  }

  /**
   * 检查是否已初始化
   */
  public get initialized(): boolean {
    return this._initialized;
  }

  /**
   * 销毁上下文
   */
  public dispose(): void {
      this._canvas2DManager.dispose();
      this._controllerManager.dispose();
      this._object2DManager.dispose();
      this._selectionManager.dispose();
      this._eventCenter.dispose();
    this._initialized = false;
  }
} 