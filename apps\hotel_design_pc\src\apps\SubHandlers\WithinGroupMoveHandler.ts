

import { EventName, LayoutAI_App, LayoutAI_Commands, TBaseEntity, TBaseGroupEntity, TFurnitureEntity, T_GroupOperationInfo } from "@layoutai/layout_scheme";
import { Vector3 } from "three";
import { I_MouseEvent } from "@layoutai/z_polygon";
import { AI2BaseModeHandler } from "../AI2BaseModeHandler";
import { CadBaseSubHandler } from "./CadBaseSubHandler";



export class WithinGroupMoveHandler extends CadBaseSubHandler {
    _last_pos: Vector3;
    constructor(cad_mode_handler: AI2BaseModeHandler) {
        super(cad_mode_handler);
        this.name = LayoutAI_Commands.Transform_Moving;
        this._last_pos = null;
    }
    enter(state?: number): void {
        super.enter(state);
        let entity = TBaseGroupEntity.getOrMakeEntityOfCadRect(this.selected_target.selected_rect) as TBaseGroupEntity;
  
        let opertion_info = new T_GroupOperationInfo(this.manager, "UnGroup");
        opertion_info._group_base_entity = entity;
        opertion_info.redo();
        this.cleanSelection();
        this.updateCandidateRects();
        this.update();
    }

    onmousedown(ev: I_MouseEvent): void {
        let pos = { x: ev.posX, y: ev.posY, z: 0 };
        if (ev.button == 2) return;
        this.updateSelectedRect(pos);

        this.updateExsorbRects();

        this.updateWholeBox();
        if (!this.selected_target.selected_transform_element || !this.selected_target.selected_transform_element._target_rect) {
            this._cad_mode_handler.runCommand(LayoutAI_Commands.LeaveSubHandler);
            return;
        }
        if(this.selected_target.selected_rect)
        {
            this.EventSystem.emit_M(EventName.SelectingTarget, this.selected_target.selected_rect, ev._ev);
        }
        else{
            this.EventSystem.emit_M(EventName.SelectingTarget, null, ev._ev);

        }
        this.selected_target.selected_transform_element.recordOriginRect(this.selected_target.selected_rect);
        this.selected_target.selected_transform_element.startTransform(this.exsorb_rects);
        this._last_pos = new Vector3(ev.posX, ev.posY, 0);
        this.EventSystem.emit_M(EventName.SelectingTarget, null, ev._ev);
        this._cad_mode_handler._is_moving_element = false;
    }
    onmousemove(ev: I_MouseEvent): void {
        super.onmousemove(ev);
        if (ev.buttons != 1) return;
    
        let transform_element = this.selected_target.selected_transform_element;
        if (!transform_element) return;
        this._cad_mode_handler._is_moving_element = true;

        let current_pos = new Vector3(ev.posX, ev.posY, 0);

        let movement = current_pos.clone().sub(this._last_pos);

        transform_element.applyTransformByMovement(movement, !ev.shiftKey? this.exsorb_rects:null);
        

        this.updateTransformElements();

        this.update();
    }

    onmouseup(ev: I_MouseEvent): void {
        if (this.selected_target.selected_transform_element) {
            let info = this.selected_target.selected_transform_element.updateOperaionInfo(this.manager);
            if (info) {
                this.manager.appendOperationInfo(info);
            }
            this.selected_target.selected_transform_element.recordOriginRect(null);
            this.selected_target.selected_transform_element.recordOriginCombinationRect(null);

            let rightTop =  this.computeRightVerical();

            if(!rightTop) return;
            let _pp = this.painter.worldToCanvas(rightTop);
            this.EventSystem.emit_M(EventName.SelectingTarget, this.selected_target.selected_rect, ev._ev, _pp);
            this.selected_target.selected_rect._attached_elements['is_move'] = true;
        }
        if(TBaseEntity.get_polygon_type(this.selected_target.selected_rect) === 'Group')
        {
            this.combineSelectedCombinationRects();
        }
        if (this.selected_target.selected_rect?._attached_elements?.Entity instanceof TFurnitureEntity) {
            this.manager.layout_container.findAndBindRoomForFurnitures([this.selected_target.selected_rect._attached_elements.Entity]);
        }
        this.updateCandidateRects();
        this._onMouseUpDone();

        this._cad_mode_handler._is_moving_element = false;
        LayoutAI_App.emit_M(EventName.UpdateLayoutScore,true);
        this.selected_target.selected_transform_element._align_line = false;
        this.updateAttributes("init");
        this.update();
    }
    drawCanvas(): void {
        super.drawCanvas();
    }
}