import {  AISpaceDesignManager } from "@/apps/AISpaceDesignManager";
import { AI2DesignBasicModes, IndexedDBService, LayoutAI_App, LayoutAI_Commands, LayoutAI_Configs, LayoutAI_Events, TAppManagerBase } from "@layoutai/layout_scheme";
import { useEffect, useRef, useState, useTransition } from "react";
import { observer } from 'mobx-react-lite';
import useStyles from './style';
import { Button, Modal, message } from '@svg/antd';
import { useNavigate } from "react-router-dom";
import { useStore } from "@/models";
import LeftMenuBar, { ILeftMenuItem } from "@svg/antd-cloud-design/lib/LeftMenuBar";
import LeftPanel from "@/components/LeftPanel";
import TopMenu from "@/components/TopMenu/topMenu";
import AttributeEdit from "@/components/AttributeEdit/attributeEdit";

const stateData = {
  popupType: "Layout",
  sceneMode: "2D",
  prev3DSceneMode: "3D",
  usingPanelType: 0,
  prevSceneUpdateTime: 0
}

const HotelFrame: React.FC = () => {
  const { styles } = useStyles();
  const [preparing, setPreparing] = useState<{ opening: boolean; title: string }>({ opening: false, title: "" });
  const [openingScheme, setOpeningScheme] = useState<boolean>(false);
  const [showSubHandlerBtn, setShowSubHandlerBtn] = useState<boolean>(false);
  const [messageApi, contextHolder] = message.useMessage();
  const messageKey = 'SaveSchemeProgress';
  const [issueReportVisible, setIssueReportVisible] = useState<boolean>(false);

  const [isSaveAs, setIsSaveAs] = useState<boolean>(false);

  const [IsLandscape, setIsLandscape] = useState<boolean>(window.innerWidth < window.innerHeight);

  const [layoutSchemeName, setLayoutSchemeName] = useState<string>(null);
  const object_id = "HotelFrame";
  const navigate = useNavigate();
  const containerRef = useRef(null);
  let store = useStore();

  const updateCanvasSize = () => {
    if (LayoutAI_App.instance) {
      LayoutAI_App.instance.bindCanvas(document.getElementById("cad_canvas") as HTMLCanvasElement);
      LayoutAI_App.instance.update();
    }
    updateLandscape();
  };
  const updateIsWebSiteDebug = () => {
    if (LayoutAI_App.instance) {
    //   LayoutAI_App.instance._is_website_debug = is_debugmode_website;
    }
  }

  const updateLandscape = () => {
    if (LayoutAI_App.instance) {
      LayoutAI_App.instance._is_landscape = window.innerWidth > window.innerHeight;
    }

    document.documentElement.style.setProperty('--vh', `${window.innerHeight * 0.01}px`);
  }
  // const navigate = useNavigate();
  LayoutAI_App.NewApp(AISpaceDesignManager.AppName);

  LayoutAI_App.UseApp(AISpaceDesignManager.AppName);



  useEffect(() => {
    updateIsWebSiteDebug();


    // LayoutAI_App.instance.bindCanvas(document.getElementById("cad_canvas") as HTMLCanvasElement);
    window.addEventListener('resize', updateCanvasSize);
    updateCanvasSize();


    if (LayoutAI_App.instance) {
      if (!LayoutAI_App.instance.initialized) {

        // LayoutAI_App.emit(EventName.OnPreparingHandle, { opening: true, title: "加载方案中...." });
        LayoutAI_App.instance.init();


        LayoutAI_App.instance.Configs.needs_adjust_ceiling_after_matching = true;
        LayoutAI_App.instance.Configs.default_is_auto_sub_area = true;
        LayoutAI_App.instance.Configs.prepare_auto_layout = false;
        LayoutAI_App.instance.Configs.drawing_down_lights_in_ceiling = true;
        LayoutAI_App.instance.Configs.update3d_when_xmlscheme_onloaded = true;
        LayoutAI_App.instance.Configs.is_auto_predict_roomname = true;
        LayoutAI_App.instance.Configs.is_drawing_ceiling_lines_in_2d = true;
        LayoutAI_App.instance.Configs.is_post_add_main_lights = false;
        LayoutAI_App.instance.Configs.app_specific_domain = "Hotel";


        // 默认先进入AiCadMode
        LayoutAI_App.RunCommand(AI2DesignBasicModes.AiCadMode);

        LayoutAI_App.instance.prepare().then(() => {

        })
        LayoutAI_App.instance.bindCanvas(document.getElementById("cad_canvas") as HTMLCanvasElement);

      }
      LayoutAI_App.instance.update();
    }


    return () => {
      LayoutAI_App.off_M_All({ object_id: object_id });


    }
  }, []);

  const items: ILeftMenuItem[] = [
    {
      key: '',
      label: ``,
      icon: [''],
      children: <LeftPanel />
    },
  ]
  const handleMenuCommand = (command: string) => {
    let layout_container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    switch (command) {
      case LayoutAI_Commands.SaveMyLayoutSchemeAs:
        if (layout_container._room_entities.length == 0) {
          messageApi.open({
            key: messageKey,
            type: 'error',
            content:  LayoutAI_App.t("当前方案为空，无法保存！"),
            duration: 2,
            style: {
              marginTop: '13vh',
              zIndex: 9999,
            }
          });
        } else {
          setIsSaveAs(true);
          store.homeStore.setShowSaveLayoutSchemeDialog(true);
        }
        break;
      case LayoutAI_Commands.SaveMyLayoutScheme:
        if (layout_container._room_entities.length == 0) {
          messageApi.open({
            key: messageKey,
            type: 'error',
            content:  LayoutAI_App.t("当前方案为空，无法保存！"),
            duration: 2,
            style: {
              marginTop: '13vh',
              zIndex: 9999,
            }
          });
        } else {
          if (layout_container._layout_scheme_id == null) {
            setIsSaveAs(false);
            store.homeStore.setShowSaveLayoutSchemeDialog(true);
          } else {
            LayoutAI_App.DispatchEvent(LayoutAI_Events.SaveLayoutScheme, null);
          }
        }
        break;
      case LayoutAI_Commands.SaveSchemeAs:
        if (layout_container._room_entities.length == 0) {
          messageApi.open({
            key: messageKey,
            type: 'error',
            content:  LayoutAI_App.t("当前方案为空，无法保存！"),
            duration: 2,
            style: {
              marginTop: '13vh',
              zIndex: 9999,
            }
          });
        } else {
          store.homeStore.setShowSaveLayoutSchemeDialog(true);
        }
        break;
      default:
        break;
    }
  };

  return (
    <div className={styles.root + " " + (IsLandscape ? styles.landscape : "")}>
      <TopMenu create3DLayout={null} title={<><span style={{ color: '#FFFFFF0F' }}>|</span>{(!layoutSchemeName ? "" : " 【" + layoutSchemeName + "】")}</>} handler={handleMenuCommand} />
      <div id='Canvascontent' className={styles.content}>
        <div ref={containerRef} id="body_container" className={styles.canvas_pannel}>
          <div className={styles.side_pannel} id={'pad_left_panel'}>
              <LeftMenuBar items={items} contentClassName={styles.left_content}/>
          </div>
          <canvas
            id="cad_canvas"
            className="canvas"
            onMouseEnter={() => {
              store.homeStore.setIsmoveCanvas(false);
            }}
            onMouseLeave={() => {
              store.homeStore.setIsmoveCanvas(true);
            }}
            onTouchStart={(e) => {
              if (e.touches.length === 2) {
                const dx = e.touches[0].clientX - e.touches[1].clientX;
                const dy = e.touches[0].clientY - e.touches[1].clientY;
                const distance = Math.sqrt(dx * dx + dy * dy)
                store.homeStore.setInitialDistance(distance / store.homeStore.scale);
              }
            }}
            onTouchMove={(e) => {
              if (e.touches.length === 2) {
                const dx = e.touches[0].clientX - e.touches[1].clientX;
                const dy = e.touches[0].clientY - e.touches[1].clientY;
                const distance = Math.sqrt(dx * dx + dy * dy);
                let newScale = distance / store.homeStore.initialDistance;
                if (newScale > 5) {
                  newScale = 5;
                } else if (newScale < 0.05) {
                  newScale = 0.05;
                }
                store.homeStore.setScale(newScale);

                LayoutAI_App.DispatchEvent(LayoutAI_Events.scale, newScale)
              }
            }}
            onTouchEnd={() => {
              store.homeStore.setInitialDistance(null);
            }}
          />
          <div className="canvas_btns">

          </div>
        </div>


      </div>
      <AttributeEdit></AttributeEdit>
    </div>
  );
}

export default observer(HotelFrame);