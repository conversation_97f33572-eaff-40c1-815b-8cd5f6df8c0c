/**
 * 画布坐标轴渲染器 
 * 负责绘制和管理画布的坐标轴
 */
export class CanvasAxisRenderer {
  private _ctx: CanvasRenderingContext2D | undefined;
  private _canvas: HTMLCanvasElement | undefined;
  
  // 坐标轴配置
  private _axisColor: string = 'rgba(255, 0, 0, 0.2)'; // 红色，70%透明度
  private _axisLineWidth: number = 1;
  private _gridLineWidth: number = 0.5;

  // 网格相关属性  
  private _showGrid: boolean = false;
  private _gridColor: string = '#d0d0d0'; // 稍微深一点的网格颜色
  private _gridSize: number = 500; // 网格大小
  private _backgroundColor: string = '#ffffff';

  constructor() {}

  /**
   * 初始化坐标轴渲染器
   */
  init(ctx: CanvasRenderingContext2D, canvas: HTMLCanvasElement): void {
    this._ctx = ctx;
    this._canvas = canvas;
  }

  /**
   * 绘制网格
   */
  drawGrid(offsetX: number, offsetY: number, scale: number): void {
    if (!this._ctx || !this._canvas || !this._showGrid) {
      return;
    }

    const ctx = this._ctx;
    const width = this._canvas.width;
    const height = this._canvas.height;

    // 根据缩放比例动态计算网格大小
    const dynamicGridSize = this._gridSize;

    // 设置网格样式 - 直接使用固定宽度，不受缩放影响
    ctx.strokeStyle = this._gridColor;
    ctx.lineWidth = this._gridLineWidth / scale; // 补偿缩放效果

    // 计算网格起始位置（考虑偏移）
    const startX = Math.floor(-offsetX / scale / dynamicGridSize) * dynamicGridSize;
    const startY = Math.floor(-offsetY / scale / dynamicGridSize) * dynamicGridSize;
    const endX = startX + width / scale + dynamicGridSize;
    const endY = startY + height / scale + dynamicGridSize;

    // 绘制垂直线
    for (let x = startX; x <= endX; x += dynamicGridSize) {
      ctx.beginPath();
      ctx.moveTo(x, startY);
      ctx.lineTo(x, endY);
      ctx.stroke();
    }

    // 绘制水平线
    for (let y = startY; y <= endY; y += dynamicGridSize) {
      ctx.beginPath();
      ctx.moveTo(startX, y);
      ctx.lineTo(endX, y);
      ctx.stroke();
    }

    // 绘制坐标轴（X轴和Y轴）
    this._drawCoordinateAxes(startX, startY, endX, endY, scale);
  }

  /**
   * 绘制背景
   */
  drawBackground(): void {
    if (!this._ctx || !this._canvas || !this._showGrid) {
      return;
    }

    const ctx = this._ctx;
    const width = this._canvas.width;
    const height = this._canvas.height;

    // 绘制背景覆盖整个canvas
    ctx.fillStyle = this._backgroundColor;
    ctx.fillRect(0, 0, width, height);
  }

  /**
   * 绘制坐标轴
   */
  private _drawCoordinateAxes(startX: number, startY: number, endX: number, endY: number, scale: number): void {
    if (!this._ctx) {
      return;
    }

    const ctx = this._ctx;
    
    // 保存当前样式
    ctx.save();
    
    // 设置坐标轴样式
    ctx.strokeStyle = this._axisColor;
    ctx.lineWidth = (this._axisLineWidth * 2) / scale; // 更粗的线条
    
    // 绘制X轴（水平线，y=0）
    ctx.beginPath();
    ctx.moveTo(startX, 0);
    ctx.lineTo(endX, 0);
    ctx.stroke();
    
    // 绘制Y轴（垂直线，x=0）
    ctx.beginPath();
    ctx.moveTo(0, startY);
    ctx.lineTo(0, endY);
    ctx.stroke();
    
    // 绘制原点标记
    ctx.fillStyle = this._axisColor;
    ctx.beginPath();
    ctx.arc(0, 0, 5 / scale, 0, 2 * Math.PI);
    ctx.fill();
    
    // 恢复样式
    ctx.restore();
  }

  /**
   * 设置网格属性
   */
  setGrid(config: {
    visible?: boolean;
    size?: number;
    color?: string;
    lineWidth?: number;
    backgroundColor?: string;
  } = {}): void {
    if (config.visible !== undefined) {
      this._showGrid = config.visible;
    }

    if (config.size !== undefined) {
      this._gridSize = config.size;
    }

    if (config.color !== undefined) {
      this._gridColor = config.color;
    }
    if (config.lineWidth !== undefined) {
      this._gridLineWidth = config.lineWidth;
    }
    if (config.backgroundColor !== undefined) {
      this._backgroundColor = config.backgroundColor;
    }
  }

  /**
   * 获取是否显示网格
   */
  get showGrid(): boolean {
    return this._showGrid;
  }

  /**
   * 销毁坐标轴渲染器
   */
  destroy(): void {
    this._ctx = undefined;
    this._canvas = undefined;
  }
} 
 