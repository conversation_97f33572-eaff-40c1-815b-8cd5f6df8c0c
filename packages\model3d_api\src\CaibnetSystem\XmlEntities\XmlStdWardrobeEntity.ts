import { I_XmlStdWardrobeEntity } from "../IWardrobeEntityInterface";
import { XmlCBoardPartEntity } from "./XmlCBoardPartEntity";
import { XmlCRealFurnitureEntity } from "./XmlCRealFurnitureEntity";
import { XmlCSwingdoorEntity } from "./XmlCSwingdoorEntity";
import { XmlCWardrobeEntity } from "./XmlCWardrobeEntity";
import { XmlCWhSpaceEntity } from "./XmlCWhSpaceEntity";
import { XmlEntityBase } from "../XmlEntityBase";

export class XmlStdWardrobeEntity extends XmlEntityBase implements I_XmlStdWardrobeEntity {
    isFunctorSyncB?: boolean;
    isQuoteB?: boolean;
    classTypeS?: string;
    isBoardPointEditUnlimitedB?: boolean;
    isQuoteSplitN?: number;
    isOutsourcingN?: number;
    ocTypeS?: string;
    spaceSaveTypeN?: number;
    unitEnumN?: number;
    styleSchemeVoIdS?: string;
    RoomIdS?: string;
    __protoflag__?: string;
    Visible?: {
        MiddleVariable?: {
            nameS?: string;
            valueS?: string;
            valueExpressionS?: string;
        };
    };

    CWhMaterialComponent?: Record<string, unknown>;
    CBackBoardComponent?: {
        CGenerateBackBoardData?: {
            idS?: string;
            bbEnumN?: number;
            configIdS?: string;
            generateIdsU?: string;
            braceIdsU?: string;
            backBoardIdsU?: string;
            childrenIdsU?: string;
            boardStyleIdS?: string;
            braceVoS?: string;
            craftVoS?: string;
            hasSlotB?: boolean;
            createTypeN?: number;
            spaceIdN?: number;
            mainPartIdN?: number;
        };
    };
    declare Children?: {
        CRealFurnitureEntity?: XmlCRealFurnitureEntity[];
        CWhSpaceEntity?: XmlCWhSpaceEntity[];
        CBoardPartEntity?: XmlCBoardPartEntity[];
        CSwingdoorEntity?: XmlCSwingdoorEntity[];
        [key: string]: XmlEntityBase[];
    };
    MaterialIDs?: {
        idsS?: string;
    };
    CabinetCornerCuts?: Record<string, unknown>;

    constructor(data?: Partial<I_XmlStdWardrobeEntity>) {
        super(data);

        this.Visible = this.Visible ?? {};
        if(data.Visible)
        {
            this.Visible = {...data.Visible};
        }
        if(data.CBackBoardComponent)
        {
            this.CBackBoardComponent = {...data.CBackBoardComponent}
        }
        if(data.CWhMaterialComponent)
        {
            this.CWhMaterialComponent = {...data.CWhMaterialComponent};
        }

        this.RoomIdS = data.RoomIdS ?? "";
        this.isFunctorSyncB = data.isFunctorSyncB ?? false;
        this.isQuoteB = data.isQuoteB ?? false;
        this.classTypeS = data.classTypeS ?? "";
        this.isBoardPointEditUnlimitedB = data.isBoardPointEditUnlimitedB ?? false;
        this.isQuoteSplitN = data.isQuoteSplitN ?? 0;
        this.isOutsourcingN = data.isOutsourcingN ?? 0;
        this.ocTypeS = data.ocTypeS ?? "";
        this.spaceSaveTypeN = data.spaceSaveTypeN ?? 0;
        this.unitEnumN = data.unitEnumN ?? 0;
        this.styleSchemeVoIdS = data.styleSchemeVoIdS ?? "";
    }
}
