import "./index.less";
import { DesignMaterialService, get_swj_xml_from_url, testAllFormulas } from "@layoutai/model3d_api";
import { getImgDomain } from "@svg/request";
import { Button } from "antd";
import { useEffect, useState } from "react";
import XmlViewer from "./XmlViewer";
import { XmlCRealFurnitureEntity, XmlCWardrobeGroupEntity, XmlEntityBase,parseWardrobeXML,XmlParamsSystem} from "@layoutai/model3d_api";


const XmlContentViewer: React.FC = () => {
    const [queryHistory, setQueryHistory] = useState<{ materialId: string, date: number }[]>([]);
    const [xmlDataText,setXmlDataText] = useState<string>("");
    const onLoadMaterial = async (materialId: string,jsonDataUrl:string = null) => {
        let res = await DesignMaterialService.getDesignMaterialInfoByIds([materialId]);
        if(res[0]?.XmlContentUrl && res[0]?.XmlContentUrl.length > 0)
        {
            let xml_data = await fetch(getImgDomain()+ res[0].XmlContentUrl).then(res=>res.text()).catch(e=>{
                console.log(e);
                return null;
            });
            let obj = new DOMParser().parseFromString(xml_data, "application/xml");
            if(obj && obj.childNodes[0])
            {
                let rootNode = obj.childNodes[0];
   
            }
            setXmlDataText(xml_data);
        }
        else if(res[0]?.ContentUrl)
        {
            let xml_data = await get_swj_xml_from_url(getImgDomain()+ res[0].ContentUrl);
            let id = xml_data.indexOf("<");
            if(id >= 0)
            {
                if(xml_data[id+1] == '<')
                {
                    id=id+1;
                }
            }
            xml_data = xml_data.substring(id);

            let obj = new DOMParser().parseFromString(xml_data, "application/xml");
            if(obj && obj.childNodes[0])
            {
    

                if((obj.documentElement)?.tagName==="CWardrobeEntity" || (obj.documentElement?.tagName==="CWardrobeGroupEntity"))
                {
                    let wardrobeData= parseWardrobeXML(obj.documentElement);

                    console.time("XmlAnalysis")
                    let res =  XmlEntityBase.GenerateEntity(obj.documentElement.tagName, wardrobeData);
                    console.log(res,res.nameS,res.EntityName);

                    let system = new XmlParamsSystem();
                    system.clear();
                    system.bindWardrobeEntity(res);

                    console.log(system);
                    system.checkVisibleB();
                    console.timeEnd("XmlAnalysis")

                }
   

            }
            setXmlDataText(xml_data);
        }
        
      }
    const localStorageKey = {
        queryHistory: 'XmlContentViewer' + "_" + "QueryHistory"
      }
      const saveLocalHistory = (queryHistory: { materialId: string, date: number }[]) => {
        if (queryHistory) {
          if (localStorage) {
            localStorage.setItem(localStorageKey.queryHistory, JSON.stringify(queryHistory));
          }
        }
      }
      const loadLocalHistory = () => {
        if (localStorage) {
          try {
            let data = localStorage.getItem(localStorageKey.queryHistory);
            if (data) {
              let list = JSON.parse(data);
              if (list && list instanceof Array) {
                setQueryHistory(list);
              }
            }
          } catch (error) {
    
          }
    
        }
      }
    const onSaveHistoryItem = (materialId: string) => {
        let history = [...queryHistory];
        let id = history.findIndex((item) => item.materialId == materialId);
        if (id >= 0) {
          history[id].date = new Date().getTime();
          history.sort((a, b) => b.date - a.date);
        }
        else {
          history = [{ materialId: materialId, date: new Date().getTime() }, ...queryHistory];
        }
        history.length = Math.min(30, history.length);
        saveLocalHistory(history);
        setQueryHistory(history);
      }
      const QueryMaterialIdInput = () => {
        const [showHistory, setShowHistory] = useState<boolean>(false);
        return <>
          <input id="designMaterialId" defaultValue={queryHistory[0]?.materialId || ""} autoComplete='off' onFocus={() => {
            setShowHistory(true);
          }} onBlur={() => {
            // setShowHistory(false);
          }}></input>
          <div id="queryHistoryList" className='queryHistoryList' style={{ display: showHistory ? "block" : "none" }}>
            {queryHistory.map((item, index) => <div key={"history_item" + index} className={"item"} onClick={() => {
              console.log(item.materialId);
              let history = [item,...queryHistory.filter((hisItem)=>hisItem!=item)];
              setQueryHistory(history);
              let designMaterialIdInput = document.getElementById("designMaterialId") as HTMLInputElement;
              designMaterialIdInput.value = item.materialId;
    
              setShowHistory(false);
    
    
            }}> {item.materialId}-{new Date(item.date).toLocaleString()}</div>)}
          </div>
          <Button onClick={async () => {
            let value = (document.getElementById("designMaterialId") as HTMLInputElement).value;
    
            const materialId = value;
            onSaveHistoryItem(materialId);
            onLoadMaterial(materialId);
    
          }}>查看模型</Button>
    
        </>
      }

      const TestXmlCases = ()=>{
   
        testAllFormulas();
      }
      useEffect(()=>{
        loadLocalHistory();
        // TestXmlCases();
      },[])
    return <>
        <div className='leftPanelContainer'>
             <QueryMaterialIdInput></QueryMaterialIdInput>
         </div>
         <div style={{ position: "absolute", left: 300 }}>
            <div style={{width:1000,height:700,overflow:"auto"}}>
              <XmlViewer xmlString={xmlDataText}></XmlViewer>
            </div>
        </div>
    </>
}

export default XmlContentViewer;