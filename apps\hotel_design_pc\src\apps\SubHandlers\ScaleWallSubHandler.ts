

import { <PERSON><PERSON><PERSON>, LayoutAI_Commands, TBaseEntity } from "@layoutai/layout_scheme";
import { Vector3 } from "three";
import { I_MouseEvent, ZRect, makeDimensionsOfEdge } from "@layoutai/z_polygon";
import { AI2Base<PERSON>odeHandler } from "../AI2BaseModeHandler";
import { CadBaseSubHandler } from "./CadBaseSubHandler";




export class ScaleWallSubHandler extends CadBaseSubHandler
{
    _previous_rect : ZRect;

    _last_pos : Vector3;
    constructor(cad_mode_handler: AI2BaseModeHandler)
    {
        super(cad_mode_handler);
        this.name = LayoutAI_Commands.Transform_Scaling;
        this._previous_rect = null;

        this._last_pos = null;
    }

    onmousedown(ev: I_MouseEvent): void {
        if(!this.selected_target.selected_transform_element || !this.selected_target.selected_transform_element._target_rect)
        {
            this._cad_mode_handler.runCommand(LayoutAI_Commands.LeaveSubHand<PERSON>);
            return;
        }
        if(this.selected_target && this.selected_target.selected_rect)
        {
            this._cad_mode_handler._is_moving_element = true;
        } 
        else 
        {
            this._cad_mode_handler._is_moving_element = false;
        }


        this._previous_rect = this.selected_target.selected_transform_element._target_rect.clone();
        this.selected_target.selected_transform_element.recordOriginRect(this.selected_target.selected_transform_element._target_rect);
        this.selected_target.selected_transform_element.recordOriginCombinationRect(this.selected_target.selected_combination_entitys);
        this._last_pos = new Vector3(ev.posX,ev.posY,0);
        this.EventSystem.emit_M(EventName.SelectingTarget, null, ev._ev);

    }
    onmousemove(ev: I_MouseEvent): void {
        if(ev.buttons != 1) return;

        let transform_element = this.selected_target.selected_transform_element;
        if(!transform_element) return;
        let current_pos = new Vector3(ev.posX,ev.posY,0);

        let movement = current_pos.clone().sub(this._last_pos);

        transform_element.applyTransformByMovement(movement,this.exsorb_rects);

        this.updateTransformElements();

        this.update();
        
        this.updateAttributes("edit");

    }
    onmouseup(ev: I_MouseEvent): void {
        if(this.selected_target.selected_transform_element)
        {
            this.selected_target.selected_transform_element.doneTransform();
            let info = this.selected_target.selected_transform_element.updateOperaionInfo(this.manager);
            if(info)
            {
                this.manager.appendOperationInfo(info);
            }
            this.selected_target.selected_transform_element.recordOriginRect(null);
            this.selected_target.selected_transform_element.recordOriginCombinationRect(null);
        }
        this.updateAttributes("edit");

        this._cad_mode_handler.runCommand(LayoutAI_Commands.LeaveSubHandler);
    }

    drawCanvas(): void {
        super.drawCanvas();
        if(this.selected_target?.selected_transform_element?._target_rect)
        {
            let entity = TBaseEntity.getEntityOfRect(this.selected_target.selected_transform_element._target_rect);

            if(entity)
            {
                let rect = entity.rect;

                let rightEdge = rect.rightEdge;
    
                let dim = makeDimensionsOfEdge(rightEdge,1);
                dim.offset_len = 240;
                dim._font_size = 2 / this.painter._p_sc;
                
                dim.text_offset_len = 100;
                
                this.painter.drawDimension(dim);
            }

        }
    }
}