import { IEntityWall } from "@layoutai/design_domain";
import { Object2DBase } from "./Object2DBase";
import { TPainter } from "../draw/TPainter";
import { Vector3 } from "three";


/**
 * 墙 2D 对象
 */
export class Wall2D extends Object2DBase {

    constructor(uuid: string) {
        super(uuid);
    }

    public get wall(): IEntityWall {
        return this.entity as IEntityWall;
    }

    public update(): any | undefined {
        if (!this.wall) {
            console.error("墙实体不存在，无法更新");
            return undefined;
        }
        // 更新墙体数据
        return this.wall;
    }

    public hitTest(point: Vector3): boolean {
        if(!this.wall?.rect) {
            return false;
        }
        return this.wall.rect.containsPoint(point);
    }

    /**
     * 计算两点之间的距离（毫米）
     * @param point1 点1
     * @param point2 点2
     * @returns 距离（毫米）
     */
    private distance(point1: { x: number; y: number }, point2: { x: number; y: number }): number {
        const dx = point2.x - point1.x;
        const dy = point2.y - point1.y;
        return Math.sqrt(dx * dx + dy * dy);
    }

    /**
     * 归一化向量
     * @param vector 向量
     * @returns 归一化后的向量
     */
    private normalize(vector: { x: number; y: number }): { x: number; y: number } {
        const length = Math.sqrt(vector.x * vector.x + vector.y * vector.y);
        if (length === 0) {
            return { x: 0, y: 0 };
        }
        return { x: vector.x / length, y: vector.y / length };
    }

    /**
     * 绘制墙体
     * @param ctx Canvas 2D 上下文
     */
    public render(painter: TPainter): void {
        if (!this.wall) {
            console.error("墙实体不存在，无法绘制");
            return;
        }

        // 检查墙体是否可见
        if (!this.wall.visible) {
            return;
        }

        let ctx = painter._context;

        // 获取墙体数据
        const wall = this.wall;
        const startX = wall.startX;
        const startY = wall.startY;
        const endX = wall.endX;
        const endY = wall.endY;
        const thickness = wall.thickness;

        // 计算墙体方向向量
        const dirX = endX - startX;
        const dirY = endY - startY;
        const length = this.distance({ x: startX, y: startY }, { x: endX, y: endY });

        if (length === 0) {
            console.warn("墙体长度为0，跳过绘制");
            return;
        }

        // 归一化方向向量
        const normalizedDir = this.normalize({ x: dirX, y: dirY });

        // 计算法向量（垂直于方向向量）
        const normalX = -normalizedDir.y;
        const normalY = normalizedDir.x;

        // 计算墙体四个角点（考虑厚度）
        const halfThickness = thickness / 2;

        // 墙体四个角点的世界坐标
        const srcPoints = [
            { x: startX + normalX * halfThickness, y: startY + normalY * halfThickness }, // 左上
            { x: startX - normalX * halfThickness, y: startY - normalY * halfThickness }, // 左下
            { x: endX - normalX * halfThickness, y: endY - normalY * halfThickness },     // 右下
            { x: endX + normalX * halfThickness, y: endY + normalY * halfThickness }      // 右上
        ];

        let points = srcPoints.map(point => painter.project2D(point));

        // 保存画布状态
        ctx.save();

        // 绘制墙体主体
        ctx.beginPath();
        ctx.moveTo(points[0].x, points[0].y);
        for (let i = 1; i < points.length; i++) {
            ctx.lineTo(points[i].x, points[i].y);
        }
        ctx.closePath();

        // 设置墙体样式
        ctx.fillStyle = '#e0e0e0'; // 墙体填充色
        ctx.fill();
        ctx.strokeStyle = '#654321'; // 墙体边框色
        ctx.lineWidth = 2;
        ctx.stroke();

        // 恢复画布状态
        ctx.restore();
    }
} 