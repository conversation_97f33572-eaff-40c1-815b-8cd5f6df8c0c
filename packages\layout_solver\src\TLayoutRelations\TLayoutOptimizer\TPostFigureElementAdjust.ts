import { CloseDirectionType, I_Window } from "@layoutai/basic_data";
import { FigureRectForEditing, TFigureElement, TGraphBasicConfigs, TRoom, TRoomShape } from "@layoutai/layout_scheme";
import { ZEdge, compareNames } from "@layoutai/z_polygon";


/**
 *  针对图元级别的后处理
 *  Shiwei - 2024.05.07
 */

export class TPostFigureElementAdjust
{
    static _instance : TPostFigureElementAdjust = null;

    _config = {side_to_wall_dist: 50,back_to_wall_dist:70};
    constructor()
    {   
    }

    public static get instance(): TPostFigureElementAdjust
    {
        if(!TPostFigureElementAdjust._instance)
        {
            TPostFigureElementAdjust._instance = new TPostFigureElementAdjust();
        } 
        return TPostFigureElementAdjust._instance;
    }

    static PostAdjustElements(room:TRoom, figure_elements:TFigureElement[], figure_editing_type:FigureRectForEditing=FigureRectForEditing.Default)
    {
        if(compareNames([room.roomname],["厨房"]))
        {
            TPostFigureElementAdjust.post_process_kitchen_orientaion(room,figure_elements);
            // TPostFigureElementAdjust.instance.post_replace_boards(room,figure_elements);
            return;
        }
        let scope = TPostFigureElementAdjust.instance;

        scope.post_adjust_win_curtain(room,figure_elements,figure_editing_type);
        let adjust_back_to_wall = true;
        if(adjust_back_to_wall)
        {
            //  先处理背景墙等严格靠墙
            scope.post_adjust_back_to_wall_elements(room,figure_elements,figure_editing_type,0);

            //  然后其他物品靠墙要考虑靠在背景墙上
            scope.post_adjust_back_to_wall_elements(room,figure_elements,figure_editing_type,1);
        }
        // 调整两次
        scope.post_adjust_side_to_wall_elements(room,figure_elements,1,figure_editing_type);
        scope.post_adjust_side_to_wall_elements(room,figure_elements,-1,figure_editing_type);


        scope.post_adjust_split_room_elements(room,figure_elements,figure_editing_type);
    }
    static post_remove_unvalid_onwall_elements(room:TRoom,figure_elements:TFigureElement[] = null)
    {
        if(!figure_elements) figure_elements = room._furniture_list;
        let removed_not_onwall_elements : TFigureElement[] = [];
        let onwall_elements = figure_elements.filter((val)=>compareNames([val.sub_category],["背景墙","墙饰","挂画","挂饰"]));

        for(let ele of onwall_elements)
        {
            let back_edge :ZEdge = null;
            room.room_shape._poly.edges.forEach(edge=>{
                if(edge.islayOn(ele.rect.backEdge,300,0.5))
                {
                    back_edge = edge;
                }
            });
            if(!back_edge)
            {
                removed_not_onwall_elements.push(ele);
            }
        }
        for(let ele of removed_not_onwall_elements)
        {
            let id = figure_elements.indexOf(ele);
            if(id >=0)
            {
                figure_elements.splice(id,1);
            }
        }

    }
    static UpdateCabinetsNeighbors(room:TRoom,floor_cabinets:TFigureElement[],attached_element_dist :number = 50,attached_wall_dist:number=110)
    {
        for(let cabinet0 of floor_cabinets)
        {
            let rect0 = cabinet0.rect;
            rect0.clearAttached(TFigureElement.RightElement);
            rect0.clearAttached(TFigureElement.LeftElement);
            rect0.clearAttached(TFigureElement.RightWallEdge);
            rect0.clearAttached(TFigureElement.LeftWallEdge);

           for(let cabinet1 of floor_cabinets)
           {
               if(cabinet0 == cabinet1) continue;
               let rect1 = cabinet1.rect;

               // 保持同方向
               if(!rect0.checkSameNormal(rect1.nor,false)) {
                    if(compareNames([room.roomname],["厨房"]))
                    {
                        let pp = rect0.project(rect1.rect_center);
 
                        if(Math.abs(pp.y) > (rect0.h + rect1.w) / 2) continue;

                        let t_width = rect0.w/2 + rect1.h/2;
 
                        const OnsideElementName = pp.x > 0?TFigureElement.RightElement : TFigureElement.LeftElement;
                                    
                        if(Math.abs(pp.x)-t_width <+attached_element_dist)
                        {
                            let t_dist = Math.abs(pp.x) - t_width;
                            let right_element:TFigureElement = rect0._attached_elements[OnsideElementName] || null;
                            let r_dist = attached_element_dist;
                            if(right_element)
                            {
                                r_dist = Math.abs(rect0.project(right_element.rect.rect_center).x) -t_width;
                            }
            
                            if(t_dist < r_dist)
                            {
                                rect0._attached_elements[OnsideElementName] = cabinet1;
                            }
                        }
                    }
                    else{
                        continue;
                    }
               }
 
               let pp = rect0.project(rect1.rect_center);
 
               if(Math.abs(pp.y) > (rect0.h + rect1.h) / 2) continue;
               let t_width = rect0.w/2 + rect1.w/2;
 
               const OnsideElementName = pp.x > 0?TFigureElement.RightElement : TFigureElement.LeftElement;
 
               if(Math.abs(pp.x)-t_width <+attached_element_dist)
               {
                   let t_dist = Math.abs(pp.x) - t_width;
                   let right_element:TFigureElement = rect0._attached_elements[OnsideElementName] || null;
                   let r_dist = attached_element_dist;
                   if(right_element)
                   {
                       r_dist = Math.abs(rect0.project(right_element.rect.rect_center).x) -t_width;
                   }
 
                   if(t_dist < r_dist)
                   {
                       rect0._attached_elements[OnsideElementName] = cabinet1;
                   }
               }
 
           }
        }


        for(let cabinet of floor_cabinets)
        {
           let rect = cabinet.rect;
           for(let edge of room.room_shape._feature_shape._w_poly.edges)
           {
               if(!rect.checkSameNormal(edge.dv,true)) continue;  // 斜墙处理可能有问题

               let pp = rect.project(edge.center);

               let rect_side_edge = pp.x >0 ? rect.rightEdge:rect.leftEdge;
               const OnSideWallEdge = pp.x > 0 ? TFigureElement.RightWallEdge : TFigureElement.LeftWallEdge;

               if(edge.islayOn(rect_side_edge, attached_wall_dist,0.2) && rect_side_edge.checkSameNormal(edge.nor,false))
               {   
                   let t_old_edge : ZEdge = rect._attached_elements[OnSideWallEdge] || null;
                    if (!t_old_edge || Math.abs(rect.project(t_old_edge.center).x) > Math.abs(pp.x)) {
                        rect._attached_elements[OnSideWallEdge] = edge;
                    }
               }
           }
        }
    }
    static post_process_cabinet_close_direction(room:TRoom, cabinet_elements:TFigureElement[] = null)
    {
        // 厨房暂时不用考虑
        if(compareNames([room.roomname],["厨房"])) return;
        if(!cabinet_elements || cabinet_elements.length == 0) return;

        TPostFigureElementAdjust.UpdateCabinetsNeighbors(room,cabinet_elements,50, 50);


        for(let fe of cabinet_elements)
        {
            let left_check0 = 0;
            let right_check0 = 0;

            const close_direction_list:CloseDirectionType[] =["左右见光","右收口","左收口","左右收口"];


            if(fe.rect._attached_elements[TFigureElement.LeftWallEdge] || fe.rect._attached_elements[TFigureElement.LeftElement]) // 左侧靠墙
            {
                left_check0 = 1;
            }   

            if(fe.rect._attached_elements[TFigureElement.RightWallEdge] || fe.rect._attached_elements[TFigureElement.RightElement]) // 左侧靠墙
            {
                right_check0 = 1;
            }

            let rect = fe.rect;

            if(left_check0 && !right_check0)
            {
     
                let left_element:TFigureElement = rect._attached_elements[TFigureElement.LeftElement];
                let right_element:TFigureElement = rect._attached_elements[TFigureElement.RightElement];

                let left_wall :ZEdge = rect._attached_elements[TFigureElement.LeftWallEdge];
                let right_wall :ZEdge = rect._attached_elements[TFigureElement.RightWallEdge];

                rect._u_dv_flag = -rect._u_dv_flag;
                rect.updateRect();

                rect._attached_elements[TFigureElement.LeftElement] = right_element;
                rect._attached_elements[TFigureElement.RightElement] = left_element;
                rect._attached_elements[TFigureElement.LeftWallEdge] = right_wall;
                rect._attached_elements[TFigureElement.RightWallEdge] = left_wall;


                left_check0 = 1;
                right_check0 = 0;
            }

            let left_check = 0;
            let right_check = 0;

            if(left_check0)
            {
                if(rect.u_dv_flag > 0)
                {
                    left_check = 1;
                }
                else{
                    right_check = 1;
                }
            }
            if(right_check0)
            {
                if(rect.u_dv_flag > 0)
                {
                    right_check = 1;
                }
                else{
                    left_check = 1;
                }
            }

            let t_i = left_check * 1 + right_check * 2;

            fe.params.close_direction = close_direction_list[t_i] as any;
        }

    }
    static post_process_kitchen_orientaion(room:TRoom,figure_elements:TFigureElement[])
    {
         if(!compareNames([room.roomname],["厨房"])) return;

         let floor_cabinets = figure_elements.filter((ele)=>compareNames([ele.sub_category],["地柜"]));
         let hangon_cabinets = figure_elements.filter((ele)=>compareNames([ele.sub_category],["吊柜"]));
         TPostFigureElementAdjust.UpdateCabinetsNeighbors(room,floor_cabinets);
         TPostFigureElementAdjust.UpdateCabinetsNeighbors(room,hangon_cabinets);

         // 处理转角柜

         floor_cabinets.forEach((cabinet)=>{
            if(compareNames([cabinet.sub_category],["转角","收口板"]))
            {
                let rect = cabinet.rect;
                let left_element:TFigureElement = rect._attached_elements[TFigureElement.LeftElement];
                let right_element:TFigureElement = rect._attached_elements[TFigureElement.RightElement];

                let left_wall :ZEdge = rect._attached_elements[TFigureElement.LeftWallEdge];
                let right_wall :ZEdge = rect._attached_elements[TFigureElement.RightWallEdge];

                let left_check = 0;
                if(left_element && !(left_element.sub_category.endsWith("板")))
                {
                    left_check = 1;
                }
                let right_check = 0;
                if(right_element && !(right_element.sub_category.endsWith("板")))
                {
                    right_check = 1;
                }
                if(right_check) // 左边连结了非板件的柜子
                {
                    rect._u_dv_flag = -rect._u_dv_flag;
                    rect.updateRect();
                    rect._attached_elements[TFigureElement.LeftElement] = right_element;
                    rect._attached_elements[TFigureElement.RightElement] = left_element;
                    rect._attached_elements[TFigureElement.LeftWallEdge] = right_wall;
                    rect._attached_elements[TFigureElement.RightWallEdge] = left_wall;
                }
                else if(left_check)
                {

                }

                TPostFigureElementAdjust.kitchen_replace_close_boards(cabinet);

                
            }
         });

         hangon_cabinets.forEach((cabinet)=>{
            if(compareNames([cabinet.category],["转角"]))
            {
                let rect = cabinet.rect;
                let left_element:TFigureElement = rect._attached_elements[TFigureElement.LeftElement];
                let right_element:TFigureElement = rect._attached_elements[TFigureElement.RightElement];

                let left_wall :ZEdge = rect._attached_elements[TFigureElement.LeftWallEdge];
                let right_wall :ZEdge = rect._attached_elements[TFigureElement.RightWallEdge];

                let left_check = 0;
                if(left_element && !(left_element.sub_category.endsWith("板")))
                {
                    left_check = 1;
                }
                let right_check = 0;
                if(right_element && !(right_element.sub_category.endsWith("板")))
                {
                    right_check = 1;
                }

                if(right_check) // 左边连结了非板件的柜子
                {
                    rect._u_dv_flag = -rect._u_dv_flag;
                    rect.updateRect();
                    rect._attached_elements[TFigureElement.LeftElement] = right_element;
                    rect._attached_elements[TFigureElement.RightElement] = left_element;
                    rect._attached_elements[TFigureElement.LeftWallEdge] = right_wall;
                    rect._attached_elements[TFigureElement.RightWallEdge] = left_wall;
                }



            }
            TPostFigureElementAdjust.kitchen_replace_close_boards(cabinet);

         });



    }

    static kitchen_replace_close_boards(cabinet:TFigureElement)
    {
        if(!compareNames([cabinet.category,cabinet.sub_category],["收口板"])) return;
        let rect = cabinet.rect;

        let left_check = rect._attached_elements[TFigureElement.RightWallEdge] || rect._attached_elements[TFigureElement.RightElement];
        let right_check = rect._attached_elements[TFigureElement.LeftWallEdge] || rect._attached_elements[TFigureElement.LeftElement];


  

        if(!left_check || !right_check)
        {
            const visible_board_width = 18;
            let flag = (!right_check)?1:-1;
            let pos = rect.unproject({x:(rect.w/2-visible_board_width/2)*flag,y:0});

            rect._w = visible_board_width;
            rect.rect_center =pos;

            let cabinet_type = compareNames([cabinet.category],["吊柜"])?"吊柜":"地柜";
            cabinet.category = "小板件-"+cabinet_type+"见光板";
            cabinet.sub_category = cabinet_type+"见光板";
            cabinet.public_category = cabinet.sub_category;


        }
    }

        /**
     * 侧贴墙规则
     * ../../../../../..param room 
     * ../../../../../..param figure_elements 
     */
    private post_adjust_back_to_wall_elements(room:TRoom,figure_elements:TFigureElement[],
            
            figure_editing_type:FigureRectForEditing=FigureRectForEditing.Default,level:number =0)
        {
            let back_to_wall_dist = this._config.back_to_wall_dist;
            if(level == 0)
            {
                back_to_wall_dist = 1000; // 背景墙要强制贴墙
            }

            let background_walls = figure_elements.filter((ele)=>{
                return compareNames([ele.sub_category],["背景墙"]);
             });
            // 先找到要背靠墙的图元
            let target_figure_elements : TFigureElement[] = [];

            if(level == 0)
            {
                target_figure_elements = background_walls;
            }
            else if(level == 1)
            {
                target_figure_elements = figure_elements.filter((ele)=>{
                    return !compareNames([ele.sub_category],["背景墙"])
                })
            }
    
            let poly = room.room_shape?._feature_shape?._w_poly;
            if(!poly) return;
    
            let wall_edges : ZEdge[] = [...poly.edges];

            if(level == 1) // 要把背景墙当做参考图元
            {
                background_walls.forEach((ele)=>{
                    let rect =  figure_editing_type === FigureRectForEditing.Default? ele.rect : ele.matched_rect;
                    if(rect){
                        let t_edge = rect.frontEdge._deep_clone();
                        t_edge._nor.copy(rect.nor);
                        t_edge._nor.negate();
                        t_edge._ex_props['background_wall'] = 1;
                        wall_edges.push(t_edge);
                    }

                })
            }
            
            for(let ele of target_figure_elements)
            {
                let rect =  figure_editing_type === FigureRectForEditing.Default? ele.rect : ele.matched_rect;
                if(!rect) continue;
    
                let closest_back_wall_edge : ZEdge = null;
    
                let back_edge = rect.backEdge;
    
                let min_dist =  1000;
                let max_back_to_wall_dist = min_dist;
                wall_edges.forEach((edge)=>{

                    if(edge.islayOn(back_edge,Math.abs(max_back_to_wall_dist),0.2))
                    {
                        if(rect.checkSameNormal(edge.nor.clone().negate(),false))
                        {
                            let dist = -edge.projectEdge2d(back_edge.center).y;

                            if(!closest_back_wall_edge || dist < min_dist)
                            {
                                closest_back_wall_edge = edge;
                                min_dist = dist;
                            }
            
                            

                        }
                    }
                });

                if(closest_back_wall_edge)
                {
                    if(min_dist < back_to_wall_dist) // 包括小于0的情况
                    {
                        let offset_dv = closest_back_wall_edge.nor.clone().multiplyScalar(min_dist);
    
                        rect.back_center = rect.back_center.add(offset_dv);
                        rect.updateRect();
    
                    }
                }
    
    
            }
    
    
    
    
        }
    
    /**
     * 侧贴墙规则
     * ../../../../../..param room 
     * ../../../../../..param figure_elements 
     */
    private post_adjust_side_to_wall_elements(room:TRoom,figure_elements:TFigureElement[],side_flag:number=1,figure_editing_type:FigureRectForEditing=FigureRectForEditing.Default)
    {
        // 先找到要侧靠墙的图元
        let target_figure_elements : TFigureElement[] = figure_elements;

    
        let poly = room.room_shape?._feature_shape?._w_poly;
        if(!poly) return;

        
        let attached_wall_categories = [...TGraphBasicConfigs.MainEleCategories,...TGraphBasicConfigs.MainCabinetsCategories,...TGraphBasicConfigs.OnwallEleCategories];
        for(let ele of target_figure_elements)
        {
            let rect =  figure_editing_type === FigureRectForEditing.Default? ele.rect : ele.matched_rect;
            if(!rect) continue;

            let closest_side_wall_edge : ZEdge = null;
            // 部分图元只作裁剪
            let min_target_wall_dist = compareNames([ele.sub_category],attached_wall_categories)? this._config.side_to_wall_dist:0;  
            

            let left_edge = rect.leftEdge;
            let right_edge = rect.rightEdge;

            let min_dist = 100000;
            poly.edges.forEach((edge)=>{
                if(side_flag < 0)
                {
                    if(left_edge.checkSameNormal(edge.nor,false) && edge.islayOn(left_edge,Math.abs(min_dist),0.3))
                    {
                        let dist = -edge.projectEdge2d(left_edge.center).y;
                        if(!closest_side_wall_edge || Math.abs(dist) < Math.abs(min_dist))
                        {
                            closest_side_wall_edge = edge;
                            min_dist = dist;
                        }
                    }
                }
                else{
                    if(right_edge.checkSameNormal(edge.nor,false) && edge.islayOn(right_edge,Math.abs(min_dist),0.3))
                    {
                        let dist = -edge.projectEdge2d(right_edge.center).y;
                        if(!closest_side_wall_edge || Math.abs(dist) < Math.abs(min_dist))
                        {
                            closest_side_wall_edge = edge;
                            min_dist = dist;
                        }
                    }
                }
   


            });

            if(closest_side_wall_edge)
            {
                if(min_dist < min_target_wall_dist && Math.abs(min_dist) < rect.w/2) // 包括小于0的情况
                {
                    if(min_dist < 0)  // 如果穿墙了, 以裁剪处理为主
                    {
                        let offset_dv = rect.dv.clone().multiplyScalar(side_flag).multiplyScalar(min_dist/2);
                        rect._w += min_dist;
                        rect.back_center = rect.back_center.add(offset_dv);
                        rect.updateRect();

                    }
                    else{
                        let offset_dv = rect.dv.clone().multiplyScalar(side_flag).multiplyScalar(min_dist);

                        rect.back_center = rect.back_center.add(offset_dv);
                        rect.updateRect();
                    }
  



                }
            }


        }




    }


    /**
     *  对挡门的情况进行后处理
     */
    private post_adjust_occulusion_of_doors(room:TRoom,figure_elements:TFigureElement[],figure_editing_type:FigureRectForEditing=FigureRectForEditing.Default)
    {



    }

    /**
     * 分割空间的图元
     *    --- 其实一般只有 "一字型淋浴房"
     * ../../../../../..param room 
     * ../../../../../..param figure_elements 
     * ../../../../../..param figure_editing_type 
     */
    private post_adjust_split_room_elements(room:TRoom,figure_elements:TFigureElement[],figure_editing_type:FigureRectForEditing=FigureRectForEditing.Default)
    {
        let poly = room.room_shape?._feature_shape?._w_poly;
        if(!poly) return;

        let target_figure_elements : TFigureElement[] = figure_elements.filter((ele)=>{
            return compareNames([ele.sub_category],["一字形淋浴房"])
        });
        for(let ele of target_figure_elements)
        {
            let rect =  figure_editing_type === FigureRectForEditing.Default? ele.rect : ele.matched_rect;
            if(!rect) continue;
            let left_edge = rect.leftEdge;
            let right_edge = rect.rightEdge;

            let left_wall_edge :ZEdge = null;
            let right_wall_edge : ZEdge = null;

            let left_min_dist = 100000;
            let right_min_dist = 100000;
            let side_flag = 1;
            poly.edges.forEach((edge)=>{
                if(left_edge.checkSameNormal(edge.nor,false) && edge.islayOn(left_edge,Math.abs(left_min_dist),0.3))
                {
                    let dist = -edge.projectEdge2d(left_edge.center).y;
                    if(!left_wall_edge || Math.abs(dist) < left_min_dist)
                    {
                        left_wall_edge = edge;
                        left_min_dist = dist;
                        side_flag = -1;
                    }
                }

                if(right_edge.checkSameNormal(edge.nor,false) && edge.islayOn(left_edge,Math.abs(right_min_dist),0.3))
                {
                    let dist = -edge.projectEdge2d(right_edge.center).y;
                    if(!right_wall_edge || Math.abs(dist) < right_min_dist)
                    {
                        right_wall_edge = edge;
                        right_min_dist = dist;
                        side_flag = 1;
                    }
                }
            });

            if(left_wall_edge && right_wall_edge)
            {
                let ll = rect.project(left_wall_edge.center).x;
                let rr = rect.project(right_wall_edge.center).x;

                if(ll < rr) // 左 小于 右
                {
                    let w = rr - ll;
                    let center = rect.unproject({x:(ll+rr)/2,y:0});
                    rect._w = w;
                    rect.rect_center = center;
                }
            }

        }
    }

    /**
     * 一些居中对齐的关系 微调
     * ../../../../../..param room  
     * ../../../../../..param figure_elements 
     * ../../../../../..param figure_editing_type 
     */
    private post_adjust_center_pairs(room:TRoom,figure_elements:TFigureElement[], figure_editing_type:FigureRectForEditing=FigureRectForEditing.Default)
    {

    }

    private post_adjust_length_filling(room:TRoom,figure_elements:TFigureElement[], figure_editing_type:FigureRectForEditing=FigureRectForEditing.Default)
    {
        let target_figure_elements : TFigureElement[] = figure_elements.filter((ele)=>{
            return compareNames([ele.sub_category],[...TGraphBasicConfigs.MainCabinetsCategories])
        });

        let poly = room.room_shape?._feature_shape?._w_poly;
        if(!poly) return;

        
        for(let ele of target_figure_elements)
        {
            let rect =  figure_editing_type === FigureRectForEditing.Default? ele.rect : ele.matched_rect;
            if(!rect) continue;
            

        }
    }



    private post_adjust_win_curtain(room:TRoom,figure_elements:TFigureElement[], figure_editing_type:FigureRectForEditing=FigureRectForEditing.Default )
    {
        let curtain_elements = figure_elements.filter((ele)=>compareNames([ele.category],["窗帘"]));


        curtain_elements.forEach((ele)=>{
            let t_edge : ZEdge = null;
            let t_win : I_Window = null;
            let min_dist = 100000;
            for(let edge of room.room_shape._poly.edges)
            {
                let wins = TRoomShape.getWindowsOfEdge(edge);

                if(wins)
                {
                    for(let win of wins)
                    {
                        if(win.rect && win.rect.rect_center.distanceTo(ele.rect.rect_center)<min_dist)
                        {
                            min_dist = win.rect.rect_center.distanceTo(ele.rect.rect_center);
                            t_edge = edge;
                            t_win = win;
                        }
                    }
                }
            }

            if(t_win && t_edge)
            {
                let pp = t_edge.projectEdge2d(t_win.rect.rect_center);
                let ww = t_win.length+100;

                let rr = Math.min(pp.x + ww/2,t_edge.length);

                let ll = Math.max(pp.x - ww/2,0);

                ww = rr - ll;

                let pos = t_edge.unprojectEdge2d({x:(ll+rr)/2,y:0});

                ele.rect.back_center = pos;
                ele.rect.nor = t_edge.nor.clone().negate();

                ele.rect._w = ww;
                ele.rect.updateRect();
            }
        });
    }
}